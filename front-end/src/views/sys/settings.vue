<template>
  <div class="app-container" v-loading="loading" style="position: relative">
    <div style="position: absolute;right: 10px;top: 20px;z-index: 999">
      <router-link :to="{name:'pod-index',query:{'cluster':'k8s0','namespace':'foneshare','app':'fs-k8s-app-manager'}}" target="_blank">
        <span style="font-size: 14px;color: #409EFF;margin-right: 30px;">查看本系统实例</span>
      </router-link>
      <el-button
        type="text"
        style="padding: 0;float: right;"
        @click="clear_cache">清除系统缓存
      </el-button>
    </div>
    <div style="position: relative">
      <el-tabs tab-position="top" v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="settings.json" name="settings.json">
          <div style="font-size: 14px;position: absolute; right: 10px;top: 0;z-index: 999">
            <el-button type="text" @click="getImagesDistinct" style="color: #aaa;margin-right: 30px;">获取去重后的镜像</el-button>
            <clipboard-icon :text="JSON.stringify(this.settings, null, 2)" button-text="一键复制"></clipboard-icon>
          </div>
          <vue-json-pretty :data="this.settings">
          </vue-json-pretty>
        </el-tab-pane>
        <el-tab-pane label="oncall.json" name="oncall.json">
          <div style="font-size: 14px;position: absolute; right: 10px;top: 0;z-index: 999">
            <clipboard-icon :text="JSON.stringify(this.oncallConfig, null, 2)" button-text="一键复制"></clipboard-icon>
          </div>
          <vue-json-pretty :data="this.oncallConfig">
          </vue-json-pretty>
        </el-tab-pane>
        <el-tab-pane label="lb-pools.json" name="lb-pools.json">
          <div style="font-size: 14px;position: absolute; right: 10px;top: 0;z-index: 999">
            <clipboard-icon :text="JSON.stringify(this.lbPools, null, 2)" button-text="一键复制"></clipboard-icon>
          </div>
          <vue-json-pretty :data="this.lbPools"></vue-json-pretty>
        </el-tab-pane>
        <el-tab-pane label="parent-pom" name="parent-pom">
          <parent-pom></parent-pom>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog title="内容" :visible.sync="dialogVisible" width="50%">
      <div style="margin-top: -30px;">
        <div style="text-align: center;">
          <clipboard-icon :text="this.dialogText" button-text="一键复制"></clipboard-icon>
        </div>
        <pre style="padding: 5px;border:solid 1px #999;">{{ this.dialogText }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty'
import {clearCache, getLBPools, getOncallConfig} from "@/api/sys";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";
import ParentPom from "@/views/sys/parent-pom.vue";
import fa from 'element-ui/src/locale/lang/fa';

export default {
  data() {
    return {
      loading: false,
      dialogVisible:false,
      dialogText: "",
      oncallConfig: {
        "err": "数据未加载"
      },
      lbPools: {
        "err": "数据未加载"
      },
      settings: {},
      activeTab: 'settings.json'
    }
  },
  mounted() {
    let tabName = this.$route.query.tabName
    if (tabName) {
      this.activeTab = tabName
    }
    this.loadOncallConfig()
    this.loadLBPools()
    this.settings = this.$settings
  },
  methods: {
    loadOncallConfig() {
      getOncallConfig().then(response => {
        this.oncallConfig = response.data;
      }).catch((e) => {
        this.$message.error("oncall config load fail, err:" + e.message);
      })
    },
    loadLBPools() {
      getLBPools().then(response => {
        this.lbPools = response.data;
      }).catch((e) => {
        this.$message.error("lb pools load fail, err:" + e.message);
      })
    },
    clear_cache() {
      this.loading = true
      clearCache().then(response => {
        this.$message.success("操作成功");
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    handleTabClick(tab, event) {
      this.$router.push({
        query: {tabName: tab.name}
      })
    },
    getImagesDistinct() {
      let items = []
      for (let clu of this.settings.clusters) {
        for (let img of clu.baseImages) {
          if (items.indexOf(img) === -1) {
            items.push(img)
          }
        }
      }
      this.dialogText = items.join("\n")
      this.dialogVisible = true
    }
  },
  components: {
    ParentPom,
    ClipboardIcon,
    VueJsonPretty
  },
}
</script>

