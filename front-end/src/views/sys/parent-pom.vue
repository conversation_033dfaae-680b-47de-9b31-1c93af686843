<template>
  <div class="app-container" v-loading="loading" style="position: relative">
    <div>
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData"
               @submit.native.prevent size="small">
        <el-form-item label="关键字">
          <el-input v-model.trim="searchForm.keyword" style="width: 320px" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="text" icon="el-icon-circle-plus-outline" @click="addParentPom"
                     style="margin-left: 20px;">新建
          </el-button>
          <export-button :table-ref="this.$refs.table001"></export-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="table001" :data="tableData" style="width: 100%">
      <el-table-column prop="name" label="名称"/>
      <el-table-column prop="showName" label="显示名称"/>
      <el-table-column prop="remark" label="描述"/>
      <el-table-column prop="isArchive" label="是否归档">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isArchive" :disabled="true"></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="enable" label="是否启用">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.enable" :disabled="true"></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="namespaces" label="命名空间">
        <template slot="header">
          命名空间
          <el-tooltip effect="dark" placement="top" content="表示允许被使用的环境，空则表示不做环境限制">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="sortRank" label="排序分数">
        <template slot="header">
          排序分数
          <el-tooltip effect="dark" placement="top" content="分数越高，排序越靠前">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="CreatedAt"
        label="创建时间"
      >
        <template slot-scope="scope">
          {{ (scope.row.CreatedAt && scope.row.CreatedAt.length > 19) ? scope.row.CreatedAt.slice(0, 19) : scope.row.CreatedAt }}
        </template>
      </el-table-column>
      <el-table-column
        prop="UpdatedAt"
        label="更新时间"
      >
        <template slot-scope="scope">
          {{ (scope.row.UpdatedAt && scope.row.UpdatedAt.length > 19) ? scope.row.UpdatedAt.slice(0, 19) : scope.row.UpdatedAt }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="editParentPom(scope.row)">编辑</el-button>
          <el-button type="text" @click="deleteParentPom(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-dialog :visible.sync="dialogVisible" :title="editForm.id ? '编辑父pom' : '新建父pom'" width="600px">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="名称">
          <el-input v-model.trim="editForm.name" :disabled="editForm.id !== 0">
          </el-input>
        </el-form-item>
        <el-form-item label="显示名称">
          <el-input v-model.trim="editForm.showName"/>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model.trim="editForm.remark"/>
        </el-form-item>
        <el-form-item label="允许的命名空间">
          <el-input v-model.trim="editForm.namespaces" placeholder=""/>
          <div style="color: #666;margin-top: -10px;font-size: 12px;">多个命名空间用逗号分隔，空白表示允许所有命名空间</div>
        </el-form-item>
        <el-form-item label="排序分数">
          <el-input v-model.number="editForm.sortRank" type="number" min="0"/>
        </el-form-item>
        <el-form-item label="是否归档">
          <el-switch v-model="editForm.isArchive"/>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="editForm.enable"/>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveParentPom">保存</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>

import {deleteParentPom, saveParentPom, searchParentPom} from "@/api/parentpom";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: 'ParentPom',
  components: {ExportButton},
  data() {
    return {
      loading: false,
      searchForm: {
        keyword: '',
      },
      tableData: [],
      dialogVisible: false,
      editForm: {
        id: 0,
        name: '',
        remark: '',
        isArchive: false,
        sortRank: 0,
        namespaces: '',
      },
    }
  },
  mounted() {
    this.loadTableData()
  },
  methods: {
    saveParentPom() {
      this.loading = true
      saveParentPom(this.editForm).then(res => {
        this.loadTableData()
        this.dialogVisible = false
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    getMaxSortRank() {
      let ret = 1;
      for (let item of this.tableData) {
        if (item.sortRank > ret) {
          ret = item.sortRank
        }
      }
      return ret
    },
    addParentPom() {
      this.dialogVisible = true
      this.editForm = {
        id: 0,
        name: 'fxiaoke-parent-pom-',
        showName: '',
        remark: '',
        isArchive: false,
        enable: true,
        sortRank: this.getMaxSortRank() + 2,
      }
    },
    editParentPom(row) {
      this.dialogVisible = true
      this.editForm = {
        id: row.ID,
        name: row.name,
        showName: row.showName,
        remark: row.remark,
        isArchive: row.isArchive,
        enable: row.enable,
        sortRank: row.sortRank,
        namespaces: row.namespaces,
      }
    },
    deleteParentPom(row) {
      this.$confirm('确定删除该父pom吗? ' + row.name, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteParentPom({id: row.ID}).then(res => {
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false
        })
      })
    },
    loadTableData() {
      this.loading = true
      searchParentPom(this.searchForm).then(res => {
        this.tableData = res.data
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    }
  },
}
</script>

