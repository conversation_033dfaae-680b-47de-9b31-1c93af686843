<template>
  <div class="app-deploy-status-container">
    <el-card class="box-card" :body-style="{ padding: '0px 5px' }">
      <div slot="header" class="clearfix">
        <span class="card-panel-text">应用部署情况<small style="padding-left: 10px;">（按访问历史倒序）</small></span>
        <div style="display: inline-block; padding-left: 120px;font-size: 12px;">
          <el-badge :value="'实例数'" type="primary"
                    style="background-color:#dcdfe6;border: 1px solid #dcdfe6;margin: 2px 8px;">
            <div style="display: inline-block;padding: 2px 5px">运行环境</div>
            <div style="display: inline-block;padding: 2px 5px;background-color: white" >运行版本</div>
          </el-badge>
        </div>
      </div>
      <div class="component-item">
        <div :class="className" :style="{height:height,width:width}">
          <el-table
            :data="tableData"
            v-loading="tableLoading"
            :max-height="tableHeight"
            style="width: 100%">
            <el-table-column
              label=""
              width="100px">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="el-icon-position"
                  @click="pipelinePage(scope.row)">发布流程
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="app"
              width="280"
              label="应用">
            </el-table-column>
            <el-table-column
              prop="namespace"
              label="运行情况">
                <template slot-scope="scope">
                  <div style="padding: 5px">
                    <el-badge v-for="it in scope.row.status" v-if="it && it.replicas > 0" :value="it.replicas" type="primary" style="background-color:#dcdfe6;border: 1px solid #dcdfe6;margin: 2px 8px;">
                        <div style="display: inline-block;padding: 2px 5px">{{it.namespace}}</div>
                        <div style="display: inline-block;padding: 2px 5px;background-color: white" >{{ it.version }}</div>
                    </el-badge>
                  </div>
                </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {getAppDeployStatus} from "@/api/dashboard";

export default {
  props: {
    className: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    tableHeight: {
      type: String,
      default: '300'
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.tableLoading = true
      getAppDeployStatus().then(response => {
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
  }
}
</script>

<style>

.app-deploy-status-container .el-table td  {
  padding: 5px 0;
}
</style>
