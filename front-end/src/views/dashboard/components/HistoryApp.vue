<template>
  <div class="chart-wrapper">
    <el-card class="box-card" :body-style="{ overflowY: 'auto', padding: '0px 5px' }">
      <div slot="header" class="clearfix" style="font-weight: bold;">
        <i class="el-icon-aim"></i>
        <span class="card-panel-text">访问的历史应用</span>
      </div>
      <div class="component-item">
        <div :class="className" :style="{height:height,width:width}">
          <div v-for="it in apps">
            <el-button type="text" @click="pipelinePage(it)" style="padding: 5px">{{ it }}</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {getUserInfo} from "@/api/user";

export default {
  props: {
    className: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: 'auto'
    }
  },
  data() {
    return {
      apps: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      getUserInfo().then(response => {
        this.apps = response.data.recentApps
      }).catch((e) => {
        this.$message.error(e.message)
      })
    },
    pipelinePage(app) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app":app}});
    },
  }
}
</script>
