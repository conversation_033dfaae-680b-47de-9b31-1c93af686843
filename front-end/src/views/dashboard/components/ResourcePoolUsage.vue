<template>
  <div class="resource-pool-usage-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-panel-text">资源池使用情况</span>
        <div style="display: inline-block;padding-left: 20px">
          <el-checkbox v-model="hideCommon" @change="hideCommonToggle">隐藏【通用】资源池</el-checkbox>
        </div>
        <span style="float: right">
           <el-button type="text" @click="clusterCapacityPage" style="padding: 6px 10px;">查看详情</el-button>
        </span>
        <el-select v-model="cluster" placeholder="请选择集群" size="mini" @change="loadData" style="float: right;margin-right: 20px;">
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </div>
      <div class="component-item">
        <div id="echarts-wrapper" :style="{height:height,width:width}"/>
      </div>
    </el-card>
  </div>
</template>

<script>
import echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import {getResourcePool} from "@/api/dashboard";

const animationDuration = 1000

export default {
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      hideCommon:false,
      cluster:"",
      pool: {
        "name": [],
        "cpuCapacity": [],
        "cpuRequire": [],
        "cpuLeft": [],
        "memCapacity": [],
        "memRequire": [],
        "memLeft": [],
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.loadData()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    }
  },
  methods: {
    clusterCapacityPage() {
      this.$message.info("todo")
    },
    hideCommonToggle() {
      this.$message.info("todo")
    },
    loadData() {
      if(!this.cluster) {
        this.chart.setOption({
          title: {
            text: '请选择集群',
            textStyle: {
              color: '#666'
            },
            top: '20%',
            subtext: '',
            left: 'center'
          }
        });
        return;
      }
      this.chart.showLoading();
      getResourcePool(this.cluster).then(response => {
        this.pool = {
          "name": [],
          "cpuCapacity": [],
          "cpuRequire": [],
          "cpuLeft": [],
          "memCapacity": [],
          "memRequire": [],
          "memLeft": [],
        }
        if (!response.data || response.data.size < 1) {
          this.chart.setOption({
            title: {
              text: '资源池使用情况',
              subtext: '没有任何数据',
              left: 'center'
            }
          });
          return
        }
        for (let it of response.data) {
          this.pool.name.push(it.name)
          this.pool.cpuCapacity.push(it.cpuCapacity)
          this.pool.cpuRequire.push(it.cpuRequire)
          this.pool.cpuLeft.push(it.cpuCapacity - it.cpuRequire)
          this.pool.memCapacity.push(it.memCapacity)
          this.pool.memRequire.push(it.memRequire)
          this.pool.memLeft.push(it.memCapacity - it.memRequire)
        }
        console.log(this.pool)
        this.chart.setOption({
          xAxis: {
            data: this.pool.name,
          },
          series: [
            {
              name: '内存-已分配',
              data: this.pool.memRequire
            },
            {
              name: '内存-剩余',
              data: this.pool.memLeft
            },
            {
              name: 'CPU-已分配',
              data: this.pool.cpuRequire,
            },
            {
              name: 'CPU-剩余',
              data: this.pool.cpuLeft,
            }
          ]
        });

      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.chart.hideLoading();
      })
    },
    initChart() {
      this.chart = echarts.init(document.getElementById("echarts-wrapper"), 'macarons')

      this.chart.setOption({
        tooltip: {
        },
        legend: {
        },
        grid: {
          top: 30,
          left: 5,
          right: 5,
          bottom: 5,
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.pool.name,
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [
          {
            type: 'value',
            name:"内存值",
            position: 'left',
            axisLabel: {
              formatter: '{value} Gi'
            }
          },
          {
            type: 'value',
            name:"CPU值",
            position: 'right',
            axisTick: {
              show: true
            },
            axisLabel: {
              formatter: '{value} Core'
            }
          }
        ],
        series: [
          {
            name: '内存-已分配',
            type: 'bar',
            stack: 'mem',
            barWidth: '30',
            barGap:"5%",
            data: this.pool.memRequire,
            itemStyle: {
              color: '#ffb980'
            },
            label: {
              show: true,
              formatter: '{c}'
            },
            animationDuration
          },
          {
            name: '内存-剩余',
            type: 'bar',
            stack: 'mem',
            barWidth: '30',
            barGap:"5%",
            data: this.pool.memLeft,
            itemStyle: {
              color: '#2fc7c9'
            },
            label: {
              show: true,
              formatter: '{c}'
            },
            animationDuration
          },
          {
            name: 'CPU-已分配',
            type: 'bar',
            stack: 'cpu',
            barWidth: '30',
            yAxisIndex: 1,
            barGap:"5%",
            data: this.pool.cpuRequire,
            itemStyle: {
              color: '#fc8452'
            },
            label: {
              show: true,
              formatter: '{c}'
            },
            animationDuration
          },
          {
            name: 'CPU-剩余',
            type: 'bar',
            stack: 'cpu',
            barWidth: '30',
            yAxisIndex: 1,
            barGap:"5%",
            data: this.pool.cpuLeft,
            itemStyle: {
              color: '#91cc75'
            },
            label: {
              show: true,
              formatter: '{c}'
            },
            animationDuration
          }
        ]
      })
    }
  }
}
</script>
