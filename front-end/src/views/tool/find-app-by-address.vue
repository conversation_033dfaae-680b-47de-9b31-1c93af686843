<template>
  <div class="app-container" v-loading="loading">
    <div style="text-align: center;margin-bottom: 10px;">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="findApp" @submit.native.prevent>
        <el-form-item>
          <el-input v-model.trim="searchForm.address" style="width: 460px" :placeholder="inputPlaceholder">
            <el-select v-model="searchForm.addrType" slot="prepend" placeholder="请选择" style="width: 180px;" @change="addrTypeChange">
              <el-option
                v-for="item in addrTypeOptions"
                :key="item.name"
                :label="item.label"
                :value="item.name">
              </el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="findApp">查询</el-button>
        </el-form-item>
      </el-form>
      <el-card class="box-card" v-if="app.name" style="width: 460px;margin: 30px auto;text-align: left">
        <el-form label-width="80px">
          <el-form-item label="运行环境:" style="margin-bottom:0;">
            {{ app.namespace }}
          </el-form-item>
          <el-form-item label="k8s集群:" style="margin-bottom:0;">
            {{ app.cluster }}
          </el-form-item>
          <el-form-item label="应用名称:" style="margin-bottom:0;">
            {{ app.name }}
          </el-form-item>
          <el-form-item v-if="app.pod" label="Pod名称:" style="margin-bottom:0;">
            {{ app.pod }}
          </el-form-item>
        </el-form>
        <div style="margin-top: 10px;">
          <el-button
            type="text"
            class="el-icon-position"
            style="margin: 0 20px;"
            @click="pipeline_page">发布流程页面
          </el-button>
          <el-button
            type="text"
            class="el-icon-menu"
            @click="pod_page">实例管理页面
          </el-button>
        </div>
      </el-card>
    </div>

  </div>
</template>


<script>
import {findAppByAddress, findPodByIP} from "@/api/tool";
import AppAddress from '@/views/components/app-address'

export default {
  mounted() {
    let addrType = this.addrTypeOptions[0];
    this.inputPlaceholder = addrType.placeholder;
    this.searchForm.addrType = addrType.name;
  },
  computed: {},
  components: {
    AppAddress
  },
  data() {
    return {
      loading: false,
      searchForm: {
        addrType: "",
        address: ""
      },
      inputPlaceholder: "",
      addrTypeOptions: [
        {
          name: "nodeVIP",
          label: "访问地址查应用",
          placeholder: "请输入应用访问地址，格式为 IP:PORT",
        },
        {
          name: "podIP",
          label: "Pod IP查实例",
          placeholder: "请输入pod ip",
        },
      ],
      app: {}
    }
  },
  methods: {
    findApp() {
      let addr = this.searchForm.address
      if (!addr) {
        this.$message.error("地址不能为空");
        return
      }
      this.loading = true;
      let api = this.searchForm.addrType === "nodeVIP" ? findAppByAddress : findPodByIP;
      api(this.searchForm.address).then(response => {
        this.app = response.data
      }).catch((e) => {
        this.$message.error(e.message);
        this.app = {}
      }).finally(() => {
        this.loading = false;
      })
    },
    addrTypeChange(v) {
      let addrType = this.addrTypeOptions.filter(item => {
        return item.name === v
      })
      this.inputPlaceholder = addrType && addrType[0] ? addrType[0].placeholder : "";
    },
    pipeline_page() {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": this.app.name}});
    },
    pod_page() {
      this.$router.push({
        name: 'pod-index', query: {
          "cluster": this.app.cluster,
          "namespace": this.app.namespace,
          "app": this.app.name,
        }
      });
    }
  }
}
</script>
