<template>
  <div class="app-container" v-loading="loading">
    <div>
      <div style="position: absolute;right: 30px;top: -10px;z-index: 999">
        <export-button :table-ref="this.$refs.table001" file-name="lb-split-for-apibus"></export-button>
      </div>
      <el-table :data="data" style="width: 100%" size="mini" :highlight-selection-row="true" ref="table001">
        <el-table-column type="index" width="30"></el-table-column>
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="app" label="应用名称" sortable></el-table-column>
        <el-table-column label="环境">
          <template slot-scope="scope">
            {{ scope.row.cluster }} / {{ scope.row.namespace }}
          </template>
        </el-table-column>
        <el-table-column prop="runningPodNum" label="运行副本" width="80">
          <template slot-scope="scope">
            <span :style="(isNaN(Number(scope.row.runningPodNum)) || scope.row.runningPodNum === 0) ? 'color:orangered;font-weight:bold;' : ''">
              {{ scope.row.runningPodNum }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="portName" label="端口名"></el-table-column>
        <el-table-column prop="beforeAddr" label="Before地址 (被引用配置文件数)" min-width="220">
          <template slot-scope="scope">
            <span>{{ scope.row.beforeAddr }}</span>
            <span><clipboard-icon :text="scope.row.beforeAddr"></clipboard-icon></span>
            <span style="font-weight: bold;color:#e36a08;">({{ scope.row.beforeAddrCmsRefCounter }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="afterAddr" label="After地址 (被引用配置文件数)" min-width="220">
          <template slot-scope="scope">
            <span>{{ scope.row.afterAddr }}</span>
            <span><clipboard-icon :text="scope.row.afterAddr"></clipboard-icon></span>
            <span style="font-weight: bold;color:#e36a08;">({{ scope.row.afterAddrCmsRefCounter }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="cmsRefCounter" label="操作">
          <template slot-scope="scope">
            <el-button style="padding: 0" type="text" @click="batchEditCMS('', scope.row.beforeAddr, scope.row.afterAddr)">去修改</el-button>
            <el-button style="color:#999;" type="text" @click="batchEditCMS('', scope.row.afterAddr,scope.row.beforeAddr,'确定要打开回滚页面吗？')">回滚</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import {loadApiBusAddr, searchCmsConfig} from "@/api/tool";
import ClipboardIcon from '../components/clipboard-icon.vue';
import ExportButton from "@/views/components/export-button.vue";
import {deploymentDetail} from "@/api/k8s/app";

export default {
  components: {ExportButton, ClipboardIcon},
  data() {
    return {
      data: [],
      loading: false,
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {},
  methods: {
    loadData() {
      this.loading = true
      loadApiBusAddr().then(response => {
        let newData = []
        for(let it of  response.data) {
          this.fnSearchCmsConfig(it)
          this.findDeployment(it)
          newData.push(it)
        }
        this.data = newData
        this.$message.success("数据加载成功")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    batchEditCMS(profile, oldContent, newContent, confirmMsg) {
      if (confirmMsg) {
        this.$confirm(confirmMsg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let url = `/api/page/redirect?type=cmsBatchEdit&profile=${profile}&oldContent=${oldContent}&newContent=${newContent}&_t` + Date.now();
          window.open(url)
        })
      } else {
        let url = `/api/page/redirect?type=cmsBatchEdit&profile=${profile}&oldContent=${oldContent}&newContent=${newContent}&_t` + Date.now();
        window.open(url)
      }
    },
    fnSearchCmsConfig(row) {
      row.beforeAddrCmsRefCounter = "--"
      searchCmsConfig(row.beforeAddr).then(response => {
        row.beforeAddrCmsRefCounter = response.data.length
      }).catch((e) => {
        row.beforeAddrCmsRefCounter = "error"
        console.log(e.message)
      }).finally(() => {
      })
      row.afterAddrCmsRefCounter = "--"
      searchCmsConfig(row.afterAddr).then(response => {
        row.afterAddrCmsRefCounter = response.data.length
      }).catch((e) => {
        row.afterAddrCmsRefCounter = "error"
        console.log(e.message)
      }).finally(() => {
      })
    },
    findDeployment(row) {
      row.runningPodNum = "--"
      deploymentDetail(row.cluster, row.namespace, row.app).then(response => {
        row.runningPodNum = response.data.replicas
      }).catch((e) => {
        row.runningPodNum = e.message
      });
    },
  }
}
</script>

<style scoped>

</style>
