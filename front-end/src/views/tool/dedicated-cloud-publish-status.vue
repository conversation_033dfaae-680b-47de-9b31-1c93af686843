<template>
  <div  class="app-container">
    <el-progress :text-inside="true" :stroke-width="24" :percentage="progress" status="success"></el-progress>
    <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        prop="app"
        label="应用"
        width="180">
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="180">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

export default {
  name: "clone",
  data() {
    return {
      progress: 0,
      tableData: [],
      timerId: null,
    }
  },
  mounted() {
    let _this=this
    setTimeout(function (){
      _this.loadData()
    }, 10)
  },
  beforeDestroy: function () {
    clearTimeout(this.timerId);
    console.log("clear timer, id:" + this.timerId);
  },
  methods: {
    loadData() {
      this.$message.info("todo...")
      // publishDedicatedCloudProgress().then(response => {
      //   this.tableData = response.data.appStatus;
      //   this.progress = response.data.progress
      //   let _this = this
      //   if (this.progress !== 100) {
      //     setTimeout(function () {
      //       _this.loadData()
      //     }, 3000)
      //   }
      // }).catch((e) => {
      //   this.$message.error(e.message);
      // });
      // for (let i=0; i<= 100;i++) {
      //   console.log("kkkkk")
      //
      //   // this.progress = i
      //   let _this = this
      //   this.timerId = setTimeout(function (){
      //     console.log(i)
      //     _this.progress=i
      //   }, 10000)
      // }
    }
  }
}
</script>

<style scoped>

</style>
