<template>
  <div class="app-container" v-loading="loading">
    <div>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="环境">
          <el-select v-model="form.env" value-key="id" filterable style="width: 260px;">
            <el-option
              v-for="item in envOptions"
              :key="item.id"
              :label="item.id"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="LB地址">
          <el-select v-model="form.lbAddr" filterable style="width: 420px;">
            <el-option
              v-for="item in lbOptions"
              :key="item.addr"
              :label="item.addr + ' / ' + item.name + ' / ' + item.remark"
              :value="item.addr">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
          <export-button :table-ref="this.$refs.table001" file-name="k8s-lb-split"></export-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="data" style="width: 100%" size="mini" ref="table001" :highlight-selection-row="true">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="app" label="应用名称" sortable></el-table-column>
        <el-table-column prop="appLevel" label="应用等级" width="80"></el-table-column>
        <el-table-column prop="appMainOwner" label="应用负责人" width="120"></el-table-column>
        <el-table-column label="环境">
          <template slot-scope="scope">
            {{ scope.row.cluster }} / {{ scope.row.namespace }}
          </template>
        </el-table-column>
        <el-table-column prop="portName" label="端口名"></el-table-column>
        <el-table-column prop="remark" label="Remark"></el-table-column>
        <el-table-column prop="beforeAddr" label="Before地址 (被引用配置文件数)" min-width="220">
          <template slot-scope="scope">
            <span>{{ scope.row.beforeAddr }}</span>
            <span><clipboard-icon :text="scope.row.beforeAddr"></clipboard-icon></span>
            <span style="font-weight: bold;color:#e36a08;">({{ scope.row.beforeAddrCmsRefCounter }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="afterAddr" label="After地址 (被引用配置文件数)" min-width="220">
          <template slot-scope="scope">
            <span>{{ scope.row.afterAddr }}</span>
            <span><clipboard-icon :text="scope.row.afterAddr"></clipboard-icon></span>
            <span style="font-weight: bold;color:#e36a08;">({{ scope.row.afterAddrCmsRefCounter }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="cmsRefCounter" label="操作">
          <template slot-scope="scope">
            <el-button style="padding: 0" type="text" @click="batchEditCMS('', scope.row.beforeAddr, scope.row.afterAddr)">去修改</el-button>
            <el-button style="color:#999;" type="text" @click="batchEditCMS('', scope.row.afterAddr,scope.row.beforeAddr,'确定要打开回滚页面吗？')">回滚</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import {loadAppByLbAddr, searchCmsConfig} from "@/api/tool";
import {getLBPools} from "@/api/sys";
import ExportButton from "@/views/components/export-button.vue";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  components: {ClipboardIcon, ExportButton},
  data() {
    return {
      form: {
        env: {
          cluster: null,
          namespace: null
        },
        lbAddr: '',
      },
      data: [],
      lbPools: [],
      loading: false,
    }
  },
  mounted() {
    this.loadLBPools();
  },
  computed: {
    lbOptions: function () {
      let ret = []
      for (const pool of this.lbPools) {
        if (pool.cluster === this.form.env.cluster) {
          for (const lb of pool.lbAllocList) {
            ret.push({
              name: lb.name,
              addr: lb.addr,
              remark: lb.remark,
            })
          }
        }
      }
      if (ret.length > 0) {
        ret.unshift({
          name: '_all_',
          addr: '_all_',
          remark: '所有LB地址'
        })
      }
      return ret
    },
    envOptions: function () {
      const clusters = []
      for (const clu of this.$settings.clusters) {
        clusters.push({
          cluster: clu.name,
          namespace: '_all_',
          id: clu.name + '/_all_'
        })
        for (const nm of clu.namespaces) {
          const c = {}
          c.cluster = clu.name
          c.namespace = nm
          c.id = clu.name + '/' + nm
          clusters.push(c)
        }
      }
      return clusters
    },

  },
  methods: {
    loadData() {
      if (!this.form.env.cluster || !this.form.env.namespace || !this.form.lbAddr) {
        this.$message.error('请选择查询条件')
        return
      }
      this.loading = true
      loadAppByLbAddr(this.form.env.cluster, this.form.env.namespace, this.form.lbAddr).then(response => {
        let newData = []
        for(let it of  response.data) {
          this.fnSearchCmsConfig(it)
          newData.push(it)
        }
        this.data = newData
        this.$message.success("数据加载成功")
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    loadLBPools() {
      getLBPools().then(response => {
        this.lbPools = response.data;
      }).catch((e) => {
        this.$message.error("lb pools load fail, err:" + e.message);
      })
    },
    batchEditCMS(profile, oldContent, newContent,confirmMsg) {
      if (confirmMsg) {
        this.$confirm(confirmMsg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let url = `/api/page/redirect?type=cmsBatchEdit&profile=${profile}&oldContent=${oldContent}&newContent=${newContent}&_t` + Date.now();
          window.open(url)
        })
      } else {
        let url = `/api/page/redirect?type=cmsBatchEdit&profile=${profile}&oldContent=${oldContent}&newContent=${newContent}&_t` + Date.now();
        window.open(url)
      }
    },
    fnSearchCmsConfig(row) {
      row.beforeAddrCmsRefCounter = "--"
      searchCmsConfig(row.beforeAddr).then(response => {
        row.beforeAddrCmsRefCounter = response.data.length
      }).catch((e) => {
        row.beforeAddrCmsRefCounter = "error"
        console.log(e.message)
      }).finally(() => {
      })
      row.afterAddrCmsRefCounter = "--"
      searchCmsConfig(row.afterAddr).then(response => {
        row.afterAddrCmsRefCounter = response.data.length
      }).catch((e) => {
        row.afterAddrCmsRefCounter = "error"
        console.log(e.message)
      }).finally(() => {
      })
    }
  }
}
</script>

<style scoped>

</style>
