<template>
  <div class="app-container">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData"
             @submit.native.prevent>
      <el-form-item label="关键字">
        <el-input v-model.trim="searchForm.keyword" style="width: 260px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" icon="el-icon-circle-plus-outline" @click="showEditDialog()"
                   style="margin-left: 20px;">新建
        </el-button>
      </el-form-item>
    </el-form>


    <table style="color: rgb(119, 119, 119);font-size: 12px;padding: 10px;">
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">定时重启</th>
        <td style="width: 1000px;">
          服务会在配置的时间点上进行重启，按批重启所有实例，每批启动25%的实例
        </td>
      </tr>
    </table>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>
      <el-table-column label="运行环境"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="所在集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="重启时间点"
                       align="center"
                       prop="rebootHour" width="140">
        <template slot-scope="scope">
          每天的 <b>{{ scope.row.rebootHour }}</b> 点
        </template>
      </el-table-column>
      <el-table-column label="创建人"
                       align="center"
                       prop="author">
      </el-table-column>
      <el-table-column label="备注"
                       prop="remark"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="操作"
                       width="120px"
                       fixed="right">
        <template slot-scope="scope">

          <el-popconfirm :title="'确定要删除吗？'" @confirm="deleteReboot(scope.row)">
            <el-button
              style="margin-right: 5px"
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="创建服务重启任务" :visible.sync="dialogVisible" width="700px">
      <el-form :model="editForm" ref="dialogEditForm" label-width="120px" :rules="editFormRules">
        <el-form-item label="应用" prop="app">
          <el-input v-model.trim="editForm.app"></el-input>
        </el-form-item>
        <el-form-item label="k8s集群">
          <el-select v-model="editForm.cluster" style="width: 100%;"
                     @change="clusterChange">
            <el-option
              v-for="item in clusterOptions"
              :key="item.name"
              :label="item.name + ' (' + item.description + ')'"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运行环境">
          <el-select v-model="editForm.namespace" style="width: 100%">
            <el-option
              v-for="item in namespaceOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="重启时间点">
          <el-select v-model="editForm.rebootHour" style="width: 100%">
            <el-option
              v-for="item in [23,0,1,2,3,4,5,6]"
              :key="item"
              :label="item + '点'"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model.trim="editForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="create()" v-loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {createReboot, deleteReboot, searchReboot} from "@/api/reboot";

export default {
  components: {},
  mounted() {
    this.loadTableData()
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.editForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.editForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  data() {
    return {
      searchForm: {
        keyword: ""
      },
      editForm: {},
      editFormRules: {
        app: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cluster: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        namespace: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        rebootHour: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ]
      },
      tableData: [],
      tableLoading: false,
      dialogVisible: false,
      submitLoading: false,
      appOptions: [],
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchReboot(this.searchForm.keyword).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    editFormReset() {
      this.editForm = {
        app: "",
        cluster: "",
        namespace: "",
        rebootHour: 23,
        remark: "",
      }
    },
    clusterChange() {
      this.editForm.namespace = ""
    },
    showEditDialog() {
      this.editFormReset();
      this.dialogVisible = true;
    },
    deleteReboot(row) {
      deleteReboot(row.id).then(response => {
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    create() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.submitLoading = true
        createReboot(this.editForm).then(response => {
          this.dialogVisible = false
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.submitLoading = false
        })
      });
    }
  }
}
</script>
