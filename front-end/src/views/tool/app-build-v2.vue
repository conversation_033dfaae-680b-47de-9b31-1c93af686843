<template>
  <div class="app-container" v-loading="pageLoading">
    <el-alert
      title="应用批量构建"
      type="info"
      description="使用当前运行的版本重新构建镜像。"
      show-icon>
    </el-alert>
    <el-form label-width="120px" style="max-width: 800px;">
      <el-form-item label="查询条件" style="margin: 3px">
        <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 5, maxRows: 10}"
                  placeholder="内容格式：集群/环境/应用名，多个之间用换行分割"></el-input>
      </el-form-item>
      <el-form-item label="构建备注" style="margin: 3px">
        <el-input v-model="buildRemark"></el-input>
      </el-form-item>
      <el-form-item style="margin: 3px;text-align: right">
        <el-button type="primary" @click="loadTableData" size="small" style="margin: 3px;">查询</el-button>
      </el-form-item>
    </el-form>

    <el-card class="box-card" style="margin-top: 20px;">
      <el-table
        :data="tableData"
        border
        fit
        highlight-current-row>
        <el-table-column type="index">
        </el-table-column>
        <el-table-column
          type="selection"
          width="50">
        </el-table-column>

        <el-table-column label="构建过？"
                         width="100px">
          <template slot-scope="scope">
            {{ scope.row.extraAttr.reDeployed }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="160px">
          <template slot-scope="scope">
            <div>
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;color: #FF9800"
                @click="reBuild(scope.row)">使用当前版本构建
              </el-button>
              <br/>
              <router-link :to="{name: 'cicd-image-build-history', query: {}}" target="_blank">
                <el-button
                  type="text"
                  size="mini"
                  style="margin-top: 0;padding-top: 0;">
                  历史
                </el-button>
              </router-link>
              <router-link :to="{name: 'cicd-app-deploy', query: {'app': scope.row.app}}" target="_blank">
                <el-button
                  type="text"
                  size="mini"
                  style="margin-top: 0;padding-top: 0;">
                  发布流程
                </el-button>
              </router-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="应用名"
                         prop="app">
          <template slot-scope="scope">
            <div style="font-size: 10px;">
              <div>{{ scope.row.app }}</div>
              <div style="size: 10px;color: #999;">{{ scope.row.namespace }} ({{ scope.row.cluster }})</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100px">
        </el-table-column>
        <el-table-column label="模块数" prop="status" width="80px" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.appModules.length }}</div>
            <div>
              <el-button
                type="text"
                @click="contentShow(scope.row)">查看
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="模块镜像">
          <template slot-scope="scope">
            <div style="font-size: 10px;">
              <div>{{ scope.row.extraAttr.deployModuleImages }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog :title="contentDialog.title" :visible.sync="contentDialog.visible" width="70%" top="5vh" :close-on-click-modal="false">
      <div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ this.contentDialog.content }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {searchPipelineByProperties} from "@/api/pipeline";
import {deploymentWholeDetail} from "@/api/k8s/app";
import {buildImageWithCurrentVersion} from "@/api/job";

export default {
  components: {},
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      pageLoading: false,
      searchForm: "",
      buildRemark: "",
      tableData: [],
      contentDialog: {
        visible: false,
        title: '',
        content: ''
      }
    }
  },
  methods: {
    loadTableData() {
      this.pageLoading = true
      this.tableData = []
      searchPipelineByProperties(this.searchForm).then(response => {
        for (let it of response.data) {
          it.extraAttr.deployModuleImages = "?"
          it.extraAttr.reDeployed = "NO"
          it.extraAttr.deployModules = "?"
          this.findDeployment(it)
        }
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.pageLoading = false
      })
    },
    findDeployment(pipe) {
      if (pipe === null) {
        this.$message.warning(`找不到发布流程，应用：${cluster} / ${namespace} / ${app}`)
        return
      }
      deploymentWholeDetail(pipe.cluster, pipe.namespace, pipe.app).then(response => {
        if (response.data.spec.template.spec.initContainers.length > 0) {
          let deployModuleImages = []
          for (let it of response.data.spec.template.spec.initContainers) {
            deployModuleImages.push(it.image)
          }
          pipe.extraAttr.deployModuleImages = deployModuleImages.join("<br/>")
          pipe.extraAttr.deployModules = response.data.spec.template.spec.initContainers
        }
      }).catch((e) => {
        console.log(e.message)
      });
    },
    reBuild(pipe) {
      buildImageWithCurrentVersion(pipe.cluster, pipe.namespace, pipe.app, this.buildRemark).then(response => {
        this.$message.success("操作成功")
        pipe.extraAttr.reDeployed = "YES"
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    contentShow(row) {
      this.contentDialog.content = row.extraAttr.deployModules
      this.contentDialog.title = "应用：" + row.app
      this.contentDialog.visible = true
    },
  }
}
</script>
