<template>
  <div class="app-container">
    <el-dialog title="格式化内容" :visible.sync="dialogVisible" width="50%" top="5vh" :close-on-click-modal="false">
      <div style="margin-top: -50px;overflow: auto;">
        <div style="text-align: center">
          <el-button type="text" @click="copyToClipboard" size="mini" icon="el-icon-document-copy">一键复制内容</el-button>
        </div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ exportData }}</pre>
      </div>
    </el-dialog>
    <div style="text-align: center;margin-top: -10px;">
      <el-button type="text" @click="dialogVisible=true;" size="mini" >查看格式化内容</el-button>
      <export-button :table-ref="this.$refs.table001"></export-button>
    </div>
    <el-table
      v-loading="tableLoading"
      ref="table001"
      :data="tableData"
      element-loading-text="数据加载中..."
      :default-sort="{prop: 'status', order: 'ascending'}"
      border
      fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="环境"
                       sortable
                       prop="cluster">
        <template slot-scope="scope">
          {{ scope.row.cluster }} / {{ scope.row.namespace }}
        </template>
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>
      <el-table-column label="持久存储">
        <template slot-scope="scope">
          <div>
            {{ scope.row.pvc.enable ? scope.row.pvc.name : '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="资源池" prop="resourcePool">
      </el-table-column>
      <el-table-column label="应用信息"
                       prop="appRemark" width="320px;">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 14px;">
            <div v-if="scope.row.appRemark">
              描述：{{ scope.row.appRemark }}
            </div>
            <div v-if="scope.row.appOwner">
              Owner：{{ scope.row.appOwner }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center"
                       :filters="[{ text: '正常', value: 'enabled' }, { text: '已迁移', value: 'migrated' },{ text: '待审核', value: '待审核' }]"
                       :filter-method="filterStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'enabled'" type="success">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
          <el-tag v-else type="warning">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="配置实例数" align="center" prop="replicas" sortable>
      </el-table-column>
      <el-table-column label="修改时间" prop="updatedTime" sortable>
      </el-table-column>
      <el-table-column
        label="操作"
        width="120px"
        fixed="right">
        <template slot-scope="scope">
          <router-link :to="{name: 'cicd-app-deploy', query: {app: scope.row.app}}" target="_blank" style="margin-left: 2px;">
            <span style="color:#409EFF;font-weight: 500;">发布流程页</span>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

import {searchPipeline} from "@/api/pipeline";
import {searchApp} from "@/api/app";
import ExportButton from "@/views/components/export-button.vue";

export default {
  components: {ExportButton},
  data() {
    return {
      tableData: [],
      apps: {},
      tableLoading: false,
      cmsUpdateVisible: false,
      cmsUpdateOld: {
        cluster: "",
        namespace: "",
        app: "",
        text: "",
      },
      cmsUpdateNew: {
        cluster: "",
        namespace: "",
        app: "",
        text: "",
      },
      dialogVisible: false,
    }
  },
  computed: {
    exportData: function () {
      let data = [];
      for (let it of this.tableData) {
        data.push({
          "cluster": it.cluster,
          "namespace": it.namespace,
          "app": it.app,
          "appRemark": it.appRemark,
          "appOwner": it.appOwner,
          "status": it.status,
          "replicas": it.replicas,
          "pvc": it.pvc.enable ? it.pvc.name : '-',
          "resourcePool": it.resourcePool,
          "updatedTime": it.updatedTime,
        })
      }
      return JSON.stringify(data, null, 2);
    }
  },
  mounted() {
    this.loadAppAndPipeline();
  },
  methods: {
    loadAppAndPipeline() {
      this.tableLoading = true;
      searchApp({
        keyword: "",
        page: 1,
        limit: 10000,
      }).then(response => {
        for (let it of response.data.data) {
          this.apps[it.name] = it
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
        this.loadTableData()
      })
    },
    loadTableData() {
      this.tableLoading = true;
      searchPipeline({"cluster": "k8s1", "page": 1, "limit": 10000}).then(response => {
        this.tableData = response.data.data
        for (let it of this.tableData) {
          it.appRemark = "";
          it.appOwner = "";
          let appObj = this.apps[it.app]
          if (appObj) {
            it.appRemark = appObj.remark;
            if (appObj.admins) {
              it.appOwner = appObj.admins.join(",")
            }
            it.appOwner = it.appOwner + (it.appOwner && appObj.mainOwner ? "," : "") + appObj.mainOwner;
          }
          it.resourcePool = it.schedule.node ? it.schedule.node : "-";
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    filterStatus(value, row) {
      return row.status === value
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "正常"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        default:
          return "未知"
      }
    },
    copyToClipboard() {
      let text = this.exportData
      if (!text) {
        this.$message.warning("内容为空")
        return
      }
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message.success("复制成功")
        })
        .catch(() => {
          const input = document.createElement('input')
          document.body.appendChild(input)
          input.setAttribute('value', text)
          input.select()
          if (document.execCommand('copy')) {
            document.execCommand('copy')
          }
          document.body.removeChild(input)
          this.$message.success("复制成功")
        })
    }
  }
}
</script>

