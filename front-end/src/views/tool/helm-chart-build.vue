<template>
  <div class="app-container">
    <div>
      <el-form ref="searchForm" :model="searchForm">
        <el-form-item label="部署到纯私有环境">
          <el-switch
            @change="cloudTypeChange"
            v-model="privateCloud"
            :active-value="true"
            :inactive-value="false">
          </el-switch>
        </el-form-item>
        <el-form-item label="选择集群" prop="cluster">
          <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" @change="loadApp" style="width: 600px;" :disabled="privateCloud">
            <el-option
              v-for="item in this.clusterOptions"
              :key="item.name"
              :label="item.name + ' (' + item.description + ')'"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择环境" prop="namespace">
          <el-select v-model="searchForm.namespace" placeholder="选择Namespace" filterable @change="loadApp" style="width: 600px;" :disabled="privateCloud">
            <el-option
              v-for="item in  this.namespaceOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择应用" prop="app">
          <el-select v-model="searchForm.app" filterable placeholder="选择应用" style="width: 600px;" multiple>
            <el-option value="" label="全部"></el-option>
            <el-option
              v-for="item in this.appOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="覆盖环境" v-if="privateCloud">
          <el-select v-model="searchForm.overrideNamespace" style="width: 340px;">
            <el-option value="cmhk-crm-di-std" label="招商局集团测试（cmhk-crm-di-std）"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div style="margin-top: 20px;padding-left: 160px">
            <el-button type="text" @click="logHistory" style="margin-left: 20px;">查看历史结果</el-button>
            <el-button type="primary" @click="createHelmChartBuildJob">开始构建</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="jobResult.length > 0" style="margin-top: 20px;border: 1px solid #ccc;background-color: #eee; padding: 5px;width: 760px;font-size: 12px;">
      <div v-for="item in jobResult">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<script>
import {searchDeployment} from "@/api/k8s/app";
import {helmChartBuild} from "@/api/tool";

export default {
  components: {},
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
  },
  data() {
    return {
      searchForm: {
        cluster: '',
        namespace: '',
        app: '',
        overrideNamespace: "",
      },
      appOptions: [],
      jobResult: [],
      privateCloud: false
    }
  },
  methods: {
    cloudTypeChange() {
      console.log(this.privateCloud)
      if (this.privateCloud) {
        // this.searchForm.cluster = "k8s0"
        // this.searchForm.namespace = "firstshare"
        this.searchForm.cluster = "forceecrm-k8s1"
        this.searchForm.namespace = "forceecrm-public-prod"
        this.loadApp();
      } else {
        this.searchForm.overrideNamespace = ""
      }
    },
    loadApp() {
      let cluster = this.searchForm.cluster;
      let namespace = this.searchForm.namespace;
      if (!cluster || !namespace) {
        this.appOptions = [];
        return;
      }
      let vThis = this;
      searchDeployment(cluster, namespace).then(response => {
        vThis.appOptions = response.data;
      }).catch((e) => {
        console.error(e);
      });
    },
    createHelmChartBuildJob() {
      if (this.privateCloud && !this.searchForm.overrideNamespace) {
        this.$message.warning("私有部署环境，必须选择覆盖环境")
        return
      }
      helmChartBuild(this.searchForm.cluster, this.searchForm.namespace, this.searchForm.app, this.searchForm.overrideNamespace).then(response => {
        this.jobResult.push("- 操作成功，构建完成后结果会存储到审计日志。构建服务比较多时，耗时会比较长。" + response.data)
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {

      })
    },
    logHistory() {
      let rou = this.$router.resolve({name: 'log-list', query: {"operate": "helm-chart-build-job"}});
      window.open(rou.href, '_blank');
    },
  }
}
</script>
