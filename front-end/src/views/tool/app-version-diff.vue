<template>
  <div class="app-container">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData">
      <el-form-item label="集群">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" style="width: 100%;"
                   @change="clusterChange">
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="name">
      </el-table-column>
      <el-table-column label="运行环境"
                       sortable
                       prop="namespace">
      </el-table-column>
      <el-table-column label="所在集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="实例数"
                       prop="targetCPUUtilizationPercentage">
        <template slot-scope="scope">
          {{ scope.row.targetCPUUtilizationPercentage }}%
        </template>
      </el-table-column>
      <el-table-column label="最小副本数"
                       prop="minReplicas">
      </el-table-column>
      <el-table-column label="最大副本数"
                       prop="maxReplicas">
      </el-table-column>
      <el-table-column label="操作"
                       width="320px"
                       fixed="right">
        <template slot-scope="scope">
          <el-button
            style="margin-right: 16px"
            type="text"
            icon="el-icon-edit"
            @click="showHpaDialog(scope.row)"
            slot="reference">编辑
          </el-button>
          <el-popconfirm :title="'确定要删除【 ' + scope.row.name + ' 】吗？'" @confirm="deleteHpa(scope.row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="创建/编辑扩缩容配置" :visible.sync="dialogVisible">
      <el-form :model="editForm" ref="dialogEditForm" label-width="140px" :rules="editFormRules">
        <el-form-item label="应用" prop="name">
          <el-select v-model="editForm.name" filterable style="width: 100%" :disabled="editFormAppEditDisable">
            <el-option
              v-for="value in appOptions"
              :key="value"
              :label="value"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运行环境">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-form-item prop="cluster">
                <el-input v-model="editForm.cluster" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="namespace">
                <el-input v-model="editForm.namespace" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="最小副本">
          <span style="color: #333;">= 发布流程里配置的副本数</span>
        </el-form-item>
        <el-form-item label="当CPU使用率超过" prop="targetCPUUtilizationPercentage">
          <el-input v-model.number="editForm.targetCPUUtilizationPercentage" type="number" max="100" min="50" step="1" style="width: 180px;">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="扩容的最大副本数" prop="maxReplicas">
          <el-input-number v-model.number="editForm.maxReplicas" :min="2" :max="100"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createHpa" v-loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {createHpa, deleteHpa, searchHpa} from "@/api/k8s/hpa";
import {getAllAppName} from "@/api/app";
import {cloneObject} from "@/utils/my-util";

export default {
  mounted() {
    this.searchForm.cluster = this.$settings.clusters[0].name;
    this.searchForm.namespace = this.$settings.clusters[0].namespaces[0];
    this.loadTableData()
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  data() {
    return {
      searchEnv: "",
      searchForm: {
        cluster: "",
        namespace: "",
        keyword: ""
      },
      editForm: {},
      editFormAppEditDisable: false,
      editFormRules: {
        name: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cluster: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        namespace: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        targetCPUUtilizationPercentage: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        maxReplicas: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ]
      },
      tableData: [],
      tableLoading: false,
      dialogVisible: false,
      submitLoading: false,
      appOptions: []
    }
  },
  methods: {
    loadTableData() {
      if (!this.searchForm.cluster || !this.searchForm.namespace) {
        this.$message.error("请选择环境");
        return
      }
      this.tableLoading = true;
      searchHpa(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    searchFormReset() {
      this.editForm = {
        name: "",
        cluster: this.searchForm.cluster,
        namespace: this.searchForm.namespace,
        targetCPUUtilizationPercentage: 85,
        maxReplicas: 2,
      }
    },
    showHpaDialog(row) {
      if (this.appOptions.length < 1) {
        getAllAppName().then(response => {
          this.appOptions = response.data
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
      if (!row) {
        this.searchFormReset();
        this.editFormAppEditDisable = false
      } else {
        this.editForm = cloneObject(row)
        this.editFormAppEditDisable = true
      }
      this.dialogVisible = true;
    },
    deleteHpa(row) {
      deleteHpa(row.cluster, row.namespace, row.name).then(response => {
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    clusterChange() {
      this.searchForm.namespace = ""
    },
    createHpa() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.submitLoading = true
        createHpa(this.editForm).then(response => {
          this.dialogVisible = false
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.submitLoading = false
        })
      });
    }
  }
}
</script>
