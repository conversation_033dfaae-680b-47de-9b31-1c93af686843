<template>
  <div style="margin-top: -10px;" class="tool-app-manage-tab">
    <el-tabs v-model="activeTab" tab-position="left" style="margin-top: 10px;" @tab-click="handleClick">
      <el-tab-pane label="应用清理" name="app-gc" :lazy="true">
        <app-gc></app-gc>
      </el-tab-pane>
      <el-tab-pane label="摘除Pod管理" name="pod-deregister-manage" :lazy="true">
        <pod-deregister-manage></pod-deregister-manage>
      </el-tab-pane>
      <el-tab-pane label="应用定时重启" name="app-cron-reboot" :lazy="true">
        <app-cron-reboot></app-cron-reboot>
      </el-tab-pane>
      <el-tab-pane label="应用批量重启" name="app-restart" :lazy="true">
        <app-restart></app-restart>
      </el-tab-pane>
      <el-tab-pane label="应用批量重发" name="app-redeploy" :lazy="true">
        <app-redeploy></app-redeploy>
      </el-tab-pane>
      <el-tab-pane label="应用批量重发V2" name="app-redeploy-v2" :lazy="true">
        <app-redeploy-v2></app-redeploy-v2>
      </el-tab-pane>
      <el-tab-pane label="应用批量构建V2" name="app-build-v2" :lazy="true">
        <app-build-v2></app-build-v2>
      </el-tab-pane>
      <el-tab-pane label="应用批量发布" name="app-deploy" :lazy="true">
        <app-deploy></app-deploy>
      </el-tab-pane>
      <el-tab-pane label="应用地址查询" name="app-address-query" :lazy="true">
        <app-address-query></app-address-query>
      </el-tab-pane>
      <el-tab-pane label="发布流程资源修改" name="pipeline-resource-update" :lazy="true">
        <pipeline-resource-update></pipeline-resource-update>
      </el-tab-pane>
      <el-tab-pane label="发布流程克隆" name="pipeline-clone" :lazy="true">
        <clone-pipeline></clone-pipeline>
      </el-tab-pane>
      <el-tab-pane label="配置文件迁移" name="cms-config-migrate" :lazy="true">
        <cms-config-migrate></cms-config-migrate>
      </el-tab-pane>
      <el-tab-pane label="专属云发布助手" name="dedicated-cloud-publish-helper" :lazy="true">
        <dedicated-cloud-publish-helper></dedicated-cloud-publish-helper>
      </el-tab-pane>
      <el-tab-pane label="Yaml导出" name="yaml-export" :lazy="true">
        <yaml-export></yaml-export>
      </el-tab-pane>
      <el-tab-pane label="HelmChart导出" name="helm-chart-build" :lazy="true">
        <helm-chart-build></helm-chart-build>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import PodDeregisterManage from "@/views/tool/pod-deregister-manage.vue";
import AppGc from "@/views/tool/app-gc.vue";
import AppCronReboot from "@/views/tool/app-cron-reboot.vue";
import AppRestart from "@/views/tool/app-restart.vue";
import AppAddressQuery from "@/views/tool/app-address-query.vue";
import PipelineResourceUpdate from "@/views/tool/pipeline-resource-update.vue";
import AppRedeploy from "@/views/tool/app-redeploy.vue";
import AppRedeployV2 from "@/views/tool/app-redeploy-v2.vue";
import AppBuildV2 from "@/views/tool/app-build-v2.vue";
import AppDeploy from "@/views/tool/app-deploy.vue";
import CmsConfigMigrate from "@/views/tool/cms-config-migrate.vue";
import HelmChartBuild from "@/views/tool/helm-chart-build.vue";
import YamlExport from "@/views/tool/yaml-export.vue";
import ClonePipeline from "@/views/tool/clone-pipeline.vue";
import DedicatedCloudPublishHelper from "@/views/tool/dedicated-cloud-publish-helper.vue";

export default {
  components: {
    DedicatedCloudPublishHelper,
    ClonePipeline,
    YamlExport,
    HelmChartBuild, CmsConfigMigrate, AppDeploy, AppBuildV2, AppRedeployV2, AppRedeploy, PipelineResourceUpdate, AppAddressQuery, AppRestart, AppCronReboot, AppGc, PodDeregisterManage
  },
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  mounted() {
    let tabName = this.$route.query.tab
    if (!tabName) {
      tabName = "app-gc"
    }
    this.activeTab = tabName
  },
  computed: {},
  data() {
    return {
      activeTab: this.activeName
    }
  },
  methods: {
    handleClick(tab, event) {
      let tabName = tab.name
      this.$router.push({
        query: {...this.$route.query, "tab": tabName}
      });
    }
  }
}
</script>

<style>
.tool-app-manage-tab .el-tabs__item {
  padding: 0 10px;
}

.tool-app-manage-tab .el-tabs__nav-wrap.is-left {
  min-height: calc(100vh - 50px)
}
</style>

