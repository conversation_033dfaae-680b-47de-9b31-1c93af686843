<template>
  <div class="app-container" v-loading="loading">
    <el-table :data="tableData" style="width: 100%">
      <el-table-column type="index" label="" width="80" />
      <el-table-column width="100">
        <template slot-scope="scope">
          <el-button type="primary" @click="handleEdit(scope.row)">实例管理页</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="app" label="应用名称" />
      <el-table-column prop="namespace" label="运行环境" />
      <el-table-column prop="cluster" label="集群" />
      <el-table-column prop="owner" label="负责人" />
    </el-table>
  </div>
</template>
<script>

export default {
  name: "not-java-app-list",
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  mounted() {
  },
  methods: {

  }
}
</script>


