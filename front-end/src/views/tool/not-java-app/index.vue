<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="tabClick">
      <el-tab-pane label="应用列表" name="not-java-app-list" :lazy="true">
        <not-java-app-list></not-java-app-list>
      </el-tab-pane>
      <el-tab-pane label="创建GitLab CI配置" name="gitlab-ci-create" :lazy="true">
        <gitlab-ci-create></gitlab-ci-create>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {cloneObject} from "@/utils/my-util";
import NotJavaAppList from "@/views/tool/not-java-app/not-java-app-list.vue";
import GitlabCiCreate from "@/views/tool/not-java-app/gitlab-ci-create.vue";

export default {
  components: {GitlabCiCreate, NotJavaAppList},
  mounted() {
    let tab = this.$route.query.activeTab;
    if (tab) {
      this.activeTab = tab;
    }
  },
  computed: {},
  data() {
    return {
      activeTab: "not-java-app-list"
    }
  },
  methods: {
    tabClick(tab) {
      let urlParam = cloneObject(this.$route.query)
      urlParam["activeTab"] = tab.name;
      this.$router.push({query: urlParam});
    }
  }
}
</script>
