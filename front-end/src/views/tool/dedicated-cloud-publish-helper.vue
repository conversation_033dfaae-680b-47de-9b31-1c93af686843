<template>
  <div class="app-container">
    <div style="float: right;right: 10px">
      <el-button type="text" @click="statusPage">发布状态页</el-button>
    </div>
    <el-form :inline="true" :rules="rules" ref="ruleForm" :model="form" class="demo-form-inline">
      <el-form-item label="专属云环境" prop="sourceCluster">
        <el-select v-model="form.sourceCluster" value-key="id" placeholder="源集群">
          <el-option
            v-for="item in dedicatedClusterOptions"
            :key="item.id"
            :label="item.cluster + '/' + item.namespace"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="参考环境" prop="targetCluster">
        <el-select v-model="form.targetCluster" value-key="id" placeholder="目标集群">
          <el-option
            v-for="item in clusterOptions"
            :key="item.id"
            :label="item.cluster + '/' + item.namespace"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadTableData">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row type="flex" justify="space-between" style="margin: 5px">
      <el-col :span="12">
        <el-select v-model="tagValue" @change="tagChange">
          <el-option
            v-for="item in tagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="12" style="text-align: right">
        <el-button type="primary" @click="batchPublish">使用 [{{ this.currentTargetCluster.cluster }}/{{ this.currentTargetCluster.namespace }}] 版本重发选择的应用</el-button>
        <el-button type="primary" @click="batchPublish('master')">使用 [Master分支] 重发选择的应用</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="数据加载中..."
      border
      fit
      :cell-style="cellStyle"
      @selection-change="handleClonePipelineTableSelectionChange"
      highlight-current-row>
      <el-table-column
        type="selection"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column label="应用名"
                       width="120px"
                       sortable prop="sourcePipeline.app">
      </el-table-column>
      <el-table-column label="环境描述"
                       width="120px"
                       sortable prop="sourcePipeline.extraAttr.clusterSummary">
      </el-table-column>
      <el-table-column label="运行环境"
                       sortable prop="sourcePipeline.namespace">
        <template slot-scope="scope">
          <div v-if="scope.row.enable">
            <el-tooltip effect="dark" placement="top" content="点击发布">
              <span @click="showDeployDialog([scope.row])">
                <el-link type="primary" :underline="false"><b>{{ scope.row.sourcePipeline.namespace }}</b></el-link> ({{ scope.row.sourcePipeline.cluster }})
              </span>
            </el-tooltip>
          </div>
          <div v-else>
            {{ scope.row.sourcePipeline.namespace }} ({{ scope.row.sourcePipeline.cluster }})
          </div>
        </template>
      </el-table-column>

      <el-table-column label="运行版本" sortable prop="sourcePipeline.extraAttr.deployTag">
      </el-table-column>

      <el-table-column label="实例" align="center">
        <el-table-column
          label="配置数" width="80" align="center">
          <template slot-scope="scope">
            <el-tooltip effect="dark" content="发布流程里配置的实例数" placement="top">
              <span>{{ scope.row.sourcePipeline.replicas }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="运行数" width="100" align="center">
          <template slot-scope="scope">
            <el-tooltip effect="dark" content="当前运行的实例数" placement="top">
              <span>{{ scope.row.sourcePipeline.extraAttr.runningPodNum }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column :label='this.currentTargetCluster.cluster + " / " + this.currentTargetCluster.namespace+"环境"'>
        <el-table-column
          label="运行版本" width="100" align="center">
          <template slot-scope="scope">
            <el-tooltip effect="dark" content="当前运行的版本" placement="top">
              <span>{{ scope.row.targetPipeline.extraAttr.deployTag }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="运行数" width="80" align="center">
          <template slot-scope="scope">
            <el-tooltip effect="dark" content="当前运行的实例数" placement="top">
              <span>{{ scope.row.targetPipeline.extraAttr.runningPodNum }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>

      <!--      <el-table-column-->
      <!--        label="操作"-->
      <!--        width="80px"-->
      <!--        fixed="right">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            type="text"-->
      <!--            class="el-icon-position"-->
      <!--            :style="{'visibility':scope.row.sourcePipeline.enable ? 'visible':'hidden'}"-->
      <!--            @click="showDeployDialog([scope.row])">发布-->
      <!--          </el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

  </div>
</template>

<script>
import {getDedicatedCloudPublishPipeline} from "@/api/pipeline";

export default {
  name: "dedicated-cloud-publish-helper",
  components: {},
  data() {
    let validateClusterAndNamespace = (rule, value, callback) => {
      if (value.cluster === null && value.namespace === null) {
        callback(new Error('请选择集群'))
      } else {
        callback()
      }
    };
    return {
      tableData: [],
      tableDataTmp: [],
      tableLoading: false,
      form: {
        sourceCluster: {
          cluster: null,
          namespace: null
        },
        targetCluster: {
          cluster: null,
          namespace: null
        },
      },
      currentTargetCluster: {
        cluster: "",
        namespace: ""
      },
      tagValue: 'all',
      pipelineTableMultipleSelection: [],
      rules: {
        sourceCluster: [
          {validator: validateClusterAndNamespace, trigger: 'change'},
          {required: true, trigger: 'change'}
        ],
        targetCluster: [
          {validator: validateClusterAndNamespace, trigger: 'change'},
          {required: true, trigger: 'change'}
        ],
      },
    }
  },
  mounted() {
    for (let clu of this.$settings.clusters) {
      for (let nm of clu.namespaces) {
        // todo: 2022.08.08 本次迁移kubernetes集群需要临时去掉
        // if (nm.includes("vip") ||
        //   nm.includes("urgent") ||
        //   nm.includes("gray") ||
        //   nm.includes("compatible") ||
        //   nm.includes("stage")   ) {
        //   continue
        // }
        this.form.targetCluster.cluster = clu.name
        this.form.targetCluster.namespace = nm
        this.form.targetCluster.id = 0
        this.switchCluster(this.form.targetCluster.cluster, this.form.targetCluster.namespace)
        return
      }
    }
  },
  computed: {
    clusterOptions: function () {
      let clusters = []
      let i = 0
      for (let clu of this.$settings.clusters) {
        for (let nm of clu.namespaces) {
          // todo: 2022.08.08 本次迁移kubernetes集群需要临时去掉
          // if (nm.includes("vip") ||
          //   nm.includes("urgent") ||
          //   nm.includes("gray") ||
          //   nm.includes("compatible") ||
          //   nm.includes("stage")   ) {
          //   continue
          // }
          let c = {}
          c.cluster = clu.name
          c.namespace = nm
          c.id = i
          clusters.push(c)
          i++
        }
      }
      return clusters;
    },
    dedicatedClusterOptions: function () {
      let clusters = []
      let i = 0
      for (let clu of this.$settings.clusters) {
        if (clu.cloudCategory === "fxiaokeCloud") {
          continue
        }
        for (let nm of clu.namespaces) {
          let c = {}

          c.cluster = clu.name
          c.namespace = nm
          c.id = i
          clusters.push(c)
          i++
        }
      }
      // 将foneshare-k8s1环境暴露出来提供给江总对比服务差异，后续可删除
      let c = {}
      c.cluster = "k8s1"
      c.namespace = "foneshare"
      c.id = this.$settings.clusters.length + 1
      clusters.push(c)
      return clusters;
    },
    tagOptions: function () {
      let options = []
      options.push({value: "all", label: "所有"})
      options.push({
        value: "inconsistent", label: "[运行版本] 与 [" +
          this.currentTargetCluster.cluster + " / " + this.currentTargetCluster.namespace + "] 版本不一致"
      })
      options.push({
        value: "isEmpty", label: "[ " + this.currentTargetCluster.cluster + " / " + this.currentTargetCluster.namespace +
          "] 版本为空"
      })

      return options
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true
      getDedicatedCloudPublishPipeline(this.form.sourceCluster.cluster, this.form.sourceCluster.namespace, this.form.targetCluster.cluster, this.form.targetCluster.namespace).then(response => {
        this.tableData = response.data
        this.tableDataTmp = this.tableData
        this.switchCluster(this.form.targetCluster.cluster, this.form.targetCluster.namespace)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    switchCluster(cluster, namespace) {
      this.currentTargetCluster.cluster = cluster
      this.currentTargetCluster.namespace = namespace
    },
    tagChange(tag) {
      switch (tag) {
        case "all":
          this.loadTableData()
          break
        case "inconsistent":
          let dataTmp = []
          for (let i in this.tableDataTmp) {
            if (this.tableDataTmp[i].sourcePipeline.extraAttr.deployTag !== this.tableDataTmp[i].targetPipeline.extraAttr.deployTag) {
              dataTmp.push(this.tableDataTmp[i])
            }
          }
          this.tableData = dataTmp
          break
        case "isEmpty":
          let dataTmp2 = []
          for (let i in this.tableDataTmp) {
            if (this.tableDataTmp[i].targetPipeline.extraAttr.deployTag === "") {
              dataTmp2.push(this.tableDataTmp[i])
            }
          }
          this.tableData = dataTmp2
          break
      }
      // this.loadTableData()
    },
    // table的回调函数，用于判断修改颜色
    cellStyle({row, column, rowIndex, columnIndex}) {
      if (column.level === 1 && column.label === "运行版本") {
        if (row.sourcePipeline.extraAttr.deployTag === row.targetPipeline.extraAttr.deployTag) {
          return 'color:green'
        }
        if (row.targetPipeline.extraAttr.deployTag === "") {
          return 'color:green'
        }
        if (row.sourcePipeline.extraAttr.deployTag !== row.targetPipeline.extraAttr.deployTag) {
          return 'color:red'
        }
      }
    },
    handleClonePipelineTableSelectionChange(val) {
      this.pipelineTableMultipleSelection = val;
    },
    batchPublish(tag) {
      this.$message.info("todo...")
      //
      // const pipelines = []
      // for (const i in this.pipelineTableMultipleSelection) {
      //   const p = {}
      //   p.sourceID = this.pipelineTableMultipleSelection[i].sourcePipeline.id
      //   p.targetID = this.pipelineTableMultipleSelection[i].targetPipeline.id
      //   if (tag === "master") {
      //     p.deployTag = "master"
      //   }else {
      //     p.deployTag = this.pipelineTableMultipleSelection[i].targetPipeline.extraAttr.deployTag
      //   }
      //   pipelines.push(p)
      // }
      // const data = {}
      // data.pipelines = pipelines
      // publishDedicatedCloud(data).then(response => {
      //   this.$message.success(response.message)
      //   this.statusPage()
      // }).catch((e) => {
      //   this.$message.error(e.message);
      // }).finally(() => {
      //   this.tableLoading = false;
      // })
    },
    statusPage() {
      this.$router.push({name: 'dedicated-cloud-publish-status'})
    }

    // showDeployDialog(rows) {
    //   let firstRow = rows[0]
    //   for (let it of rows) {
    //     if (!it.sourcePipeline.enable) {
    //       this.$message.warning("有发布流程还在待审核中")
    //       return
    //     }
    //     if (it.sourcePipeline.appModules.length !== firstRow.sourcePipeline.appModules.length) {
    //       this.$message.warning("发布流程之间的部署模块不一样，不能一起批量发布")
    //       return
    //     }
    /*  }*/
    //   this.loading = true;
    //   getDeployParams(rows.map(x => x.sourcePipeline.id).join(",")).then(response => {
    //     this.loading = false
    //     this.dialogDeployForm = response.data
    //     this.dialogDeployApp = firstRow.app
    //     this.dialogDeployVisible = true
    //   }).catch((e) => {
    //     this.loading = false
    //     this.$message.error(e.message);
    //   });
    // },
  }
}
</script>

<style scoped>

</style>
