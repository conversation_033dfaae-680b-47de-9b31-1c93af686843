<template>
  <div class="devops-event-container" style="margin: 20px;">
    <div style="margin-top: -10px;">
      <el-link :href="devOpsEventUrl" target="_blank" type="primary" icon="el-icon-link">在新窗口打开</el-link>
    </div>
    <iframe :src="devOpsEventUrl" style="border-width: 0;padding: 0;margin: 10px 0 0 0;width: 100%;min-height: 1200px;"></iframe>
  </div>
</template>

<script>

export default {
  components: {},
  mounted() {
  },
  beforeDestroy() {

  },
  computed: {
    devOpsEventUrl() {
      return this.$settings.thirdServices.devopsEventUrl;
    }
  },
  data() {
    return {
      url: "https://grafana.firstshare.cn/d/d9bc6512-3423-4784-99ec-22f88b070b69/event-center",
    }
  },
  methods: {}
}
</script>

<style>
.devops-event-container .el-timeline-item__timestamp {
  color: #222;
  font-weight: bold
}
</style>
