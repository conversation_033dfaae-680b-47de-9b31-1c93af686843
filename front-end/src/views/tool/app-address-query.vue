<template>
  <div class="app-container" v-loading="loading">
    <div>
      <div style="width: 480px;float: left;">
        <app-selector2 :show-history="false" @change="changeCurrApp"></app-selector2>
      </div>
      <div style="float: left;padding: 10px;">
        <el-button type="primary" @click="loadData">查询</el-button>
        <export-button :table-ref="this.$refs.table001" v-if="data.length > 0" style="margin-left: 20px;"></export-button>
      </div>
    </div>
    <div>
      <el-table :data="data" style="width: 100%" size="mini" ref="table001" :stripe="true">
        <el-table-column prop="cluster" label="集群"></el-table-column>
        <el-table-column prop="namespace" label="命名空间"></el-table-column>
        <el-table-column prop="app" label="应用" min-width="120"></el-table-column>
        <el-table-column prop="name" label="端口名"></el-table-column>
        <el-table-column prop="protocol" label="协议"></el-table-column>
        <el-table-column prop="port" label="Port"></el-table-column>
        <el-table-column prop="targetPort" label="TargetPort"></el-table-column>
        <el-table-column prop="nodePort" label="NodePort"></el-table-column>
        <el-table-column prop="clusterInnerAddress" label="集群内地址" min-width="220">
          <template slot-scope="scope">
            <div v-for="it in scope.row.clusterInnerAddress">
              {{ it }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="clusterOuterAddress" label="集群外地址" min-width="120">
          <template slot-scope="scope">
            <div v-for="it in scope.row.clusterOuterAddress">
              {{ it }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>


  </div>
</template>

<script>
import AppSelector2 from "@/views/cicd/app-selector2.vue";
import {appAddress} from "@/api/openapi";
import ExportButton from "@/views/components/export-button.vue";

export default {
  components: {
    ExportButton,
    AppSelector2,
  },
  data() {
    return {
      currApp: "",
      data: [],
      loading: false
    }
  },
  computed: {},
  methods: {
    changeCurrApp(app) {
      this.currApp = app
    },
    loadData() {
      if (!this.currApp) {
        this.$message.error('请选择应用')
        return
      }
      this.loading = true
      appAddress(this.currApp).then(response => {
        this.data = response.data
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    podPage(cluster, namespace, app) {
      let routeUrl = this.$router.resolve({
        name: "pod-index",
        query: {
          "cluster": cluster,
          "namespace": namespace,
          "app": app,
        }
      });
      window.open(routeUrl.href, '_blank');
    },
  }
}
</script>

<style scoped>

</style>
