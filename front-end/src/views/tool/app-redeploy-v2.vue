<template>
  <div class="app-container" v-loading="pageLoading">
    <el-alert
      title="应用批量重发V2(CI/CD分离模式）"
      type="info"
      description="使用当前运行的版本重新发布应用。"
      show-icon>
    </el-alert>
    <el-row style="max-width: 1080px;margin-top:10px;" :gutter="20">
      <el-col :span="18">
        <el-form>
          <el-form-item>
            <el-input type="textarea" v-model="searchForm" autosize :autosize="{ minRows: 5, maxRows: 10}"
                      placeholder="内容格式：集群/环境/应用名，多个之间用换行分割"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" @click="loadTableData" size="small">查询</el-button>
        <br/><br/>
        <el-checkbox v-model="openVersionCompare">开启版本对比</el-checkbox>
      </el-col>
    </el-row>

    <el-table
      :data="tableData"
      border
      fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column
        type="selection"
        width="50">
      </el-table-column>

      <el-table-column label="发布过？"
                       width="100px">
        <template slot-scope="scope">
          {{ scope.row.extraAttr.reDeployed }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="160px">
        <template slot-scope="scope">
          <div v-if="!openVersionCompare || scope.row.cluster === 'k8s0'">
            <el-button
              type="text"
              size="mini"
              style="margin-top: 0;padding-top: 0;color: #FF9800"
              @click="reDeploy(scope.row)">使用当前版本重发
            </el-button>
            <br/>
            <router-link :to="{name: 'cicd-app-deploy-history', query: {}}" target="_blank">
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;">
                发布历史
              </el-button>
            </router-link>
            <router-link :to="{name: 'cicd-app-deploy', query: {'app': scope.row.app}}" target="_blank">
              <el-button
                type="text"
                size="mini"
                style="margin-top: 0;padding-top: 0;">
                发布流程
              </el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="应用名"
                       prop="app">
      </el-table-column>
      <el-table-column label="集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="环境"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="状态" width="100px" prop="status">
      </el-table-column>
      <el-table-column label="实例数(运行/配置)" align="center" prop="replicas">
        <template slot-scope="scope">
          <div v-if="openVersionCompare && scope.row.cluster === 'k8s0' && !replicasIsSame(scope.row.app, scope.row.namespace)" style="color: #FB5151;">
            {{ scope.row.extraAttr.runningPodNum }} / {{ scope.row.replicas }}
          </div>
          <div v-else>
            {{ scope.row.extraAttr.runningPodNum }} / {{ scope.row.replicas }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="版本" align="center">
        <template slot-scope="scope">
          <div v-if="openVersionCompare && scope.row.cluster === 'k8s0' && !versionIsSame(scope.row.app, scope.row.namespace)" style="color: #FB5151;">
            {{ scope.row.extraAttr.deployTag }}
          </div>
          <div v-else>
            {{ scope.row.extraAttr.deployTag }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发版信息">
        <template slot-scope="scope">
          <div style="font-size: 10px;">
            <div>发布人:{{ scope.row.extraAttr.deployUser }}</div>
            <div>{{ scope.row.extraAttr.deployRemark }}</div>
          </div>

        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import {searchPipelineByProperties} from "@/api/pipeline";
import {deploymentDetail} from "@/api/k8s/app";
import {deployAppWithCurrentVersion} from "@/api/job";

export default {
  components: {},
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      pageLoading: false,
      openVersionCompare: false,
      searchForm: "",
      tableData: [],
      deployRemark: "",
    }
  },
  methods: {
    loadTableData() {
      this.pageLoading = true
      this.tableData = []
      searchPipelineByProperties(this.searchForm).then(response => {
        for (let it of response.data) {
          it.extraAttr.deployTag = "?"
          it.extraAttr.runningPodNum = "?"
          it.extraAttr.reDeployed = "NO"
          this.findDeployment(it)
        }
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.pageLoading = false
      })
    },
    versionIsSame(app, namespace) {
      let version0 = ""
      let version1 = ""
      for (let it of this.tableData) {
        if (it.cluster === "k8s0" && it.namespace === namespace && it.app === app) {
          version0 = it.extraAttr.deployTag
        }
        if (it.cluster === "k8s1" && it.namespace === namespace && it.app === app) {
          version1 = it.extraAttr.deployTag
        }
      }
      if (version0 === "" || version1 === "") {
        return false
      }
      return version0 === version1
    },
    replicasIsSame(app, namespace) {
      let k8s0Replicas = ""
      let k8s1Replicas = ""
      let k8s0RunningPodNum = ""
      let k8s1RunningPodNum = ""
      for (let it of this.tableData) {
        if (it.cluster === "k8s0" && it.namespace === namespace && it.app === app) {
          k8s0Replicas = it.replicas
          k8s0RunningPodNum = it.runningPodNum
        }
        if (it.cluster === "k8s1" && it.namespace === namespace && it.app === app) {
          k8s1Replicas = it.replicas
          k8s1RunningPodNum = it.runningPodNum
        }
      }
      return k8s0Replicas === k8s1Replicas && k8s0RunningPodNum === k8s1RunningPodNum
    },
    findDeployment(pipe) {
      if (pipe === null) {
        this.$message.warning(`找不到发布流程，应用：${cluster} / ${namespace} / ${app}`)
        return
      }
      deploymentDetail(pipe.cluster, pipe.namespace, pipe.app).then(response => {
        pipe.extraAttr.deployTag = response.data.deployTag
        pipe.extraAttr.runningPodNum = response.data.replicas
        pipe.extraAttr.deployRemark = response.data.deployRemark
        pipe.extraAttr.deployUser = response.data.deployUser
      }).catch((e) => {
        console.log(e.message)
      });
    },
    reDeploy(pipe) {
      this.$prompt('请填写发版备注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.deployRemark,
      }).then(({value}) => {
        this.deployRemark = value
        deployAppWithCurrentVersion(pipe.cluster, pipe.namespace, pipe.app, value).then(response => {
          this.$message.success("操作成功")
          pipe.extraAttr.reDeployed = "YES"
        }).catch((e) => {
          this.$message.error(e.message);
        });
      })
    }
  }
}
</script>
