<template>
  <div class="app-container"
       v-loading="pageLoading"
       element-loading-text="操作比较重，耗时比较长，请耐心等待..."
       element-loading-background="rgba(0, 0, 0, 0.8)"
  >

    <div>
      <div style="margin-bottom: 10px;">
        <el-alert
          title="应用版本快照"
          type="info"
          style="width: 670px"
          :closable="false"
          description="对选择环境下所有应用当前运行版本进行快照保留（包括git和镜像），便于后续私有云环境的发布"
          show-icon>
        </el-alert>
      </div>
      <el-form ref="searchForm" :model="searchForm">
        <el-form-item label="选择集群" prop="cluster">
          <el-select v-model="searchForm.cluster" style="width: 600px;">
            <el-option
              v-for="item in this.clusterOptions"
              :key="item.name"
              :label="item.name + ' (' + item.description + ')'"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择环境" prop="namespace">
          <el-select v-model="searchForm.namespace" filterable style="width: 600px;">
            <el-option
              v-for="item in  this.namespaceOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="快照名称" prop="version">
          <el-input v-model="searchForm.version" style="width: 600px;">
            <template slot="append">{{ this.currentDate }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="备注信息" prop="version">
          <el-input v-model="searchForm.remark" style="width: 600px;"></el-input>
        </el-form-item>
        <el-form-item label="模拟执行" prop="dryRun">
          <el-select v-model="searchForm.dryRun" style="width: 600px;">
            <el-option value="true" label="true"></el-option>
            <el-option value="false" label="false"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <div style="margin-top: 20px;width: 600px;text-align: right;">
            <el-button type="primary" @click="submit">生成快照</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <pre></pre>
    </div>
    <div v-if="output !== null" style="margin-top: 20px;border: 1px solid #ccc;background-color: #eee; padding: 5px;font-size: 12px;">
      <pre style="word-wrap: break-word; white-space: pre-wrap;">{{ jsonOutput }}</pre>
    </div>
  </div>
</template>

<script>
import appManageTab from "@/views/tool/app-manage-tab";
import {appVersionSnapshot} from "@/api/tool";

export default {
  name: "appVersionSnapshotCreate",
  components: {
    appManageTab
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
    currentDate: function () {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0'); // Month is 0-based, so we add 1
      const day = String(now.getDate()).padStart(2, '0');
      return `-${year}${month}${day}`;
    },
    jsonOutput() {
      return JSON.stringify(this.output, null, 2); // 用null, 2可以使输出更易读
    }
  },
  data() {
    return {
      pageLoading: false,
      searchForm: {
        cluster: '',
        namespace: '',
        version: "",
        remark: "",
        dryRun: true,
      },
      output: null,
    }
  },
  methods: {
    submit() {
      this.pageLoading = true
      this.output = null
      appVersionSnapshot(this.searchForm.cluster, this.searchForm.namespace, this.searchForm.version + this.currentDate, this.searchForm.remark, this.searchForm.dryRun).then(response => {
        this.output = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.pageLoading = false
      })
    },
  }
}
</script>
