<template>
  <div class="app-container"
       v-loading="pageLoading"
  >

    <div>
      <el-table
        :data="tableData"
        element-loading-text="数据加载中..."
        border
        fit
        highlight-current-row>
        <el-table-column type="index">
        </el-table-column>
        <el-table-column label="快照名称"
                         prop="Column01">
        </el-table-column>
        <el-table-column label="快照ID"
                         width="100"
                         prop="ID">
        </el-table-column>
        <el-table-column label="备注"
                         prop="Column04">
        </el-table-column>
        <el-table-column label="快照集群"
                         prop="Column02">
        </el-table-column>
        <el-table-column label="快照环境"
                         prop="Column03">
        </el-table-column>
        <el-table-column label="创建人"
                         prop="Column05">
        </el-table-column>
        <el-table-column label="创建时间"
                         sortable
                         prop="CreatedAt">
        </el-table-column>
        <el-table-column
          label="操作"
          width="280px"
          fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="contentShow(scope.row)">详细内容
            </el-button>
            <el-button
              type="text"
              @click="deleteData(scope.row)">删除
            </el-button>
            <el-button
              type="text"
              icon="el-icon-position"
              @click="deployPage(scope.row)">发布到复制云
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :title="contentDialog.title" :visible.sync="contentDialog.visible" width="70%" top="5vh" :close-on-click-modal="false">
      <div style="margin-top: -50px;overflow: auto;">
        <div style="text-align: center">
          <el-button type="text" @click="copyToClipboard" size="mini" icon="el-icon-document-copy">一键复制内容</el-button>
        </div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ this.contentDialog.content }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {deleteAppVersionSnapshot, searchAppVersionSnapshot} from "@/api/tool";
import {cloneObject} from "@/utils/my-util";

export default {
  name: "AppVersionSnapshotList",
  components: {},
  mounted() {
    this.pageLoading = true
    this.loadTableData()
  },
  beforeDestroy() {
  },
  computed: {},
  data() {
    return {
      tableData: [],
      pageLoading: false,
      contentDialog: {
        visible: false,
        title: '',
        content: ''
      }
    }
  },
  methods: {
    loadTableData() {
      this.pageLoading = true
      this.tableData = []
      searchAppVersionSnapshot().then(response => {
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.pageLoading = false
      })
    },
    deleteData(row) {
      this.$confirm('此操作将删除数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true
        deleteAppVersionSnapshot(row.ID).then(response => {
        }).catch((e) => {
          this.$message.error(e.message)
        }).finally(() => {
          this.pageLoading = false
          this.loadTableData()
        })
      })
    },
    deployPage(row) {
      let urlParam = cloneObject(this.$route.query)
      urlParam["tab"] = "deploy";
      urlParam["snapshotId"] =row.ID;
      this.$router.push({ query: urlParam });
      window.location.reload()
    },
    contentShow(row) {
      this.contentDialog.content = row.Content
      this.contentDialog.title = "快照名称：" + row.Column01
      this.contentDialog.visible = true
    },
    copyToClipboard() {
      let text = this.contentDialog.content
      if (!text) {
        this.$message.warning("内容为空")
        return
      }
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message.success("复制成功")
        })
        .catch(() => {
          const input = document.createElement('input')
          document.body.appendChild(input)
          input.setAttribute('value', text)
          input.select()
          if (document.execCommand('copy')) {
            document.execCommand('copy')
          }
          document.body.removeChild(input)
          this.$message.success("复制成功")
        })
    }
  }
}
</script>
