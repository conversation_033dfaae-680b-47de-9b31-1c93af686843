<template>
  <div class="app-container"
       element-loading-text="操作比较重，耗时比较长，请耐心等待..."
       element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="快照-创建" name="create" :lazy="true">
        <app-version-snapshot-create></app-version-snapshot-create>
      </el-tab-pane>
      <el-tab-pane label="快照-查看" name="list" :lazy="true">
        <app-version-snapshot-list></app-version-snapshot-list>
      </el-tab-pane>
      <el-tab-pane label="快照-发布" name="deploy" :lazy="true">
        <app-version-snapshot-batch-deploy></app-version-snapshot-batch-deploy>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import appManageTab from "@/views/tool/app-manage-tab";
import AppVersionSnapshotList from "@/views/tool/app-version-snapshot/list.vue";
import AppVersionSnapshotCreate from "@/views/tool/app-version-snapshot/create.vue";
import AppVersionSnapshotBatchDeploy from "@/views/tool/app-version-snapshot/batch-deploy.vue";

export default {
  components: {
    AppVersionSnapshotBatchDeploy,
    AppVersionSnapshotCreate,
    AppVersionSnapshotList,
    appManageTab
  },
  mounted() {
    let tab = this.$route.query.tab
    if (!tab) {
      tab = "create"
    }
    this.activeTab = tab
  },
  beforeDestroy() {
  },
  computed: {
  },
  data() {
    return {
      activeTab: ""
    }
  },
  methods: {

  }
}
</script>
