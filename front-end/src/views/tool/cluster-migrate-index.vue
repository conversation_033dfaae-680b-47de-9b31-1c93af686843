<template>
  <div>
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="集群lb拆分" name="cluster-lb-split" :lazy="true">
        <cluster-migrate-lb-split/>
      </el-tab-pane>
      <el-tab-pane label="集群lb拆分(ApiBus)" name="cluster-lb-split-apibus" :lazy="true">
        <cluster-migrate-lb-split-apibus/>
      </el-tab-pane>
      <el-tab-pane label="老集群（k8s1)" name="old-k8s" :lazy="true">
        <cluster-migrate-old></cluster-migrate-old>
      </el-tab-pane>
      <el-tab-pane label="新集群（k8s0)" name="new-k8s" :lazy="true">
        <cluster-migrate-new/>
      </el-tab-pane>
      <el-tab-pane label="应用迁移处理" name="migrate-operation" :lazy="true">
        <cluster-migrate-operation/>
      </el-tab-pane>
      <el-tab-pane label="发布流程处理" name="pipeline-batch-operation" :lazy="true">
        <cluster-migrate-pipeline-batch-operation/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ClusterMigrateLbSplit from "@/views/tool/cluster-migrate-lb-split.vue";
import ClusterMigrateLbSplitApibus from "@/views/tool/cluster-migrate-lb-split-apibus.vue";
import ClusterMigrateOld from "@/views/tool/cluster-migrate-old.vue";
import ClusterMigrateNew from "@/views/tool/cluster-migrate-new.vue";
import ClusterMigrateOperation from "@/views/tool/cluster-migrate-operation.vue";
import ClusterMigratePipelineBatchOperation from "@/views/tool/cluster-migrate-pipeline-batch-operation.vue";

export default {
  components: {ClusterMigratePipelineBatchOperation, ClusterMigrateOperation, ClusterMigrateNew, ClusterMigrateOld, ClusterMigrateLbSplitApibus, ClusterMigrateLbSplit},
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  mounted() {
    let tabName = this.$route.query.tab
    if (!tabName) {
      tabName = "app-gc"
    }
    this.activeTab = tabName
  },
  computed: {},
  data() {
    return {
      activeTab: this.activeName
    }
  },
  methods: {
    handleClick(tab, event) {
      let tabName = tab.name
      this.$router.push({
        query: {...this.$route.query, "tab": tabName}
      });
    }
  }
}
</script>
