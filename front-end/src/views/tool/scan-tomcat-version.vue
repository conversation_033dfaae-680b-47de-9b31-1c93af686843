<template>
  <div class="app-container" v-loading="loading">
    <div style="text-align: center;margin-bottom: 10px;">

      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="findApp" @submit.native.prevent>
        <env-selector ref="nsSelector" :show-all-namespaces="true" ></env-selector>
        <el-form-item>
          <el-button type="primary" @click="scanTomcatVersion">提交扫描任务</el-button>
        </el-form-item>
      </el-form>
      <el-card class="box-card" style="width: 460px;margin: 30px auto;text-align: left">
        <div style="margin-top: 10px;">
          <el-button
            type="text"
            style="margin: 0 20px;"
            @click="scanHistory">历史扫描结果
          </el-button>
        </div>
      </el-card>
    </div>

  </div>
</template>


<script>
import {scanTomcatVersion} from "@/api/tool";
import EnvSelector from "@/views/components/env-selector.vue";

export default {
  mounted() {
  },
  computed: {},
  components: {
    EnvSelector,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        cluster: "",
        namespace: ""
      }
    }
  },
  methods: {
    scanTomcatVersion() {
      this.searchForm.cluster = this.$refs.nsSelector.cluster;
      this.searchForm.namespace = this.$refs.nsSelector.namespace;
      this.loading = true;
      scanTomcatVersion(this.searchForm).then(response => {
        this.$message.success(response.data)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })
    },
    scanHistory() {
      let rou = this.$router.resolve({name: 'log-list', query: {"operate": "tomcat-version-scan"}});
      window.open(rou.href, '_blank');
    },
  }
}
</script>
