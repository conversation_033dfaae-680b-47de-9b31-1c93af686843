<template>
  <div class="app-container" v-loading="loading">
    <div>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="模式" prop="sourceCluster">
          <el-select v-model="form.op">
            <el-option label="NodePort转Service" value="nodeport-to-service"></el-option>
            <el-option label="Service短名转长名" value="serviceShort-to-serviceLong"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="环境" prop="sourceCluster">
          <el-select v-model="form.sourceCluster" value-key="id" filterable style="width: 360px;">
            <el-option
              v-for="item in clusterOptions"
              :key="item.id"
              :label="item.cluster + '/' + item.namespace"
              :value="item">
            </el-option>
          </el-select>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="data" style="width: 100%" size="mini">
        <el-table-column prop="name" label="文件"></el-table-column>
        <el-table-column prop="profile" label="配置组"></el-table-column>
        <el-table-column prop="editor" label="最后修改人"></el-table-column>
        <el-table-column label="内容" width="80">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="cmsContentPreview(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="addrs" label="地址" min-width="300">
          <template slot-scope="scope">
            <span v-for="(val,key) in scope.row.repairAddrs" style="margin: 2px 5px;padding: 0 2px; border: 1px solid #e2e2e2;display: inline-block;">
              {{ key }}
              <el-button type="text" size="mini" style="margin-left: 10px;" @click="editCMS(scope.row.id)">修改</el-button>
              <el-tooltip :content="key + ' -> ' + val" placement="right">
                <el-button type="text" size="mini" style="margin-left: 5px;" @click="batchEditCMS(scope.row.profile, key,val)">批量替换</el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div>
      <el-dialog
        :title="cmsPreview.name + ' (' + cmsPreview.profile + ')'"
        :visible.sync="cmsPreview.visible"
        width="50%">
        <div style="margin-top: -40px;border: 1px solid #e2e2e2;padding: 5px;max-height: 600px;overflow: auto;">
          <pre>{{ cmsPreview.content }}</pre>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import {loadCmsProfileConfigs} from "@/api/tool";

export default {
  components: {},
  data() {
    return {
      form: {
        op: 'nodeport-to-service',
        sourceCluster: {
          cluster: null,
          namespace: null
        }
      },
      data: [],
      loading: false,
      cmsPreview: {
        visible: false,
        name: '',
        profile: '',
        content: ''
      }
    }
  },
  computed: {
    clusterOptions: function () {
      const clusters = []
      let i = 0
      for (const clu of this.$settings.clusters) {
        for (const nm of clu.namespaces) {
          const c = {}
          c.cluster = clu.name
          c.namespace = nm
          c.id = i
          clusters.push(c)
          i++
        }
      }
      return clusters
    }
  },
  methods: {
    loadData() {
      if (!this.form.sourceCluster.cluster || !this.form.sourceCluster.namespace) {
        this.$message.error('请选择环境')
        return
      }
      this.loading = true
      loadCmsProfileConfigs(this.form.sourceCluster.cluster, this.form.sourceCluster.namespace, this.form.op).then(response => {
        this.data = response.data
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    editCMS(cmsId) {
      let url = `/api/page/redirect?type=cmsEdit&cmsId=${cmsId}&_t` + Date.now();
      window.open(url)
    },
    batchEditCMS(profile, oldContent, newContent) {
      let url = `/api/page/redirect?type=cmsBatchEdit&profile=${profile}&oldContent=${oldContent}&newContent=${newContent}&_t` + Date.now();
      window.open(url)
    },
    cmsContentPreview(row) {
      this.cmsPreview.name = row.name
      this.cmsPreview.profile = row.profile
      this.cmsPreview.content = row.content
      this.cmsPreview.visible = true
    }
  }
}
</script>

<style scoped>

</style>
