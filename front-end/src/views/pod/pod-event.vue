<template>
  <div class="pod-event" v-loading="eventsLoading" element-loading-text="事件加载中...">
    <div v-if="events && events.length > 0">
      <el-timeline>
        <el-timeline-item
          placement="top"
          :hide-timestamp="true"
          :timestamp="item.lastTime ? item.lastTime: item.createTime"
          v-for="(item, index) in events"
          :key="index">
          <div>
            <span>{{ item.lastTime ? item.lastTime: item.createTime}}</span>
            <el-tag size="small" :type="item.type === 'Warning'? 'warning': 'info'" style="margin: 0 10px;">{{ item.type }}</el-tag>
            <span style="padding: 0 10px"> ( x{{ item.count }} )</span>
            <span style="padding-right: 10px;">{{ item.reason }}:</span> {{ item.message }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <div v-else-if="events && events.length === 0">
      <el-empty description="暂无事件"></el-empty>
    </div>
  </div>
</template>
<script>
import {podEvents} from "@/api/k8s/pod";

export default {
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    pod: {
      type: String,
      required: true
    },
    hiddenStartUpEvent: {
      type: Boolean,
      default: true
    }
  },
  created() {
    //解决element table fixed下组件重复加载的问题
    //参见：https://github.com/ElemeFE/element/issues/12177
    if (this.$parent.$el
      && this.$parent.$el.offsetParent
      && this.$parent.$el.offsetParent.className
      && this.$parent.$el.offsetParent.className.includes("fixed")) {
      return
    }
    this.$nextTick(() => {
      this.loadEvents()
    })

  },
  computed: {},
  data() {
    return {
      events: null,
      eventsLoading: false,
    }
  },
  methods: {
    loadEvents: function () {
      this.eventsLoading = true;
      podEvents(this.cluster, this.namespace, this.pod).then(response => {
        let items = response.data;
        if (this.hiddenStartUpEvent) {
          items = items.filter(item => !item.message.includes("Startup probe failed"));
        }
        this.events = items;
      }).catch((e) => {
        this.$message.error("pod事件加载失败: " + e.message);
      }).finally(() => {
        this.eventsLoading = false;
      });
    }
  }
}
</script>

<style>

</style>
