<template>
  <div class="pod-metrics" v-loading="loading">
    <div style="padding-left: 36px;position: relative;">
      <div style="position: absolute;left:0;top:0;font-size: 13px;line-height: 13px;">CPU</div>
      <span v-else>--</span>
    </div>
    <div style="padding-left: 36px;position: relative;">
      <div style="position: absolute;left:0;top:0;font-size: 13px;line-height: 13px;">内存</div>
      <span v-else>--</span>
    </div>
  </div>
</template>
<script>
import {podMetric} from "@/api/k8s/pod";

export default {
  name: "podMetric",
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    pod: {
      type: String,
      required: true
    },
  },
  created() {
    this.loadMetric()
  },
  computed: {},
  data() {
    return {
      metric: {},
      loading: false,
    }
  },
  methods: {
    loadMetric: function () {
      this.loading = true;
      podMetric(this.cluster, this.namespace, this.pod).then(response => {
        this.metric = response.data;
      }).catch((e) => {
        console.error("Pod资源使用数据加载失败: " + e.message)
      }).finally(() => {
        this.loading = false;
      });
    }
  }
}
</script>

<style>

</style>
