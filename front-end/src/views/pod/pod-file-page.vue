<template>
  <div class="app-container ">
    <pod-file v-if="cluster && namespace && pod && path" :cluster="cluster" :namespace="namespace" :pod="pod" :path="path" :from-page="true"></pod-file>
  </div>
</template>

<script>
import PodFile from "@/views/pod/pod-file.vue";

export default {
  components: {PodFile},
  data() {
    return {
      cluster: "",
      namespace: "",
      pod: "",
      path: ""
    }
  },
  mounted() {
    if (this.$route.query.path) {
      this.path = this.$route.query.path;
    } else {
      this.path = "/opt/tomcat/logs"
    }
    let routerQuery = this.$route.query;
    if (!routerQuery.cluster || !routerQuery.namespace || !routerQuery.pod) {
      this.$message.error("请输入完整参数");
      return
    }
    this.cluster = routerQuery.cluster;
    this.namespace = routerQuery.namespace;
    this.pod = routerQuery.pod;
  },
  methods: {}
}
</script>

<style>
</style>
