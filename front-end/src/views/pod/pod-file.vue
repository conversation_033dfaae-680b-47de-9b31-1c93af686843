<template>
  <div class="app-container log-file-container" v-loading="pageLoading">
    <div :style="this.btnStyle">
      <el-tooltip class="item" effect="dark" content="点击进入到容器里" placement="top">
        <el-button type="text" class="el-icon-bank-card" @click="podShell">进入容器</el-button>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="上传文件到当前目录（支持批量上传）" placement="top" style="margin-left: 10px;">
        <el-button type="text" class="el-icon-upload" style="margin-left: 30px;" @click="uploadDialogVisible = true;">上传文件
        </el-button>
      </el-tooltip>
      <el-tooltip v-if="!this.fromPage" class="item" effect="dark" content="在新窗口打开当前页面" placement="top" style="margin-left: 10px;">
        <router-link :to="{name:'pod-file-page',query:{'cluster':this.cluster,'namespace':this.namespace,'pod':this.pod,'path':this.path}}" target="_blank">
          <i class="el-icon-news" style="color:#409EFF;font-size: 14px;font-weight: 500;">新窗口打开</i>
        </router-link>
      </el-tooltip>
    </div>
    <el-form :inline="true" size="mini">
      <el-form-item label="" prop="cluster">
        <el-input v-model="this.cluster" placeholder="" style="width: 160px;" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item prop="namespace">
        <el-input v-model="this.namespace" placeholder="" style="width: 240px;" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="" prop="pod">
        <el-input v-model="this.pod" style="width: 340px;" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="" prop="path">
        <el-input v-model="this.path" style="width: 180px;" :disabled="true"></el-input>
      </el-form-item>
    </el-form>

    <el-button v-if="multipleSelection.length > 0" class="el-icon-paperclip" size="mini"
               style="margin-bottom: 10px;" @click="archiveFiles">
      文件打包
    </el-button>
    <el-table
      :data="tableData"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @selection-change="selectionChange"
      :row-class-name="tableRowClassName"
    >
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="类型"
                       width="60"
                       align="center">
        <template slot-scope="scope">
          <svg-icon v-if="scope.row.isDir" icon-class="folder" style="fill: #f6af34;"/>
          <span v-else-if="scope.row.name.includes('.hprof')"
                style="color: red;font-weight: bold;">OOM</span>
          <svg-icon v-else-if="scope.row.name.endsWith('.log') || scope.row.name.endsWith('.txt')" icon-class="txt"/>
          <svg-icon v-else-if="scope.row.name.endsWith('.zip') || scope.row.name.endsWith('.gz')" icon-class="zip"/>
          <svg-icon v-else-if="scope.row.name.endsWith('.svg')" icon-class="svg"/>
          <svg-icon v-else-if="scope.row.name.endsWith('.htm') || scope.row.name.endsWith('.html')" icon-class="html"/>
          <svg-icon v-else icon-class="file-unknown"/>
        </template>
      </el-table-column>
      <el-table-column label="文件名"
                       sortable
                       min-width="200"
                       prop="name">
        <template slot-scope="scope">
          <div v-if="scope.row.isDir" style="display: inline-block">
            <el-tooltip class="item" effect="dark" content="点击进入目录" placement="top">
            <span @click="entryDir(scope.row.name)"
                  style="color: #3a8ee6;cursor: pointer;font-weight: bold;">
              {{ scope.row.name === ".." ? ".. (上级目录）" : scope.row.name }}
            </span>
            </el-tooltip>
          </div>
          <div v-else style="display: inline-block">
            {{ scope.row.name }}
          </div>
          <div style="display: inline-block;padding-right: 10px;">
            <div v-if="scope.row.name.startsWith('gc.log')">
              <el-tag type="warning" effect="plain" style="font-weight: bold;" size="mini">GC文件</el-tag>
              <el-tooltip placement="top" content="可以通过可视化工具进行分析，推荐：gceasy、gcviewer">
                <el-tag type="info" effect="plain" size="mini">
                  <i class="el-icon-info help-text-icon" style="font-size: 12px;">提示</i>
                </el-tag>
              </el-tooltip>
            </div>
            <div v-else-if="scope.row.name.includes('.hprof')">
              <el-tag type="warning" effect="plain" style="font-weight: bold;" size="mini">
                OOM-Dump文件
              </el-tag>
              <el-tooltip placement="top" content="请通过预览按钮查看如何在线分析dump文件">
                <el-tag type="info" effect="plain" size="mini">
                  <i class="el-icon-info help-text-icon" style="font-size: 12px;">提示</i>
                </el-tag>
              </el-tooltip>
            </div>
            <div v-else-if="scope.row.name.startsWith('tomcat-access')">
              <el-tag type="warning" effect="plain" style="font-weight: bold;" size="mini">
                http访问日志
              </el-tag>
            </div>
            <div v-else-if="scope.row.name.startsWith('thread-dump')">
              <el-tag type="warning" effect="plain" style="font-weight: bold;" size="mini">
                线程dump文件
              </el-tag>
            </div>
            <div v-else-if="scope.row.name.startsWith('log_before_restart')">
              <el-tag type="warning" effect="plain" style="font-weight: bold;" size="mini">
                重启前的日志文件
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="大小"
                       sortable
                       prop="humanizeSize">
      </el-table-column>
      <el-table-column label="修改时间"
                       sortable
                       prop="modifyTime">
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right">
        <template slot-scope="scope">
          <div v-if="!scope.row.isDir">
            <el-button
              type="text"
              icon="el-icon-download"
              v-if="scope.row.size <= downloadLimitSize || scope.row.name.endsWith('.jprof') || scope.row.name.endsWith('.zip')"
              @click="download(scope.row)">下载
            </el-button>
            <el-button
              type="text"
              icon="el-icon-view"
              v-if="scope.row.name.endsWith('.svg') || scope.row.name.endsWith('.htm') || scope.row.name.endsWith('.html')"
              @click="preview(scope.row)">预览
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="文件上传" :visible.sync="uploadDialogVisible" width="32%" :close-on-click-modal="false" append-to-body>
      <el-upload
        class="upload-demo"
        ref="upload"
        action="/api/v1/k8s/pod/file/upload"
        :multiple="true"
        :file-list="uploadData.fileList"
        :on-progress="uploadProgress"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :limit="50"
        name="file"
        :data="this.searchForm"
        :auto-upload="false">
        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        <el-button style="margin-left: 10px;" size="small" type="success" @click="uploadFiles">上传到服务器</el-button>
        <div slot="tip" class="el-upload__tip">备注： 上传的文件在容器删除后也会一并被删除，不建议上传太大的文件</div>
      </el-upload>
      <div style="height: 50px;"></div>
    </el-dialog>
  </div>
</template>

<script>
import {archiveFiles, downloadFile, getPodFiles, previewFile, readyFile} from '@/api/k8s/pod'

export default {
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    pod: {
      type: String,
      required: true
    },
    path: {
      type: String,
      default: "/opt/tomcat/logs",
      required: false
    },
    fromPage: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  watch: {
    cluster(val) {
      this.searchForm.cluster = val;
      this.loadFile();
    },
    namespace(val) {
      this.searchForm.namespace = val;
      this.loadFile();
    },
    pod(val) {
      this.searchForm.pod = val;
      this.loadFile();
    },
    path(val) {
      this.searchForm.path = val;
      this.loadFile();
    }
  },
  data() {
    return {
      dedicatedCloudDownloadMaxSize: 100 * 1024 * 1024,
      searchForm: {
        cluster: "",
        namespace: "",
        pod: "",
        path: ""
      },
      downloadLimitSize: 500 * 1024 * 1024 * 1024,
      tableData: [],
      pageLoading: false,
      uploadDialogVisible: false,
      uploadData: {
        fileList: [],
      },
      multipleSelection: [],
      highLightRow: ""
    }
  },
  computed: {
    isDedicatedCloud: function () {
      if (this.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.cluster === clu.name) {
            return clu.cloudCategory !== "fxiaokeCloud";
          }
        }
      }
      return false;
    },
    btnStyle: function () {
      if (this.fromPage) {
        return {}
      } else {
        return {marginTop: "-45px", zIndex: 9999, position: 'absolute', marginLeft: '120px'}
      }
    }
  },
  mounted() {
    if (this.$route.query.downloadLimitSize) {
      this.downloadLimitSize = parseInt(this.$route.query.downloadLimitSize);
    } else {
      this.downloadLimitSize = 500 * 1024 * 1024 * 1024;
    }

    this.searchForm.cluster = this.cluster
    this.searchForm.namespace = this.namespace
    this.searchForm.pod = this.pod
    this.searchForm.path = this.path

    if (this.$route.query.dedicatedCloudDownloadMaxSize) {
      this.dedicatedCloudDownloadMaxSize = parseInt(this.$route.query.dedicatedCloudDownloadMaxSize);
    }

    this.loadFile();
  },
  methods: {
    loadFile() {
      if (!this.path.startsWith("/opt")) {
        this.$message.warning(`只允许查看 /opt 目录下的文件`);
        this.path = "/opt"
      }
      this.pageLoading = true;
      getPodFiles(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.pageLoading = false;
      });
    },
    podShell() {
      let url = `/api/page/redirect?type=webShell&cluster=${this.cluster}&namespace=${this.namespace}&pods=${this.pod}`
      window.open(url)
    },
    parentDir() {
      let path = this.path;
      if (!path.includes("/") || path === "/") {
        this.$message.warning(`已经是最顶层目录了`);
        return
      }
      let newPath = path.substring(0, path.lastIndexOf("/"))
      this.path = newPath ? newPath : "/"
    },
    entryDir(name) {
      if (name === "..") {
        this.parentDir()
        return
      }
      let path = this.path;
      if (path.endsWith("/")) {
        path = `${this.path}${name}`
      } else {
        path = `${this.path}/${name}`
      }
      this.path = path
    },
    parseFilePath(path) {
      return path.endsWith("/") ? path.slice(0, -1) : path
    },
    preview(row) {
      let param = {
        "cluster": this.cluster,
        "namespace": this.namespace,
        "pod": this.pod,
        "path": this.parseFilePath(this.path) + "/" + row.name,
        "fileSize": row.size
      };
      if (row.size > 10 * 1024 * 1024) {
        this.$message.error(`文件大于10MB，无法预览`);
        return
      }
      this.pageLoading = true;
      readyFile(param).then(response => {
        this.pageLoading = false;
        previewFile(response.data);
      }).catch((e) => {
        this.pageLoading = false;
        this.$message.error(e.message);
      });
    },
    download(row) {
      let param = {
        "cluster": this.cluster,
        "namespace": this.namespace,
        "pod": this.pod,
        "path": this.parseFilePath(this.path) + "/" + row.name,
        "fileSize": row.size,
      };
      if (this.isDedicatedCloud) {
        console.log("download file from dedicated cloud")
        if (row.size > this.dedicatedCloudDownloadMaxSize) {
          this.$message.error(`为避免网络带宽占用，专属云不允许下载 ${Math.floor(this.dedicatedCloudDownloadMaxSize / 1024 / 1024)}MB 以上的文件。如果有需要请联系 @吴志辉 处理`);
          return
        }
      }
      this.pageLoading = true;
      readyFile(param).then(response => {
        this.pageLoading = false;
        downloadFile(response.data, row.name);
      }).catch((e) => {
        this.pageLoading = false;
        this.$message.error(e.message);
      });
    },
    uploadFiles() {
      this.$refs.upload.submit();
    },
    uploadProgress(event, file, fileList) {
    },

    uploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.$message.success(`上传成功，文件名：${file.name}`);
      } else {
        this.$message.error(`上传失败，文件名：${file.name}，msg: ${response.message}`);
        //失败的文件从文件列表（页面）里删除掉
        fileList.splice(fileList.findIndex(e => e.name === file.name), 1);
        // file.status = "fail"
      }
      if (fileList && fileList.length > 0 && file.name === fileList[fileList.length - 1].name) {
        //所有文件已上传完成，自动刷新页面数据
        let vThis = this;
        setTimeout(function () {
          vThis.loadFile();
        }, 1000)
      }
    },

    uploadError(err, file, fileList) {
      this.$message.error(`上传失败，文件名：${file.name}，msg: ${err}`);
    },
    selectionChange(val) {
      this.multipleSelection = val;
    },
    archiveFiles() {
      // if(this.multipleSelection.filter(item=>item.isDir).length > 0) {
      //   this.$message.warning("不能选择目录")
      //   return
      // }
      let totalSize = this.multipleSelection.map(function (item) {
        return item.size;
      }).reduce(function (prev, next) {
        return prev + next;
      })
      if (totalSize > 5 * 1024 * 1024 * 1024) {
        this.$message.warning("总文件大小不能超过5G")
        return;
      }

      let files = this.multipleSelection.map(function (item) {
        return item.name
      })
      let param = {
        "cluster": this.cluster,
        "namespace": this.namespace,
        "pod": this.pod,
        "dir": this.parseFilePath(this.path),
        "files": files
      };
      archiveFiles(param).then(response => {
        this.pageLoading = false;
        this.tableRowClassName = function ({row, rowIndex}) {
          if (row.name === response.data) {
            return 'high-light-row';
          }
          return '';
        }
        this.loadFile();
        this.$message.success("归档成功，文件名：" + response.data)
      }).catch((e) => {
        this.pageLoading = false;
        this.$message.error(e.message);
      });
    },
    tableRowClassName({row, rowIndex}) {
      //noting
      return '';
    }
  }
}
</script>

<style>
.log-file-container .svg-icon {
  width: 2em;
  height: 2em;
}

.log-file-container .el-table .high-light-row {
  background: #67C23A;
}

.log-file-container .el-input.is-disabled .el-input__inner {
  color: #444;
  font-weight: bold;
}
</style>
