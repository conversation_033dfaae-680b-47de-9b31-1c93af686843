<template>
  <div class="app-container pod-index" v-loading="pageLoading">
    <el-card class="box-card" :body-style="{padding: '4px',overflow: 'auto',maxHeight: '600px'}">
      <div>
        <app-selector2 @change="currAppChange" :show-detail="false" :update-history="true"></app-selector2>
      </div>
    </el-card>
    <div>
      <div class="env-selector" style="position: absolute;width: 180px" v-loading="envPageLoading">
        <el-card class="box-card" :body-style="{padding: '4px',overflow: 'auto',height: '560px'}">
          <div slot="header">
            <span style="font-size: 14px;">
              运行环境
                <el-tooltip
                  placement="right"
                  effect="dark"
                  width="480"
                  content=" hover的内容格式为：环境 | 集群 | 集群描述 | 配置副本数 | 发布流程状态">
                   <i class="el-icon-warning-outline" style="margin-left: 5px;"></i>
                </el-tooltip>
            </span>
          </div>
          <div style="margin-top: 10px;">
            <div v-if="envSelectorDesc">
              <el-alert
                style="margin:10px 0;padding:5px 0;"
                :title="this.envSelectorDesc"
                type="error"
                :closable="false">
              </el-alert>
            </div>
            <el-radio-group v-model="envSelected" @input="envSelectedChange">
              <el-tooltip v-for="item in currAppEnvs" effect="dark" :content="item.namespace + ' | ' + item.cluster + ' | ' + item.clusterDesc + ' | ' + item.replicas + ' | ' + item.pipelineStatus" placement="right">
                <div>
                  <el-radio :label="item.cluster + '/' + item.namespace">
                    <span v-html="item.envDesc"></span>
                    <span v-if="item.pipelineStatus && item.pipelineStatus !== '正常'" style="color: orangered;font-size: 10px;">({{item.pipelineStatus}})</span>
                  </el-radio>
                </div>
              </el-tooltip>
            </el-radio-group>
          </div>
        </el-card>
      </div>
      <div style="margin-left: 185px;">
        <el-tabs class="pod-index-tabs" type="border-card" :active-name="this.currTab" @tab-click="tabChange" style="margin-top: 10px;min-height: 600px;">
          <el-tab-pane name="pod" :lazy="true">
            <span slot="label"><svg-icon icon-class="application" style="margin-right: 2px;"/>实例管理</span>
            <div v-if="appIsSelected" style="padding: 10px;">
              <pod-list :cluster="this.currCluster" :namespace="this.currNamespace" :app="this.currApp"></pod-list>
            </div>
            <div v-else>
              <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择运行环境</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="appEvent" :lazy="true">
            <span slot="label"><svg-icon icon-class="event" style="margin-right: 2px;"/>应用事件</span>
            <div v-if="appIsSelected" style="padding: 10px;">
              <app-event :cluster="this.currCluster" :namespace="this.currNamespace" :app="this.currApp"></app-event>
            </div>
            <div v-else>
              <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择运行环境</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="appLog" :lazy="true">
            <span slot="label"><i class="el-icon-tickets" style="margin-right: 2px;"></i>Logback日志</span>
            <div v-if="appIsSelected" style="position: relative;margin: -24px;" v-loading="tabIframeLoading">
              <iframe :src="this.appLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
              <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;" @click="clickhouseLogPage('app_log','')">新页面打开
              </el-button>
            </div>
            <div v-else>
              <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择运行环境</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="tomcatAccessLog" :lazy="true">
            <span slot="label"><svg-icon icon-class="tomcat" style="margin-right: 2px;"/>Tomcat访问日志</span>
            <div v-if="appIsSelected" style="position: relative;margin: -24px;" v-loading="tabIframeLoading">
              <iframe :src="this.tomcatAccessLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
              <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;"
                         @click="clickhouseLogPage('tomcat_access','')">新页面打开
              </el-button>
            </div>
            <div v-else>
              <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择运行环境</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="monitor" :lazy="true">
            <span slot="label"><svg-icon icon-class="monitor" style="margin-right: 2px;"/>资源监控</span>
            <div v-if="appIsSelected" style="position: relative;" v-loading="tabIframeLoading">
              <iframe :src="this.monitorUrl" style="border-width: 0;padding: 0;margin: 10px 0 0 0;width: 100%;min-height: 1200px;"></iframe>
              <el-button type="text" icon="el-icon-link" style="position: absolute;right: 520px;top:10px;" @click="grafanaPage('')">新页面打开
              </el-button>
            </div>
            <div v-else>
              <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择运行环境</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="doc" :lazy="true">
            <span slot="label"><svg-icon icon-class="help" style="margin-right: 2px;"/>帮助文档</span>
            <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
              ◉ 服务日志接入：
              <el-link href="https://u9icsn6fl7.feishu.cn/docx/FFVVdph88o7RPFxH32ecwkPCnEf" target="_blank" type="primary">文档链接</el-link>
            </div>
            <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
              ◉ 日志查询语法：日志查询条件使用的是 ClickHouse 原生 Where 子句语法。
              <el-link href="https://clickhouse.com/docs/zh/sql-reference/statements/select/where/" target="_blank" type="primary">文档链接</el-link>
            </div>
            <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
              ◉ 日志平台(ClickVisual)文档：
              <el-link href="https://clickvisual.gocn.vip/clickvisual/" target="_blank" type="primary">文档链接</el-link>
            </div>
          </el-tab-pane>
        </el-tabs>

      </div>
    </div>

  </div>
</template>

<script>
import {appsWithEnv} from "@/api/app";
import AppEvent from "@/views/pod/app-event.vue";
import PodExpand from "@/views/pod/pod-expand.vue";
import PodList from "@/views/pod/pod-list.vue";
import AppSelector2 from "@/views/cicd/app-selector2.vue";
import {cloneObject} from "@/utils/my-util";

export default {
  components: {AppSelector2, PodList, PodExpand, AppEvent},
  data() {
    return {
      pageLoading: false,
      envPageLoading: true,
      currCluster: "",
      currNamespace: "",
      currApp: "",
      currTab: "pod",
      tabIframeLoading: false,
      appEnvs: {},
      currAppEnvs: [],
      monitorUrl: "",
      appLogUrl: "",
      tomcatAccessLogUrl: ""
    };
  },
  created() {
    this.loadAppEnvs();
  },
  mounted() {
  },
  watch: {},
  computed: {
    appIsSelected() {
      return this.currCluster && this.currNamespace && this.currApp;
    },
    envSelected: {
      get() {
        return this.currCluster + '/' + this.currNamespace
      },
      set(val) {
        [this.currCluster, this.currNamespace] = val.split('/')
      }
    },
    envSelectorDesc() {
      // if(this.currAppEnvs.length === 0) {
      //   return "未找到任何环境"
      // }
      if (!this.currAppEnvs.some(item => item.cluster === this.currCluster && item.namespace === this.currNamespace)) {
        return "请选择一个正确的环境"
      }
    }
  },
  methods: {
    loadAppEnvs() {
      this.envPageLoading = true;
      appsWithEnv().then(response => {
        this.appEnvs = response.data;
        this.currAppChange(this.currApp)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.envPageLoading = false;
      })
    },
    envSelectedChange(val) {
      this.updateQueryParam()
      this.reloadTab()
    },
    selectFirstEnv() {
      if(!this.appEnvs || this.appEnvs.length < 1) {
        return
      }
      this.currAppEnvs = this.appEnvs[this.currApp] || [];
      if (this.currAppEnvs.length > 0) {
        this.currCluster = this.currAppEnvs[0].cluster
        this.currNamespace = this.currAppEnvs[0].namespace
      }
    },
    reloadTab() {
      let tabName = this.currTab;
      if (tabName === "monitor") {
        this.reloadMonitorUrl()
      } else if (tabName === "appLog") {
        this.reloadAppLogUrl()
      } else if (tabName === "tomcatAccessLog") {
        this.reloadTomcatAccessLogUrl()
      }
      //todo: 其他tab的刷新逻辑调整， 比如只有当前tab为pod时才刷新pod内容
    },
    updateQueryParam() {
      let urlParam = cloneObject(this.$route.query)
      // urlParam["tab"] = this.currTab;
      urlParam["app"] = this.currApp;
      urlParam["cluster"] = this.currCluster;
      urlParam["namespace"] = this.currNamespace;
      this.$router.push({ query: urlParam });
    },
    currAppChange(value) {
      this.currApp = value
      if(!this.currCluster) {
        this.currCluster = this.$route.query.cluster || ""
      }
      if(!this.currNamespace) {
        this.currNamespace = this.$route.query.namespace || ""
      }
      this.currAppEnvs = this.appEnvs[value] || [];
      if (this.currAppEnvs.length > 0) {
        console.info("currApp: " + this.currApp + ", currCluster: " + this.currCluster + ", currNamespace: " + this.currNamespace)
        if (this.currApp && (!this.currCluster || !this.currNamespace)) {
          this.selectFirstEnv()
        }
      }
      this.updateQueryParam()
      this.reloadTab()
    },
    tabChange(tab, event) {
      if (this.currTab === tab.name) {
        return
      }
      this.currTab = tab.name
      this.tabChangeHandler()
      this.updateQueryParam()
    },
    reloadMonitorUrl() {
      this.monitorUrl = `/api/page/redirect?type=grafana&cluster=${this.currCluster}&app=${this.currApp}&namespace=${this.currNamespace}&kiosk=tv&theme=light`
    },
    reloadAppLogUrl() {
      this.appLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=app_log&cluster=${this.currCluster}&namespace=${this.currNamespace}&app=${this.currApp}`;
    },
    reloadTomcatAccessLogUrl() {
      this.tomcatAccessLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=tomcat_access&cluster=${this.currCluster}&namespace=${this.currNamespace}&app=${this.currApp}`;
    },
    tabChangeHandler() {
      try {
        this.tabIframeLoading = true;
        console.info('tab change to: ' + this.currTab)
        this.reloadTab()
      } finally {
        let vThis = this;
        setTimeout(function () {
          vThis.tabIframeLoading = false;
        }, 1000)

      }
    },
    clickhouseLogPage(logName, pod) {
      let url = `/api/page/redirect?type=clickhouse&logName=${logName}&cluster=${this.currCluster}&namespace=${this.currNamespace}&app=${this.currApp}&ip=${pod}&_t` + Date.now();
      window.open(url)
    },
    grafanaPage(pod) {
      let url = `/api/page/redirect?type=grafana&cluster=${this.currCluster}&app=${this.currApp}&namespace=${this.currNamespace}&pod=${pod}`
      window.open(url)
    },
  }
};
</script>

<style>

.el-loading-spinner {
  top: 50px;
}

.table-history-apps .el-table__cell {
  padding: 0;
}

.el-cascader-menu__wrap {
  height: 380px;
}

.pod-index .el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

.pod-index .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #444;
}

.pod-index .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #f57b22;
}

.pod-index .el-tabs--border-card > .el-tabs__header {
  background-color: #e0e0e0;
}

.pod-index .env-selector .el-card__header {
  padding: 10px;
}

.pod-index .env-selector .el-radio__label {
  padding-left: 5px;
}

.pod-index .env-selector .el-radio {
  padding: 3px;
}
</style>
