<template>
  <div class="app-container pod-index">
    <el-button style="border-radius:0;padding-left: 10px;padding-right: 10px;" type="text" disabled>
      <b style="color: #666;">应用选择</b>
    </el-button>
    <el-cascader
      v-model="currApp"
      :options="apps"
      placeholder="选择应用"
      class="app-cascader"
      style="width: 100%;max-width: 600px;margin-left: -8px"
      filterable
      clearable
      @change="appChange">
    </el-cascader>


    <el-popover
      placement="right-start"
      ref="historyAppPopover"
      trigger="click">
      <div style="max-height: 600px;width: 300px">
          <el-table
            row-class-name="table-history-apps"
            :data="appEnvs">
            <el-table-column
              label="应用部署环境">
              <template v-slot="scope">
                <el-button type="text" @click="envChange(scope.row.cluster,scope.row.namespace)">
                  {{ scope.row.cluster }} / {{ scope.row.namespace }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
      </div>
      <el-button slot="reference" style="border-radius:0;padding-left: 10px;padding-right: 10px;margin-left: 20px;" type="text" plain>
        快速切环境
      </el-button>
    </el-popover>

    <el-popover
      placement="right-start"
      width="600"
      ref="historyAppPopover"
      trigger="click">
      <div style="max-height: 600px;overflow-y: auto;">
        <el-table
          row-class-name="table-history-apps"
          :data="historyApps">
          <el-table-column
            prop="app"
            label="历史访问记录">
            <template v-slot="scope">
              {{ scope.row.cluster }} / {{ scope.row.namespace }} /
              <el-button type="text" @click="historyAppSelect(scope.row.cluster,scope.row.namespace,scope.row.app)">
                {{ scope.row.app }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button slot="reference" style="border-radius:0;padding-left: 10px;padding-right: 10px;" type="text" plain>
        访问历史
      </el-button>
    </el-popover>

    <router-link :to="{name: 'cicd-app-deploy', query: {'app': this.currApp[2]}}" target="_blank">
      <el-button style="border-radius:0;padding-left: 10px;padding-right: 10px;" type="text" plain >
        发布流程页
      </el-button>
    </router-link>



    <el-tabs class="pod-index-tabs" type="border-card" :active-name="this.currTab" @tab-click="tabChange" style="margin-top: 10px;" v-loading="tabLoading">
      <el-tab-pane name="pod" :lazy="true">
        <span slot="label"><svg-icon icon-class="application" style="margin-right: 2px;"/>实例管理</span>
        <div v-if="appSelected" style="padding: 10px;">
          <pod-list :cluster="this.currApp[0]" :namespace="this.currApp[1]" :app="this.currApp[2]"></pod-list>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="appEvent" :lazy="true">
        <span slot="label"><svg-icon icon-class="event" style="margin-right: 2px;"/>应用事件</span>
        <div v-if="appSelected" style="padding: 10px;">
          <app-event :cluster="this.currApp[0]" :namespace="this.currApp[1]" :app="this.currApp[2]"></app-event>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="appLog" :lazy="true">
        <span slot="label"><i class="el-icon-tickets" style="margin-right: 2px;"></i>Logback日志</span>
        <div v-if="appSelected" style="position: relative;margin: -24px;">
          <iframe :src="this.appLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;" @click="clickhouseLogPage('app_log',currApp[0],currApp[1],currApp[2],'')">新页面打开</el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="tomcatAccessLog" :lazy="true">
        <span slot="label"><svg-icon icon-class="tomcat" style="margin-right: 2px;"/>Tomcat访问日志</span>
        <div v-if="appSelected" style="position: relative;margin: -24px;">
          <iframe :src="this.tomcatAccessLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;" @click="clickhouseLogPage('tomcat_access',currApp[0],currApp[1],currApp[2],'')">新页面打开
          </el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="monitor" :lazy="true">
        <span slot="label"><svg-icon icon-class="monitor" style="margin-right: 2px;"/>资源监控</span>
        <div v-if="appSelected" style="position: relative;">
          <iframe :src="this.monitorUrl" style="border-width: 0;padding: 0;margin: 10px 0 0 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 520px;top:10px;" @click="grafanaPage(currApp[0],currApp[1],currApp[2],'')">新页面打开</el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;color: #666;font-size: 14px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="doc" :lazy="true">
        <span slot="label"><svg-icon icon-class="help" style="margin-right: 2px;"/>帮助文档</span>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 服务日志接入：
          <el-link href="https://u9icsn6fl7.feishu.cn/docx/FFVVdph88o7RPFxH32ecwkPCnEf" target="_blank" type="primary">文档链接</el-link>
        </div>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 日志查询语法：日志查询条件使用的是 ClickHouse 原生 Where 子句语法。
          <el-link href="https://clickhouse.com/docs/zh/sql-reference/statements/select/where/" target="_blank" type="primary">文档链接</el-link>
        </div>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 日志平台(ClickVisual)文档：
          <el-link href="https://clickvisual.gocn.vip/clickvisual/" target="_blank" type="primary">文档链接</el-link>
        </div>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import {groupByNamespace} from "@/api/app";
import AppEvent from "@/views/pod/app-event.vue";
import {getUserInfo} from "@/api/user";
import PodExpand from "@/views/pod/pod-expand.vue";
import PodList from "@/views/pod/pod-list.vue";

export default {
  components: {PodList, PodExpand, AppEvent},
  data() {
    return {
      currApp: [],
      currTab: "pod",
      tabLoading: false,
      historyApps: [],
      apps: [],
      monitorUrl: "",
      appLogUrl: "",
      tomcatAccessLogUrl: ""
    };
  },
  async mounted() {
    let routerQuery = this.$route.query;
    if (routerQuery.cluster && routerQuery.namespace && routerQuery.app) {
      this.currApp = [routerQuery.cluster, routerQuery.namespace, routerQuery.app]
    }

    //todo: update history app

    this.loadApps()
    this.loadAccessHistory()
  },
  computed: {
    appSelected() {
      return this.currApp.length === 3;
    },
    appEnvs() {
      if (!this.appSelected) {
        return [];
      }
      let ret = []
      for (let cluIt of this.apps) {
        let cluster = cluIt.value;
        for (let nsIt of cluIt.children) {
          let namespace = nsIt.value;
          for (let appIt of nsIt.children) {
            if (appIt.value === this.currApp[2]) {
              ret.push({
                "cluster": cluster,
                "clusterLabel": cluIt.label,
                "namespace": namespace
              })
              break
            }
          }
        }
      }
      return ret
    }
  },
  methods: {
    loadApps() {
      groupByNamespace().then(response => {
        this.apps = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    getAppObj() {
      return {
        "cluster": this.currApp[0],
        "namespace": this.currApp[1],
        "app": this.currApp[2],
      }
    },
    loadAccessHistory() {
      getUserInfo().then(response => {
        let newData = []
        for (let it of response.data.recentPods) {
          let parts = it.split("/")
          if (parts.length === 3) {
            newData.push({
              cluster: parts[0],
              namespace: parts[1],
              app: parts[2]
            })
          }
        }
        this.historyApps = newData
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        if ((!this.currApp || this.currApp.length < 1) && this.historyApps.length > 0) {
          this.currApp = [
            this.historyApps[0].cluster,
            this.historyApps[0].namespace,
            this.historyApps[0].app,
          ]
        }
      })
    },
    appChange(value) {
      this.$router.push({
        name: "pod-index",
        query: this.getAppObj()
      });
      this.tabChangeHandler()
    },
    envChange(cluster, namespace) {
      let app = this.currApp[2]
      this.currApp = [cluster, namespace,app]
      this.appChange();
    },
    historyAppSelect(cluster, namespace, app) {
      this.currApp = [cluster, namespace, app]
      this.tabChangeHandler();
      this.$refs.historyAppPopover.doClose();
    },
    tabChange(tab, event) {
      if (this.currTab === tab.name) {
        return
      }
      this.currTab = tab.name
      this.tabChangeHandler()
    },
    tabChangeHandler() {
      try {
        this.tabLoading = true;
        let tabName = this.currTab;
        console.info('tab change to: ' + tabName)
        let cluster = this.currApp[0];
        let namespace = this.currApp[1];
        let app = this.currApp[2];
        if (tabName === "monitor") {
          this.monitorUrl = `/api/page/redirect?type=grafana&cluster=${cluster}&app=${app}&namespace=${namespace}&kiosk=tv&theme=light`
        } else if (tabName === "appLog") {
          this.appLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=app_;og&cluster=${cluster}&namespace=${namespace}&app=${app}`;
        } else if (tabName === "tomcatAccessLog") {
          this.tomcatAccessLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=tomcat_access&cluster=${cluster}&namespace=${namespace}&app=${app}`;
        }
      } finally {
        let vThis = this;
        setTimeout(function () {
          vThis.tabLoading = false;
        }, 200)

      }
    },

    clickhouseLogPage(logName, cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=clickhouse&logName=${logName}&cluster=${cluster}&namespace=${namespace}&app=${app}&ip=${pod}&_t` + Date.now();
      window.open(url)
    },
    grafanaPage(cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=grafana&cluster=${cluster}&app=${app}&namespace=${namespace}&pod=${pod}`
      window.open(url)
    },
  }
};
</script>

<style>
.app-cascader .el-input__inner {
  border-radius: 0;
}

.el-loading-spinner {
  top: 50px;
}

.table-history-apps .el-table__cell {
  padding: 0;
}

.el-cascader-menu__wrap {
  height: 380px;
}
.pod-index .el-tabs--border-card>.el-tabs__content {
  padding: 0;
}

.pod-index .el-tabs--border-card>.el-tabs__header .el-tabs__item {
  color: #444;
}
.pod-index .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  color: #f57b22;
}
.pod-index .el-tabs--border-card>.el-tabs__header {
  background-color: #e0e0e0;
}
</style>
