<template>
  <div>
    <el-descriptions :column="columns" border size="small" v-loading="loading1 || loading2" labelClassName="app-desc-label">
      <el-descriptions-item>
        <template slot="label">
          应用
        </template>
        <div>
          <b :class="appHighLight? 'app-high-light' : ''">{{ this.appMeta.name }}</b>
          <clipboard-icon :text="appMeta.name"></clipboard-icon>
          <el-tooltip v-if="appMeta.level" content="服务等级" placement="top">
            <el-tag size="mini" style="margin-left: 10px;font-size: 12px;" type="warning" title="应用级别">{{ appMeta.level }}</el-tag>
          </el-tooltip>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>环境</b>
        </template>
        <b :class="appHighLight? 'app-high-light' : ''">{{ this.namespace }}</b>
        <clipboard-icon :text="this.namespace"></clipboard-icon>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>集群</b>
        </template>
        <b :class="appHighLight? 'app-high-light' : ''">{{ this.cluster }} </b>
        <clipboard-icon :text="this.cluster"></clipboard-icon>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>实例数</b>
          <el-tooltip content="运行实例数 / 发布流程配置实例数" placement="top">
            <el-icon :size="16" class="el-icon-question"></el-icon>
          </el-tooltip>
        </template>
        <b :class="appHighLight? 'app-high-light' : ''">
          {{ this.deployMeta ? this.deployMeta.replicas : "--" }}
          <span v-if="this.deployMeta && this.deployMeta.pipelineReplicas >= 0">
           / {{ this.deployMeta.pipelineReplicas }}
        </span>
        </b>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>运行版本</b>
        </template>
        <div v-if="this.deployMeta">
          {{ this.deployMeta.deployTag && this.deployMeta.deployTag.length > 50 ? this.deployMeta.deployTag.substring(1, 50) + "..." : this.deployMeta.deployTag }}
        </div>
        <div v-else>
          --
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>CPU分配</b>
        </template>
        <div v-if="this.deployMeta">
          {{ (this.deployMeta.requestCpu / 1000).toFixed(2) }}
          -
          {{ (this.deployMeta.limitCpu / 1000).toFixed(2) }}
        </div>
        <div v-else>
          --
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <b>内存分配 (MB)</b>
        </template>
        <div v-if="this.deployMeta">
          {{ Math.floor(this.deployMeta.requestMemory / 1024 / 1024) }}
          -
          {{ Math.floor(this.deployMeta.limitMemory / 1024 / 1024) }}
        </div>
        <div v-else>
          --
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">负责人
        </template>
        <el-tooltip :content="appMeta.owners && appMeta.owners.length > 0 ? appMeta.owners.join(',') : '没有配置负责人' " placement="top">
            <span>
              {{
                appMeta.owners && appMeta.owners.length > 0
                  ? (
                    appMeta.owners.join(',').length > 24
                      ? appMeta.owners.join(',').substring(0, 24) + '...'
                      : appMeta.owners.join(',')
                  )
                  : "--"
              }}
            </span>
        </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">第一负责人 / 部门</template>
        <div>
          {{ appMeta.mainOwner ? appMeta.mainOwner : "--" }}  / {{ appMeta.department || "--"}}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">应用描述</template>
        {{ appMeta.remark }}
      </el-descriptions-item>
      <el-descriptions-item style="padding:0 10px;">
        <template slot="label">
          <b>部署信息</b>
        </template>
        <div style="line-height:1.4em;font-size: 11px;">
          <div style="display: inline-block" v-if="this.deployMeta">最后发布：{{ this.deployMeta.deployTime }}</div>
          <div style="display: inline-block;padding-left: 20px;" v-if="this.deployMeta">发布人：{{ this.deployMeta.deployUser }}</div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <el-alert
      v-for="item in clusterLabels"
      :title="'提示：' + item"
      type="warning"
      style="margin-top: 10px;font-weight: bold"
      :closable="false"
      effect="dark">
    </el-alert>
  </div>
</template>
<script>


import {deploymentDetail} from "@/api/k8s/app";
import {findApp} from "@/api/app";
import {findPipelinesByEnv} from "@/api/pipeline";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  components: {ClipboardIcon},
  props: {
    cluster: {
      type: String,
      default: "",
      required: true
    },
    namespace: {
      type: String,
      default: "",
      required: true
    },
    app: {
      type: String,
      default: "",
      required: true
    },
    columns: {
      type: Number,
      default: 4,
      required: false
    },
    appHighLight: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  mounted() {
    this.loadApp();
  },
  watch: {
    cluster(val) {
      this.loadApp();
    },
    namespace(val) {
      this.loadApp();
    },
    app(val) {
      this.loadApp();
    }
  },
  computed: {
    clusterLabels: function () {
      for (let it of this.$settings.clusters) {
        if (it.name === this.cluster) {
          return it.labels
        }
      }
      return []
    }
  },
  data() {
    return {
      loading1: true,
      loading2: true,
      appMeta: {},
      deployMeta: null
    }
  },
  methods: {
    loadApp() {
      this.loading1 = true;
      this.loading2 = true;
      findApp(this.app).then(response => {
        this.appMeta = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
        this.appMeta = {};
      }).finally(() => {
        this.loading1 = false;
      });
      deploymentDetail(this.cluster, this.namespace, this.app).then(response => {
        response.data.pipelineReplicas = -1;
        this.deployMeta = response.data;
        this.loadPipeline();
      }).catch((e) => {
        this.deployMeta = null;
      }).finally(() => {
        this.loading2 = false;
      });
    },
    loadPipeline() {
      findPipelinesByEnv(this.cluster, this.namespace, this.app).then(response => {
        this.deployMeta.pipelineReplicas = response.data.replicas;
      }).catch((e) => {
        this.deployMeta.pipelineReplicas = -1;
      });
    }
  }
}
</script>

<style>
.app-desc-label {
  width: 120px;
  font-weight: bold !important;
}

.app-high-light {
  color: #e36a08;
  font-weight: bold;
  font-size: 14px;
}
</style>
