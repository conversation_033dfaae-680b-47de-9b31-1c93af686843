<template>
  <div class="app-event" v-loading="eventsLoading">
    <div style="padding: 15px 0 30px;font-size:14px;color: #444;font-weight: bold;">应用：{{this.app}} | 运行环境： {{this.namespace}} | 集群： {{this.cluster}}</div>
    <el-timeline v-if="this.events.length >0">
      <el-timeline-item
        v-for="(item,index) in events"
        :timestamp="item.created + '（操作人：' + item.author +')'" placement="top" :color="'#409EFF'">
        <p> {{ item.content }}</p>
      </el-timeline-item>
    </el-timeline>
    <div v-else style="color: #888;font-size: 14px;">
      无任何数据
    </div>
  </div>
</template>
<script>
import {queryEventsByApp} from "@/api/event";

export default {
  props: {
    cluster: {
      type: String,
      default: ""
    },
    namespace: {
      type: String,
      default: ""
    },
    app: {
      type: String,
      default: ""
    },
  },
  watch: {
    cluster(val) {
      this.loadEvents();
    },
    namespace(val) {
      this.loadEvents();
    },
    app(val) {
      this.loadEvents();
    }
  },
  mounted() {
    this.loadEvents(this.cluster, this.namespace, this.app);
  },
  computed: {},
  data() {
    return {
      events: [],
      lastId: -1,
      eventsLoading: false,
    }
  },
  methods: {
    loadEvents: function () {
      this.eventsLoading = true;
      queryEventsByApp(this.cluster, this.namespace, this.app, this.lastId, 500).then(response => {
        this.events = response.data
      }).catch((e) => {
        this.$message.error("加载事件信息失败: " + e.message);
      }).finally(() => {
        this.eventsLoading = false;
      });
    }
  }
}
</script>

<style>
.app-event ul {
  padding-inline-start: 5px;
}
</style>
