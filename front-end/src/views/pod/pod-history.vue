<template>
  <div>
    <div style="margin-bottom: 10px;height: 30px;overflow: hidden;">
      <label style="display: inline-block;width: 70px;color: #999;font-size: 14px;padding-right:12px;text-align: right;">访问历史</label>
      <el-button
        v-for="item in accessHistory"
        size="mini"
        type="plain"
        plain
        style="font-size:14px;padding:7px;margin-bottom: 10px;"
        @click="historyChange(item)">
        <span style="color: #666;">{{item.cluster}}/{{item.namespace}}/</span><b style="color: #409EFF">{{ item.app }}</b>
      </el-button>
    </div>
  </div>
</template>
<script>


import {getUserInfo} from "@/api/user";

export default {
  name: "podHistory",
  props: {},
  mounted() {
    this.loadAccessHistory()
  },
  computed: {},
  data() {
    return {
      accessHistory: []
    }
  },
  methods: {
    historyChange(item) {
      this.$emit("changeHandler", item.cluster, item.namespace, item.app)
      // let vThis = this
      // setTimeout(function() {
      //   vThis.loadAccessHistory();
      // },3000)
    },
    loadAccessHistory() {
      getUserInfo().then(response => {
        let newData = []
        for(let it of response.data.recentPods) {
          let parts = it.split("/")
          if (parts.length === 3) {
            newData.push({
              cluster:parts[0],
              namespace:parts[1],
              app:parts[2]
            })
          }
        }
        this.accessHistory =newData
      }).catch((e) => {
        console.log(e)
      })
    },
  }
}
</script>

