<template>
  <div class="app-container pod-index">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="normal">
      <el-form-item label="应用">
        <el-select v-model="searchForm.app" placeholder="请选择" style="width: 400px" filterable>
          <el-option
            v-for="item in apps"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="环境">
        <el-select v-model="searchForm.env" placeholder="请选择" style="width: 320px">
          <el-option
            v-for="item in envOptions"
            :key="item.value"
            :label="item.value"
            :value="item.value">
            <div>{{ item.label }}</div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
      </el-form-item>
    </el-form>

    <el-tabs class="pod-index-tabs" type="border-card" :active-name="this.currTab" @tab-click="tabChange" v-loading="tabLoading">
      <el-tab-pane name="pod" :lazy="true">
        <span slot="label"><svg-icon icon-class="application" style="margin-right: 2px;"/>实例管理</span>
        <div v-if="hasCurrApp" style="padding: 10px;">
          <pod-list :cluster="currApp.cluster" :namespace="currApp.namespace" :app="currApp.app"></pod-list>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="appEvent" :lazy="true">
        <span slot="label"><svg-icon icon-class="event" style="margin-right: 2px;"/>应用事件</span>
        <div v-if="hasCurrApp" style="padding: 10px;">
          <app-event :cluster="currApp.cluster" :namespace="currApp.namespace" :app="currApp.app"></app-event>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="appLog" :lazy="true">
        <span slot="label"><i class="el-icon-tickets" style="margin-right: 2px;"></i>Logback日志</span>
        <div v-if="hasCurrApp" style="position: relative;margin: -24px;">
          <iframe :src="this.appLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;" @click="clickhouseLogPage('app_log',currApp.cluster,currApp.namespace,currApp.app,'')">新页面打开</el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="tomcatAccessLog" :lazy="true">
        <span slot="label"><svg-icon icon-class="tomcat" style="margin-right: 2px;"/>Tomcat访问日志</span>
        <div v-if="hasCurrApp" style="position: relative;margin: -24px;">
          <iframe :src="this.tomcatAccessLogUrl" style="border-width: 0;padding: 0;margin: 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 30px;top:20px;" @click="clickhouseLogPage('tomcat_access',currApp.cluster,currApp.namespace,currApp.app,'')">新页面打开
          </el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="monitor" :lazy="true">
        <span slot="label"><svg-icon icon-class="monitor" style="margin-right: 2px;"/>资源监控</span>
        <div v-if="hasCurrApp" style="position: relative;">
          <iframe :src="this.monitorUrl" style="border-width: 0;padding: 0;margin: 10px 0 0 0;width: 100%;min-height: 1200px;"></iframe>
          <el-button type="text" icon="el-icon-link" style="position: absolute;right: 520px;top:10px;" @click="grafanaPage(currApp.cluster,currApp.namespace,currApp.app,'')">新页面打开</el-button>
        </div>
        <div v-else>
          <div style="padding: 30px 20px;">请先选择应用</div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="doc" :lazy="true">
        <span slot="label"><svg-icon icon-class="help" style="margin-right: 2px;"/>帮助文档</span>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 服务日志接入：
          <el-link href="https://u9icsn6fl7.feishu.cn/docx/FFVVdph88o7RPFxH32ecwkPCnEf" target="_blank" type="primary">文档链接</el-link>
        </div>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 日志查询语法：日志查询条件使用的是 ClickHouse 原生 Where 子句语法。
          <el-link href="https://clickhouse.com/docs/zh/sql-reference/statements/select/where/" target="_blank" type="primary">文档链接</el-link>
        </div>
        <div style="color: #444;font-size: 14px;padding-left: 10px;line-height: 40px;">
          ◉ 日志平台(ClickVisual)文档：
          <el-link href="https://clickvisual.gocn.vip/clickvisual/" target="_blank" type="primary">文档链接</el-link>
        </div>
      </el-tab-pane>
    </el-tabs>


    <div class="divider-blockquote qa" style="padding-top: 30px;margin: 10px">
      <span>FAQ (常见问题解答）</span>
      <el-divider></el-divider>
      <div style="font-size: 12px;color: #777;">
        <p><b>Q: 各种操作对服务的影响是什么？</b></p>
        <p>A: 点击页面上的操作按钮时，会弹出相关的说明信息。操作前请仔细阅读，确保不要误操作</p>
        <p><b>Q: 实例删除后为什么会再创建一个新的实例？</b></p>
        <p>A: 平台会根据你配置的副本数来保证运行的实例数量，当[运行实例数（不包含摘除的实例）] 不等于 [副本数]时，平台会自动创建或删除实例来使其相等</p>
        <p><b>Q: 一般在什么场景下需要摘除实例？</b></p>
        <p>A:
          实例被摘除后，不会再接受http/Dubbo流量不受限制,RocketMQ消费者会停止。注意Dubbo服务必须有一个正常Provider，否则消费者会强制消费已摘除的实例。当某个实例出现问题后，我们可以先摘除实例，然后再对其运行状态和日志进行分析，分析完以后再把它删除掉</p>
        <p><b>Q: 怎么查看实例重新原因？当实例状态不健康（红色）时怎么查看原因？</b></p>
        <p>A: 点击事件展开按钮，可以显示实例运行状态更多详细信息，包括它的重启原因和历史事件（运行过程中发生过的事情）等</p>
        <p><b>Q: 实例的OOM事件</b></p>
        <p>A: 实例OOM主要分两种，一种是ContainerOOM, 一种是JvmOOM，导致的原因请参见事件中的描述。
          <span
            class="new-line">ContainerOOM： 容器出现了OOM（容器使用的内存超出了容器最大分配内存）。一般需要把jvm的堆外内存 (容器内存 - 最大堆内存）调大</span>
          <span class="new-line">JvmOOM： java进程出现了OOM。当出现JvmOOM时， 平台会自动dump文件，并zip压缩后放到重启后pod的tomcat/logs目录下</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import {getAllAppName} from "@/api/app";
import AppEvent from "@/views/pod/app-event.vue";
import {getUserInfo} from "@/api/user";
import PodExpand from "@/views/pod/pod-expand.vue";
import PodList from "@/views/pod/pod-list.vue";

export default {
  components: {PodList, PodExpand, AppEvent},
  data() {
    return {
      currApp: {},
      searchForm: {
        app: "",
        env: ""
      },
      currTab: "pod",
      tabLoading: false,
      historyApps: [],
      apps: [],
      monitorUrl: "",
      appLogUrl: "",
      tomcatAccessLogUrl: ""
    };
  },
  async mounted() {
    let routerQuery = this.$route.query;
    if (routerQuery.cluster && routerQuery.namespace && routerQuery.app) {
      this.changeCurrApp(routerQuery.cluster, routerQuery.namespace, routerQuery.app)
    }
    this.loadApps()
    this.loadAccessHistory()
  },
  computed: {
    hasCurrApp() {
      return this.currApp && this.currApp.app && this.currApp.cluster && this.currApp.namespace
    },
    envOptions() {
      let ret = []
      for (let clu of this.$settings.clusters) {
        for (let ns of clu.namespaces) {
          ret.push({
            "value": `${clu.name}/${ns}`,
            "label": `${clu.description}(${clu.name}) / ${ns}`
          })
        }
      }
      return ret
    },
  },
  methods: {
    search() {
      if (this.searchForm.app && this.searchForm.env) {
        let parts = this.searchForm.env.split("/")
        if (parts.length === 2) {
          this.currApp = {
            "cluster": parts[0],
            "namespace": parts[1],
            "app": this.searchForm.app
          }
        }
      }
    },
    loadApps() {
      getAllAppName().then(response => {
        this.apps = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    loadAccessHistory() {
      getUserInfo().then(response => {
        let newData = []
        for (let it of response.data.recentPods) {
          let parts = it.split("/")
          if (parts.length === 3) {
            newData.push({
              cluster: parts[0],
              namespace: parts[1],
              app: parts[2]
            })
          }
        }
        this.historyApps = newData
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        if (!this.hasCurrApp && this.historyApps.length > 0) {
          this.changeCurrApp(this.historyApps[0].cluster, this.historyApps[0].namespace, this.historyApps[0].app)
        }
      })
    },
    historyAppSelect(cluster, namespace, app) {
      this.currApp = [cluster, namespace, app]
      this.tabChangeHandler();
      this.$refs.historyAppPopover.doClose();
    },
    changeCurrApp(cluster,namespace,app) {
      this.currApp = {
        "cluster": cluster,
        "namespace": namespace,
        "app": app
      }
      this.searchForm.app = app
      this.searchForm.env = `${cluster}/${namespace}`
    },
    tabChange(tab, event) {
      if (this.currTab === tab.name) {
        return
      }
      this.currTab = tab.name
      this.tabChangeHandler()
    },
    tabChangeHandler() {
      try {
        this.tabLoading = true;
        let tabName = this.currTab;
        console.info('tab change to: ' + tabName)
        let cluster = this.currApp.cluster
        let namespace = this.currApp.namespace;
        let app = this.currApp.app;
        if (tabName === "monitor") {
          this.monitorUrl = `/api/page/redirect?type=grafana&cluster=${cluster}&app=${app}&namespace=${namespace}&kiosk=tv&theme=light`
        } else if (tabName === "appLog") {
          this.appLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=app_log&cluster=${cluster}&namespace=${namespace}&app=${app}`;
        } else if (tabName === "tomcatAccessLog") {
          this.tomcatAccessLogUrl = `/api/page/redirect?type=clickhouse&pageType=share&logName=tomcat_access&cluster=${cluster}&namespace=${namespace}&app=${app}`;
        }
      } finally {
        let vThis = this;
        setTimeout(function () {
          vThis.tabLoading = false;
        }, 200)

      }
    },

    clickhouseLogPage(logName, cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=clickhouse&logName=${logName}&cluster=${cluster}&namespace=${namespace}&app=${app}&ip=${pod}&_t` + Date.now();
      window.open(url)
    },
    grafanaPage(cluster, namespace, app, pod) {
      let url = `/api/page/redirect?type=grafana&cluster=${cluster}&app=${app}&namespace=${namespace}&pod=${pod}`
      window.open(url)
    },
  }
};
</script>

<style>
.app-cascader .el-input__inner {
  border-radius: 0;
}

.el-loading-spinner {
  top: 50px;
}

.table-history-apps .el-table__cell {
  padding: 0;
}

.el-cascader-menu__wrap {
  height: 380px;
}

.pod-index .el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

</style>
