<template>
  <div class="app-container sync-config-to-other" v-loading="loading" element-loading-text="拼命加载中">
    <div style="margin: 10px auto;text-align: center">
    <el-checkbox-group v-model="columns" @change="columnChange">
      <el-checkbox label="选项开关" :checked="true"></el-checkbox>
      <el-checkbox label="部署模块"></el-checkbox>
      <el-checkbox label="CPU"></el-checkbox>
      <el-checkbox label="内存"></el-checkbox>
      <el-checkbox label="副本数"></el-checkbox>
      <el-checkbox label="流程状态"></el-checkbox>
      <el-checkbox label="基础镜像"></el-checkbox>
      <el-checkbox label="Webhook"></el-checkbox>
      <el-checkbox label="端口"></el-checkbox>
    </el-checkbox-group>
    </div>
    <el-row>
      <el-col :span="24">
        <el-card>
          <el-table
            :data="pipelineSrc"
            element-loading-text="数据加载中..."
            size="small"
            fit>
            <el-table-column label="应用" prop="app"></el-table-column>
            <el-table-column label="环境" prop="namespace" min-width="140">
              <template slot-scope="scope">
                <div style="font-weight: bold;">
                  {{ scope.row.namespace }} （{{ scope.row.cluster }}）
                </div>
              </template>
            </el-table-column>
            <el-table-column label="选项开关" min-width="400" v-if="this.columns.includes('选项开关')" :key="Math.random()">
              <template slot-scope="scope">
                  <template v-for="(val, key, index) in scope.row.options">
                    <el-checkbox :label="pipelineOptionDesc(key)" :checked="val"  disabled></el-checkbox>
                  </template>
              </template>
            </el-table-column>
            <el-table-column label="部署模块" min-width="400" v-if="this.columns.includes('部署模块')" :key="Math.random()">
              <template slot-scope="scope">
                  <el-table :data="scope.row.appModules" size="mini" border>
                    <el-table-column type="index" width="40"></el-table-column>
                    <el-table-column prop="gitUrl" label="Git地址" min-width="200px"></el-table-column>
                    <el-table-column prop="module" label="子模块"></el-table-column>
                    <el-table-column prop="contextPath" label="ContextPath"></el-table-column>
                  </el-table>
              </template>
            </el-table-column>
            <el-table-column label="CPU"
                             prop="resources.limitCPU"
                             v-if="this.columns.includes('CPU')" :key="Math.random()">
              <template slot-scope="slot" slot="header">
                CPU
                <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope" v-if="scope.row.resources">
                {{ scope.row.resources.requestCPU.toFixed(1) }} - {{ scope.row.resources.limitCPU.toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column prop="resources.limitMemory" v-if="this.columns.includes('内存')" :key="Math.random()">
              <template slot-scope="slot" slot="header">
                内存 (MB)
                <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope" v-if="scope.row.resources">
                {{ scope.row.resources.requestMemory }} - {{ scope.row.resources.limitMemory }}
              </template>
            </el-table-column>
            <el-table-column label="副本数"
                             prop="replicas"
                             v-if="this.columns.includes('副本数')" :key="Math.random()">
            </el-table-column>
            <el-table-column label="状态" width="80" align="center" v-if="this.columns.includes('流程状态')" :key="Math.random()">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 'enabled'" type="success" size="small">
                  {{ convertStatus(scope.row.status) }}
                </el-tag>
                <el-tag v-else type="warning" size="small">
                  {{ convertStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="基础镜像" v-if="this.columns.includes('基础镜像')" :key="Math.random()">
              <template slot-scope="scope">
                {{ scope.row.baseImage.split("/").pop() }}
              </template>
            </el-table-column>
            <el-table-column label="Webhook" v-if="this.columns.includes('Webhook')" :key="Math.random()">
              <template slot-scope="scope">
                {{ scope.row.webhook }}
              </template>
            </el-table-column>
            <el-table-column label="端口" v-if="this.columns.includes('端口')" :key="Math.random()">
              <template slot-scope="scope">
                <div v-for="item in scope.row.ports">
                  {{ item.name }} = {{ item.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div style="text-align: center;margin: 0 auto;">
          <div style="display: inline-block;width: 100px;">
            <div style="display: flex;margin: 20px auto;">
              <div style="display: inline-block;height: 80px; margin: 0 20px;">
                <svg-icon icon-class="down2" style="font-size: 60px"></svg-icon><br/>
                <span style="font-size: 20px;">同步到</span>
              </div>
            </div>
          </div>
        </div>

      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card>
          <el-table
            :data="pipelineDest"
            ref="table-dest"
            size="small"
            element-loading-text="数据加载中..."
            fit>
            <el-table-column
              type="selection"
              align="center"
              width="60">
            </el-table-column>
            <el-table-column label="应用" prop="app"></el-table-column>
            <el-table-column label="环境" prop="namespace" min-width="140">
              <template slot-scope="scope">
                <div style="font-weight: bold;">
                  {{ scope.row.namespace }} （{{ scope.row.cluster }}）
                </div>
              </template>
            </el-table-column>
            <el-table-column label="选项开关" min-width="400" v-if="this.columns.includes('选项开关')" :key="Math.random()">
              <template slot-scope="scope">
                  <template v-for="(val, key, index) in scope.row.options">
                    <el-checkbox :label="pipelineOptionDesc(key)" :checked="val"  disabled></el-checkbox>
                  </template>
              </template>
            </el-table-column>
            <el-table-column label="部署模块" min-width="400" v-if="this.columns.includes('部署模块')" :key="Math.random()">
              <template slot-scope="scope">
                  <el-table :data="scope.row.appModules" size="mini" border>
                    <el-table-column type="index" width="40"></el-table-column>
                    <el-table-column prop="gitUrl" label="Git地址" min-width="200px"></el-table-column>
                    <el-table-column prop="module" label="子模块"></el-table-column>
                    <el-table-column prop="contextPath" label="ContextPath"></el-table-column>
                  </el-table>
              </template>
            </el-table-column>
            <el-table-column label="CPU"
                             prop="resources.limitCPU"
                             v-if="this.columns.includes('CPU')" :key="Math.random()">
              <template slot-scope="slot" slot="header">
                CPU
                <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope" v-if="scope.row.resources">
                {{ scope.row.resources.requestCPU.toFixed(1) }} - {{ scope.row.resources.limitCPU.toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column prop="resources.limitMemory" v-if="this.columns.includes('内存')" :key="Math.random()">
              <template slot-scope="slot" slot="header">
                内存 (MB)
                <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope" v-if="scope.row.resources">
                {{ scope.row.resources.requestMemory }} - {{ scope.row.resources.limitMemory }}
              </template>
            </el-table-column>
            <el-table-column label="副本数"
                             prop="replicas"
                             v-if="this.columns.includes('副本数')" :key="Math.random()">
            </el-table-column>
            <el-table-column label="状态" width="80" align="center" v-if="this.columns.includes('流程状态')" :key="Math.random()">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 'enabled'" type="success" size="small">
                  {{ convertStatus(scope.row.status) }}
                </el-tag>
                <el-tag v-else type="warning" size="small">
                  {{ convertStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="基础镜像" v-if="this.columns.includes('基础镜像')" :key="Math.random()">
              <template slot-scope="scope">
                {{ scope.row.baseImage.split("/").pop() }}
              </template>
            </el-table-column>
            <el-table-column label="Webhook" v-if="this.columns.includes('Webhook')" :key="Math.random()">
              <template slot-scope="scope">
                {{ scope.row.webhook }}
              </template>
            </el-table-column>
            <el-table-column label="端口" v-if="this.columns.includes('端口')" :key="Math.random()">
              <template slot-scope="scope">
                <div v-for="item in scope.row.ports">
                  {{ item.name }} = {{ item.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <div style="margin-top: 10px;margin-bottom: 30px;text-align: center;">
      <el-button type="warning" @click="onAbort">取消</el-button>
      <el-button type="primary" style="margin-left: 40px" @click="onSubmit">提交</el-button>
    </div>
  </div>
</template>

<script>
import {getAllAppName} from "@/api/app";
import PipelineExpand from "@/views/pipeline/pipeline-expand.vue";
import {findPipelinesByApp, syncPipeline} from "@/api/pipeline";
import {pipelineOptionDesc} from "@/utils/my-util";

export default {
  components: {PipelineExpand},
  data() {
    return {
      loading: false,
      app: "",
      columns:[],
      pipelineSrcId: null,
      appModules: [],
      dialogAppModuleVisible: false,
      submitLoading: false,
      pipelineSrc: [],
      pipelineDest: [],
    }
  },
  mounted() {
    this.pipelineSrcId = parseInt(this.$route.query.pipelineId)
    this.app = this.$route.query.app
    this.loadTableData()
  },
  computed: {},
  methods: {
    pipelineOptionDesc,
    loadTableData() {
      this.loading = true
      findPipelinesByApp(this.app).then(response => {
        for (let pi of response.data) {
          if (pi.id === this.pipelineSrcId) {
            this.pipelineSrc.push(pi)
          } else {
            this.pipelineDest.push(pi)
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })
    },
    columnChange(columns) {
      this.columns = columns
    },
    pipelinePage() {
      let p = {
        "app": this.app,
      };
      this.$router.push({name: 'cicd-app-deploy', query: p});
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "可用"
        case "disabled":
          return "禁用"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        default:
          return "未知"
      }
    },
    onAbort() {
      this.$confirm(`确认取消吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pipelinePage()
      })
    },
    onSubmit() {
      const h = this.$createElement;
      let srcPipe = this.pipelineSrc[0];
      let destPipes = this.$refs["table-dest"].store.states.selection;
      if (!destPipes || destPipes.length < 1) {
        this.$message.warning("请选择需要被同步的发布流程");
        return
      }
      if(this.columns.length<1){
        this.$message.warning("请选择需要被同步的配置项");
        return
      }
      this.$msgbox({
        title: '提示',
        message: h('p', null, [
          h('span', { style: 'word-break: break-all' }, `${srcPipe.namespace}(${srcPipe.cluster}) 的配置( ${this.columns.join(",")} )将被同步到 ${destPipes.map(p => `${p.namespace}(${p.cluster})`).join(",")}`),
          h('div', null, `是否继续？`),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.submitLoading = true;
        let param = {
          "pipelineSrcId": srcPipe.id,
          "pipelineDestIds": destPipes.map(p => p.id),
          "columns": this.columns,
        }

        syncPipeline(param).then(response => {
          this.$message.success("操作成功, 请刷新页面查看结果");
          this.submitLoading = false;
        }).catch((e) => {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: e.message,
            type: 'error'
          })
          this.submitLoading = false;
        });
      })
    },
  }
}
</script>

<style>
.sync-config-to-other .el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: revert;
}
.sync-config-to-other .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #555;
}
</style>
