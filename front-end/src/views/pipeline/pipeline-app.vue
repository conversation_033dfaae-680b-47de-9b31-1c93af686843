<template>
  <div>
    <el-descriptions class="margin-top" :column="columns" size="mini" border labelClassName="app-desc-label" v-loading="loading">
      <el-descriptions-item>
        <template slot="label">
          应用
          <el-tooltip content="应用名称" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        <div style="min-width: 160px">
          {{ appMeta.name }}
          <clipboard-icon :text="appMeta.name"></clipboard-icon>
          <el-tooltip v-if="appMeta.level" content="服务等级" placement="top">
            <el-tag size="mini" style="margin-left: 10px;font-size: 12px;" type="warning" title="应用级别">{{ appMeta.level }}</el-tag>
          </el-tooltip>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">第一负责人
          <el-tooltip content="应用的主负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        {{ appMeta.mainOwner ? appMeta.mainOwner : "--" }}
        <el-button type="text" style="font-size: 12px;padding: 0;margin-left: 8px;" @click="appEditPage">编辑
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          负责人
          <el-tooltip content="应用的负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        <span>
          <el-tooltip :content="appMeta.owners && appMeta.owners.length > 0 ? appMeta.owners.join(',') : '没有配置负责人' " placement="top">
            <span>
              {{
                appMeta.owners && appMeta.owners.length > 0
                  ? (
                    appMeta.owners.join(',').length > 24
                      ? appMeta.owners.join(',').substring(0, 24) + '...'
                      : appMeta.owners.join(',')
                  )
                  : "--"
              }}
            </span>
          </el-tooltip>
        </span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          发布权限
          <el-tooltip content="如果配置了权限，则拥有权限的同学才能发布。如果没有配置，则所有人都可发布。" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        <div style="display: inline-block" v-if="appMeta.orgs && appMeta.orgs.length > 0">
          <div style="max-width: 400px;">
            部门：{{ appMeta.orgs.join(',') }}
            <el-button type="text" style="font-size: 12px;padding: 0;margin-left: 8px;" @click="orgPage">查看部门成员
            </el-button>
          </div>
        </div>
        <div style="display: inline-block" v-else>-任何人-</div>
        <el-button type="text" style="font-size: 12px;padding: 0;margin-left: 8px;" @click="appAuthPage">编辑
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item :span="2">
        <template slot="label">
          发布时间窗口
          <el-tooltip content="如果配置了时间窗口，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        <div>
          <template v-if="this.$settings.timeWindow.open === false">
            - 系统已关闭时间窗口限制 -
          </template>
          <template v-else-if="appMeta.timeWindow && appMeta.timeWindow.length > 0">
            <el-tooltip effect="light" placement="top">
              <div slot="content">
                <pre>{{ appMeta.timeWindowDesc }}</pre>
              </div>
              <span style="display: inline-block">
                {{ appMeta.timeWindowDesc.substring(0, 60).replaceAll("\n", "&nbsp;&nbsp;") + (appMeta.timeWindowDesc.length > 60 ? "..." : "") }}
              </span>
            </el-tooltip>
            <el-button type="text" style="font-size: 12px;padding: 0;margin-left: 8px;" @click="appEditPage">编辑
            </el-button>
            <div v-if="this.excludeNamespaces.length > 0" style="color: #d45e0c;font-weight: bold;max-width: 800px;">以下环境不受限制：{{ this.excludeNamespaces.join(", ") }}</div>
          </template>
          <template v-else>-任何时间-
            <el-button type="text" style="font-size: 12px;padding: 0;margin-left: 8px;" @click="appEditPage">编辑
            </el-button>
          </template>

        </div>
      </el-descriptions-item>

      <el-descriptions-item>
        <template slot="label">
          描述
          <el-tooltip content="应用描述信息" placement="top">
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        {{ appMeta.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>


import {findApp} from "@/api/app";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  name: "pipeline-app",
  components: {ClipboardIcon},
  props: {
    app: {
      type: String,
      default: "",
      required: true
    },
    columns: {
      type: Number,
      default: 2,
      required: false
    },
  },
  mounted() {
    this.loadApp();
  },
  watch: {
    app(val) {
      this.loadApp();
    }
  },
  computed: {
    excludeNamespaces: function () {
      if (this.$settings.timeWindow && this.$settings.timeWindow.excludeNamespaces) {
        return this.$settings.timeWindow.excludeNamespaces
      }
      return []
    }
  },
  data() {
    return {
      loading: false,
      appMeta: {},
    }
  },
  methods: {
    loadApp() {
      if (!this.app) {
        console.log(this.app)
        return
      }
      this.loading = true;
      findApp(this.app).then(response => {
        this.appMeta = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
        this.appMeta = {};
      }).finally(() => {
        this.loading = false;
      });
    },
    appAuthPage() {
      let href = this.$router.resolve({name: 'app-permission', query: {"app": this.app}}).href
      window.open(href, '_blank');
    },
    orgPage() {
      let href = this.$router.resolve({name: 'auth-org', query: {}}).href
      window.open(href, '_blank');
    },
    appEditPage() {
      let href = this.$router.resolve({
        name: 'app-list',
        query: {"showEditDialog": "true", "app": this.app}
      }).href
      window.open(href, '_blank');
    },
  }
}
</script>

<style>
.app-desc-label {
  width: 110px;
  font-weight: bold !important;
}
</style>
