<template>
  <div :style="{fontSize:fontsize}">
    <div class="expand-row-wrapper" v-if="pipeline.app">
      <div class="expand-row-item">
        <label>备注</label>
        <span>{{ pipeline.remark || '--' }}</span>
      </div>
      <div class="expand-row-item">
        <label>k8s集群</label>
        <span>{{ pipeline.cluster }}</span>
      </div>
      <div class="expand-row-item">
        <label>基础镜像</label>
        <span>{{ pipeline.baseImage.substring(pipeline.baseImage.lastIndexOf("/") + 1) }}</span>
      </div>
      <div class="expand-row-item">
        <label>资源池调度</label>
        <span>调度策略 ({{ pipeline.schedule.strategy }})</span>
        <span>资源池 ({{ pipeline.schedule.node }})</span>
      </div>
      <div class="expand-row-item">
        <label>部署策略</label>
        <span>{{ pipeline.deployStrategy }}</span>
      </div>
      <div class="expand-row-item">
        <label>选项开关</label>
        <div style="padding-left: 10px;display: inline-block;">
          <el-checkbox-group v-model="pipeOptions">
          <el-checkbox :style="{fontSize:fontsize}" v-for="it in pipeOptionsAll" :label="it" size="mini" disabled class="pipeline-option"></el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="expand-row-item">
        <label>健康检测</label>
        <div style="padding-left: 10px;display: inline-block;">
          <el-checkbox label="启动检测" :checked="pipeline.startupProbe.enable" size="mini" disabled class="pipeline-option"></el-checkbox>
          <el-checkbox label="存活检查" :checked="pipeline.livenessProbe.enable" size="mini" disabled class="pipeline-option"></el-checkbox>
          <el-checkbox label="就绪检测" :checked="pipeline.readinessProbe.enable" size="mini" disabled class="pipeline-option"></el-checkbox>
        </div>
      </div>
      <div class="expand-row-item">
        <label>JVM参数</label>
        <span>{{ pipeline.jvmOpts || '--' }}</span>
      </div>
      <div class="expand-row-item" v-if="pipeline.pvc.enable">
        <label>持久存储</label>
        <span>{{ pipeline.pvc.enable ? pipeline.pvc.name : '' }}</span>
      </div>
      <div class="expand-row-item">
        <label>关闭前保留时间</label>
        <span>{{ pipeline.preStopRetainSeconds }}</span>
      </div>
      <div class="expand-row-item">
        <label>关闭前回调地址</label>
        <span>{{ pipeline.preStopWebhook || '--'}}</span>
      </div>
      <div class="expand-row-item">
        <label>Eolinker任务数</label>
        <span>{{ pipeline.eolinkerIDs && pipeline.eolinkerIDs.length > 0 ? pipeline.eolinkerIDs.length : "--"}}</span>
      </div>
      <div class="expand-row-item">
        <label>Webhook</label>
        <span>{{ pipeline.webhook.url || '--'}}</span>
      </div>
      <div class="expand-row-item">
        <label>端口</label>
        <span>{{ pipeline.ports.map(x => x.value).join(",") }}</span>
      </div>
      <div class="expand-row-item">
        <label>自定义环境变量</label>
        <span>
          <template v-for="(item,index) in pipeline.envs">
            <div v-if="item.type === 'USER'" style="display: inline-block;padding-right: 20px;">
              {{ item.name }}={{item.value}}
            </div>
          </template>
        </span>
      </div>
      <div class="expand-row-item">
        <label>创建时间</label>
        <span>{{ pipeline.createdTime }}</span>
      </div>
    </div>
    <div v-else>
      数据丢失
    </div>
  </div>
</template>
<script>


import {cloneObject, pipelineOptionDesc} from "@/utils/my-util";

export default {
  props: {
    pipeline: {
      type: Object,
      default: () => ({}),
      required: true
    },
    fontsize: {
      type: String,
      default: "12px",
      required: false
    }
  },
  mounted() {
    for(const [key,val] of Object.entries(this.pipeline.options)) {
      this.pipeOptionsAll.push(pipelineOptionDesc(key))
      if(val) {
        this.pipeOptions.push(pipelineOptionDesc(key))
      }
    }
    this.pipeOptionsOrigin = cloneObject(this.pipeOptions)
  },
  computed: {},
  data() {
    return {
      pipeOptionsOrigin:[],
      pipeOptions:[],
      pipeOptionsAll:[]
    }
  },
  methods: {
  }
}
</script>

<style>
.expand-row-wrapper {
  margin-bottom: 10px;
}

.expand-row-wrapper .expand-row-item {
  line-height: 1.5em;
}

.expand-row-wrapper .expand-row-item > label {
  display: inline-block;
  width: 100px;
  text-align: right;
  font-weight: bold;
  float: left;
}

.expand-row-wrapper .expand-row-item span {
  padding-left: 10px;
}

.pipeline-option {
  margin-right: 20px;
}
.pipeline-option.is-disabled  .el-checkbox__input {
  padding-left: 0;
}
.pipeline-option.is-disabled span.el-checkbox__label {
  color: #606266;
  font-size: 12px;
  padding-left: 5px;
}

</style>
