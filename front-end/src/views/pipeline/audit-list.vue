<template>
  <div class="app-container">
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="数据加载中..."
      :default-sort="{prop: 'createdTime', order: 'descending'}"
      border
      fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="集群"
                       sortable
                       prop="cluster">
      </el-table-column>
      <el-table-column label="命名空间"
                       sortable
                       prop="namespace">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>
      <el-table-column label="模块数" sortable width="100px" align="center">
        <template slot-scope="scope">
          {{ scope.row.appModules.length }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'enabled'" type="success">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
          <el-tag v-else type="warning">
            {{ convertStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="实例数" align="center" prop="replicas" sortable>
      </el-table-column>
      <el-table-column label="CPU"
                       sortable>
        <template slot-scope="scope" v-if="scope.row.resources">
          {{ scope.row.resources.requestCPU.toFixed(1) }} - {{ scope.row.resources.limitCPU.toFixed(1) }}
        </template>
      </el-table-column>
      <el-table-column label="内存 (MB)"
                       sortable>
        <template slot-scope="scope" v-if="scope.row.resources">
          {{ scope.row.resources.requestMemory }} - {{ scope.row.resources.limitMemory }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" property="author" sortable>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdTime" sortable>
      </el-table-column>
      <el-table-column
        label="操作"
        width="260px"
        fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="auditPage(scope.row)">审核
          </el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="removePipe(scope.row)">删除
          </el-button>
          <el-button
            type="text"
            icon="el-icon-position"
            @click="pipelinePage(scope.row)">发布流程页
          </el-button>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>

import {findPipelinesByStatus, removePipeline} from "@/api/pipeline";

export default {
  data() {
    return {
      tableData: [],
      tableLoading: false,
    }
  },
  computed: {},
  mounted() {
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      findPipelinesByStatus("audit").then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    auditPage(row) {
      this.$router.push({
        name: 'app-pipeline-edit', query: {
          "pipelineId": row.id,
          "operate": "audit",
        }
      });
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    removePipe(row) {
      this.$confirm(`确认要删除吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removePipeline(row.id).then(response => {
          this.$message.success("删除成功")
          this.loadTableData(this.$route.query.app)
        }).catch((e) => {
          this.$message.error(e.message);
        });
      })
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "正常"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        default:
          return "未知"
      }
    }
  }
}
</script>

