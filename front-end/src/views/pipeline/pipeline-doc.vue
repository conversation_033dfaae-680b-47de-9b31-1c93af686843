<template>
  <div>
    <div class="divider-blockquote qa" style="padding-top: 20px;">
      <span>FAQ (常见问题解答）</span>
      <el-divider></el-divider>
      <div style="font-size: 12px;color: #777;max-width: 1200px;">
        <p><b>Q: 如何创建发布流程</b></p>
        <p>A: 研发同学在页面新建发布流程，创建好以后发布流程状态为[待审核] → 联系 @魏贺 进行审批 → 使用发布流程进行发布</p>
        <p><b>Q: 发布流程下线包含哪些操作？</b></p>
        <p>A: 首先会把环境下的实例数缩容为0，然后再把发布流程删除掉。对于其他Meta信息，管理员后期会统一做清理</p>
        <p><b>Q: 应用实例需要访问外网怎么办？</b></p>
        <p>A: 出于安全考虑，默认情况下应用是无法访问外网的。如果需要访问外网，请按照
          <a target="_blank" style="color: #01AAED;"
             href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404601">k8s应用外网访问-申请</a> 文档进行处理
        </p>
        <p><b>Q: 如何获取k8s环境下应用访问外网的出口IP？</b></p>
        <p>A: 请咨询 @丰莹莹</p>
        <p><b>Q: 为什么会有 [运行实例数] > [配置实例数] 的情况？</b></p>
        <p>A: 服务进行了自动扩容或手动扩容</p>
        <p><b>Q: 日志中心搜不到服务任何日志？</b></p>
        <p>A: 如果应用日志需要被收集起来，请在发布流程里勾上 [接入ClickHouse日志] 选项，并且在工程里引入和配置我们的oss-metrics组件<br/>
          oss-metrics组件使用说明：http://git.firstshare.cn/JavaCommon/oss-metrics</p>
      </div>
    </div>
  </div>
</template>
<script>


export default {
  props: {},
  mounted() {
  },
  computed: {},
  data() {
    return {}
  },
  methods: {}
}
</script>

<style scoped>
.divider-blockquote > .el-divider {
  background-color: #409EFF;
  margin: 3px 0 10px;
}

.divider-blockquote.qa > span {
  color: #999;
}

.divider-blockquote.qa > .el-divider {
  background-color: #bbb;
}

.divider-blockquote.qa span.new-line {
  padding: 5px 10px 0;
  display: block
}

</style>
