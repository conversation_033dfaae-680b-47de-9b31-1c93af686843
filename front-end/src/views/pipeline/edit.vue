<template>
  <div class="app-container pipeline-edit-container" v-loading="loading" element-loading-text="拼命加载中">
    <div style="max-width: 960px;">
      <el-form ref="form" :rules="rules" :model="form" label-width="120px">
        <el-form-item label="ID" v-show="false">
          <el-input v-model="form.id" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="所属应用" prop="app">
          <el-input v-model="form.app" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="运行环境">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-form-item prop="cluster">
                <el-select v-model="form.cluster" placeholder="选择k8s集群" style="width: 100%;" :disabled="isEdit || isClone"
                           @change="clusterChange" filterable>
                  <el-option
                    v-for="item in clusterOptions"
                    v-if="item.enable"
                    :key="item.name"
                    :label="item.name + ' (' + item.description + ')'"
                    :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="namespace">
                <el-select v-model="form.namespace" placeholder="选择运行环境" style="width: 100%" :disabled="isEdit || isClone"
                           @change="namespaceChange" filterable>
                  <el-option
                    v-for="item in namespaceOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
              <template slot="content">
                <div class="tooltip-wrapper">
                  k8s集群：一般保持默认即可。如果是专属云，一般都有自己专有的k8s集群，请选择对应的集群
                </div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
            </el-tooltip>
          </el-row>
        </el-form-item>
        <el-form-item label="基础镜像" prop="baseImage">
          <el-select v-model="form.baseImage" style="width: 100%" filterable>
            <el-option
              v-for="item in baseImageOptions"
              :key="item"
              :label="item.split('/').reverse()[0]"
              :value="item">
            </el-option>
          </el-select>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                基础镜像包含应用需要的运行环境，比如jdk版本, tomcat版本。一般保持默认即可
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="部署策略">
          <el-select v-model="form.deployStrategy" style="width: 100%">
            <el-option
              v-for="item in deployStrategyOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>

          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                <b>滚动：</b>启动一个新实例，等达到健康状态后删除一个旧实例。如此循环，直到所有旧实例都被替换掉（平滑度好，推荐）<br/>
                <b>重建：</b>删除所有旧实例 -> 启动所有新实例（平滑度低，不会有多版本并存，发版速度快）
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="实例数">
          <el-input-number v-model="form.replicas" :step="1" :min="0" :max="50"
                           @change="replicaChange"></el-input-number>
        </el-form-item>
        <el-form-item label="资源（最大值）">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-input v-model.number="form.resources.limitCPU" type="number" @change="limitCPUChange"
                        step="0.1" min="0.2" max="15">
                <template slot="prepend">CPU</template>
              </el-input>
            </el-col>
            <el-col :span="12">
              <el-input v-model.number="form.resources.limitMemory" type="number" @change="limitMemChange"
                        step="128" min="128" max="30720">
                <template slot="prepend">内存</template>
                <template slot="append">MB</template>
              </el-input>
            </el-col>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
              <template slot="content">
                <div class="tooltip-wrapper">
                  CPU：单个实例可使用的最大CPU核心数 <br/>
                  内存：单个实例可使用的最大内存
                </div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
            </el-tooltip>
          </el-row>
        </el-form-item>
        <el-form-item label="资源（最小值）">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-input v-model.number="form.resources.requestCPU" type="number" step="0.1" min="0.2" max="15"
                        :disabled="!this.userIsAdmin">
                <template slot="prepend">CPU</template>
              </el-input>
            </el-col>
            <el-col :span="12">
              <el-input v-model.number="form.resources.requestMemory" type="number" step="128" min="128" max="30720"
                        :disabled="!this.userIsAdmin">
                <template slot="prepend">内存</template>
                <template slot="append">MB</template>
              </el-input>
            </el-col>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
              <template slot="content">
                <div class="tooltip-wrapper">
                  CPU：分配给单个实例的最低保障CPU <br/>
                  内存：分配给单个实例的最低保障内存
                </div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
            </el-tooltip>
          </el-row>
        </el-form-item>
        <el-form-item style="margin-top:-22px;">
          <div style="color: #dc5907"><b>提示：</b>您可以降低资源配置（CPU/内存/副本数）。如需上调配置，请走审批进行申请（管理员无此限制）。具体请参考
            <a class="el-link" target="_blank" href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404592"
               style="color: #409EFF">操作文档</a>
          </div>
        </el-form-item>
        <el-form-item label="部署模块">
          <el-table
            :data="form.appModules"
            element-loading-text="Loading"
            border
            fit
            highlight-current-row>
            <template slot="empty">
              <span style="color: #F56C6C;">请添加部署模块</span>
            </template>
            <el-table-column label="Git地址" min-width="200">
              <template slot-scope="scope">
                {{ scope.row.gitUrl }}
              </template>
            </el-table-column>
            <el-table-column label="子模块">
              <template slot-scope="scope">
                {{ scope.row.module }}
              </template>
            </el-table-column>
            <el-table-column label="ContextPath">
              <template slot-scope="scope">
                {{ scope.row.contextPath }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="60px"
              fixed="right">
              <template slot-scope="scope">
                <el-popconfirm :title="'确定要删除吗？'" @confirm="deleteAppModule(scope.row.gitUrl,scope.row.module)">
                  <el-button type="text" slot="reference">删除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <div style="padding-top:10px;text-align: center">
            <el-button icon="el-icon-plus" size="small" @click="dialogAppModuleVisible=true">添加模块</el-button>
          </div>
        </el-form-item>
        <el-form-item label="JVM参数">
          <el-input type="textarea" :rows="3" v-model="form.jvmOpts" style="word-break: break-all;"></el-input>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                <p style="font-weight: bold">Java进程参数</p>
                <p style="margin: 10px 0 0 0;font-weight: bold">● 内部参数说明</p>
                <div>
                  -Dprocess.profile.candidates：配置中心的候选配置组，用户跨配置组加载配置文件。详情参考配置中心文档<br/>
                  -Dserver.tomcat.max-keep-alive-requests：配置 Tomcat Connector maxKeepAliveRequests参数，默认值为500<br/>
                  -Dserver.tomcat.protocol：配置 Tomcat Connector protocol 参数，默认值为org.apache.coyote.http11.Http11Nio2Protocol<br/>
                  -Dserver.tomcat.max-threads: 配置 Tomcat Executor maxThreads 参数，默认值为500<br/>
                </div>
                <p style="margin: 10px 0 0 0;">
                  <b>● OpenJDK8的默认值:</b> https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/no-jdk/files/fs-jvm-options.sh
                </p>
                <p style="margin: 10px 0 0 0;">
                  <b>● DragonWell8的默认值:</b> https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/ali-dragonwell8/files/fs-jvm-options.sh
                </p>
                <p style="margin: 10px 0 0 0;">
                  <b>● OpenJDK17的默认值:</b> https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk17/files/fs-jvm-options.sh
                </p>
                <p style="margin: 10px 0 0 0;">
                  <b>● OpenJDK21的默认值:</b> https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk21/files/fs-jvm-options.sh
                </p>
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="关闭前回调地址" prop="preStopWebhook">
          <el-input v-model="form.preStopWebhook"/>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                Pod在关闭之前的回调的地址。localhost代表Pod本身，比如： http://localhost/fs-app/shutdown<br/>
                Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → <b>call回调地址</b> → 等待一段时间 → 关闭
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="关闭前保留时间">
          <el-input-number v-model="form.preStopRetainSeconds" :max="3600" :min="10" :step="10"/>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement" style="float: unset">
            <template slot="content">
              <div class="tooltip-wrapper">
                Pod在关闭之前的保留时间（单位：秒）。主要用在需要对实例进行优雅关闭的场景<br/>
                Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → call回调地址 → <b>等待当前配置的时间</b> → 关闭
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="伙伴应用">
          <el-select v-model="form.partnerApps" filterable multiple placeholder="请选择应用" style="width: 100%;">
            <el-option
              v-for="item in appOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                调度时，会尽量选择伙伴应用实例所在的宿主机
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="排斥应用">
          <el-select v-model="form.exclusiveApps" filterable multiple placeholder="请选择应用" style="width: 100%;">
            <el-option
              v-for="item in appOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>

          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                调度时，会尽量避开排斥应用实例所在的宿主机<br/>
                默认会添加自己，这样可以让应用的多个实例尽量放到不同的宿主机上
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="扩展容器">
          <el-input v-model="form.extInitContainer"/>
          <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
            <template slot="content">
              <div class="tooltip-wrapper">
                额外追加自定义的initContainer，输入镜像全称，多个英文逗号分割
              </div>
            </template>
            <svg-icon icon-class="question" class-name="question-icon-right question-icon-size"/>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark"/>
        </el-form-item>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <el-row>
              <b class="box-card-title">一些选项开关</b>
              <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
                <template slot="content">
                  <div class="tooltip-wrapper">
                    <div>
                      【内核参数修改】包含：<br/>
                      net.ipv4.tcp_keepalive_time=600 <br/>
                      net.ipv4.tcp_keepalive_intvl=20 <br/>
                      net.ipv4.tcp_keepalive_probes=3
                    </div>
                    <div>
                      【只允许发布Tag】: 发版时将只能选择tag, 不能选择branch。线上业务应用建议开启该选项，当发布代码出现问题是能够更好地回滚版本
                    </div>
                  </div>
                </template>
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </el-row>
          </div>
          <div>
            <el-row :gutter="5">
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.onlyDeployTag">{{ 'onlyDeployTag' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement"
                            content="发布时只允许发tag，不允许发分支。线上建议开启，便于服务版本回滚">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.jvmGcLog">{{ 'jvmGcLog' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement" content="开启jvm的gc日志并保存到日志文件里">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.isCoreApp" disabled>{{ 'isCoreApp' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement" content="目前只做服务标记">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.appLogToKafka">{{ 'appLogToKafka' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement" content="业务日志上报到ClickHouse日志中心">
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.buildUseRuntimeJDK">{{ 'buildUseRuntimeJDK' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
                  <template slot="content">
                    <div class="tooltip-wrapper">
                      默认使用jdk8编译源码。勾选后，则使用运行环境（基础镜像）的jdk版本去编译源码
                      <div style="padding-top: 5px;">
                        <b>jdk升级参考资料</b><br/>
                        <a
                          href="https://blogs.oracle.com/javamagazine/post/its-time-to-move-your-applications-to-java-17-heres-why-and-heres-how"
                          style="color: #409EFF" target="_blank">
                          It’s time to move your applications to Java 17. Here’s why—and how
                        </a><br/>
                        <a href="https://javaalmanac.io/" style="color: #409EFF" target="_blank">
                          The Java Version Almanac
                        </a><br/>
                      </div>
                      <div style="padding-top: 5px;">
                        <b>Java交叉编译参考资料</b><br/>
                        <a href="https://www.sunmoonblog.com/2018/08/27/javac-source/" style="color: #409EFF"
                           target="_blank">
                          如何使用Javac的source参数
                        </a><br/>
                        <a
                          href="https://stackoverflow.com/questions/38882080/specifying-java-version-in-maven-differences-between-properties-and-compiler-p"
                          style="color: #409EFF" target="_blank">
                          Specifying Java version in maven
                        </a><br/>
                      </div>
                    </div>
                  </template>
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="6" style="padding: 5px 0;">
                <el-checkbox v-model="form.options.addSysctlKeepalive">{{ 'addSysctlKeepalive' | optionDesc }}</el-checkbox>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
                  <template slot="content">
                    <div class="tooltip-wrapper">
                      【内核参数修改】包含：<br/>
                      net.ipv4.tcp_keepalive_time=600 <br/>
                      net.ipv4.tcp_keepalive_intvl=20 <br/>
                      net.ipv4.tcp_keepalive_probes=3
                    </div>
                  </template>
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
            </el-row>

          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <b class="box-card-title">健康检查</b>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
              <template slot="content">
                <div class="tooltip-wrapper">
                  在Kubernetes中，健康检查是确保应用程序正常运行的关键。通过不同类型的健康检查，您可以有效地管理应用程序的启动、运行和可用性
                </div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
            <div style="display: inline-block; margin-left: 10px;font-size: 12px;">
              <span style="color: #666;"> （如果没有特殊需求，保持系统默认配置即可） </span>
              <a href="https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/"
                 style="color: #409EFF;display: inline-block;margin-left: 10px;" target="_blank">
                查看k8s官方文档
              </a>
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="8" style="padding: 0 5px">
                <el-card shadow="never" class="health-check-card">
                  <div slot="header" class="clearfix" style="padding: 5px;">
                    <span>就绪检查</span>
                    <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement"
                                content="pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败时，则k8s会将pod从服务负载均衡中剔除(摘除HTTP流量），直到pod再次就绪。建议配置该检查。">
                      <svg-icon icon-class="question" class-name="question-icon-size"/>
                    </el-tooltip>
                    <div style="float: right;">
                      <span style="font-size: 14px;">启用</span>
                      <el-switch
                        v-model="form.readinessProbe && form.readinessProbe.enable">
                      </el-switch>
                    </div>
                  </div>
                  <div>
                    <el-input class="input-list-item" v-model.number="form.readinessProbe.initialDelaySeconds" type="number"
                              min="10" max="300">
                      <template slot="prepend">
                        <div class="health-check-slot">初始延迟</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.readinessProbe.periodSeconds" type="number"
                              min="20" max="300">
                      <template slot="prepend">
                        <div class="health-check-slot">检查间隔</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.readinessProbe.timeoutSeconds" type="number"
                              min="1" max="30">
                      <template slot="prepend">
                        <div class="health-check-slot">超时时间</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.readinessProbe.failureThreshold" type="number"
                              min="1" max="60">
                      <template slot="prepend">
                        <div class="health-check-slot">失败阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.readinessProbe.successThreshold" type="number"
                              min="1" max="2" disabled>
                      <template slot="prepend">
                        <div class="health-check-slot">成功阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8" style="padding: 0 5px">
                <el-card shadow="never" class="health-check-card">
                  <div slot="header" class="clearfix" style="padding: 5px;">
                    <span>存活检查</span>
                    <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement"
                                content="pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败，则k8s会对pod进行重启">
                      <svg-icon icon-class="question" class-name="question-icon-size"/>
                    </el-tooltip>
                    <div style="float: right;">
                      <span style="font-size: 14px;">启用</span>
                      <el-switch
                        v-model="form.livenessProbe && form.livenessProbe.enable">
                      </el-switch>
                    </div>
                  </div>
                  <div>
                    <el-input class="input-list-item" v-model.number="form.livenessProbe.initialDelaySeconds" type="number"
                              min="1200" max="3600">
                      <template slot="prepend">
                        <div class="health-check-slot">初始延迟</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.livenessProbe.periodSeconds" type="number"
                              min="20" max="300">
                      <template slot="prepend">
                        <div class="health-check-slot">检查间隔</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.livenessProbe.timeoutSeconds" type="number"
                              min="1" max="30">
                      <template slot="prepend">
                        <div class="health-check-slot">超时时间</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.livenessProbe.failureThreshold" type="number"
                              min="1" max="60">
                      <template slot="prepend">
                        <div class="health-check-slot">失败阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.livenessProbe.successThreshold" type="number"
                              min="1" max="5" disabled>
                      <template slot="prepend">
                        <div class="health-check-slot">成功阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>

                  </div>
                </el-card>
              </el-col>
              <el-col :span="8" style="padding: 0 5px">
                <el-card shadow="never" class="health-check-card">
                  <div slot="header" class="clearfix" style="padding: 5px;">
                    <span>启动检查</span>
                    <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement"
                                content="在pod启动时执行，检查pod是否已准备好接收流量(tomcat是否已经完成启动）。容器在尝试其他健康检查之前，将等待此检查成功。">
                      <svg-icon icon-class="question" class-name="question-icon-size"/>
                    </el-tooltip>
                    <div style="float: right;">
                      <span style="font-size: 14px;">启用</span>
                      <el-switch
                        v-model="form.startupProbe && form.startupProbe.enable">
                      </el-switch>
                    </div>
                  </div>
                  <div>
                    <el-input class="input-list-item" v-model.number="form.startupProbe.initialDelaySeconds" type="number"
                              min="10" max="60">
                      <template slot="prepend">
                        <div class="health-check-slot">初始延迟</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.startupProbe.periodSeconds" type="number"
                              min="5" max="30">
                      <template slot="prepend">
                        <div class="health-check-slot">检查间隔</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.startupProbe.timeoutSeconds" type="number"
                              min="1" max="30">
                      <template slot="prepend">
                        <div class="health-check-slot">超时时间</div>
                      </template>
                      <template slot="append">秒</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.startupProbe.failureThreshold" type="number"
                              min="300" max="1000">
                      <template slot="prepend">
                        <div class="health-check-slot">失败阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>
                    <el-input class="input-list-item" v-model.number="form.startupProbe.successThreshold" type="number"
                              min="1" max="5" disabled>
                      <template slot="prepend">
                        <div class="health-check-slot">成功阈值</div>
                      </template>
                      <template slot="append">次</template>
                    </el-input>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <b class="box-card-title">资源池调度</b>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
              <template slot="content">
                <div class="tooltip-wrapper">
                  调度策略：服务实例在调度时选择资源池的策略
                  资源池：服务实例运行的宿主机集合
                </div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </div>
          <div>
            <el-form-item label="调度策略：" label-width="100">
              <el-select v-model="form.schedule.strategy" style="width: 600px">
                <el-option
                  v-for="item in scheduleStrategyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
                <template slot="content">
                  <div class="tooltip-wrapper">
                    【必须调度到选择的资源池】: 当选择的资源池资源不足时会调度失败，这时需要联系管理员对资源池做扩容 <br/>
                    【优先调度到选择的资源池】: 当选择的资源池资源不足时会把实例调度到【通用】宿主机上运行
                  </div>
                </template>
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="资源池：" label-width="100">
              <div v-for="item in nodeOptions" style="padding-left: 85px;">
                <el-radio :label="item.value" v-model.trim="form.schedule.node">{{ item.name }}</el-radio>
              </div>
            </el-form-item>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <el-row>
              <el-col :span="16">
                <b class="box-card-title">持久存储</b>
                <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement">
                  <template slot="content">
                    <div class="tooltip-wrapper">
                      给应用容器挂载磁盘，该磁盘下的文件不会随着应用的重新发版或重启而删除掉。一般在以下场景才需要开启持久存储<br/>
                      1. 应用需要比较大的存储空间<br/>
                      2. 应用需要永久存储一些过程中产生的文件(重新发版/重启应用实例不会对这些文件进行删除)<br/>
                      3. 应用的多个实例需要共享读写一个存储盘
                    </div>
                  </template>
                  <svg-icon icon-class="question" class-name="question-icon-size"/>
                </el-tooltip>
              </el-col>
              <el-col :span="8" style="text-align: right;">
                <el-switch
                  v-model="form.pvc && form.pvc.enable"
                  active-text="使用"
                  inactive-text="未使用">
                </el-switch>
              </el-col>
            </el-row>
          </div>
          <div v-show="form.pvc && form.pvc.enable">
            <el-input class="input-list-item" v-model.trim="form.pvc.name">
              <template slot="prepend">
                <div style="width: 80px">PVC名称</div>
              </template>
            </el-input>
            <el-input class="input-list-item" v-model.trim="form.pvc.mountPath">
              <template slot="prepend">
                <div style="width: 80px">挂载目录路径</div>
              </template>
            </el-input>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <el-row>
              <el-col :span="16">
                <b class="box-card-title">性能监控</b>
                <a v-if="currentCluster && currentCluster.apm.enable && currentCluster.apm.skyWalkingUI"
                   :href="currentCluster.apm.skyWalkingUI"
                   style="padding-left: 10px;color:#409EFF;font-size: 14px;" target="_blank">UI页面</a>
              </el-col>
              <el-col :span="8" style="text-align: right;">
                <div v-if="currentCluster && currentCluster.apm.enable">
                  <el-switch
                    v-model="form.options.skyWalkingAgent"
                    active-text="开启"
                    inactive-text="关闭">
                  </el-switch>
                </div>
                <div v-else>
                  <small style="padding-left: 10px;color: #777;">（暂未开放）</small>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="font-size: 14px;color: #777;">
            性能监控管理(APM)：使用Skywalking搭建，开启后会使用Skywalking Agent来收集应用的一些性能指标信息（如：请求耗时，分布式追踪等）<br/>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <el-row>
              <el-col :span="16">
                <b class="box-card-title">接口测试（Eolinker）</b>
              </el-col>
            </el-row>
          </div>
          <div>
            <div style="font-size: 14px;color: #777;padding-bottom: 10px;">
              可以选择一个Eolinker任务，那么应用发布成功后会调用执行该任务进行测试，格式： 项目名 / 任务名
            </div>
            <el-cascader clearable
                         v-model="eolinkerModel"
                         :key="eolinkerFlushCascader"
                         :options="eolinkerOptions"
                         :props="eolinkerProps"
                         separator=" / "
                         style="width: 100%">
            </el-cascader>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <el-row>
              <el-col :span="16">
                <b class="box-card-title">发布后回调（Webhook）</b>
              </el-col>
              <el-col :span="8" style="text-align: right;">
              </el-col>
            </el-row>
          </div>
          <div>
            <div style="font-size: 14px;color: #777;padding-bottom: 10px;">
              发布成功后，会调用一次Webhook地址
            </div>
            <el-input class="input-list-item" v-model.trim="form.webhook.url">
              <template slot="prepend">
                <div style="width: 80px">URL</div>
              </template>
            </el-input>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <b class="box-card-title">环境变量</b>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement" content="系统环境变量，发布系统维护的变量不允许修改">
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </div>
          <div>
            <div v-for="item in form.envs" style="margin: 5px 0;">
              <el-input placeholder="变量名"
                        clearable
                        style="display: inline-block;width: 260px;"
                        :disabled="item.type === 'SYSTEM'"
                        v-model.trim="item.name"
              >
              </el-input>
              =
              <el-input style="display: inline-block;width: 400px;"
                        v-model.trim="item.value"
                        :disabled="item.type === 'SYSTEM'"
              >
              </el-input>
              <el-button @click.prevent="removeEnv(item)" v-show="item.type !== 'SYSTEM'" icon="el-icon-delete">删除
              </el-button>
            </div>
            <div style="padding-top: 10px;padding-left: 310px;">
              <el-button @click="addEnv" icon="el-icon-plus">添加变量</el-button>
              <br/>
              <el-popover
                placement="top"
                title="一些特殊用途的环境变量"
                width="600"
                trigger="click">
                <div>
                  <div>JEMALLOC_ENABLE=true</div>
                  <div style="font-size: 12px;color: #666;">启用jemalloc内存分配器</div>
                  <el-divider class="env-divider"></el-divider>
                  <div>SPRING_BOOT_JAR_APP=true</div>
                  <div style="font-size: 12px;color: #666;">如果需要使用Jar包类型的SpringBoot应用，请开启这个变量</div>
                  <el-divider class="env-divider"></el-divider>
                  <div>CMS_STARTER_PRIVATE_KEY=[字符串内容]</div>
                  <div style="font-size: 12px;color: #666;">使用自定义的秘钥对配置中心内容加解密</div>
                  <el-divider class="env-divider"></el-divider>
                  <div>SPRING_BOOT_METRICS_ENABLE=true</div>
                  <div style="font-size: 12px;color: #666;">开启Prometheus PodMonitor</div>
                  <el-divider class="env-divider"></el-divider>
                  <div>SPRING_PROFILER_AGENT_ENABLE=true</div>
                  <div style="font-size: 12px;color: #666;">开启应用启动时间分析，详情请查阅spring-startup-analyzer资料</div>
                </div>
                <el-button slot="reference" icon="el-icon-search" type="text">查看特殊环境变量</el-button>
              </el-popover>
            </div>
          </div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <b class="box-card-title">端口</b>
            <el-tooltip :effect="tooltip.effect" :placement="tooltip.placement"
                        content="应用需要暴露的端口，如FCP服务可添加：fcp = 5432 。发布系统维护的端口不允许修改">
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </div>
          <div>
            <div v-for="item in form.ports" style="margin: 5px 0;">
              <el-input placeholder="端口名"
                        clearable
                        style="display: inline-block;width: 260px;"
                        :disabled="item.type === 'SYSTEM'"
                        v-model.trim="item.name">
              </el-input>
              =
              <el-input-number placeholder="端口值"
                               controls-position="right"
                               style="display: inline-block;width: 260px;"
                               :min="0"
                               :max="65535"
                               :step="1000"
                               v-model="item.value"
                               :disabled="item.type === 'SYSTEM'">

              </el-input-number>

              <el-button @click.prevent="removePort(item)" v-show="item.type !== 'SYSTEM'" icon="el-icon-delete">删除
              </el-button>
            </div>
            <div style="padding-top: 10px;padding-left: 310px;">
              <el-button @click="addPort" icon="el-icon-plus">添加端口</el-button>
            </div>
          </div>
        </el-card>

        <div style="padding: 20px;">
          <el-row>
            <el-col :span="12">
              <div>
                <el-button @click="onAbort" type="warning">取消</el-button>
              </div>
            </el-col>
            <el-col :span="12">
              <div style="text-align: right;">
                <el-button type="primary" @click="onSubmit('form')" :loading="submitLoading"
                           style="padding-right: 30px;padding-left: 30px;">提交
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <el-dialog title="添加部署模块" :visible.sync="dialogAppModuleVisible" width="840px" :close-on-click-modal="false">
        <el-form :model="dialogAppModuleForm" label-width="100px">
          <el-form-item label="部署模块">
            <el-select filterable clearable v-model="dialogAppModuleForm.module" placeholder="请选择部署模块。格式：Git地址 --- 子模块"
                       @change="deployModuleSelChanged" style="width: 100%">
              <el-option
                v-for="item in artifactOptions"
                :key="item.id"
                :label="`${item.gitUrl} --- ${item.module}`"
                :value="`${item.gitUrl}@@${item.module}`">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="ContextPath">
            <el-input v-model.trim="dialogAppModuleForm.contextPath"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogAppModuleVisible = false">取 消</el-button>
          <el-button type="primary" @click="addAppModule()">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {findPipelinesById, initNewPipeline, postPipeline} from "@/api/pipeline";
import {getAllArtifact} from "@/api/artifact";
import {getAllAppName} from "@/api/app";
import {listEolinkerPorjects, listEolinkerTimedTask} from "@/api/eolinker";
import {isAdmin} from "@/api/user";
import {cloneObject, pipelineOptionDesc} from "@/utils/my-util";

export default {
  data() {
    let validatePorts = (rule, value, callback) => {
      for (let it of value) {
        console.log(it)
        if (!it.name || !it.value) {
          callback(new Error('请输入所有端口的名称和值'));
          return
        }
      }
      callback();
    };
    let validateEnvs = (rule, value, callback) => {
      for (let it of value) {
        console.log(it)
        if (!it.name) {
          callback(new Error('部分环境的变量名未填写'));
          return
        }
      }
      callback();
    };
    let validatePreStopWebhook = (rule, value, callback) => {
      if (value) {
        if (!value.startsWith("http://") && !value.startsWith("https://")) {
          callback(new Error('Pod关闭前回调地址必须使用 http:// 或者 https:// 开头'));
          return
        }
      }
      callback();
    };
    return {
      loading: true,
      submitLoading: false,
      userIsAdmin: false,
      appOptions: [],
      artifactOptions: [],
      artifactSelected: {},
      tooltip: {
        effect: "light",
        placement: "right-start"
      },
      formOrigin: {},
      form: {
        appModules: [],
        resources: {},
        livenessProbe: {},
        readinessProbe: {},
        startupProbe: {},
        schedule: {},
        pvc: {},
        envs: [],
        ports: [],
        options: {},
        eolinkerIDs: [],
        partnerApps: [],
        exclusiveApps: [],
        webhook: {},
        preStopWebhook: "",
        preStopRetainSeconds: 20
      },
      rules: {
        app: [
          {required: true, message: '请输入应用名称'},
          {min: 4, message: '最少4个字符'}
        ],
        cluster: [
          {required: true, message: '请选择集群'},
        ],
        namespace: [
          {required: true, message: '请选择运行环境'},
        ],
        baseImage: [
          {required: true, message: '请选择基础镜像'},
        ],
        envs: [
          {validator: validateEnvs, message: '部分环境的变量名未填写'}
        ],
        ports: [
          {validator: validatePorts, message: '请输入所有端口的名称和值'}
        ],
        preStopWebhook: [
          {validator: validatePreStopWebhook, message: 'Pod关闭前回调地址必须使用 http:// 或 https:// 开头'}
        ]
      },
      dialogAppModuleVisible: false,
      dialogAppModuleForm: {
        module: "",
        contextPath: "/"
      },
      eolinkerProps: {
        multiple: true,
        lazy: true,
        // 使用箭头函数可以解决this作用域的问题
        lazyLoad: (async (node, resolve) => {
          const {level} = node
          switch (level) {
            case 0:
              break;
            case 1:
              resolve(await this.listEolinkerTimedTask(node.data.value))
              break;
            default:
              resolve([])
              return
          }
        })
      },
      eolinkerOptions: [],
      eolinkerFlushCascader: Math.random(), //重新渲染标识key，否则不会回显 https://segmentfault.com/a/1190000023768426

    }
  },
  filters: {
    optionDesc: function (opt, a) {
      return pipelineOptionDesc(opt)
    }
  },
  mounted() {
    let app = this.$route.query.app
    let pipelineId = this.$route.query.pipelineId
    if (pipelineId) {
      this.loadPipeline(pipelineId)
    } else {
      this.initPipeline(app)
    }
    this.loadArtifacts()
    this.loadApps()

    isAdmin().then(response => {
      this.userIsAdmin = response.data
    }).catch((e) => {
      console.log(e)
    })
  },
  computed: {
    isEdit: function () {
      return this.form.id > 0
    },
    isClone: function () {
      return "clone" === this.$route.query.operate;
    },
    isAudit: function () {
      return "audit" === this.$route.query.operate;
    },
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.form.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.form.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
    currentCluster: function () {
      let clu = this.form.cluster;
      if (clu) {
        for (let it of this.$settings.clusters) {
          if (it.name === clu) {
            return it
          }
        }
      }
    },
    nodeOptions: function () {
      if (this.form.cluster) {
        for (let i in this.$settings.clusters) {
          let curr = this.$settings.clusters[i];
          if (this.form.cluster === curr.name) {
            return curr.nodes;
          }
        }
      }
      return []
    },
    scheduleStrategyOptions: function () {
      return [
        {
          "value": "PREFERRED",
          "label": "优先调度到选择的资源池"
        },
        {
          "value": "REQUIRED",
          "label": "必须调度到选择的资源池"
        },
      ]
    },
    baseImageOptions: function () {
      let clu = this.currentCluster;
      if (clu) {
        return clu.baseImages;
      }
      return []
    },
    armBaseImageOptions: function () {
      return this.$settings.armBaseImages;
    },
    deployStrategyOptions: function () {
      return this.$settings.deployStrategies;
    },
    eolinkerModel: {
      get: function () {
        let ids = []
        for (let i = 0; i < this.form.eolinkerIDs.length; i++) {
          let id = []
          id.push(this.form.eolinkerIDs[i].projectID)
          id.push(this.form.eolinkerIDs[i].timedTaskID)
          ids.push(id)
        }
        return ids;
      },
      set: function (vals) {
        let ids = []
        for (let i = 0; i < vals.length; i++) {
          if (vals[i] && vals[i].length === 2) {
            ids.push({
              projectID: vals[i][0],
              timedTaskID: vals[i][1],
            })
          }
        }
        this.form.eolinkerIDs = ids
      }
    },

  },
  methods: {
    loadApps() {
      getAllAppName().then(response => {
        this.appOptions = response.data;
      }).catch((e) => {
        this.$message.warning("加载应用数据出错！ " + e.message);
      });
    },
    loadPipeline(id) {
      findPipelinesById(id).then(response => {
        this.form = response.data
        this.formOrigin = cloneObject(this.form)
        //区分不同的操作类型
        if (this.isAudit) {
          console.log("pipeline audit, set status to enabled")
          this.form.status = "enabled"
        } else if (this.isClone) {
          console.log("pipeline clone, set cluster and namespace")
          let oldNamespace = this.form.namespace
          this.form.id = 0
          this.form.status = "audit"
          this.form.cluster = this.$route.query.targetCluster
          this.form.namespace = this.$route.query.targetNamespace
          //如果集群里没有这个专属节点，清空
          if (this.nodeOptions.filter(it => it.value === this.form.schedule.node).length === 0) {
            this.form.schedule.node = ""
          }
          if (oldNamespace !== this.form.namespace) {
            this.namespaceChange(this.form.namespace)
          }
        }
        this.loadEolinkerOptions();
      }).catch((e) => {
        this.$message.error("加载数据出错！ " + e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    loadArtifacts() {
      getAllArtifact().then(response => {
        this.artifactOptions = response.data;
      }).catch((e) => {
        this.$message.error("加载数据出错！ " + e.message);
      });
    },
    initPipeline(app) {
      console.log(app)
      initNewPipeline(app).then(response => {
        this.form = response.data;
        this.loadEolinkerOptions()
      }).catch((e) => {
        this.$message.error("加载数据出错！ " + e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    deleteAppModule(gitUrl, module) {
      let modules = this.form.appModules
      modules.splice(modules.findIndex(m => m.gitUrl === gitUrl && m.module === module), 1);
    },
    clusterChange() {
      this.form.namespace = ""
      this.form.baseImage = ""
    },
    namespaceChange(ns) {
      if (ns.indexOf("-") < 1) {
        return
      }
      //尝试给需要的环境增加配置中心候选配置组
      this.form.jvmOpts = this.form.jvmOpts.replace(/-Dprocess.profile.candidates=\S+/gi, "").trim()
      let cms_candidates = "";
      if (ns.startsWith("fstest-")) {
        cms_candidates = "fstest";
      } else if (ns.startsWith("firstshare-")) {
        cms_candidates = "firstshare"
      } else if (ns.startsWith("foneshare-")) {
        cms_candidates = "foneshare"
      } else if (ns.startsWith("foneshare") && ns !== "foneshare") {
        //eg: foneshare01, foneshare02, foneshare03...
        cms_candidates = "foneshare"
      } else { //专属云的环境名不固定
        cms_candidates = "cloud,foneshare"
      }
      this.form.jvmOpts = (this.form.jvmOpts + " -Dprocess.profile.candidates=" + cms_candidates).trim();
    },
    replicaChange(currVal, oldVal) {
      if (!this.userIsAdmin && currVal > this.formOrigin.replicas) {
        this.$message.warning("如果需要增加实例数，请发审批")
        this.$nextTick(() => {
          this.form.replicas = oldVal
        })
      }
    },
    limitCPUChange(v) {
      if (!this.userIsAdmin && v > this.formOrigin.resources.limitCPU) {
        this.$message.warning("如果需要增加CPU，请发审批")
        this.form.resources.limitCPU = this.formOrigin.resources.limitCPU
        return
      }
      //cpu使用2倍的超售
      let val = parseFloat((v / 4).toFixed(1));
      this.form.resources.requestCPU = val <= 0.1 ? 0.1 : val
    },
    limitMemChange(v) {
      if (!this.userIsAdmin && v > this.formOrigin.resources.limitMemory) {
        this.$message.warning("如果需要增加内存，请发审批")
        this.form.resources.limitMemory = this.formOrigin.resources.limitMemory
        return
      }
      //内存避免超售
      let val = parseInt(v)
      this.form.resources.requestMemory = val < 64 ? 64 : val
    },
    deployModuleSelChanged() {
      let f = this.dialogAppModuleForm;
      let items = f.module.split("@@")
      if (items.length === 2) {
        f.contextPath = "/" + items[1]
      }
    },
    addAppModule() {
      //todo: 更多校验
      let f = this.dialogAppModuleForm;
      if (!f.module || !f.contextPath) {
        this.$message.error("请填写完整数据")
        return
      }
      if (!f.contextPath.startsWith("/")) {
        this.$message.error("ContextPath 必须以斜线开头")
        return
      }

      let items = f.module.split("@@")
      if (items.length !== 2) {
        this.$message.error("无法解析模块数据");
        return false
      }
      let m = {
        "gitUrl": items[0],
        "module": items[1],
        "contextPath": f.contextPath
      };

      if (this.form.appModules.filter(item => item.gitUrl === m.gitUrl && item.module === m.module).length > 0) {
        this.$message.error("模块已经存在，不能重复添加")
        return
      }
      if (this.form.appModules.filter(item => item.contextPath === m.contextPath).length > 0) {
        this.$message.error("ContextPath 出现重复")
        return
      }
      this.form.appModules.push(m)
      this.dialogAppModuleVisible = false;
    },
    isProdEnv() {
      return window.location.host.indexOf("foneshare") > -1;
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return
        }
        if (!this.form.appModules || this.form.appModules.length < 1) {
          this.$message.error("没有配置部署模块");
          return
        }
        let whUrl = this.form.webhook.url
        if (whUrl && !(whUrl.startsWith("http://") || whUrl.startsWith("https://"))) {
          this.$message.error("WebHook Url地址必须以 http:// 或者 https:// 开头");
          return
        }

        if (this.form.id === 0 && this.isProdEnv() && this.form.cluster === 'k8s1') {
          if (this.$route.query.allowCreate !== 'yes') {
            this.$message.error("k8s1集群不允许创建新的发布流程");
            return
          }
        }
        this.$confirm(`是否继续？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitLoading = true;
          postPipeline(this.form).then(response => {
            this.$message.success("操作成功");
            this.pipelinePage();
            this.submitLoading = false;
          }).catch((e) => {
            this.$message({
              dangerouslyUseHTMLString: true,
              message: e.message,
              type: 'error'
            })
            this.submitLoading = false;
          });
        })
      })
    },
    onAbort() {
      this.$confirm(`确认取消吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pipelinePage()
      })
    },
    pipelinePage() {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": this.form.app}});
    },
    addPort() {
      this.form.ports.push({
        name: '',
        value: 0,
        type: 'USER'
      });
    },
    removePort(item) {
      if (item.type === "SYSTEM") {
        this.$message.error("不允许删除该端口");
        return;
      }
      let index = this.form.ports.indexOf(item);
      if (index !== -1) {
        this.form.ports.splice(index, 1)
      }
    },
    addEnv() {
      this.form.envs.push({
        name: '',
        value: '',
        type: 'USER'
      });
    },
    removeEnv(item) {
      if (item.type === "SYSTEM") {
        this.$message.error("不允许删除该变量");
        return;
      }
      let index = this.form.envs.indexOf(item);
      if (index !== -1) {
        this.form.envs.splice(index, 1)
      }
    },
    async loadEolinkerOptions() {
      // 加载第一级数据
      this.eolinkerOptions = await this.listEolinkerProject()
      // 循环eolinker多选的值

      for (let i = 0; i < this.form.eolinkerIDs.length; i++) {
        let projectId = this.form.eolinkerIDs[i].projectID;
        if (projectId) {
          for (let i = 0; i < this.eolinkerOptions.length; i++) {
            if (this.eolinkerOptions[i].value === projectId && !this.eolinkerOptions[i].children) {
              this.eolinkerOptions[i].children = await this.listEolinkerTimedTask(projectId)
            }
          }
          this.eolinkerFlushCascader = Math.random()
        }
      }

    },
    async listEolinkerProject() {
      let projects = []
      await listEolinkerPorjects().then(response => {
        projects = response.data.map((value, i) => ({
          value: value.project_id,
          label: value.project_name,
        }))
      }).catch((e) => {
        this.$message.error("加载测试接口（Eolinker）数据出错！ " + e.message);
      });
      return projects
    },
    async listEolinkerTimedTask(projectID) {
      let projs = this.eolinkerOptions.filter(item => item.value === projectID);
      if (projs && projs[0].children) {
        return
      }

      let taskParams = {}
      let task = []
      taskParams["project_id"] = projectID
      await listEolinkerTimedTask(taskParams).then(response => {
        task = response.data.map((value, i) => ({
          value: value.task_id.toString(),
          label: value.task_name,
          leaf: true // 最后一级
        }))
      }).catch((e) => {
        this.$message.error("加载测试接口（Eolinker）数据出错！ " + e.message);
      });
      return task
    }
  }
}
</script>

<style>
.pipeline-edit-container .el-card.box-card {
  margin-top: 10px;
  margin-bottom: 10px;
}

.pipeline-edit-container .el-card.box-card .el-card__header {
  background-color: #eee;
}

.health-check-card .el-card__header {
  padding: 5px 20px;
}

.pipeline-edit-container .input-list-item {
  margin-top: 2px;
  margin-bottom: 2px;
}

.pipeline-edit-container .health-check-slot {
  width: 60px;
  text-align: right;
  color: #888;
}

.pipeline-edit-container .el-loading-spinner {
  top: 160px
}


.pipeline-edit-container .box-card-title {
  font-size: 1.2em;
}

.el-cascader-menu__wrap {
  min-height: 400px;
}

.tooltip-wrapper {
  max-width: 600px;
}

.question-icon-right {
  float: right;
  margin-right: -20px;
  margin-top: 15px
}

.question-icon-size {
  font-size: 12px;
}

.env-divider.el-divider--horizontal {
  margin: 10px 0;
}
</style>

