<template>
  <div>
      <div>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <export-button :table-ref="this.$refs.table001"></export-button>
      </div>
    <el-card class="box-card">
      <div>
        <el-table
          ref="table001"
          max-height="700"
          v-loading="tableLoading"
          :data="tableData"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
        >
          <el-table-column type="index">
          </el-table-column>
          <el-table-column label="应用名" sortable prop="app" width="240px;">
          </el-table-column>
          <el-table-column v-for="(it,idx) in this.tableColumns" :label="it" sortable :prop="it">
            <template slot-scope="scope">
              <div v-if="scope.row[it] > 0" style="color:dodgerblue;font-weight: bold;">{{ scope.row[it] }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label=""
            fixed="right" width="120px">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="pipelinePage(scope.row)">发布流程页
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>

import {allPipeline} from "@/api/pipeline";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "pipelineReplicas",
  data() {
    return {
      tableColumns: new Set(),
      tableData: [],
      tableLoading: false,
      envType: ""
    }
  },
  components: {ExportButton},
  mounted() {
    // this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      allPipeline().then(response => {
        this.tableData = this.tableDataHandler(response.data);
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pipelinePage(row) {
      let routeUrl = this.$router.resolve({
        name: "/share",
        query: {"app": row.app}
      });
      window.open(routeUrl.href, '_blank');
    },
    search() {
      this.loadTableData();
    },
    tableDataHandler(data) {
      this.tableColumns = new Set();
      let data2 = []
      for (let it of data) {
        data2.push(it)
        this.tableColumns.add(it.namespace)
      }
      let ret = []
      for (let it of data2) {
        let items = ret.filter((i) => i['app'] === it.app)
        let item = {'app': it.app}
        if (items && items.length > 0) {
          item = items[0]
        } else {
          ret.push(item)
        }
        item[it.namespace] = it.replicas
      }

      for (let it of ret) {
        for (let col of this.tableColumns) {
          if (!it[col]) {
            it[col] = 0
          }
        }
      }
      return ret;
    }
  }
}
</script>

