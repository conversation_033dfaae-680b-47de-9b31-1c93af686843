<template>
  <div style="padding: 5px 10px;">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="发布流程分析" name="analysis-pipeline" :lazy="true">
        <pipeline-analysis ></pipeline-analysis>
      </el-tab-pane>
      <el-tab-pane label="发布流程实例数（所有云）" name="analysis-pipeline-replicas" :lazy="true">
        <pipeline-replicas></pipeline-replicas>
      </el-tab-pane>
      <el-tab-pane label="部署模块分析" name="analysis-artifact" :lazy="true">
        <artifact-analysis></artifact-analysis>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import PipelineAnalysis from "@/views/analysis/pipeline";
import ArtifactAnalysis from "@/views/analysis/artifact-analysis";
import PipelineReplicas from "@/views/analysis/pipeline-replicas";

export default {
  components: {PipelineReplicas, ArtifactAnalysis, PipelineAnalysis},
  mounted() {
  },
  computed: {},
  data() {
    return {
      activeTab: 'k8s-clusters',
    }
  },
  methods: {}
}
</script>
