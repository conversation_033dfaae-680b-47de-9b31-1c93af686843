<template>
  <div class="app-container">
    <div v-loading="loading">
      <div>
          <span v-if="reloadTime">
            <small style="color: #999;">数据加载时间：{{ reloadTime }}</small>
          </span>
        <span style="margin-left: 20px;">
              显示行数:
              <el-select v-model.number="tailLines" @change="loadSysLog" style="width: 120px;">
                <el-option label="1000" value="1000"></el-option>
                <el-option label="2000" value="2000"></el-option>
                <el-option label="5000" value="5000"></el-option>
                <el-option label="10000" value="10000"></el-option>
              </el-select>
          </span>
        <span style="margin-left: 20px;">
            <el-button type="text" class="el-icon-refresh" style="margin-left: 10px;" @click="loadSysLog()">刷新
          </el-button>
          </span>
      </div>
      <el-card class="box-card" style="margin-top: 10px;">
        <pre class="log-content">{{ content }}</pre>
      </el-card>
    </div>
    <div>
      <el-backtop></el-backtop>
    </div>
  </div>
</template>

<script>

import moment from "moment";
import {getSysLog} from "@/api/sys";

export default {
  mounted() {
    this.loadSysLog()
  },
  computed: {},
  data() {
    return {
      loading: false,
      tailLines: 2000,
      reloadTime: "",
      content: ""
    }
  },
  methods: {
    loadSysLog() {
      this.loading = true;
      getSysLog(this.tailLines).then(response => {
        this.content = response.data;
        this.reloadTime = moment().format("MM-DD HH:mm:ss");
      }).catch((e) => {
        this.$message.error(e.message);
        this.stopReloadTimer()
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>

<style>
.log-content {
  word-break: break-word;
  white-space: pre-wrap;
  overflow-y: auto;
  font-size: 14px;
  color: #222;
}
</style>
