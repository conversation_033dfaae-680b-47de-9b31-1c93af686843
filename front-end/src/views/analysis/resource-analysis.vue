<template>
  <div class="app-container">
<!--    <el-tabs v-model="activeTab">-->
<!--      <el-tab-pane label="集群资源分析" name="cluster-capacity" :lazy="true">-->
<!--        <cluster-resource></cluster-resource>-->
<!--      </el-tab-pane>-->
<!--      <el-tab-pane label="服务资源分析" name="app-resources" :lazy="true">-->
<!--        <app-resource></app-resource>-->
<!--      </el-tab-pane>-->
<!--    </el-tabs>-->
  </div>
</template>
<script>

export default {
  components: {},
  mounted() {
  },
  computed: {},
  data() {
    return {
      activeTab: "cluster-capacity"
    }
  },
  methods: {}
}
</script>
