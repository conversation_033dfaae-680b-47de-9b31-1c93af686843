<template>
  <div class="app-container">
    <div>
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
        <env-selector ref="nsSelector" :show-all-namespaces="true"></env-selector>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
          <export-button :table-ref="this.$refs.table001"></export-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="Git地址"
                       sortable
                       prop="gitUrl">
      </el-table-column>
      <el-table-column label="模块"
                       sortable
                       prop="module">
      </el-table-column>
      <el-table-column label="描述" prop="remark">
      </el-table-column>
      <el-table-column label="创建人" prop="author">
      </el-table-column>
      <el-table-column label="被引用数" prop="extraAttr.refCount" sortable  width="120">
      </el-table-column>
      <el-table-column label="引用发布流程"  width="560">
          <template slot-scope="scope">
            <div v-for="it in scope.row.extraAttr.ref">
              {{it}}
            </div>
          </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

import {analysisArtifact} from "@/api/artifact";
import EnvSelector from "@/views/components/env-selector.vue";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "artifactAnalysis",
  data() {
    return {
      tableData: [],
      tableLoading: false,
      searchForm: {
        cluster: "",
        namespace: ""
      }
    }
  },
  components: {
    ExportButton,
    EnvSelector,
  },
  computed: {},
  mounted() {
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      this.searchForm.cluster = this.$refs.nsSelector.cluster;
      this.searchForm.namespace = this.$refs.nsSelector.namespace;
      analysisArtifact(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pipelinePage(row) {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": row.app}});
    },
    podPage(row) {
      let p = {
        "cluster": row.cluster,
        "namespace": row.namespace,
        "app": row.app,
      };
      this.$router.push({name: 'pod-index', query: p});
    }
  }
}
</script>

