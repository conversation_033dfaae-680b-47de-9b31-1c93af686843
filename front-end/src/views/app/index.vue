<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane name="app" :lazy="true">
        <span slot="label"><svg-icon icon-class="app" style="margin-right: 5px;"/>应用</span>
        <app-list></app-list>
      </el-tab-pane>
      <el-tab-pane label="部署模块" name="artifact" :lazy="true">
        <span slot="label"><svg-icon icon-class="module2" style="margin-right: 5px;"/>部署模块</span>
        <artifact-list></artifact-list>
      </el-tab-pane>
      <el-tab-pane label="配置中心CMDB" name="permission" :lazy="true">
        <span slot="label"><svg-icon icon-class="permission" style="margin-right: 5px;"/>配置中心CMDB</span>
        <cms-cmdb></cms-cmdb>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>

import AppList from "@/views/app/app-list";
import ArtifactList from "@/views/app/artifact-list";
import CmsCmdb from "@/views/app/cms-cmdb.vue";
export default {
  components: {CmsCmdb, ArtifactList, AppList},
  mounted() {
  },
  computed: {},
  data() {
    return {
      activeTab: "app"
    }
  },
  methods: {}
}
</script>
