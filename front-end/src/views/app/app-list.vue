<template>
  <div v-loading="pageLoading">
    <div>
      <el-row>
        <el-col :span="4">
          <div>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="createPage">新建</el-button>

          </div>
        </el-col>
        <el-col :span="20">
          <div style="text-align: right;">
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
              <el-form-item label="服务等级" style="margin-bottom: 0;">
                <el-select v-model="searchForm.level" placeholder="请选择" style="width: 180px;">
                  <el-option label="所有" value=""></el-option>
                  <el-option label="L0-底层公共服务" value="L0"></el-option>
                  <el-option label="L1-核心业务服务" value="L1"></el-option>
                  <el-option label="L2-一般业务服务" value="L2"></el-option>
                  <el-option label="L3-非业务服务" value="L3"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="部门" style="margin-bottom: 0;">
                <el-input v-model="searchForm.department" clearable style="width: 220px;"></el-input>
              </el-form-item>
              <el-form-item label="关键字" style="margin-bottom: 0;">
                <el-input v-model="searchForm.keyword" clearable style="width: 300px;"></el-input>
              </el-form-item>
              <el-form-item style="margin-bottom: 0;">
                <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
    <div style="margin-bottom: 10px;margin-top: 10px;border: 1px solid #eee;">
      <table style="color: rgb(119, 119, 119);font-size: 12px;padding: 10px;">
        <tr>
          <th style="width: 90px;text-align: right;">服务等级:</th>
          <td>参考文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=531923135 </td>
        </tr>
        <tr>
          <th style="text-align: right;">第一负责人:</th>
          <td>应用的主负责人，只能有一个。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等</td>
        </tr>
        <tr>
          <th style="text-align: right;">负责人:</th>
          <td>应用的负责人，可以多个。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等</td>
        </tr>
        <tr>
          <th style="text-align: right;">发布权限:</th>
          <td>授权角色内的成员可以对应用进行发布</td>
        </tr>
        <tr>
          <th style="text-align: right;">发布时间窗口:</th>
          <td>应用可以发布的时间窗口。如果配置了，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。</td>
        </tr>
      </table>
    </div>
    <div>
      <div style="display: inline-block">
        <el-pagination
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[10, 20, 50, 100,200,500,1000]"
          layout="total,prev,pager,next,sizes"
          :total="tableData.count"
          @size-change="pageSizeChange"
          @current-change="PageChange">
        </el-pagination>
      </div>
      <export-button v-if="tableData.data && tableData.data.length > 0" :table-ref="this.$refs.table001"></export-button>
      <el-button type="text" icon="el-icon-refresh" @click="syncAppFromCRM" style="color: #888; font-size: 12px;margin-left: 50px;">手动同步数据（从配置中心到发布系统）</el-button>
    </div>

    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       prop="name">
      </el-table-column>
      <el-table-column label="服务等级"
                       prop="level" width="100" align="center">
      </el-table-column>
      <el-table-column label="描述" prop="remark">
      </el-table-column>
      <el-table-column label="所属部门" prop="department">
      </el-table-column>
      <el-table-column label="服务类型" prop="category">
        <template slot-scope="scope">
          {{ scope.row.category || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间"
                       width="100px"
                       prop="createdTime">
      </el-table-column>
      <el-table-column label="发版权限" align="center" width="120px">
        <template slot-scope="scope">
          <template v-if="scope.row.orgs && scope.row.orgs.length > 0">
            <i class="el-icon-circle-check" style="color: #67c23a;font-weight: bold;font-size: 20px;"></i><br/>
            <el-tooltip effect="dark" :content="'部门：' + scope.row.orgs.join(',')" placement="top">
              <el-button type="text" style="font-size: 12px;padding: 0;">查看</el-button>
            </el-tooltip>
          </template>
          <template v-else>
            --<br/>
          </template>
          <el-button
            type="text"
            style="font-size: 12px;padding: 0;"
            @click="permPage(scope.row)">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="发布时间窗口" align="center" show-overflow-tooltip width="120px">
        <template slot-scope="scope">
          <template v-if="scope.row.timeWindow && scope.row.timeWindow.length > 0">
            <i class="el-icon-circle-check" style="color: #67c23a;font-weight: bold;font-size: 20px;"></i><br/>
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <pre>{{ scope.row.timeWindowDesc }}</pre>
              </div>
              <el-button type="text" style="font-size: 12px;padding: 0;">查看</el-button>
            </el-tooltip>
          </template>
          <template v-else>
            --<br/>
          </template>
          <el-button
            type="text"
            style="font-size: 12px;padding: 0;"
            @click="editPage(scope.row.name)">修改
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="第一负责人" prop="mainOwner">
        <template slot-scope="scope">
          <div style="font-size: 12px;">
            {{ scope.row.mainOwner }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="负责人" prop="owners">
        <template slot-scope="scope">
          <div v-if="scope.row.owners && scope.row.owners.length > 0" style="font-size: 12px;">
            {{ scope.row.owners.join(',') }}<br/>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="流程数" prop="pipelineCount" width="90px" sortable>
      </el-table-column>
      <el-table-column label="操作"
                       width="180px">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            style="padding: 0;"
            @click="editPage(scope.row.name)">修改
          </el-button>
          <el-button
            type="text"
            icon="el-icon-s-custom"
            style="padding: 0;"
            @click="permPage(scope.row)">发布权限
          </el-button><br/>
          <el-popconfirm :title="'确定要删除【 ' + scope.row.name + ' 】吗？'" @confirm="deleteApp(scope.row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              style="padding: 0;"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
          <el-button
            type="text"
            icon="el-icon-position"
            style="padding: 0;margin-left: 5px;"
            @click="pipelinePage(scope.row)">发布流程页
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="dialogEditTitle" :visible.sync="dialogEditVisible" width="900px" :close-on-click-modal="false">
      <el-form :model="dialogEditForm" ref="dialogEditForm" label-width="120px" :rules="dialogEditFormRules">
        <el-form-item label="ID" v-show="false">
          <el-input v-model="dialogEditForm.id" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="应用名" prop="name">
          <el-input v-model.trim="dialogEditForm.name" autocomplete="off" :disabled="dialogEditForm.id > 0"></el-input>
        </el-form-item>
        <el-form-item label="服务等级" prop="level">
          <el-select v-model="dialogEditForm.level" placeholder="请选择" :disabled="true" style="width: 100%;">
            <el-option label="L0-底层公共服务" value="L0"></el-option>
            <el-option label="L1-核心业务服务" value="L1"></el-option>
            <el-option label="L2-一般业务服务" value="L2"></el-option>
            <el-option label="L3-非业务服务" value="L3"></el-option>
          </el-select>
          <div style="line-height: normal;color: #888;margin-top: -5px;font-size: 12px">
            说明：自动同步自CRM【研发服务模块】对象的 服务等级 字段。
            <el-button
              type="text"
              style="font-size: 12px"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="所属部门">
          <el-input v-model.trim="dialogEditForm.department" autocomplete="off" :disabled="true"></el-input>
          <div style="line-height: normal;color: #888;margin-top: -5px;font-size: 12px">
            说明：自动同步自CRM【研发服务模块】对象的 Department 字段。
            <el-button
              type="text"
              style="font-size: 12px"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="第一负责人">
          <el-select v-model="dialogEditForm.mainOwner"
                     filterable
                     :disabled="true"
                     :filter-method="(query)=>{userPinYinMatch(query)}"
                     style="width: 100%;">
            <el-option
              v-for="item in userOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <div style="line-height: normal;color: #888;margin-top: -5px;font-size: 12px">
            说明：主负责人，自动同步自CRM【研发服务模块】对象的 Owner 字段。
            <el-button
              type="text"
              style="font-size: 12px"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="dialogEditForm.owners" multiple
                     filterable
                     :disabled="true"
                     :filter-method="(query)=>{userPinYinMatch(query)}"
                     style="width: 100%;">
            <el-option
              v-for="item in userOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <div style="line-height: normal;color: #888;margin-top: -5px;font-size: 12px">
            说明：自动同步自CRM【研发服务模块】对象的 模块负责人(多选) 字段。
            <el-button
              type="text"
              style="font-size: 12px"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogEditForm.remark" type="textarea" :rows="3" :disabled="true"></el-input>
          <div style="line-height: normal;color: #888;margin-top: -5px;font-size: 12px">
            说明：自动同步自CRM【研发服务模块】对象的 用途说明 字段。
            <el-button
              type="text"
              style="font-size: 12px"
              @click="ownerPage(dialogEditForm.name)">去修改
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="发布时间窗口" prop="timeWindow">
          <el-row v-for="(item, index) in dialogEditForm.timeWindow" :key="index" style="margin: 5px;">
            <el-col :span="12">
              <el-select v-model="item.daysOfWeek" multiple placeholder="请选择" style="width: 90%">
                <el-option
                  v-for="i in daysOfWeekOptions"
                  :key="i.index"
                  :label="i.label"
                  :value="i.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-time-picker is-range
                              format="HH:mm"
                              value-format="HH:mm"
                              v-model="item.timeRange"
                              range-separator="至"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 80%">
              </el-time-picker>
              <el-button
                type="text"
                @click="delTimePeriod(index)">删除
              </el-button>
            </el-col>
          </el-row>
          <el-button
            icon="el-icon-plus"
            size="medium"
            @click="addTimePeriod()">添加
          </el-button>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="createOrUpdate()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {countPipelines, createApp, deleteAppByName, editApp, findApp, searchApp, syncFromCRM} from "@/api/app";
import {userNames} from "@/api/user";
import pinyin from "pinyin-match";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "appList",
  components: {ExportButton},
  data() {
    let timeWindowValidate = (rule, value, callback) => {
      for (let item of value) {
        if (!item.daysOfWeek || item.daysOfWeek.length < 1) {
          callback(new Error('内容不能为空'));
          return
        }
        if (!item.timeRange || item.timeRange.length < 1 || !item.timeRange[0] || !item.timeRange[1]) {
          callback(new Error('内容不能为空'));
          return
        }
      }
      callback();
    };
    return {
      pageLoading: false,
      daysOfWeekOptions: [{
        value: 1,
        label: "周一"
      }, {
        value: 2,
        label: "周二"
      }, {
        value: 3,
        label: "周三"
      }, {
        value: 4,
        label: "周四"
      }, {
        value: 5,
        label: "周五"
      }, {
        value: 6,
        label: "周六"
      }, {
        value: 7,
        label: "周日"
      }
      ],
      searchForm: {
        keyword: "",
        department: "",
        level: "",
        page: 1,
        limit: 10,
      },
      tableData: [],
      tableLoading: false,
      dialogEditTitle: '',
      dialogEditVisible: false,
      dialogEditForm: {
        id: 0,
        name: "",
        level: "",
        department: "",
        orgs: [],
        mainOwner:"",
        owners: [],
        remark: "",
        timeWindow: []
      },
      userAllOptions: [],
      userOptions: [],
      dialogEditFormRules: {
        name: [
          {required: true, message: '请输入应用名', trigger: 'blur'},
        ],
        timeWindow: [
          {validator: timeWindowValidate, trigger: 'blur'},
        ],
      },
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.searchForm.keyword = this.$route.query.app
    this.loadTableData();
    this.loadUserNames();
    if (this.$route.query.showEditDialog === "true" && this.$route.query.app) {
      this.editPage(this.$route.query.app)
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchApp(this.searchForm).then(response => {
        let d = response.data;
        for(let it of d.data) {
          it.pipelineCount = "--";
        }
        this.analysisPipeline();
        this.tableData = d;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadUserNames() {
      userNames().then(response => {
        this.userOptions = response.data;
        this.userAllOptions = response.data;
      }).catch((e) => {
        this.$message.error("加载用户数据出错：", e.message);
      })
    },
    resetEditForm() {
      this.dialogEditForm.id = 0;
      this.dialogEditForm.name = "";
      this.dialogEditForm.org = "";
      this.dialogEditForm.mainOwner = "";
      this.dialogEditForm.owners = [];
      this.dialogEditForm.remark = "";
      this.dialogEditForm.timeWindow = [];
    },
    PageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    pageSizeChange(v) {
      this.searchForm.limit = v;
      this.loadTableData();
    },
    createPage() {
      this.dialogEditTitle = "新建"
      this.dialogEditVisible = true;
      this.resetEditForm();
    },
    analysisPipeline() {
      countPipelines().then(response => {
        for(let it of this.tableData.data) {
          it.pipelineCount = response.data[it.name]
        }
      }).catch((e) => {
        this.$message.error("加载数据出错：", e.message);
      })
    },
    syncAppFromCRM() {
      this.$confirm('确定要执行同步操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true
        syncFromCRM().then(response => {
          this.$message.success("操作成功")
          this.loadTableData();
        }).catch((e) => {
          this.$message.error("操作失败：", e.message);
        }).finally(()=>{
          this.pageLoading = false
        })
      });
    },
    deleteApp(row) {
      deleteAppByName(row.name).then(response => {
        this.tableData.data.splice(this.tableData.data.indexOf(row), 1);
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    editPage(app) {
      findApp(app).then(response => {
        console.log(response.data)
        this.dialogEditForm = response.data
        this.dialogEditTitle = "修改"
        this.dialogEditVisible = true;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    createOrUpdate() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }

        let method = this.dialogEditForm.id > 0 ? editApp : createApp;
        method(this.dialogEditForm).then(response => {
          this.dialogEditVisible = false;
          this.$message.success("操作成功");
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      });
    },
    permPage(row) {
      this.$router.push({
        name: 'app-permission', query: {
          "app": row.name,
        }
      });
    },
    pipelinePage(row) {
      let p = {
        "app": row.name,
      };
      let r = this.$router.resolve({name: 'cicd-app-deploy', query: p});
      window.open(r.href, '_blank');
    },
    ownerPage(app) {
      let url = `https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c`
      window.open(url)
      // if(!app) {
      //   this.$message.warning("应用名不能为空");
      //   return
      // }
      // let url = `/api/page/redirect?type=owner&app=${app}`
      // window.open(url)
    },
    addTimePeriod() {
      if (this.dialogEditForm.timeWindow.length < 1) {
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [1, 2, 3, 4, 5, 6, 7],
          timeRange: ["23:00", "23:59"]
        })
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [1, 2, 3, 4, 5, 6, 7],
          timeRange: ["00:00", "06:00"]
        })
        // this.dialogEditForm.timeWindow.push({
        //   daysOfWeek: [6, 7],
        //   timeRange: ["00:00", "22:00"]
        // })
      } else {
        this.dialogEditForm.timeWindow.push({
          daysOfWeek: [],
          timeRange: ["22:00", "23:59"]
        })
      }
    },
    delTimePeriod(index) {
      this.dialogEditForm.timeWindow.splice(index, 1)
    },
    userPinYinMatch(key) {
      if (key) {
        this.userOptions = this.userAllOptions.reduce((X, item) => {
          if (pinyin.match(item, key) || item.includes(key)) {
            X.push(item);
          }
          return X;
        }, []);
      }
    }
  },

}
</script>

