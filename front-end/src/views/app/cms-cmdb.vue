<template>
  <div class="app-container">
    <el-alert
      style="font-size: 1.2em;background-color: unset;border: solid 1px #898282;margin-bottom: 10px;font-weight: bold;color:#333"
      title="应用负责人管理页面"
      type="info"
      :closable="false"
      description=""
      show-icon>
      <template>
        <div style="font-weight: bold;color:#333">
          发布系统、监控告警系统会使用这些负责人信息。
        </div>
      </template>
    </el-alert>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>应用负责人</span><span style="font-size: 14px;">(数据存储于配置中心的 cmdb-mark-v2.json 文件）</span>
        <el-button type="text" icon="el-icon-tickets" size="mini" @click="toEditPage">进入配置中心</el-button>
        <export-button icon="el-icon-refresh" :table-ref="this.$refs.table001"></export-button>
        <el-button type="text" icon="el-icon-refresh" @click="syncCMDBFromCRM" style="color: #888; font-size: 12px;margin-left: 50px;">手动同步数据（从CRM到配置中心）</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%" v-loading="loading" ref="table001">
        <el-table-column prop="service" label="应用名称"/>
        <el-table-column prop="level" label="应用等级"/>
        <el-table-column prop="mainOwner" label="第一负责人"/>
        <el-table-column prop="owner" label="负责人"/>
        <el-table-column prop="departments" label="部门"/>
        <el-table-column prop="category" label="类别"/>
        <el-table-column prop="crmId" label="CRM ID"/>
        <el-table-column prop="info" label="描述"/>
      </el-table>
    </el-card>
  </div>
</template>

<script>

import {searchCmdbOwner, syncCmdbOwner} from "@/api/operation";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: 'cms-cmdb',
  components: {ExportButton},
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  created() {
    this.loadTable()
  },
  methods: {
    loadTable() {
      this.loading = true
      searchCmdbOwner().then(response => {
        this.tableData = response.data.filter(item => !(item.service && item.service.includes("www.fxiaoke.com")))
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false
      })
    },
    toEditPage() {
      window.open("https://console.foneshare.cn/cms/#/configuration/profile/detail/452", '_blank');
    },
    syncCMDBFromCRM() {
      this.$confirm('确定要执行同步操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        syncCmdbOwner().then(response => {
          this.$message.success("操作成功")
          this.loadTable();
        }).catch((e) => {
          console.log(e)
          this.$message.error("操作失败：", e.message);
        }).finally(() => {
          this.loading = false
        })
      });
    },
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
