<template>
  <div class="app-container" element-loading-text="数据加载中" v-loading="loading">
    <div style="width: 500px;margin: 0 auto;">
      <el-input v-model="searchName" placeholder="输入关键字可过滤">
        <el-button slot="append" icon="el-icon-search" @click="loadData()">查找</el-button>
      </el-input>
    </div>

    <div class="app_info_panel" style="text-align: center;padding-top: 20px;">
      <b>应用:</b><span>{{ this.$route.query.app }}</span>
      <b>Git地址:</b><span>{{ this.$route.query.git_url }}</span>
    </div>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-divider content-position="left"><b>分支列表</b>
          <small style="padding-left: 10px;">(最多显示 2000 条数据)</small>
        </el-divider>
        <el-button type="primary" @click="batchDeleteGitBranch()">批量删除</el-button>
        <el-table
          :data="tableData.branches"
          style="width: 100%;max-height: 700px;overflow: auto"
          @selection-change="branchSelectionChange"
          key="name">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column type="index">
          </el-table-column>
          <el-table-column
            prop="name"
            sortable
            label="分支名称">
          </el-table-column>
          <el-table-column
            prop="commitCreatedAt"
            label="对应Commit时间"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="message"
            label="描述">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="12">
        <el-divider content-position="left"><b>Tag列表</b>
          <small style="padding-left: 10px;">(最多显示 2000 条数据)</small>
        </el-divider>
        <el-button type="primary" @click="batchDeleteGitTag()">批量删除</el-button>
        <el-table
          :data="tableData.tags"
          style="width: 100%;max-height: 700px;overflow: auto"
          @selection-change="tagSelectionChange"
          key="name">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column type="index">
          </el-table-column>
          <el-table-column
            prop="name"
            sortable
            label="Tag名称">
          </el-table-column>
          <el-table-column
            prop="commitCreatedAt"
            label="对应Commit时间"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="message"
            label="描述">
          </el-table-column>
        </el-table>
      </el-col>

    </el-row>
  </div>

</template>

<script>
import {batchDeleteGitTagAndBranch, findGitTag} from "@/api/app";

export default {
  name: "git-tag-batch-delete",
  data() {
    return {
      loading: false,
      tableData: {},
      multipleSelectionBranches: [],
      multipleSelectionTags: [],
      searchName: ""
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      findGitTag(this.$route.query.git_url, this.searchName).then(response => {
        // 去掉master和main分支
        response.data.branches = response.data.branches.filter((item) => {
          return item.name !== "main" && item.name !== "master"
        });
        this.tableData = response.data
        this.loading = false
      }).catch((e) => {
        this.$message.warning("查询失败！ " + e.message);
        this.loading = false
      })
    },
    tagSelectionChange(val) {
      this.multipleSelectionTags = val
    },
    branchSelectionChange(val) {
      this.multipleSelectionBranches = val
    },
    batchDeleteGitTag() {
      let tags = []
      for (let i = 0; i < this.multipleSelectionTags.length; i++) {
        tags.push(this.multipleSelectionTags[i].name)
      }
      if (tags.length < 1) {
        this.$message.info("请选择需要删除的数据")
        return
      }
      this.$confirm(`确认删除？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {}
        data.tags = tags
        data.gitUrl = this.$route.query.git_url
        this.loading = true
        batchDeleteGitTagAndBranch(data).then(response => {
          this.$message.success("删除成功")
          this.loadData()
        }).catch((e) => {
          this.$message.warning("删除失败！ " + e.message);
        })
      })
    },
    batchDeleteGitBranch() {
      let branches = []
      for (let i = 0; i < this.multipleSelectionBranches.length; i++) {
        branches.push(this.multipleSelectionBranches[i].name)
      }
      if (branches.length < 1) {
        this.$message.info("请选择需要删除的数据")
        return
      }
      this.$confirm(`确认删除？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {}
        data.branches = branches
        data.gitUrl = this.$route.query.git_url
        this.loading = true
        batchDeleteGitTagAndBranch(data).then(response => {
          this.$message.success("删除成功")
          this.loadData()
        }).catch((e) => {
          this.$message.warning("删除失败！ " + e.message);
        })
      })
    },
  }
}
</script>

<style scoped>
.app_info_panel {
  color: #909399;
  margin: 10px 0;
  font-size: 14px;
}

.app_info_panel > b {
  margin: 0 10px;
  padding: 5px 10px;
  background-color: #eee;
}

.app_info_panel > span {
}
</style>
