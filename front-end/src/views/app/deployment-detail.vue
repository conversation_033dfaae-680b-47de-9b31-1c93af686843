<template>
  <div class="deployment-detail">
    <vue-json-pretty
      v-loading="loading"
      :data="data"
      style=" "
    >
    </vue-json-pretty>
  </div>
</template>
<script>
import VueJsonPretty from "vue-json-pretty";
import {deploymentWholeDetail} from "@/api/k8s/app";

export default {
  components: {VueJsonPretty},
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    app: {
      type: String,
      required: true
    }
  },
  created() {
    this.loadData()
  },
  watch: {
    cluster(val) {
      this.loadData();
    },
    namespace(val) {
      this.loadData();
    },
    app(val) {
      this.loadData();
    }
  },
  computed: {},
  data() {
    return {
      loading: true,
      data: {}
    }
  },
  methods: {
    loadData() {
      this.loading = true
      deploymentWholeDetail(this.cluster, this.namespace, this.app).then(response => {
        this.data = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>

<style>

</style>
