<template>
  <div class="app-container" element-loading-text="数据加载中" v-loading="loading">
    <div style="text-align: right">
      <el-button type="text" style="padding:0" @click="gitTagBatchDeletePage">批量删除分支或Tag</el-button>
    </div>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-divider content-position="left"><b>创建Tag</b>
          <small style="padding-left: 10px;">应用名：{{ this.$route.query.app }}</small>
        </el-divider>
        <el-form label-width="80px">
          <el-form-item label="Git地址">
            <el-select v-model="form.gitUrl" placeholder="请选择" style="width:100%" @change="gitUrlChange">
              <el-option
                v-for="(item, index) in this.forms"
                :key="item.gitUrl"
                :label="item.gitUrl"
                :value="item.gitUrl">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属分支">
            <el-select v-model="form.branch" style="width: 100%" filterable>
              <el-option v-for="item in form.branches"
                         :key="item.name"
                         :label="item.name"
                         :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Tag名称">
            <el-input placeholder="请输入内容" v-model="form.nextTag">
              <template slot="append">{{ this.tagSuffix }}</template>
            </el-input>
            <div style="text-align: right;">
              <el-checkbox v-model="tagContainsBranchName">Tag后缀包含分支名</el-checkbox>
            </div>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="form.msg"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="buildImagePage()" icon="el-icon-back">返回</el-button>
            <el-button type="primary" @click="createTag()">立即创建</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <el-divider content-position="left"><b>Tag列表</b>
          <small style="padding-left: 10px;">(只展现最近的 200 个，默认按照对应的Commit时间倒序)</small>
        </el-divider>
        <el-table
          :data="form.tags"
          style="width: 100%;max-height: 700px;overflow: auto"
          key="name">
          <el-table-column type="index">
          </el-table-column>
          <el-table-column
            prop="name"
            sortable
            label="Tag名称">
          </el-table-column>
          <el-table-column
            prop="commitCreatedAt"
            label="对应Commit时间"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="message"
            label="描述">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {createGitTag, getGitTag} from "@/api/app";
import moment from 'moment'
import {cloneObject} from "@/utils/my-util";

export default {
  data() {
    return {
      loading: false,
      forms: [],
      form: {},
      tagContainsBranchName: true,
      multipleSelectionTags: []
    }
  },
  computed: {
    tagSuffix: function () {
      let ret = ""
      if (this.tagContainsBranchName && this.form.branch) {
        ret = '-' + this.form.branch
      }
      ret = ret + "-" + this.dateFormat()
      return ret
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      getGitTag(this.$route.query.app).then(response => {
        this.forms = response.data;
        let gitUrl =  this.forms[0].gitUrl;
        if(this.$route.query.gitUrl) {
          gitUrl = this.$route.query.gitUrl
        }
        this.form = this.getForm(this.form.gitUrl ? this.form.gitUrl : gitUrl)
      }).catch((e) => {
        this.$message.warning(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    dateFormat: function () {
      return moment().format("YYYYMMDD")
    },
    pipelinePage: function () {
      this.$router.push({name: 'cicd-app-deploy', query: {"app": this.$route.query.app}});
    },
    buildImagePage(tag) {
      this.$router.push({name: 'cicd-image-build', query: {"app": this.$route.query.app}});
    },
    createTag() {
      const data = {}
      data.gitUrl = this.form.gitUrl
      data.branch = this.form.branch
      data.tag = this.form.nextTag + this.tagSuffix
      data.msg = this.form.msg
      createGitTag(data).then(response => {
        this.$message.success("创建成功")
        this.buildImagePage()
      }).catch((e) => {
        this.$message.warning("创建tag失败！ " + e.message);
      })
    },
    getForm(gitUrl) {
      if (!gitUrl || !this.forms || !this.form.length < 1) {
        return {}
      }
      let items = this.forms.filter(item => item.gitUrl === gitUrl)
      //必须要做clone， 不能互相引用而导致对forms数据做修改
      return items.length > 0 ? cloneObject(items[0]) : {}
    },
    gitUrlChange(gitUrl) {
      this.form = this.getForm(gitUrl)
    },
    gitTagBatchDeletePage() {
      let routerUrl = this.$router.resolve({
        name: 'git-tag-batch-delete',
        query: {"app": this.$route.query.app, "git_url": this.form.gitUrl}
      })
      window.open(routerUrl.href, "_blank")
    },
  }
}
</script>

<style scoped>

</style>
