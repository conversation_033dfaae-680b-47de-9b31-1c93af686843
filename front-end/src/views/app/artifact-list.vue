<template>
  <div>
    <div>
      <el-row>
        <el-col :span="4">
          <div>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="createPage">新建</el-button>
          </div>
        </el-col>
        <el-col :span="20">
          <div style="text-align: right;">
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
              <el-form-item label="关键字" style="margin-bottom: 0;">
                <el-input v-model="searchForm.keyword" clearable style="width: 360px;"></el-input>
              </el-form-item>
              <el-form-item style="margin-bottom: 0;">
                <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.limit"
      layout="total,prev,pager,next"
      :total="tableData.count"
      @current-change="PageChange">
    </el-pagination>
    <el-table
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="Git地址"
                       sortable
                       prop="gitUrl">
      </el-table-column>
      <el-table-column label="模块"
                       sortable
                       prop="module">
      </el-table-column>
      <el-table-column label="描述" prop="remark">
      </el-table-column>
      <el-table-column label="创建人" width="120px" prop="author">
      </el-table-column>
      <el-table-column label="创建时间"
                       sortable
                       width="140px"
                       prop="createdTime">
      </el-table-column>
      <el-table-column
        label="操作"
        width="160px"
        fixed="right">
        <template slot-scope="scope">
          <el-popconfirm :title="'删除部署模块不应影响到应用和发布流程，确定继续删除吗？'" @confirm="deleteRow(scope.$index, scope.row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="新建" :visible.sync="dialogEditVisible" width="50%" :close-on-click-modal="false">
      <el-form :model="dialogEditForm" ref="dialogEditForm" label-width="120px" :rules="dialogEditFormRules">
        <el-form-item label="Git地址" prop="gitUrl">
          <el-input v-model.trim="dialogEditForm.gitUrl"></el-input>
        </el-form-item>
        <el-form-item label="模块">
          <el-input v-model.trim="dialogEditForm.module"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogEditForm.remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div style="padding-left: 120px;line-height: 1.5em;">
        【Git地址】: 项目的Git地址，必须为 https 开头<br/>
        【模块】: 项目的子模块，如果没有则保留为空
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="create()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {createArtifact, deleteArtifact, searchArtifact} from "@/api/artifact";

export default {
  name:"artifactList",
  data() {
    let validateGitUrl = (rule, value, callback) => {
      if (!value.startsWith("https://") || !value.endsWith(".git")) {
        callback(new Error('地址必须为https://xxx.git格式'));
        return
      }
      callback();
    };
    return {
      searchForm: {
        keyword: "",
        page: 1,
        limit: 10,
      },
      tableData: [],
      tableLoading: false,
      dialogEditVisible: false,
      dialogEditForm: {
        id: 0,
        gitUrl: "",
        module: ""
      },
      dialogEditFormRules: {
        gitUrl: [
          {required: true, message: '请输入项目Git地址', trigger: 'blur'},
          {validator: validateGitUrl, message: 'Git地址必须为https://xxx.git格式', trigger: 'blur'}
        ],
      }
    }
  },
  computed: {},
  mounted() {
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchArtifact(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    resetEditForm() {
      this.dialogEditForm.id = 0;
      this.dialogEditForm.gitUrl = "";
      this.dialogEditForm.module = "";
    },
    PageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    createPage() {
      this.dialogEditVisible = true;
      this.resetEditForm();
    },
    deleteRow(index, row) {
      deleteArtifact(row.id).then(response => {
        this.tableData.data.splice(index, 1);
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    create() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        createArtifact(this.dialogEditForm).then(response => {
          this.dialogEditVisible = false;
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      });
    },
  }
}
</script>

<style scoped>
.svg-icon {
  width: 2em;
  height: 2em;
}
</style>
