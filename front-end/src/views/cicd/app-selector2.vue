<template>
  <div class="app-container">
    <div style="margin-bottom: 10px;height: 30px;overflow: hidden;" v-if="showHistory">
      <label style="display: inline-block;width: 80px;color: #999;font-size: 14px;padding-right:12px;text-align: right;">访问历史</label>
      <el-button
        v-for="item in recentApps"
        size="mini"
        type="primary"
        :plain="item !== currApp"
        style="font-weight: bold;margin-bottom: 5px;font-size:14px;padding: 7px"
        @click="changeCurrAppByBtn(item)">{{ item }}
      </el-button>
    </div>
    <div>
      <el-form>
        <el-form-item label="选择应用" label-width="80px" style="margin-bottom: 0;">
          <el-select v-model="currApp" filterable placeholder="请选择应用" style="width: 100%;margin-bottom: 10px;" @change="changeCurrAppBySelector">
            <el-option
              v-for="item in apps"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <pipeline-app v-if="currApp && showDetail" :app="this.currApp" :columns="4"></pipeline-app>
  </div>
</template>

<script>

import {getUserInfo, updateHistoryApp} from "@/api/user";
import {getAllAppName} from "@/api/app";
import PipelineApp from "@/views/pipeline/pipeline-app.vue";

export default {
  name: "app-selector2",
  components: {PipelineApp},
  props: {
    showDetail: {
      type: Boolean,
      default: false
    },
    showHistory: {
      type: Boolean,
      default: true
    },
    updateHistory: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      apps: [],
      recentApps: [],
      currApp: "",
      reloadHistory: false,
    }
  },
  watch: {
    currApp(val) {
      this.appChange()
    }
  },
  computed: {},
  mounted() {
    let app = this.$route.query.app
    if (app) {
      this.currApp = app
    }
    this.loadApps();
    this.loadRecentApps();
  },
  methods: {
    appChange() {
      this.$router.push({
        query: {...this.$route.query, "tab": this.currTab}
      });
      if(this.updateHistory) {
        updateHistoryApp(this.currApp).then(response => {
          if(this.reloadHistory) {
            this.loadRecentApps()
          }
        }).catch((e) => {
        })
      }
      this.$emit("change", this.currApp)
    },
    changeCurrAppByBtn(app) {
      this.reloadHistory = false
      this.currApp = app
    },
    changeCurrAppBySelector() {
      this.reloadHistory = true
    },
    loadRecentApps() {
      getUserInfo().then(response => {
        this.recentApps = response.data.recentApps
        if (!this.currApp && this.recentApps.length > 0) {
          this.currApp = this.recentApps[0]
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    loadApps() {
      getAllAppName().then(response => {
        this.apps = response.data;
      }).catch((e) => {
        this.$message.error("加载应用数据出错！ " + e.message);
      });
    },
  }
}
</script>


