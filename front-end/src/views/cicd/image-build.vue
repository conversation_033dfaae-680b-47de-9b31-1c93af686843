<template>
  <div class="app-container image-build-container" v-loading="loading" style="margin: 0;padding: 0">
    <menu-tabs tab-name="image-build"></menu-tabs>
    <maintain-alert maintain-type="ci"></maintain-alert>
    <app-selector2 @change="appChange" :update-history="true" :showDetail="true"></app-selector2>
    <job-runner-alert :app="this.currApp" ref="jobRunnerAlert" job-type="CI"></job-runner-alert>
    <div style="min-height: 180px;margin: 10px;">
      <div style="padding-top: 10px;">
        <div style="float:left;">
          <el-button type="primary" size="small" icon="el-icon-position" @click="imageBuildDialog">批量构建镜像</el-button>
          <el-button type="text" icon="el-icon-price-tag" style="margin-left: 10px;" @click="gitTagPage(null)">创建Tag</el-button>
          <el-button type="text" icon="el-icon-position" style="margin-left: 20px;" @click="pipelinePage">去发布</el-button>
          <el-button type="text" icon="el-icon-position" style="margin-left: 30px;color: #E6A23C" @click="urgentDeploy">紧急构建</el-button>
        </div>
        <div style="clear: both"></div>
      </div>
      <el-table
        :data="tableData"
        element-loading-text="数据加载中..."
        :highlight-selection-row="true"
        ref="deployModuleTable"
        fit>
        <el-table-column
          type="selection"
          align="center"
          width="60">
        </el-table-column>
        <el-table-column label="" width="300" align="center">
          <template slot-scope="scope">
            <el-button icon="el-icon-search" type="text" @click="imagePage(scope.row)">查看镜像</el-button>
            <el-button type="text" icon="el-icon-price-tag" @click="gitTagPage(scope.row.gitUrl)" style="display: none;">创建Tag</el-button>
            <el-button type="text" icon="el-icon-s-order" @click="jobHistoryPage(scope.row.gitUrl,scope.row.module)">构建记录</el-button>
            <el-button type="primary" size="mini" icon="el-icon-position" @click="showDialog1([scope.row])" style="padding: 5px 7px;margin-left: 10px">构建镜像</el-button>
          </template>
        </el-table-column>
        <el-table-column label="Git地址" prop="gitUrl">
          <template slot-scope="scope">
            <a :href="scope.row.gitUrl" target="_blank" style="color: #3a8ee6">{{ scope.row.gitUrl }}</a>
          </template>
        </el-table-column>
        <el-table-column label="Maven子模块" prop="module">
        </el-table-column>
        <el-table-column label="默认编译环境">
          <template slot-scope="scope">
            <span>{{ scope.row.mavenImage.split("/").pop() }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :visible.sync="dialog1Visible" :close-on-click-modal="false" top="5vh" width="860px"
               class="deploy-dialog">
      <template slot="title">
        部署模块镜像构建（{{ this.currApp }}）
        <span style="padding-left: 10px;color: #777;font-size: 12px;"></span>
      </template>
      <el-form :model="buildForm" ref="buildFormRef" label-width="100px" :rules="buildFormRules">
        <div v-for="(item,index) in buildForm.modules">
          <el-form-item label="" class="module-git-url" style="padding: 0;margin: 0 0 -5px 0;">
            <span style="font-size: 12px;color: #b4532a	;font-weight: bold;">
              部署模块： {{ item.gitUrl }} --- {{ item.module }}
            </span>
          </el-form-item>
          <el-form-item label="版本号">
            <div style="display: inline-block;width: 400px;">
              <el-select style="width: 100%" v-model="buildForm.modules[index].tag" filterable>
                <el-option-group label="Git分支" v-if="getVersionOptions(item.gitUrl).branchOptions.length > 0">
                  <el-option
                    v-for="opt in getVersionOptions(item.gitUrl).branchOptions"
                    :key="opt.name"
                    :label="opt.name"
                    :value="opt.name">
                    <span><b>{{ opt.name }}</b></span>
                  </el-option>
                </el-option-group>
                <el-option-group label="GitTag (message)">
                  <el-option
                    v-for="opt in getVersionOptions(item.gitUrl).tagOptions"
                    :key="opt.name"
                    :label="opt.name"
                    :value="opt.name">
                    <span><b>{{ opt.name }}</b><span style="font-size: 12px;color: #888;padding-left: 5px;">{{ opt.message ? ' (' + opt.message.substring(0, 30) + ')' : '' }}</span></span>
                  </el-option>
                </el-option-group>
              </el-select>
            </div>
            <div style="display: inline-block;width: 300px;text-align: right">
              <b style="color: #606266">编译环境</b>
              <el-select style="width: 220px" v-model="buildForm.modules[index].mavenImage" filterable>
                <el-option
                  v-for="maven in mavenOptions"
                  :key="maven"
                  :label="maven.split('/').pop()"
                  :value="maven">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="父POM" prop="parentPom">
          <div style="display: inline-block;width: 700px;">
            <el-select v-model="buildForm.parentPom" style="width: 100%;">
              <parent-pom-options></parent-pom-options>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="备注信息">
          <div style="display: inline-block;width: 700px;">
            <el-input v-model="buildForm.remark" type="textarea" :rows="2" :maxlength="32" :max="32"></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox label="如果镜像存在，则覆盖" v-model="buildForm.forceCodeCompile"
                       style="margin: 0 0 0 0"></el-checkbox>
          <el-tooltip class="item" effect="light" placement="top">
            <template slot="content">
              默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
          <el-checkbox label="执行单元测试" v-model="buildForm.unitTest" style="margin: 0 0 0 30px"></el-checkbox>
          <el-tooltip class="item" effect="light" placement="top">
            <template slot="content">
              代码在进行Maven编译时，是否执行单元测试
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
          <el-checkbox label="依赖包版本校验" v-model="buildForm.dependencyCheck" style="margin: 0 0 0 30px"
                       disabled></el-checkbox>
          <el-tooltip class="item" effect="light" placement="top">
            <template slot="content">
              对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="提示">
          <el-alert
            :closable="false"
            type="info">
            <template slot="title">
              <div><b>镜像版本号生成机制:</b></div>
              <div>
                1. 如果版本号为tag，则直接使用该名称<br/>
                2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130<br/>
                3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910<br/>
              </div>
            </template>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">

        <el-button @click="dialog1Visible = false">取 消</el-button>
        <el-button type="primary" v-loading="buildBtnLoading" @click="buildSubmit">开始构建</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {getGitModules} from "@/api/app";
import {getGitTags} from "@/api/gitlab";
import {cloneObject} from "@/utils/my-util";
import {buildImage} from "@/api/job";
import AppSelector from "@/views/components/app-selector.vue";
import AppSelector2 from "@/views/cicd/app-selector2.vue";
import MenuTabs from "@/views/cicd/menu-tabs.vue";
import JobRunnerAlert from "@/views/cicd/job-running-alert.vue";
import MaintainAlert from "@/views/cicd/manitain-alert.vue";
import ParentPomOptions from "@/views/cicd/parent-pom-options.vue";


export default {
  name: "image-build",
  watch: {
    currApp(val) {
      this.loadTableData();
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currApp: "",
      dialog1Visible: false,
      buildOptions: {
        versionOptions: {},
      },
      buildBtnLoading: false,
      buildForm: {
        app: "",
        modules: [],
        unitTest: false,
        forceCodeCompile: false,
        dependencyCheck: true,
        parentPom: "",
        remark: "",
      },
      buildFormRules: {
        parentPom: [
          {required: true, message: '请选择父POM'}
        ]
      }
    }
  },
  components: {ParentPomOptions, MaintainAlert, JobRunnerAlert, MenuTabs, AppSelector2, AppSelector},
  computed: {
    mavenOptions() {
      return this.$settings.mavenImages || []
    }
  },
  mounted() {
  },
  methods: {
    loadTableData() {
      this.loading = true;
      if (!this.currApp) {
        this.tableData = []
        return
      }
      getGitModules(this.currApp).then(response => {
        this.tableData = response.data;
        if (this.tableData.length === 1 && this.$route.query.openDialog === "true") {
          this.$refs.deployModuleTable.toggleAllSelection();
          let vthis = this
          setTimeout(() => {
            vthis.imageBuildDialog()
            let newQuery = {...this.$route.query}
            delete newQuery['openDialog'];
            vthis.$router.push({
              query: newQuery
            });
          }, 300)
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false;
      })
    },
    appChange(app) {
      this.currApp = app;
      this.updateQueryParam()
      this.loadTableData()
    },
    updateQueryParam() {
      let urlParam = cloneObject(this.$route.query)
      urlParam["app"] = this.currApp;
      this.$router.push({query: urlParam});
    },
    getVersionOptions(gitUrl) {
      return this.buildOptions.versionOptions[gitUrl] || {
        tagOptions: [],
        branchOptions: []
      };
    },
    urgentDeploy() {
      this.$alert('如果需要紧急构建，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面', '紧急构建提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭'
      });
    },
    versionIsBranch(gitUrl, version) {
      let vers = this.getVersionOptions(gitUrl)
      for (let opt of vers.branchOptions) {
        if (opt.name === version) {
          return true
        }
      }
      return false
    },
    isProdEnv() {
      return window.location.host.indexOf("foneshare") > -1;
    },
    imageBuildDialog() {
      let rows = this.$refs.deployModuleTable.store.states.selection;
      if (!rows || rows.length < 1) {
        this.$message.warning("请选择部署模块");
        return
      }
      this.showDialog1(rows)
    },
    showDialog1(rows) {
      this.loading = true;
      let params = new Set();
      let modelsVal = []
      for (let it of rows) {
        params.add(it.gitUrl)
        modelsVal.push({
          "gitUrl": it.gitUrl,
          "module": it.module,
          "mavenImage": it.mavenImage,
          "tag": "",
        })
      }
      getGitTags({"gitUrls": Array.from(params)}).then(response => {
        this.buildOptions.versionOptions = response.data;
        // for (let it of modelsVal) {
        //   let vers = this.getVersionOptions(it.gitUrl)
        //   if (vers.branchOptions.length > 0) {
        //     it.tag = vers.branchOptions[0].name
        //   } else if (vers.tagOptions.length > 0) {
        //     it.tag = vers.tagOptions[0].name
        //   }
        // }
        this.buildForm.modules = modelsVal
        this.dialog1Visible = true
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.loading = false
      })
    },
    buildSubmit() {
      this.$refs['buildFormRef'].validate((valid) => {
        if (valid) {
          let params = {items: []}
          let form = cloneObject(this.buildForm)
          for (let it of form.modules) {
            params.items.push({
              app: this.currApp,
              gitUrl: it.gitUrl,
              gitModule: it.module,
              gitTag: it.tag,
              mavenImage: it.mavenImage,
              unitTest: form.unitTest,
              forceCodeCompile: form.forceCodeCompile,
              dependencyCheck: form.dependencyCheck,
              parentPom: form.parentPom,
              remark: form.remark,
            })
          }

          // let hasBranchVersion = false
          // for (let it of params.items) {
          //   if (this.versionIsBranch(it.gitUrl, it.gitTag)) {
          //     hasBranchVersion = true
          //     break
          //   }
          // }
          this.buildBtnLoading = true;
          buildImage(params).then(response => {
            if (response.data.length === 1) {
              this.jobPage(response.data[0].jobId)
            } else {
              this.jobHistoryPage("", "")
            }
            this.dialog1Visible = false;
          }).catch((e) => {
            this.$message.error(e.message);
          }).finally(() => {
            this.buildBtnLoading = false;
          })
        }
      });
    },
    gitTagPage(gitUrl) {
      let q = {"app": this.currApp}
      if (gitUrl) {
        q.gitUrl = gitUrl
      }
      let routeUrl = this.$router.resolve({name: 'git-tag', query: q});
      window.open(routeUrl.href, '_blank');
    },
    imagePage(row) {
      let routeUrl = this.$router.resolve({name: 'cicd-image-list', query: {"gitUrl": row.gitUrl, "gitModule": row.module}})
      window.open(routeUrl.href, '_blank');
    },
    jobPage(id) {
      let rou = this.$router.resolve({name: 'cicd-image-build-detail', query: {"jobId": id}});
      window.open(rou.href, '_blank');
    },
    jobHistoryPage(gitUrl, gitModule) {
      let rou = this.$router.resolve({name: 'cicd-image-build-history', query: {gitUrl: gitUrl, gitModule: gitModule, author: ""}});
      window.open(rou.href, '_blank');
    },
    pipelinePage() {
      let routerUrl = this.$router.resolve({name: 'cicd-app-deploy', query: {"app": this.currApp}})
      window.open(routerUrl.href, "_blank")
    },
  }
}
</script>

<style>
.image-build-container .el-table__header th {
  background-color: #f5f7fa;
}

.image-build-container .el-table .el-table__cell {
  padding: 5px 0;
}

.image-build-container .el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
  background-color: #e2f0ff;
}

.deploy-dialog .el-dialog__body {
  padding: 10px;
}
</style>
