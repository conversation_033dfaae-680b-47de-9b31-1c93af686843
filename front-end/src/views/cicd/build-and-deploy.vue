<template>
  <div class="app-container build-and-deploy-container" v-loading="gitModuleLoading || pipelineLoading || gitTagLoading || submitLoading">
    <div style="font-size: 15px;font-weight: bold;margin:-32px 110px 10px"> | {{ this.pipeline.app }}</div>
    <el-card class="box-card">
      <div slot="header">
        <span style="font-size: 16px;">构建配置</span>
      </div>
      <div>
        <el-form size="small" :model="buildForm" ref="buildFormRef" label-width="120px" :rules="buildFormRules">
          <div v-for="(item,index) in buildForm.modules" :key="index">
            <el-form-item label="" class="module-git-url" style="padding: 0;margin: 0 0 -5px 0;">
            <span style="font-size: 12px;color: #b4532a	;font-weight: bold;">
              部署模块： {{ item.gitUrl }} --- {{ item.module }}
            </span>
            </el-form-item>
            <el-form-item label="版本号">
              <el-row>
                <el-col :span="14">
                  <el-select style="width: 100%" v-model="buildForm.modules[index].tag" filterable>
                    <el-option-group label="Git分支" v-if="buildForm.modules[index].branchOptions.length > 0">
                      <el-option
                        v-for="opt in buildForm.modules[index].branchOptions"
                        :key="opt.name"
                        :label="opt.name"
                        :value="opt.name">
                        <span><b>{{ opt.name }}</b></span>
                      </el-option>
                    </el-option-group>
                    <el-option-group label="GitTag (message)">
                      <el-option
                        v-for="opt in buildForm.modules[index].tagOptions"
                        :key="opt.name"
                        :label="opt.name"
                        :value="opt.name">
                        <span><b>{{ opt.name }}</b><span style="font-size: 12px;color: #888;padding-left: 5px;">{{ opt.message ? ' (' + opt.message.substring(0, 30) + ')' : '' }}</span></span>
                      </el-option>
                    </el-option-group>
                  </el-select>
                </el-col>
                <el-col :span="9" :offset="1" style="text-align: right;">
                  <b style="color: #606266">编译环境</b>
                  <el-select style="width: 230px" v-model="buildForm.modules[index].mavenImage" filterable>
                    <el-option
                      v-for="maven in mavenOptions"
                      :key="maven"
                      :label="maven.split('/').pop()"
                      :value="maven">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </div>
          <el-form-item prop="parentPom" label="父POM">
            <div style="display: inline-block;width: 100%;">
              <el-select v-model="buildForm.parentPom" style="width: 100%;">
                <parent-pom-options></parent-pom-options>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="备注信息">
            <div style="display: inline-block;width: 100%;">
              <el-input v-model="buildForm.remark" type="textarea" :rows="1" :maxlength="32" :max="32"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="构建选项">
            <el-checkbox label="如果镜像存在，则覆盖" v-model="buildForm.forceCodeCompile"
                         style="margin: 0 0 0 0"></el-checkbox>
            <el-tooltip class="item" effect="light" placement="top">
              <template slot="content">
                默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。
              </template>
              <i class="el-icon-info"></i>
            </el-tooltip>
            <el-checkbox label="执行单元测试" v-model="buildForm.unitTest" style="margin: 0 0 0 30px"></el-checkbox>
            <el-tooltip class="item" effect="light" placement="top">
              <template slot="content">
                代码在进行Maven编译时，是否执行单元测试
              </template>
              <i class="el-icon-info"></i>
            </el-tooltip>
            <el-checkbox label="依赖包版本校验" v-model="buildForm.dependencyCheck" style="margin: 0 0 0 30px"
                         disabled></el-checkbox>
            <el-tooltip class="item" effect="light" placement="top">
              <template slot="content">
                对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本
              </template>
              <i class="el-icon-info"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="提示">
            <el-alert
              :closable="false"
              type="info">
              <template slot="title">
                <div><b>镜像版本号生成机制:</b></div>
                <div>
                  1. 如果版本号为tag，则直接使用该名称<br/>
                  2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130<br/>
                  3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910<br/>
                </div>
                <div style="margin-top: 10px;color: orangered" v-if="buildForm.modules && buildForm.modules.length > 1">
                  <b>提示:</b> 受系统设计限制，在【构建+部署】场景下，若存在多个部署模块，模块镜像将按串行方式构建。
                </div>
              </template>
            </el-alert>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px;">发布配置</span>
      </div>
      <div>
        <el-form size="small" :model="deployForm" ref="deployFormRef" label-width="100px" :rules="deployFormRules">
          <el-form-item label="每批升级数" prop="maxSurge">
            <div style="display: inline-block;width: 100%;">
              <el-select v-model="deployForm.maxSurge" style="width: 100%">
                <el-option-group label="按百分比">
                  <el-option label="100%" value="100%"></el-option>
                  <el-option label="50%" value="50%"></el-option>
                  <el-option label="25%" value="25%"></el-option>
                </el-option-group>
                <el-option-group label="按个数">
                  <el-option label="3" value="3"></el-option>
                  <el-option label="2" value="2"></el-option>
                  <el-option label="1" value="1"></el-option>
                </el-option-group>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="发布描述" prop="remark">
            <div style="display: inline-block;width: 100%;">
              <el-input v-model="deployForm.remark" type="textarea" :rows="1" :maxlength="256" :max="256"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="发布环境" style="margin-bottom: 0">
            <div style="display: inline-block;">
              <el-tag style="font-weight: bold;margin-right: 8px;font-size: 14px;" effect="plain">
                {{ this.pipeline.namespace }} ({{ this.pipeline.cluster }})
              </el-tag>
            </div>
            <div style="display: inline-block;margin-left: 60px;">
              <el-checkbox label="执行Eolinker接口测试" v-model="deployForm.eolinkerTest"
                           style="margin: 0 0 0 0"></el-checkbox>
              <el-tooltip class="item" effect="light" placement="top">
                <template slot="content">
                  是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用<br/>
                  大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。
                </template>
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
            <div style="line-height: 1.3em;color:#f3794d" v-if="this.pipeline.namespace === 'forceecrm-public-prod'">
              提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <div style="margin-top: 20px;text-align: right">
      <el-button size="small" type="primary" @click="buildSubmit">构建 & 发布</el-button>
    </div>
  </div>
</template>

<script>

import {getGitModules} from "@/api/app";
import {getGitTags} from "@/api/gitlab";
import {cloneObject} from "@/utils/my-util";
import {findPipelinesById} from "@/api/pipeline";
import {buildImageAndDeployApp, findJobById} from "@/api/job";
import ParentPomOptions from "@/views/cicd/parent-pom-options.vue";


export default {
  name: "build-and-deploy",
  props: {
    pipelineId: {
      type: Number,
      required: true
    }
  },
  watch: {
    pipelineId(val) {
      this.loadGitModule();
      this.loadPipeline();
    }
  },
  data() {
    return {
      pipelineLoading: false,
      gitModuleLoading: false,
      gitTagLoading: false,
      submitLoading: false,
      buildOptions: {
        versionOptions: {},
      },
      pipeline: {},
      buildForm: {
        app: "",
        modules: [],
        unitTest: false,
        forceCodeCompile: false,
        dependencyCheck: true,
        parentPom: "",
        remark: "",
      },
      buildFormRules: {
        parentPom: [
          {required: true, message: '请选择父POM'}
        ]
      },
      deployForm: {
        pipelineIds: [],
        maxSurge: "50%",
        remark: "",
        eolinkerTest: false,
      },
      deployFormRules: {
        maxSurge: [
          {required: true, message: '请选择每批升级数'}
        ],
      },
    }
  },
  components: {ParentPomOptions},
  computed: {
    mavenOptions() {
      return this.$settings.mavenImages || []
    },
    parentPoms() {
      return this.$settings.parentPoms || []
    }
  },
  mounted() {
    this.loadGitModule();
    this.loadPipeline();
  },
  methods: {
    loadGitModule() {
      this.gitModuleLoading = true;
      getGitModules("",this.pipelineId).then(response => {
        for(let it of response.data) {
          it.tag = ""
          it.branchOptions = []
          it.tagOptions = []
        }
        this.buildForm.modules = response.data
        this.loadGitTags()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.gitModuleLoading = false;
      })
    },
    loadPipeline() {
      this.pipelineLoading = true;
      findPipelinesById(this.pipelineId).then(response => {
        this.pipeline = response.data
        }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.pipelineLoading = false;
      })
    },
    loadGitTags() {
      this.gitTagLoading = true;
      let params = new Set();
      for (let it of this.buildForm.modules) {
        params.add(it.gitUrl)
      }
      getGitTags({"gitUrls": Array.from(params)}).then(response => {
        for (const [key, value] of Object.entries(response.data)) {
          for (let m of this.buildForm.modules) {
            if (m.gitUrl === key) {
              m.branchOptions = value.branchOptions
              m.tagOptions = value.tagOptions
            }
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.gitTagLoading = false
      })
    },
    buildSubmit() {
      this.$refs['buildFormRef'].validate((buildFormValid) => {
        if (buildFormValid) {
          this.$refs['deployFormRef'].validate((deployFormValid) => {
            if (deployFormValid) {
              for (let it of this.buildForm.modules) {
                if (it.tag === "") {
                  this.$message.error("请选择每个模块的版本号");
                  return;
                }
              }
              this.submitLoading = true;
              let params = {
                buildImageParam: [],
                deployParam: [{
                  pipelineId: this.pipeline.id,
                  maxSurge: this.deployForm.maxSurge,
                  remark: this.deployForm.remark,
                  eolinkerTest: this.deployForm.eolinkerTest,
                }],
              }
              let form = cloneObject(this.buildForm)
              for (let it of form.modules) {
                params.buildImageParam.push({
                  app: this.pipeline.app,
                  gitUrl: it.gitUrl,
                  gitModule: it.module,
                  gitTag: it.tag,
                  mavenImage: it.mavenImage,
                  unitTest: form.unitTest,
                  forceCodeCompile: form.forceCodeCompile,
                  dependencyCheck: form.dependencyCheck,
                  parentPom: form.parentPom,
                  remark: form.remark,
                })
              }

              buildImageAndDeployApp(params).then(response => {
                this.$message.success("操作成功")
                this.$emit("successHandler")
                if (response.data.length === 1) {
                  this.jobDetailPage(response.data[0].jobId)
                } else {
                  this.jobHistoryPage("", "")
                }
              }).catch((e) => {
                this.$message.error(e.message);
              }).finally(() => {
                this.submitLoading = false;
              })
            }
          });
        }
      });
    },
    jobDetailPage(jobId) {
      findJobById(jobId).then(response => {
        const job = response.data;
        const name = job.type === 'CD' ? 'cicd-app-deploy-detail' : 'cicd-image-build-detail';
        let rou = this.$router.resolve({name: name, query: {"jobId": jobId}});
        window.open(rou.href, '_blank');
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    jobHistoryPage(gitUrl, gitModule) {
      let rou = this.$router.resolve({name: 'cicd-image-build-history', query: {gitUrl: gitUrl, gitModule: gitModule, author: ""}});
      window.open(rou.href, '_blank');
    },
  }
}
</script>

<style>
.build-and-deploy-container .el-card__header  {
  background-color: rgb(227, 227, 227);
  padding: 8px;
  text-align: center;
  color: #409eff;
  font-weight: bold;
}

.build-and-deploy-container .el-form-item {
  margin-bottom: 5px;
}

.build-and-deploy-container .el-card__body {
  padding: 10px;
}

</style>
