<template>
  <div>
    <el-option :key="pom.ID" :label="pom.showName" :value="pom.name" v-for="pom in this.parentPoms.normals">
      <span>{{ pom.showName }} </span>
      <span style="padding-left: 20px; color: #8492a6; font-size: 13px">
                <label style="color: orangered; font-size: 12px;">{{ pom.remark }}</label>
              </span>
    </el-option>
    <el-divider style="margin: 0;">
      <span style="font-size: 12px;color: #777;">相关文档</span>
    </el-divider>
    <div style="margin-top: -10px;font-size: 12px;color: #777;margin-left: 20px;line-height: 1.5em;">
      <div>• 如果你有 JAR 包需要部署到 Maven 仓库，请点击 <a href="https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_zKc15__c" target="_blank" style="color: #3a8ee6">链接</a></div>
      <div>• 如果你需要在父 POM 中升级 JAR 包版本，请点击 <a href="https://www.fxiaoke.com/XV/UI/Home#crm/list/=/parent_pom_update__c" target="_blank" style="color: #3a8ee6">链接</a></div>
      <div>• 如果你想查看不同父 POM 所管理的 JAR 包版本，请点击 <a href="https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md" target="_blank" style="color: #3a8ee6">链接</a>
      </div>
      <div>• 如果你想了解父 POM 的版本规范及其所管理的 JAR 包升级规范，请点击 <a href="https://365.kdocs.cn/l/cj4XYsEkBQ4r" target="_blank" style="color: #3a8ee6">链接</a></div>
    </div>
    <el-divider v-if="this.parentPoms.archives.length > 0">
      <span style="font-size: 12px;color: #777;">归档父pom (不推荐使用）</span>
    </el-divider>
    <div style="margin-top: -10px;">
      <el-option :key="pom.ID" :label="pom.showName" :value="pom.name" v-for="pom in this.parentPoms.archives">
        <span>{{ pom.showName }} </span>
        <span style="padding-left: 20px; color: #8492a6; font-size: 13px">
                <label style="color: orangered; font-size: 12px;">{{ pom.remark }}</label>
              </span>
      </el-option>
    </div>
  </div>
</template>

<script>

import {searchParentPom} from "@/api/parentpom";

export default {
  name: "parent-pom-options",
  components: {},
  data() {
    return {
      parentPoms: {
        normals: [],
        archives: []
      },
    }
  },
  computed: {},
  mounted() {
    this.loadParentPoms()
  },
  methods: {
    loadParentPoms() {
      searchParentPom({}).then(response => {
        this.parentPoms = {
          normals: [],
          archives: []
        }
        let items = response.data;
        for (let item of items) {
          if (!item.enable) {
            continue
          }
          if (item.isArchive) {
            this.parentPoms.archives.push(item);
          } else {
            this.parentPoms.normals.push(item);
          }
        }
        console.log(this.parentPoms)
      }).catch((e) => {
        this.$message.error("获取父pom失败，" + e.message);
      })
    },
  }
}
</script>


