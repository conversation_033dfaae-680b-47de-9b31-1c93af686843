<template>
  <div>
    <div style="margin: 0 auto;padding: 10px;" v-if="this.maintainIsOpen">
      <el-alert
        title=""
        :closable="false"
        type="warning">
        <template slot="title">
          <div style="line-height: 18px;color:orangered;font-weight: bold;">
            <div style="font-size:16px;">
              <span style="color:orangered;"><i class="el-icon-warning"></i></span>
              <span style="padding-left: 5px;">系统提示</span>
            </div>
            <div style="padding-top: 10px;font-size:14px;" v-html="this.maintainDesc">
            </div>
          </div>
        </template>
      </el-alert>
    </div>
    <div v-else style="display:none;"></div>
  </div>
</template>

<script>

export default {
  name: "maintain-alert",
  props: {
    maintainType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
    }
  },
  watch: {
  },
  computed: {
    maintainIsOpen() {
      if (this.maintainType.toUpperCase() === 'CD') {
        return this.$settings.maintain.cd.open;
      } else if (this.maintainType.toUpperCase() === 'CI') {
        return this.$settings.maintain.ci.open;
      } else {
        return false;
      }
    },
    maintainDesc() {
      if (this.maintainType.toUpperCase() === 'CD') {
        return this.$settings.maintain.cd.desc;
      } else if (this.maintainType.toUpperCase() === 'CI') {
        return this.$settings.maintain.ci.desc;
      } else {
        return '';
      }
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>


