<template>
  <div class="app-container image-list-container" style="margin: 0;padding: 0">
    <menu-tabs tab-name="image-list"></menu-tabs>
    <div style="padding: 10px">
      <div style="float: left">
        <el-form size="small" inline>
          <el-form-item label="部署模块" label-width="80px" style="margin-bottom: 0;">
            <el-select v-model="selectVal" filterable  clearable style="margin-bottom: 10px;width: 600px;">
              <el-option
                v-for="item in artifacts"
                :key="item.id"
                :label="item.gitUrl + (item.module ? ' --- ' + item.module : '')"
                :value="item.gitUrl + '---' + item.module">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="loadTableData">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="float: right">
        <el-pagination
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[10, 20, 50, 100]"
          layout="sizes,total,prev,pager,next"
          :total="tableData.count"
          @current-change="PageChange">
        </el-pagination>
      </div>
      <el-alert
        title="线下构建的镜像会自动同步到线上，延迟时间为十分钟左右。"
        :closable="false"
        v-if="false"
        show-icon
        type="info">
      </el-alert>
      <el-table
        v-loading="tableLoading"
        :data="tableData.data"
        element-loading-text="数据加载中..."
        fit
        highlight-current-row
      >
        <el-table-column type="index">
        </el-table-column>
        <el-table-column label="">
          <template slot-scope="slot" slot="header">
            镜像名称 <small style="padding-left: 10px">(格式：父POM/Git地址/Git模块:镜像版本)</small>
          </template>
          <template slot-scope="scope">
            {{ scope.row.repository }}:{{ scope.row.dockerTag}}
          </template>
        </el-table-column>
        <el-table-column label="git版本" prop="gitTag">
        </el-table-column>
        <el-table-column label="大小" prop="sizeDesc" sortable>
        </el-table-column>
        <el-table-column label="备注信息" prop="remark">
        </el-table-column>
        <el-table-column label="操作人" prop="author">
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" sortable>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

import {searchByArtifact} from "@/api/image";
import {getAllArtifact} from "@/api/artifact";
import MenuTabs from "@/views/cicd/menu-tabs.vue";

export default {
  name: "image-list",
  components: {MenuTabs},
  data() {
    return {
      tableData: {
        data: [],
        count: 0
      },
      artifacts: [],
      tableLoading: false,
      selectVal: "",
      searchForm: {
        gitUrl: "",
        gitModule: "",
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {},
  mounted() {
    this.loadAllArtifact();
    let gitUrl = this.$route.query.gitUrl
    let gitModule = this.$route.query.gitModule
    if(gitUrl) {
      this.selectVal = gitUrl + '---' + gitModule
      this.loadTableData();
    }
  },
  methods: {
    loadAllArtifact() {
      getAllArtifact().then(response => {
        this.artifacts = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    loadTableData() {
      if (!this.selectVal) {
        this.$message.error("请选择部署模块")
        return
      }
      this.tableLoading = true;
      let parts = this.selectVal.split("---")
      this.searchForm.gitUrl = parts[0]
      this.searchForm.gitModule = parts[1]
      searchByArtifact(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    PageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
  }
}
</script>

<style>
.image-list-container .el-table .el-table__cell {
  padding: 5px 0;
}
</style>


