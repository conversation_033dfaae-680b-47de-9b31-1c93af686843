<template>
  <div>
    <el-alert
      v-if="jobs && jobs.length > 0"
      type="info">
      <template slot="title">
        <span>{{ jobTitle }}：</span>
        <el-link style="margin: 0 10px;" type="primary" :underline="false" v-for="item in jobs"
                 @click="jobPage(item.id)">
          <div v-if="jobType === 'CD'">
            {{ item.params.namespace }} ({{ item.params.cluster }})
          </div>
          <div v-else>
            {{ item.id }}
          </div>
        </el-link>
      </template>
    </el-alert>
  </div>
</template>

<script>

import {searchJob} from "@/api/job";

export default {
  name: "job-runner-alert",
  props: {
    app: {
      type: String,
      default: ""
    },
    jobType: {
      type: String,
      required: true
    },
    jobTitle: {
      type: String,
      default: "运行中的任务",
    }
  },
  data() {
    return {
      jobs: [],
    }
  },
  computed: {},
  mounted() {
    this.loadJobs();
  },
  watch: {
    app(val) {
      this.jobs = []
      this.loadJobs();
    }
  },
  methods: {
    loadJobs() {
      if (!this.app) {
        return
      }
      let params = {
        "params": {},
        "app": this.app,
        "status": ["WAIT", "RUNNING"],
        "type": this.jobType,
        "page": 1,
        "limit": 100,
      }
      searchJob(params).then(response => {
        this.jobs = response.data.data
      }).catch((e) => {
        console.log("load job fail, " + e.message)
      })
    },
    jobPage(id) {
      let na = ""
      if (this.jobType === 'CI') {
        na = "cicd-image-build-detail"
      } else if (this.jobType === 'CD') {
        na = "cicd-app-deploy-detail"
      } else {
        this.$message.error("未知的任务类型：" + this.jobType)
      }
      let rou = this.$router.resolve({name: na, query: {"jobId": id}});
      window.open(rou.href, '_blank');
    },
  }
}
</script>


