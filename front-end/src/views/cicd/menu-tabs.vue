<template>
  <div class="cicd-menu-tabs">
    <el-tabs v-model="currTab" @tab-click="handleClick" type="border-card">
      <el-tab-pane label="aaa" name="app-deploy">
        <span slot="label"><svg-icon icon-class="guide" style="margin-right: 2px;"/>应用发布</span>
      </el-tab-pane>
      <el-tab-pane name="app-deploy-history">
        <span slot="label"><svg-icon icon-class="record" style="margin-right: 2px;"/>发布记录</span>
      </el-tab-pane>
      <el-tab-pane name="image-build">
        <span slot="label"><svg-icon icon-class="image" style="margin-right: 2px;"/>镜像构建</span>
      </el-tab-pane>
      <el-tab-pane name="image-build-history">
        <span slot="label"><svg-icon icon-class="record" style="margin-right: 2px;"/>构建记录</span>
      </el-tab-pane>
      <el-tab-pane name="image-list">
        <span slot="label"><svg-icon icon-class="image" style="margin-right: 2px;"/>镜像列表</span>
      </el-tab-pane>
      <el-tab-pane name="doc" :disabled="true">
        <span slot="label" style="color: #909399">
          <svg-icon icon-class="help" style="margin-right: 2px;"/>
        <a target="_blank"  href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474">查看使用手册</a>
        </span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  props: {
    tabName: {
      type: String,
      default: ''
    }
  },
  mounted() {
    this.currTab = this.tabName
  },
  data() {
    return {
      currTab: "",
    }
  },
  computed: {},
  methods: {
    handleClick(tab, event) {
      if ( tab.name === "app-deploy") {
        this.$router.push({name: 'cicd-app-deploy'});
      } else if ( tab.name === "app-deploy-history") {
        this.$router.push({name: 'cicd-app-deploy-history'});
      } else if ( tab.name === "image-build") {
        this.$router.push({name: 'cicd-image-build'});
      } else if ( tab.name === "image-build-history") {
        this.$router.push({name: 'cicd-image-build-history'});
      } else if ( tab.name === "image-list") {
        this.$router.push({name: 'cicd-image-list'});
      } else if ( tab.name === "doc") {
      } else {
        this.$message.error("未知操作")
      }
    }
  }
}
</script>

<style>
.cicd-menu-tabs .el-tabs--border-card>.el-tabs__header {
  border-bottom-width: 0;
}
.cicd-menu-tabs .el-tabs__content {
  display: none;
}
.cicd-menu-tabs .el-tabs--border-card {
  box-shadow: unset;
}
.cicd-menu-tabs .el-tabs--border-card>.el-tabs__header .el-tabs__item {
  color: #444;
}
.cicd-menu-tabs .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  color: #f57b22;
}

.cicd-menu-tabs .el-tabs--border-card>.el-tabs__header {
  background-color: #e0e0e0;
}

</style>
