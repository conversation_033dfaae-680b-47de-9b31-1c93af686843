<template>
  <div class="pod-simple-table-wrapper">
    <div style="text-align: center;">
      <div style="display:inline-block;width:480px;text-align: left;" v-if="podPendingAlert">
        <el-alert
          title="有pod长时间未调度到宿主机上"
          type="warning"
          show-icon>
          <div>
            <div>当资源池资源不足时，新的pod将无法正常调度到宿主机上</div>
            <div>1. 如果必须要保障此次发布平滑性，可联系 @金哲玉 @吴志辉 协助处理</div>
            <div>2. 如果允许此次发布造成业务抖动的话，可点击
              <el-button type="text" size="mini" @click="redeployWithRecreate">使用重建升级</el-button>
            </div>
            <div>重建升级: 会先删除旧版本pod，释放资源后再创建新版本pod</div>
          </div>
        </el-alert>
      </div>
    </div>
    <el-table
      :data="newPods.concat(oldPods)"
      size="small"
      v-loading="loading"
      :expand-row-keys="expandRowKeys"
      row-key="name"
    >
      <el-table-column type="expand" width="40">
        <template v-slot="scope">
          <pod-event :cluster="scope.row.cluster" :namespace="scope.row.namespace" :pod="scope.row.name"></pod-event>
        </template>
      </el-table-column>
      <el-table-column label="" width="70">
        <template slot-scope="scope">
          <el-tag size="mini" effect="plain" v-if="podVersionDesc(scope.row.name) === 'new'">{{ podVersionDesc(scope.row.name) }}</el-tag>
          <el-tag size="mini" effect="plain" v-else type='info'>{{ podVersionDesc(scope.row.name) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="实例名" prop="name">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column width="100">
        <template slot-scope="slot" slot="header">
          状态
          <el-tooltip effect="light" placement="top">
            <div slot="content">
              <el-image
                style="max-width: 800px;"
                src="images/pod-status.svg"
                alt="pod状态">
              </el-image>
            </div>
            <svg-icon icon-class="question" class-name="question-icon-size"/>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <span :class="podStatusClass(scope.row.statusDesc)"></span>
          {{ scope.row.statusDesc }}
        </template>
      </el-table-column>
      <el-table-column label="运行版本" prop="deployTag" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="重启数"
                       align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.restartCount }}</span>
          <div v-if="scope.row.restartCount > 0" style="font-size: 10px;color: #888;">重启原因: {{scope.row.restartReason}}</div>
        </template>
      </el-table-column>
      <el-table-column label="Pod IP" prop="podIP" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="Host IP" prop="hostIP" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.hostIP }}</span>
          <div v-if="scope.row.resourcePool" style="font-size: 10px;color: #888;">资源池: {{scope.row.resourcePool}}</div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="140">
      </el-table-column>
      <el-table-column
        label=""
        width="160"
        fixed="right">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="事件列表" placement="top">
            <el-button
              circle
              size="small"
              style="padding:5px;"
              @click="rowExpand(scope.row.name)">
              <svg-icon icon-class="event" style="font-size: 1.5em;"/>
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="启动日志" placement="top">
            <el-button
              circle
              size="small"
              style="padding:5px;"
              @click="podStdoutLog(scope.row)">
              <svg-icon icon-class="log" style="font-size: 1.5em;"/>
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="进入容器" placement="top">
            <el-button
              circle
              size="small"
              style="padding:5px;"
              @click="podShell(scope.row)">
              <svg-icon icon-class="console" style="font-size: 1.5em;"/>
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="容器启动日志（标准输出)"
      :visible.sync="podStdoutVisible"
      top="5vh"
      :close-on-click-modal="false"
      width="70%"
      append-to-body
      @close="podStdout.pod = null"
      center>
      <div style="margin-top: -30px">
        <pod-stdout :cluster="this.podStdout.cluster" :namespace="this.podStdout.namespace" :pod="this.podStdout.pod" :containers="this.podStdout.containers"></pod-stdout>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import moment from "moment";
import PodStdout from "@/views/components/pod-stdout";
import {useRecreateDeployStrategy} from "@/api/k8s/app";
import PodExpand from "@/views/pod/pod-expand.vue";
import PodEvent from "@/views/pod/pod-event.vue";
import {getPods} from "@/api/k8s/pod";

export default {
  name: 'DeployPodList',
  components: {PodEvent, PodExpand, PodStdout},
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    app: {
      type: String,
      required: true
    },
    timestamp: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading:false,
      execution: {},
      oldPods: [],
      newPods: [],
      expandRowKeys: [],
      podPendingAlert: false,
      podStdoutVisible: false,
      podStdout: {
        cluster: "",
        namespace: "",
        pod: "",
        containers: []
      },
    }
  },
  watch: {},
  computed: {},
  mounted() {
    this.loadPods()
  },
  beforeDestroy: function () {
  },
  methods: {
    loadPods() {
      this.loading =true
      let vThis = this;
      getPods(this.cluster,this.namespace,this.app).then(response => {
        this.oldPods = [];
        this.newPods = [];
        for(let it of response.data) {
          if(new Date(it.createTime).getTime() < this.timestamp) {
            this.oldPods.push(it)
          } else {
            this.newPods.push(it)
          }
        }
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading=false
        this.showAlert();
        if (vThis.isReloadPods()) {
          setTimeout(function () {
            vThis.loadPods();
          }, 20000);
        }
      })
    },
    rowExpand: function (pod) {
      if (this.expandRowKeys && this.expandRowKeys[0] === pod) {
        this.expandRowKeys = [];
        return;
      }
      this.expandRowKeys = [pod]
    },
    isReloadPods() {
      if (this.oldPods.length > 0) {
        return true;
      }
      let ret = false;
      for (let it of this.newPods) {
        if (!it.ready) {
          ret = true;
          break;
        }
      }
      return ret;
    },
    showAlert() {
      let showPendingAlert = false
      if (this.oldPods && this.oldPods.length > 0 && this.newPods && this.newPods.length > 0) {
        for (let it of this.newPods) {
          if (it.statusDesc === "调度中") {
            let now = moment()
            let podCreateTime = moment(it.createTime);
            let diffSec = now.diff(podCreateTime, "seconds")
            if (diffSec > 120) {
              console.log("podCreateTime pending time: " + diffSec)
              showPendingAlert = true;
              break;
            }
          }
        }
      }
      this.podPendingAlert = showPendingAlert
    },

    podVersionDesc(podName) {
      let ret = this.oldPods.filter(pod => pod.name === podName);
      if (ret && ret.length > 0) {
        return "old"
      }
      ret = this.newPods.filter(pod => pod.name === podName);
      if (ret && ret.length > 0) {
        return "new"
      }
      return "--"
    },
    redeployWithRecreate() {
      this.$confirm('此操作会先删除旧版本pod，释放资源后再创建新版本pod。在新版pod能正常提供服务前，当前服务将会处于不可用状态。确认是否继续？', '重建升级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!this.oldPods || this.oldPods.length < 1) {
          this.$message.warning("没有旧版pod，无法执行当前操作")
          return
        }
        let pod = this.oldPods[0]
        useRecreateDeployStrategy({
          "cluster": pod.cluster,
          "namespace": pod.namespace,
          "app": pod.labelApp
        }).then(response => {
          this.$message.success('操作成功');
        }).catch((e) => {
          this.$message.error(e.message);
          this.stopReloadTimer()
        })
      }).catch(() => {
      });
    },
    podStdoutLog(row) {
      this.podStdoutVisible = true
      this.podStdout.cluster = row.cluster
      this.podStdout.namespace = row.namespace
      this.podStdout.pod = row.name
      this.podStdout.containers = [row.container0Name, ...row.initContainersName]
    },
    podShell(row) {
      let url = `/api/page/redirect?type=webShell&cluster=${row.cluster}&namespace=${row.namespace}&app=${row.labelApp}&pods=${row.name}`
      window.open(url)
    },
    podStatusClass(podStatus) {
      if (podStatus) {
        if (podStatus === "运行中") {
          return "pod-status-green"
        } else if (["调度中", "准备中", "启动中"].includes(podStatus)) {
          return "pod-status-orange"
        }
      }
      return "pod-status-red"
    },
  }
}
</script>

<style>
.stdout-log-content {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 70vh;
  overflow-y: auto;
}

.pod-status-green {
  background: #69c993;
  box-shadow: 0 4px 10px rgb(87 177 126 / 30%);
  margin-right: 3px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-red {
  background: #d15352;
  box-shadow: 0 4px 10px rgb(209 83 82 / 30%);
  margin-right: 3px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-orange {
  background: #E6A23C;
  box-shadow: 0 4px 10px rgb(230 162 60 / 30%);
  margin-right: 3px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block
}
</style>
