<template>
  <div class="app-container pod-index" style="padding: 0;margin: -10px 0 0 0;">
    <el-tabs class="pod-index-tabs" type="border-card" :active-name="this.currTab" @tab-click="tabChange" style="margin-top: 10px;" v-loading="tabLoading">
      <el-tab-pane name="appDeploy" :lazy="true">
        <span slot="label"><svg-icon icon-class="guide" style="margin-right: 2px;"/>应用发布</span>
        <div>
          <app-deploy></app-deploy>
        </div>
      </el-tab-pane>
      <el-tab-pane name="deployHistory" :lazy="true">
        <span slot="label"><svg-icon icon-class="record" style="margin-right: 2px;"/>发布记录</span>
        <div>
          <deploy-history></deploy-history>
        </div>
      </el-tab-pane>
      <el-tab-pane name="imageBuild" :lazy="true">
        <span slot="label"><svg-icon icon-class="image" style="margin-right: 2px;"/>镜像构建</span>
        <div>
          <image-build></image-build>
        </div>
      </el-tab-pane>
      <el-tab-pane name="imageBuildHistory" :lazy="true">
        <span slot="label"><svg-icon icon-class="record" style="margin-right: 2px;"/>构建记录</span>
        <div>
          <image-build-history></image-build-history>
        </div>
      </el-tab-pane>
      <el-tab-pane name="imageList" :lazy="true">
        <span slot="label"><svg-icon icon-class="image" style="margin-right: 2px;"/>镜像列表</span>
        <div style="padding: 10px;">
          <image-list></image-list>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {getAllAppName, getGitModules} from "@/api/app";
import AppEvent from "@/views/pod/app-event.vue";
import {getUserInfo} from "@/api/user";
import PodExpand from "@/views/pod/pod-expand.vue";
import PodList from "@/views/pod/pod-list.vue";
import ImageList from "@/views/cicd/image-list.vue";
import ImageBuild from "@/views/cicd/image-build.vue";
import PipelineApp from "@/views/pipeline/pipeline-app.vue";
import ImageBuildHistory from "@/views/cicd/image-build-history.vue";
import DeployHistory from "@/views/cicd/app-deploy-history.vue";
import AppDeploy from "@/views/cicd/app-deploy.vue";

export default {
  components: {AppDeploy, DeployHistory, ImageBuildHistory, PipelineApp, ImageBuild, ImageList, PodList, PodExpand, AppEvent},
  data() {
    return {
      currApp: "",
      currTab: "appDeploy",
      tabLoading: false,
      recentApps: [],
      apps: [],
    };
  },
  mounted() {
    let tab = this.$route.query.tab
    if(tab) {
      this.currTab = tab
      // this.tabRouterReload()
    }
    this.loadApps();
    this.loadRecentApps();
  },
  computed: {
    appEnvs() {
      if (!this.appSelected) {
        return [];
      }
      let ret = []
      for (let cluIt of this.apps) {
        let cluster = cluIt.value;
        for (let nsIt of cluIt.children) {
          let namespace = nsIt.value;
          for (let appIt of nsIt.children) {
            if (appIt.value === this.currApp[2]) {
              ret.push({
                "cluster": cluster,
                "clusterLabel": cluIt.label,
                "namespace": namespace
              })
              break
            }
          }
        }
      }
      return ret
    }
  },
  methods: {
    loadTableData(appName) {
      this.tableLoading = true
      getGitModules(appName).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    changeCurrApp(app) {
      this.currApp = app
      this.loadTableData(app)
    },
    tabChange(tab, event) {
      if (this.currTab === tab.name) {
        return
      }
      this.currTab = tab.name
      this.tabRouterReload()
    },
    tabRouterReload() {
      this.$router.push({
        query: {...this.$route.query,"tab": this.currTab}
      });
    },
    loadRecentApps() {
      let app = this.$route.query.app
      if (app) {
        this.loadTableData(app)
      }
      getUserInfo().then(response => {
        this.recentApps = response.data.recentApps
        if (!app && this.recentApps.length > 0) {
          this.loadTableData(this.recentApps[0])
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    loadApps() {
      getAllAppName().then(response => {
        this.apps = response.data;
      }).catch((e) => {
        this.$message.error("加载应用数据出错！ " + e.message);
      });
    },
  }
};
</script>

<style>
.app-cascader .el-input__inner {
  border-radius: 0;
}

.el-loading-spinner {
  top: 50px;
}

.table-history-apps .el-table__cell {
  padding: 0;
}

.el-cascader-menu__wrap {
  height: 380px;
}

.pod-index .el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

</style>
