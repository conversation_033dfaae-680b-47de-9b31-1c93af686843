<template>
  <div class="app-container" style="margin: 0;padding: 0;">
    <div style="padding: 10px;">
      <div style="float: left">
        <el-form ref="form1" size="small" :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent
                 :rules="rules">
          <el-form-item label="日期" prop="recordDate">
            <el-date-picker
              v-model="searchForm.recordDate"
              align="right"
              type="date"
              placeholder=""
              value-format="yyyy-MM-dd"
              :default-value="defaultDate"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="环境" prop="namespace">
            <el-select v-model="searchForm.namespace" placeholder="" filterable clearable>
              <el-option
                v-for="item in  this.namespaceOptions"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用" prop="app">
            <el-input v-model.trim="searchForm.app" clearable style="width: 280px">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="float: right">
        <el-pagination
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[20, 50, 100,200,1000,5000]"
          :pager-count="5"
          layout="total,sizes,prev,pager,next"
          :total="tableData.count"
          @size-change="pageSizeChange"
          @current-change="pageChange">
        </el-pagination>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData.data"
        element-loading-text="数据加载中..."
        highlight-current-row
      >
        <el-table-column type="index">
        </el-table-column>
        <el-table-column label="应用" prop="App">
        </el-table-column>
        <el-table-column label="环境" prop="Namespace">
        </el-table-column>
        <el-table-column label="集群" prop="Cluster">
        </el-table-column>
        <el-table-column label="版本记录时间" prop="RecordDate">
        </el-table-column>
        <el-table-column label="最后发布时间" prop="DeployTime">
        </el-table-column>
        <el-table-column label="最后发布人" prop="DeployAuthor">
        </el-table-column>
        <el-table-column label="部署模块数">
          <template slot-scope="scope">
            {{ scope.row.DeployModules.length }}
          </template>
        </el-table-column>
        <el-table-column label="版本号" prop="Version">
        </el-table-column>
        <el-table-column label="版本详情" prop="" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="showVersionDetail(scope.row)">查看所有
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :title="versionDetail.title" :visible.sync="versionDetail.visible" width="50%" top="5vh" :close-on-click-modal="false">
      <div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ versionDetail.content }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import MenuTabs from "@/views/cicd/menu-tabs.vue";
import NamespaceSelector from "@/views/components/namespace-selector.vue";
import {searchAppVersionHistory} from "@/api/operation";

export default {
  name: "deploy-history",
  components: {NamespaceSelector, MenuTabs},
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        },
          {
            text: '两周前',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 14);
              picker.$emit('pick', date);
            }
          },
          {
            text: '一月前',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', date);
            }
          }]
      },
      defaultDate: new Date(),
      tableData: {
        data: [],
        count: 0
      },
      artifacts: [],
      tableLoading: false,
      searchForm: {
        app: "",
        namespace: "",
        recordDate: "",
        page: 1,
        limit: 20,
      },
      versionDetail: {
        title: "",
        content: "",
        visible: false,
      },
      rules: {
        namespace: [
          {required: true},
        ],
        recordDate: [
          {required: true}
        ]
      }
    }
  },
  computed: {
    namespaceOptions: function () {
      let items = []
      for (let clu of this.$settings.clusters) {
        for (let ns of clu.namespaces) {
          if (!items.includes(ns)) {
            items.push(ns)
          }
        }
      }
      return items
    }
  },
  mounted() {
  },
  methods: {
    loadTableData() {
      this.$refs['form1'].validate((valid) => {
        if (valid) {
          this.tableLoading = true;
          searchAppVersionHistory(this.searchForm).then(response => {
            this.tableData = response.data;
          }).catch((e) => {
            this.$message.error(e.message);
          }).finally(() => {
            this.tableLoading = false;
          })
        } else {

          return false;
        }
      });

    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    pageSizeChange(size) {
      this.searchForm.limit = size;
      this.loadTableData();
    },
    showVersionDetail(row) {
      try {
        this.versionDetail.title = `${row.RecordDate} / ${row.Cluster} / ${row.Namespace} / ${row.App} `
        this.versionDetail.content = JSON.stringify(row.DeployModules, null, 2)
        this.versionDetail.visible = true
      } catch (e) {
        console.log("json parse fail")
      }
    },
  }
}
</script>


