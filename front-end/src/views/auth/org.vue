<template>
  <div class="app-container">
    <table style="color: rgb(119, 119, 119);font-size: 12px;padding: 10px;">
      <tr>
        <th style="width: 70px;text-align: right;vertical-align: top;">说明:</th>
        <td style="width: 1000px;">发布系统基于部门来给应用进行授权。比如只允许部门A的同学才能发布应用app1</td>
      </tr>
      <tr>
        <th style="width: 70px;text-align: right;vertical-align: top;">部门新建:</th>
        <td style="width: 1000px;">只有系统管理员拥有部门新建权限</td>
      </tr>
      <tr>
        <th style="width: 70px;text-align: right;vertical-align: top;">成员修改:</th>
        <td style="width: 1000px;">部门内的所有成员可以把其他同学添加到部门内</td>
      </tr>
    </table>
    <div>
      <el-button type="text" icon="el-icon-circle-plus-outline" @click="createPage">新建</el-button>
      <export-button :table-ref="this.$refs.table001"></export-button>
    </div>
    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="部门"
                       width="220px"
                       prop="name">
      </el-table-column>
      <el-table-column label="成员"
                       prop="users">
        <template slot-scope="scope">
          <div class="org-users">
            <el-tag type="info" effect="dark" v-for="item in scope.row.users">{{ item }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="描述" prop="remark">
      </el-table-column>
      <el-table-column label="操作"
                       width="180px"
                       fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editPage(scope.row)">修改
          </el-button>
          <el-popconfirm :title="'确定要删除【 ' + scope.row.name + ' 】吗？'" @confirm="deleteApp(scope.$index, scope.row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="dialogEditTitle" :visible.sync="dialogEditVisible" width="900px" :close-on-click-modal="false">
      <el-form :model="dialogEditForm" ref="dialogEditForm" label-width="120px" :rules="dialogEditFormRules">
        <el-form-item label="ID" v-show="false">
          <el-input v-model="dialogEditForm.id" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="dialogEditForm.name" type="text" :disabled="dialogEditForm.id > 0"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="dialogEditForm.remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
        <el-form-item label="成员" prop="users">
          <el-select v-model="dialogEditForm.users" multiple
                     filterable
                     :filter-method="(query)=>{userPinYinMatch(query)}"
                     placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in userOptions"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="createOrUpdate()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {allOrg, createOrg, editOrg} from "@/api/org";
import {userNames} from "@/api/user";
import {cloneObject} from "@/utils/my-util";
import pinyin from "pinyin-match";
import ExportButton from "@/views/components/export-button.vue";

export default {
  components: {ExportButton},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      dialogEditTitle: '',
      dialogEditVisible: false,
      dialogEditForm: {
        id: 0,
        name: "",
        remark: "",
        users: []
      },
      userOptions: [],
      userAllOptions: [],
      dialogEditFormRules: {
        name: [
          {required: true, message: '内容不能为空', trigger: 'blur'},
        ],
      }
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.loadUserNames();
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      allOrg().then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadUserNames() {
      userNames().then(response => {
        this.userOptions = response.data;
        this.userAllOptions = response.data;
      }).catch((e) => {
        this.$message.error("加载用户数据出错：", e.message);
      })
    },
    resetEditForm() {
      this.dialogEditForm.id = 0;
      this.dialogEditForm.name = "";
      this.dialogEditForm.remark = "";
      this.dialogEditForm.users = [];
    },
    createPage() {
      this.dialogEditTitle = "新建"
      this.dialogEditVisible = true;
      this.resetEditForm();
    },
    deleteApp(index, row) {
      this.$message.info("todo")
    },
    editPage(row) {
      this.dialogEditForm = cloneObject(row);
      this.dialogEditVisible = true;
    },
    createOrUpdate() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }

        let method = this.dialogEditForm.id > 0 ? editOrg : createOrg;
        method(this.dialogEditForm).then(response => {
          this.dialogEditVisible = false;
          this.$message.success("操作成功");
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        });
      });
    },
    userPinYinMatch(key) {
      if (key) {
        this.userOptions = this.userAllOptions.reduce((X, item) => {
          if (pinyin.match(item, key) || item.includes(key)) {
            X.push(item);
          }
          return X;
        }, []);
      }
    }
  },
}
</script>

<style>
.org-users .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>
