<template>
  <div class="app-container">
    <div style="margin-bottom: 10px;">
      <el-alert
        type="info"
        :closable="false">
        <template slot="title">
          <div style="color: #555;font-size:16px;line-height: 18px;font-weight: bold;">
            <div style="font-size:18px;color:#333;">
              <span><i class="el-icon-warning"></i></span>
              <span style="padding-left: 5px;">提示</span>
            </div>
            <div style="padding-top: 10px;line-height: 22px;">
              权限申请后联系以下同学进行审批：{{ admins.join("、") }}
            </div>
          </div>
        </template>
      </el-alert>
    </div>
    <el-dialog title="申请临时授权" :visible.sync="dialogAddVisible" width="800px" :close-on-click-modal="false">
      <el-form ref="dialogAddForm" label-width="120px" @submit.native.prevent>
        <el-form-item label="应用名">
          <el-select
            v-model="dialogAddForm.app"
            filterable
            style="width: 100%"
            placeholder="请输入应用名称">
            <el-option
              v-for="item in apps"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label=" " style="margin: -30px 0 10px 0;color: #555;font-size: 12px">
          <div style="font-size:12px;color: #666;">需要操作的应用</div>
        </el-form-item>
        <el-form-item label="授权操作">
          <el-select v-model="dialogAddForm.operate" style="width: 100%">
            <el-option
              v-for="item in operateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label=" " style="margin: -30px 0 10px 0;color: #555;font-size: 12px;">
          <div style="font-size:12px;color: #666;">需要执行的操作，比如：镜像构建+发版</div>
        </el-form-item>
        <el-form-item label="操作时间范围">
          <el-date-picker
            style="width: 100%"
            v-model="dialogAddForm.datetimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label=" " style="margin: -30px 0 10px 0;color: #555;font-size: 12px">
          <div style="font-size:12px;color: #666;">需要操作的时间范围段</div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            maxlength="100"
            placeholder="请输入备注信息"
            v-model="dialogAddForm.remark">
          </el-input>
        </el-form-item>
        <el-form-item label=" " style="margin: -30px 0 10px 0;color: #555;font-size: 12px">
          <div style="font-size:12px;color: #666;">请填写授权原因，不要超过100个字符</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAddVisible = false">取 消</el-button>
        <el-button type="primary" @click="create()">确 定</el-button>
      </div>
    </el-dialog>
    <div>
      <el-button icon="el-icon-circle-plus-outline" @click="addAuth" type="primary" style="margin-bottom: 10px;">申请临时授权</el-button>
      <div style="float: right;">
      <el-select v-model="searchForm.approved" style="width: 180px;margin-left: 30px;" placeholder="审批状态" >
        <el-option label="所有" value=""></el-option>
        <el-option label="已审核" value="true"></el-option>
        <el-option label="待审批" value="false"></el-option>
      </el-select>
        <el-button type="primary" icon="el-icon-search" @click="search()">查询</el-button>
      </div>
    </div>
    <div style="float: right">
      <el-pagination
        :current-page="searchForm.page"
        :page-size="searchForm.limit"
        layout="total,sizes,prev,pager,next"
        :total="tableData.count"
        @current-change="pageChange"
        @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用"
                       prop="app">
      </el-table-column>
      <el-table-column label="授权操作" prop="operateDesc">
      </el-table-column>
      <el-table-column label="申请人"
                       prop="user">
      </el-table-column>
      <el-table-column label="状态"
                       prop="approved" width="120">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.approved" type="success" size="mini">审核通过</el-tag>
            <el-tag v-else type="info" size="mini">待审核</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="允许操作的时间范围" prop="date" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.startTime">
            <div><span style="color: #999;">开始:</span>&nbsp;&nbsp;{{ scope.row.startTime }}</div>
            <div><span style="color: #999;">结束:</span>&nbsp;&nbsp;{{ scope.row.endTime }}</div>
              <el-tag v-if="scope.row.timeRangeDesc" type="warning" size="mini">{{ scope.row.timeRangeDesc }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作"
                       width="160px">
        <template slot-scope="scope">
          <el-popconfirm :title="'确定要删除吗？'" @confirm="delTempAuthAuth(scope.row.id)">
            <el-button
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
          <el-button v-if="!scope.row.approved"
                     type="text"
                     slot="reference"
                     @click="auditTempAuth(scope.row.id,'true')">审批
          </el-button>
          <el-button v-else
                     type="text"
                     slot="reference"
                     @click="auditTempAuth(scope.row.id,'false')">驳回
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="审核人"
                       prop="approver">
      </el-table-column>
      <el-table-column label="申请时间"
                       prop="createdTime">
      </el-table-column>
      <el-table-column label="备注"
                       show-overflow-tooltip
                       prop="remark">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {listUserByRealName, userNames} from "@/api/user";
import {auditTempAuth, createTempAuth, deleteTempAuth, getTempAuthAdmins, searchTempAuth} from "@/api/temp_auth";
import {getAllAppName} from "@/api/app";
import moment from "moment";

export default {
  data() {
    return {
      loading: false,
      apps: [],
      dialogAddVisible: false,
      admins: [],
      dialogAddForm: {
        app: "",
        user: "",
        operate: "deploy",
        startTime:"",
        endTime:"",
        datetimeRange: [
          new Date(new Date().setHours(0, 0, 0, 0)),
          (() => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(6, 0, 0, 0);
            return tomorrow;
          })()
        ],
        remark: ""
      },
      operateOptions: [
        {
          value: "deploy",
          label: "镜像构建+发布"
        }
      ],
      userOptions: [],
      selectUserLoading: false,
      selectUserOptions: [],
      tableData: [],
      tableLoading: false,
      searchForm: {
        approved: "",
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {},
  mounted() {
    this.loadAdmins();
    this.loadApps()
    this.loadTableData()
    if (this.$route.query.showAddDialog === "true") {
      this.dialogAddForm.app = this.$route.query.app;
      this.addAuth();
    }
    this.loadUserNames()
  },
  methods: {
    addAuth() {
      this.dialogAddVisible = true
    },
    auditTempAuth(id, approved) {
      let message = approved === 'true' ? '确定要审核通过吗？通过后，被授权者可以执行授权操作' : '确定要驳回吗？驳回后，被授权者将不能执行授权操作。'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        auditTempAuth(id, approved).then(response => {
          this.$message.success("操作成功");
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message)
        })
      })
    },
    searchUserByRealName(query) {
      if (query !== '') {
        this.selectUserLoading = true
        listUserByRealName(query).then(response => {

          this.selectUserOptions = response.data
          // this.tableData = response.data;
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.selectUserLoading = false;
        })
      } else {
        this.selectUserOptions = []
      }
    },
    loadUserNames() {
      userNames().then(response => {
        this.userOptions = response.data;
      }).catch((e) => {
        this.$message.error("加载用户数据出错：", e.message);
      })
    },
    loadApps() {
      getAllAppName().then(response => {
        this.apps = response.data;
      }).catch((e) => {
        this.$message.error("加载应用数据出错！ " + e.message);
      });
    },
    create() {
      if (!this.dialogAddForm.app) {
        this.$message.error("请选择应用")
        return
      }
      if (!this.dialogAddForm.operate) {
        this.$message.error("请填写授权类型")
        return
      }
      if (!this.dialogAddForm.datetimeRange) {
        this.$message.error("请选择授权时间")
        return
      }
      if(!this.dialogAddForm.datetimeRange[0] || !this.dialogAddForm.datetimeRange[1]){
        this.$message.error("请选择授权时间范围")
        return
      }
      this.dialogAddForm.startTime = moment(this.dialogAddForm.datetimeRange[0]).utc().format('YYYY-MM-DD HH:mm:ss')
      this.dialogAddForm.endTime = moment(this.dialogAddForm.datetimeRange[1]).utc().format('YYYY-MM-DD HH:mm:ss')
      createTempAuth(this.dialogAddForm).then(response => {
        this.dialogAddVisible = false;
        this.$message.success("操作成功");
        this.loadTableData()
      }).catch((e) => {
          this.$message.error(e.message)
        }
      )
    },
    loadAdmins() {
      getTempAuthAdmins(this.searchForm).then(response => {
        this.admins = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    loadTableData() {
      this.tableLoading = true;
      searchTempAuth(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    delTempAuthAuth(id) {
      deleteTempAuth(id).then(response => {
        this.$message.success("操作成功");
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message)
      })
    },
    search() {
      this.loadTableData();
    },
    handleSizeChange(size) {
      this.searchForm.limit = size;
      this.searchForm.page = 1;
      this.loadTableData();
    }
  },
}
</script>

