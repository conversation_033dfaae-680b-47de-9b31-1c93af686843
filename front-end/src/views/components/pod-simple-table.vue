<template>
  <div class="pod-simple-table-wrapper">
    <div style="margin-top: -30px;text-align: center;">
      <b>应用：</b> {{ this.app }}
      <b style="margin-left: 20px;">环境：</b> {{ this.namespace }} ({{ this.cluster }})
    </div>
    <div style="text-align: right;padding-right: 20px;">
      <el-button
        type="text"
        @click="podPage()"
        class="el-icon-menu">实例管理页
      </el-button>
      <el-button
        style="margin-left: 20px;"
        type="text"
        @click="podsShell()"
        class="el-icon-bank-card">进入所有容器
      </el-button>
    </div>
    <el-table
      :data="pods"
      border
      fit
      highlight-current-row
      row-key="name"
      v-loading="tableLoading"
    >
      <el-table-column label="实例名" sortable prop="name">
        <template slot-scope="scope">
          <span :style="{color:scope.row.ready ? '#67C23A':'#F56C6C'}">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Pod IP"
                       width="120" sortable prop="podIP">
      </el-table-column>
      <el-table-column label="Host IP"
                       width="120" sortable prop="hostIP">
      </el-table-column>
      <el-table-column label="重启次数"
                       width="110"
                       align="center" sortable prop="restartCount">
      </el-table-column>
      <el-table-column label="创建时间" sortable prop="createTime" width="150">
      </el-table-column>
      <el-table-column
        label="操作"
        width="180"
        fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="showStdoutLogDialog(scope.row)">标准输出
          </el-button>
          <el-button
            type="text"
            @click="webShellPage(scope.row.name)">进入容器
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="容器启动日志（标准输出)"
      :visible.sync="podStdoutVisible"
      top="5vh"
      :close-on-click-modal="false"
      width="70%"
      append-to-body
      center>
      <div style="margin-top: -30px">
        <pod-stdout :cluster="this.podStdout.cluster" :namespace="this.podStdout.namespace" :pod="this.podStdout.pod" :containers="this.podStdout.containers"></pod-stdout>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {getPods} from "@/api/k8s/pod";
import podStdout from "@/views/components/pod-stdout";

export default {
  name: 'PodSimpleTable',
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    app: {
      type: String,
      required: true
    },
  },
  components: {
    podStdout
  },
  data() {
    return {
      tableLoading: false,
      pods: [],
      podStdoutVisible: false,
      podStdout: {
        cluster: "",
        namespace: "",
        pod: "",
        containers: []
      },
    }
  },
  watch: {
    cluster(val) {
      this.loadPods();
    },
    namespace(val) {
      this.loadPods();
    },
    app(val) {
      this.loadPods();
    }
  },
  computed: {},
  mounted() {
    this.loadPods()
  },
  beforeDestroy: function () {
  },
  methods: {
    loadPods() {
      this.tableLoading = true;
      console.log("load pipeline pods")
      getPods(this.cluster, this.namespace, this.app).then(response => {
        this.pods = response.data;
      }).catch((e) => {
        this.$message.error("加载实例信息失败: " + e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    podsShell() {
      if (this.pods && this.pods.length > 0) {
        this.webShellPage(this.pods.map(x => x.name).join(","))
      }
    },
    podPage() {
      let rou = this.$router.resolve({
        name: 'pod-index', query: {
          "cluster": this.cluster,
          "namespace": this.namespace,
          "app": this.app,
        }
      });
      window.open(rou.href, '_blank');
    },
    showStdoutLogDialog(row) {
      this.podStdoutVisible = true
      this.podStdout.cluster = row.cluster
      this.podStdout.namespace = row.namespace
      this.podStdout.pod = row.name
      this.podStdout.containers = [row.container0Name, ...row.initContainersName]
    },
    webShellPage(podNames) {
      let url = `/api/page/redirect?type=webShell&cluster=${this.cluster}&namespace=${this.namespace}&app=${this.app}&pods=${podNames}`
      window.open(url)
    }
  }
}
</script>

<style>
.stdout-log-content {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 70vh;
  overflow-y: auto;
}
</style>
