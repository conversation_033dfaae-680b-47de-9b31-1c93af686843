<template>
  <div class="env-selector-wrapper">
    <el-form-item :label="clusterLabel" prop="cluster" v-if="showCluster">
      <el-select v-model="cluster" placeholder="选择k8s集群" @change="clusterChange" filterable>
        <el-option
          v-for="item in this.clusterOptions"
          :key="item.name"
          :label="item.name + ' (' + item.description + ')'"
          :value="item.name">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item :label="namespaceLabel" prop="namespace" v-if="showNamespace">
      <el-select v-model="namespace" placeholder="选择namespace">
        <el-option key="*" label="所有" value="" v-if="showAllNamespaces"></el-option>
        <el-option
          v-for="item in  this.namespaceOptions"
          :key="item"
          :label="item"
          :value="item">
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>

export default {
  name: 'EnvSelector',
  props: {
    showAllNamespaces: {
      type: Boolean,
      default: false
    },
    showCluster: {
      type: Boolean,
      default: true,
    },
    showNamespace: {
      type: Boolean,
      default: true,
    },
    clusterLabel: {
      type: String,
      default: 'k8s集群'
    },
    namespaceLabel: {
      type: String,
      default: '运行环境'
    }
  },
  data() {
    return {
      cluster: '',
      namespace: '',
    }
  },
  mounted() {
    if (!this.cluster && this.clusterOptions && this.clusterOptions.length) {
      this.cluster = this.clusterOptions[0].name
    }
    if (this.cluster && !this.namespace && this.namespaceOptions && this.namespaceOptions.length) {
      this.namespace = this.namespaceOptions[0]
    }
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  methods: {
    clusterChange: function () {
      this.namespace = ""
    }
  }
}
</script>

<style>
.env-selector-wrapper {
  display: inline;
}
</style>
