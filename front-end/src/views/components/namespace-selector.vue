<template>
  <div class="namespace-selector-wrapper">
    <el-form-item :label="namespaceLabel" prop="namespace">
      <el-select v-model="namespace" placeholder="" filterable clearable>
        <el-option v-if="includeAllOption" label="所有" value=""></el-option>
        <el-option
          v-for="item in  this.namespaceOptions"
          :key="item"
          :label="item"
          :value="item">
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>

export default {
  name: 'NamespaceSelector',
  props: {
    namespaceLabel: {
      type: String,
      default: '运行环境'
    },
    includeAllOption: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      namespace: '',
    }
  },
  mounted() {
  },
  computed: {
    namespaceOptions: function () {
      let items = []
      for (let clu of this.$settings.clusters) {
        for (let ns of clu.namespaces) {
          if (!items.includes(ns)) {
            items.push(ns)
          }
        }
      }
      return items
    }
  },
}
</script>

<style>
.namespace-selector-wrapper {
  display: inline;
}
</style>
