<template>
  <div class="env-selector-form" :style="{display: display}">
    <el-form :inline="true" class="demo-form-inline" @submit.native.prevent :style="{display: display}">
      <el-form-item label="k8s集群" prop="cluster">
        <el-select v-model="cluster" placeholder="选择k8s集群" @change="clusterChange" filterable>
          <el-option
            v-for="item in this.clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-select v-model="namespace" placeholder="选择namespace" filterable>
          <el-option key="*" label="所有" value="" v-if="showAllNamespaces"></el-option>
          <el-option
            v-for="item in  this.namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-button type="primary" @click="submit">确认</el-button>
    </el-form>
  </div>
</template>

<script>

export default {
  name: 'EnvSelectorForm',
  props: {
    showAllNamespaces: {
      type: Boolean,
      default: false
    },
    display: {
      type: String,
      default: 'block'
    }
  },
  data() {
    return {
      cluster: '',
      namespace: '',
    }
  },
  mounted() {
    if (!this.cluster && this.clusterOptions && this.clusterOptions.length) {
      this.cluster = this.clusterOptions[0].name
    }
    if (this.cluster && !this.namespace && this.namespaceOptions && this.namespaceOptions.length) {
      this.namespace = this.namespaceOptions[0]
    }
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  methods: {
    clusterChange: function () {
      this.namespace = ""
    },
    submit: function () {
      this.$emit("submitHandler", this.cluster, this.namespace)
    }
  }
}
</script>

<style>

</style>
