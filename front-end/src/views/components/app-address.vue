<template>
  <div class="app-address-wrapper">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>访问地址</span>
        <span style="display: inline-block;margin-left: 20px;font-size: 13px">
          ( 应用：{{ this.app }} | 环境：{{ this.namespace }} | 集群：{{ this.cluster }} )
        </span>
      </div>
      <div>
        <el-table
          v-loading="loading"
          :data="address"
          style="width: 100%;margin-top: 10px;">
          <el-table-column
            prop="name"
            width="180"
            label="端口名称">
          </el-table-column>
          <el-table-column
            prop="port"
            width="100"
            label="端口号">
          </el-table-column>
          <el-table-column
            prop="protocol"
            width="100"
            label="协议">
          </el-table-column>
          <el-table-column
            label="访问地址">
            <template slot-scope="scope">
              <p v-for="(item,index) in scope.row.addresses" style="margin: 8px">
                <b style="padding-right: 10px;">地址{{ index + 1 }}:</b>{{ item }}
                <clipboard-icon :text="item" button-text="" style="margin-left: 10px;"></clipboard-icon>
              </p>
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注">
            <template slot-scope="scope">
              <div v-html="scope.row.remark"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import {getAppAddress} from "@/api/app";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  name: 'AppAddress',
  components: {ClipboardIcon},
  props: {
    cluster: {
      type: String,
      require: true,
    },
    namespace: {
      type: String,
      require: true,
    },
    app: {
      type: String,
      require: true
    }
  },
  data() {
    return {
      loading: false,
      address: []
    }
  },
  watch: {
    cluster(val) {
      this.showAddress();
    },
    namespace(val) {
      this.showAddress();
    },
    app(val) {
      this.showAddress();
    }
  },
  computed: {},
  mounted() {
    this.showAddress()
  },
  methods: {
    showAddress() {
      if (!this.cluster) {
        this.$message.warning("缺少参数 cluster")
      } else if (!this.namespace) {
        this.$message.warning("缺少参数 namespace")
      } else if (!this.app) {
        this.$message.warning("缺少参数 app")
      } else {
        this.loading = true;
        getAppAddress(this.cluster, this.namespace, this.app).then(response => {
          this.address = response.data;
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false;
        });
      }
    }
  }
}
</script>
<style>
.app-address-wrapper .el-card__body {
  padding: 5px 10px;
}
.app-address-wrapper .el-table .el-table__cell {
  padding: 0;
}
</style>
