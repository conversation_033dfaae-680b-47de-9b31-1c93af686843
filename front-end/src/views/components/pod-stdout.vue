<template>
  <div class="pod-stdout" v-loading="stdout.loading">
    <div style="position: relative">
      <div style="font-weight: bold;float: left;line-height: 40px;">{{ this.pod }} /
        <el-select v-model.number="container" @change="loadStdoutLog" style="width: 240px;" size="mini" placeholder="请选择容器">
          <el-option v-for="item in containers" :key="item" :label="item" :value="item"></el-option>
        </el-select>

      </div>
      <div style="float: right; margin-right: 20px;">
          <span>
             <el-checkbox v-model="stdout.previous" @change="loadStdoutLog">重启前日志</el-checkbox>
          </span>
        <span style="margin-left: 20px">
              行数:
              <el-select v-model.number="stdout.tailLines" @change="loadStdoutLog" style="width: 120px">
                <el-option label="2000" value="2000"></el-option>
                <el-option label="5000" value="5000"></el-option>
                <el-option label="10000" value="10000"></el-option>
                <el-option label="50000" value="50000"></el-option>
              </el-select>
          </span>
        <span style="display: none">
          自动刷新({{ stdout.reloadPeriod }}秒):
        <el-switch v-model="stdout.autoReload" @change="autoReloadSwitch"></el-switch>
        </span>
        <el-button type="text" class="el-icon-refresh" style="margin-left: 10px;" @click="loadStdoutLog()">刷新
        </el-button>
        <el-button type="text" class="el-icon-download" style="margin-left: 10px;" @click="podStdoutLogDownload()">下载
        </el-button>
      </div>
      <div style="clear: both"></div>
    </div>
    <div style="text-align: right;margin-right: 5px">加载时间: {{ stdout.lastReloadTime }}</div>
    <pre class="stdout-log-content" id="stdout-log-content">{{ stdout.content }}</pre>
  </div>
</template>
<script>
import {downloadPodStdoutLog, getPodStdoutLog} from "@/api/k8s/pod";

export default {
  name: 'PodStdout',
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      required: true
    },
    pod: {
      type: String,
      required: false
    },
    containers: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  data() {
    return {
      container: this.containers[0],
      stdout: {
        visible: false,
        loading: false,
        autoReload: false,
        previous: false,
        tailLines: 2000,
        reloadPeriod: 10,
        reloadTimer: null,
        content: "",
        lastReloadTime: "--",
      },
    }
  },
  watch: {
    pod: function (newVal, oldVal) {
      this.container = this.containers[0]
      this.loadStdoutLog()
    }
  },
  computed: {},
  mounted() {
    this.loadStdoutLog()
  },
  beforeDestroy: function () {
    this.stopReloadTimer()
  },
  methods: {
    showStdoutLogDialog() {
      this.stdout.visible = true;
      this.loadStdoutLog()
      if (this.stdout.autoReload) {
        this.startReloadTimer()
      }
    },
    loadStdoutLog() {
      if(!this.pod) {
        return
      }
      console.log(`load pod ${this.pod} stdout log`)
      this.stdout.loading = true;
      getPodStdoutLog(this.cluster, this.namespace, this.pod, this.container, this.stdout.tailLines, this.stdout.previous).then(response => {
        this.stdout.content = response.data;
        let vThis = this;
        //如果内容还没把dom元素高度撑开，将无法实现滚动条到底部。这里采用一定的延迟方法来实现
        setTimeout(function() {
          vThis.scrollStdoutLogView()
        },200);
        setTimeout(function() {
          vThis.scrollStdoutLogView()
        },500);
        setTimeout(function() {
          vThis.scrollStdoutLogView()
        },700);

        this.stdout.lastReloadTime = new Date().toLocaleTimeString()
      }).catch((e) => {
        this.$message.error(e.message);
        this.stopReloadTimer()
      }).finally(() => {
        this.stdout.loading = false
      })
    },
    podStdoutLogDownload() {
      downloadPodStdoutLog(this.cluster, this.namespace, this.pod, this.container, this.stdout.tailLines)
    },
    stopReload() {
      this.stdout.autoReload = false
      this.stopReloadTimer();
    },
    scrollStdoutLogView() {
      let el = document.getElementById('stdout-log-content');
      el.scrollTop = el.scrollHeight;
    },
    startReloadTimer() {
      if (this.stdout.reloadTimer) {
        this.stopReloadTimer()
      }
      let vThis = this;
      this.stdout.reloadTimer = setInterval(function () {
        vThis.loadStdoutLog()
      }, vThis.stdout.reloadPeriod * 1000)
      console.log(`started pod stdout log reload timer :` + this.stdout.reloadTimer)
    },
    stopReloadTimer() {
      clearInterval(this.stdout.reloadTimer);
      console.log(`stopped pod stdout log reload timer :` + this.stdout.reloadTimer)
    },
    autoReloadSwitch(v) {
      this.stdout.autoReload = v
      if (v) {
        this.startReloadTimer()
      } else {
        this.stopReloadTimer()
      }
    }
  }
}
</script>

<style scoped>

.stdout-log-content {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 70vh;
  overflow-y: auto;
  width: 100%;
  border: 1px #aaa solid;
  padding: 5px;
  margin-top: 2px;
  min-height: 300px;
}
</style>
