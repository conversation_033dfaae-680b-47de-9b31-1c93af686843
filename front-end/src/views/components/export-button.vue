<template>
  <div style="display: inline;">
    <el-button :type="this.buttonType" :size="this.buttonSize" icon="el-icon-download" @click="exportExcel" style="margin-left: 10px;margin-right: 10px;font-size: 12px;">导出</el-button>
  </div>
</template>

<script>

import * as XLSX from "xlsx";
import FileSaver from "file-saver";

export default {
  name: "export-button",
  components: {},
  props: {
    tableRef: {
      type: Object,
      // required: false, 不要设置为必填，因为在父组件的初始化过程中，tableRef会为空，会导致报错
    },
    buttonType: {
      type: String,
      default: "text",
    },
    fileName: {
      type: String,
      default: "export",
    },
    buttonSize: {
      type: String,
      default: "",
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {
  },
  methods: {
    exportExcel() {
      if(!this.tableRef) {
        this.$message.error("请通过table-ref属性指定要导出的表格ref名");
        return;
      }
      let tableDom = this.tableRef.$el;
      let wb = XLSX.utils.table_to_book(
        tableDom,
        {raw: true}
      );
      let wbout = XLSX.write(wb, {
        bookType: "xlsx",
        bookSST: true,
        type: "array",
      });
      try {
        // 文件名-年月日时分秒.xlsx
        let filename = this.fileName + "-" + new Date().toISOString().replace(/T/, "-").replace(/\..+/, "").replace(/[_\-:]/g, '') + ".xlsx";
        FileSaver.saveAs(
          new Blob([wbout], {type: "application/octet-stream"}),
          filename
        );
      } catch (e) {
        this.$message.error("导出失败, err: " + e.message);
        console.error(e);
      }
      return wbout;
    }
  }
}
</script>


