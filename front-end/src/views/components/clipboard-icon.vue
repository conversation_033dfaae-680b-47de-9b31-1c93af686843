<template>
  <div style="display: inline-block;margin-left: 10px;color: #409EFF;cursor: pointer;" @click="copyToClipboard()">
    <i style class="el-icon-document-copy" ></i>
    <span v-if="this.buttonText">{{this.buttonText}}</span>
  </div>
</template>

<script>

export default {
  name: 'ClipboardIcon',
  props: {
    text: {
      type: String,
      require: true
    },
    buttonText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  watch: {
  },
  computed: {},
  mounted() {
  },
  methods: {
    copyToClipboard() {
      let text = this.text
      if (!text) {
        this.$message.warning("内容为空")
        return
      }
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message.success("复制成功")
        })
        .catch(() => {
          const input = document.createElement('input')
          document.body.appendChild(input)
          input.setAttribute('value', text)
          input.select()
          if (document.execCommand('copy')) {
            document.execCommand('copy')
          }
          document.body.removeChild(input)
          this.$message.success("复制成功")
        })
    }
  }
}
</script>
