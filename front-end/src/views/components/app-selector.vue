<template>
  <div class="app-selector-wrapper">
    <el-form ref="searchForm" :inline="true" :model="searchForm" :rules="rules">
      <el-form-item label="k8s集群" prop="cluster">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" @change="loadApp">
          <el-option
            v-for="item in this.clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-select v-model="searchForm.namespace" placeholder="选择Namespace" @change="loadApp">
          <el-option
            v-for="item in  this.namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用" prop="app">
        <el-select v-model="searchForm.app" filterable placeholder="选择应用" style="width: 340px;">
          <el-option
            v-for="item in this.appOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {searchDeployment} from "@/api/k8s/app";

export default {
  name: 'AppSelector',
  props: {},
  data() {
    return {
      searchForm: {
        cluster: '',
        namespace: '',
        app: '',
      },
      rules: {
        cluster: [
          {required: true, message: '请选择k8s集群'},
        ],
        namespace: [
          {required: true, message: '请选择运行环境'}
        ],
        app: [
          {required: true, message: '请输入应用名'}
        ]
      },
      cache: {},
    }
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  beforeDestroy: function () {
  },
  methods: {
    loadApp() {
      let cluster = this.searchForm.cluster;
      let namespace = this.searchForm.namespace;
      if (!cluster || !namespace) {
        this.appOptions = [];
        return;
      }
      let cacheKey = cluster + "##" + namespace + "##app";
      let data = this.cache[cacheKey];
      if (data) {
        this.appOptions = data;
        return;
      }
      let vThis = this;
      searchDeployment(cluster, namespace).then(response => {
        vThis.appOptions = response.data;
        vThis.cache[cacheKey] = response.data;
      }).catch((e) => {
        console.error(e);
      });
    }
  }
}
</script>

<style>
.app-selector-wrapper {
}
</style>
