<template>
  <div class="app-container">
    <el-form ref="searchForm" :inline="true" :model="searchForm">
      <el-form-item label="k8s集群" prop="cluster">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群">
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-select v-model="searchForm.namespace" placeholder="选择Namespace">
          <el-option
            v-for="item in namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <div id="my-chart" :style="{width:chartSize.width,height:chartSize.height,margin: '10px'}"/>
  </div>
</template>

<script>
// 引入 ECharts 主模块
import {queryK8sEvents} from "../../api/k8s/event";

let echarts = require('echarts');
  let topNum = 15;
  export default {
    data() {
      return {
        searchForm: {
          cluster: '',
          namespace: '',
          app: '',
        },
        chartSize: {
          width: "100%",
          height: "100%",
        },
        chart: null
      }
    },
    mounted() {
      this.initChart();
      if (this.clusterOptions.length > 0) {
        this.searchForm.cluster = this.clusterOptions[0].name
      }
      if (this.namespaceOptions.length > 0) {
        this.searchForm.namespace = this.namespaceOptions[0]
      }
      if (this.searchForm.cluster && this.searchForm.namespace) {
        this.fetchData();
      }
    },
    computed: {
      clusterOptions: function () {
        return this.$settings.clusters;
      },
      namespaceOptions: function () {
        if (this.searchForm.cluster) {
          for (let i in this.$settings.clusters) {
            let curr = this.$settings.clusters[i];
            if (this.searchForm.cluster === curr.name) {
              return curr.namespaces;
            }
          }
        }
        return []
      }
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose();
      this.chart = null
    },
    methods: {
      initChart() {
        this.chart = echarts.init(document.getElementById("my-chart"));
        this.chart.setOption(
          {
            title: {
              text: '告警事件类型统计',
              subtext: '数据刷新于: --',
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{b} : {c} ({d}%)'
            },
            legend: {
              bottom: 30,
              left: 10,
              top: 10,
              orient: 'vertical',
              data: []
            },
            series: [
              {
                type: 'pie',
                name: "eventType",
                selectedMode: 'single',
                data: [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          }
        );
      },
      loadData(cluster, namespace) {
        this.chart.showLoading();
        queryK8sEvents(cluster, namespace, "Warning").then(response => {
          let seriesDataMap = {};
          let respData = response.data;
          for (let i = 0; i < respData.length; i++) {
            let d = respData[i];
            if (!seriesDataMap[d.reason]) {
              seriesDataMap[d.reason] = 1;
              continue;
            }
            seriesDataMap[d.reason] += 1;
          }

          let dataList = [];
          for (let key in seriesDataMap) {
            if (seriesDataMap.hasOwnProperty(key)) {
              dataList.push({
                name: key,
                value: seriesDataMap[key],
                label: {
                  formatter: "{b} : {c} ({d}%)"
                }
              });
            }
          }
          dataList.sort(function (a, b) {
            return b.value - a.value;
          });
          let typeList = [];
          dataList.forEach(x => {
            typeList.push(x.name);
          });
          this.chart.setOption({
            legend: {
              data: typeList
            },
            series: [{
              name: 'eventType',
              data: dataList
            }]
          });
        }).catch((e) => {
          console.error(e);
        }).then(() => {
          this.chart.hideLoading();
        });
      },
      onSearch() {
        this.fetchData();
      },
      fetchData() {
        this.$refs['searchForm'].validate((valid) => {
          if (!valid) {
            return
          }
          this.loadData(this.searchForm.cluster, this.searchForm.namespace);
        })
      },
    }
  }
</script>

<style scoped>
  .app-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 84px);
  }
</style>
