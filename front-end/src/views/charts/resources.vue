<template>
  <div class="app-container">
    <el-form ref="searchForm" :inline="true" :model="searchForm">
      <el-form-item label="k8s集群" prop="cluster">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群">
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运行环境" prop="namespace">
        <el-select v-model="searchForm.namespace" placeholder="选择Namespace">
          <el-option
            v-for="item in namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <div id="my-chart" :style="{width:chartSize.width,height:chartSize.height,margin: '10px'}"/>
  </div>
</template>

<script>
// 引入 ECharts 主模块
import {searchDeployment} from "@/api/k8s/app";

let echarts = require('echarts');

export default {
  data() {
    return {
      searchForm: {
        cluster: '',
        namespace: '',
        app: '',
      },
      chartSize: {
        width: "100%",
        height: "100%",
      },
      chart: null
    }
  },
  mounted() {
    this.initChart();
    if (this.clusterOptions.length > 0) {
      this.searchForm.cluster = this.clusterOptions[0].name
    }
    if (this.namespaceOptions.length > 0) {
      this.searchForm.namespace = this.namespaceOptions[0]
    }
    if (this.searchForm.cluster && this.searchForm.namespace) {
      // this.fetchData();
    }
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters ? this.$settings.clusters : []
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let i in this.$settings.clusters) {
          let curr = this.$settings.clusters[i];
          if (this.searchForm.cluster === curr.name) {
            return curr.namespaces;
          }
        }
      }
      return []
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose();
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById("my-chart"));
      this.chart.setOption(
        {
          title: {
            text: `应用内存分配(单位：MB)`,
            subtext: '数据刷新于：--',
            right: 'center',
          },
          color: ['#0096f3', '#34da62'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['最高内存', '请求内存'],
            right: '10%'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            inverse: true,
            data: []
          },
          series: [
            {
              name: '最高内存',
              type: 'bar',
              barGap: '0',
              label: {
                show: true,
                position: 'inside'
              },
              data: []
            },
            {
              name: '请求内存',
              type: 'bar',
              label: {
                show: true,
                position: 'inside'
              },
              data: []
            }
          ]
        }
      );
    },
    loadData(cluster, namespace) {
      this.chart.showLoading();
      searchDeployment(cluster, namespace).then(response => {
        let respData = response.data;
        let appList = [];
        let limitMemoryList = [];
        let requestMemoryList = [];
        respData.sort(function (a, b) {
          return b.limitMemory - a.limitMemory;
        });
        for (let i = 0; i < respData.length; i++) {
          let d = respData[i];
          if (d.replicas < 1) {
            continue;
          }
          appList.push(d.name);
          limitMemoryList.push(Math.floor(d.limitMemory / 1024 / 1024));
          requestMemoryList.push(Math.floor(d.requestMemory / 1024 / 1024));
        }
        // if(appList.length < 15) {
        //   this.chartSize.height = '100%'
        // }else {
        //
        // }
        this.chart.resize({
          height: appList.length * 30
        });
        this.chart.setOption({
          yAxis: {
            data: appList
          },
          series: [{
            name: '最高内存',
            data: limitMemoryList
          },
            {
              name: '请求内存',
              data: requestMemoryList
            }]
        });
        // this.chart.resize();

      }).catch(() => {
        console.log("query app fail!");
      }).then(() => {
        this.chart.hideLoading();
      });
    },
    onSearch() {
      this.fetchData();
    },
    fetchData() {
      this.$refs['searchForm'].validate((valid) => {
        if (!valid) {
          return
        }
        this.loadData(this.searchForm.cluster, this.searchForm.namespace);
      })
    },
  }
}
</script>

<style scoped>
.app-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
  overflow-y: auto;
}
</style>
