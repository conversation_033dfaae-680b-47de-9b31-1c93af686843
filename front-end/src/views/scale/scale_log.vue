<template>
  <div class="app-container">
    <div style="text-align: right">
      <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
        <el-form-item label="关键字" style="margin-bottom: 0;">
          <el-input v-model="searchForm.keyword" style="width: 400px;" placeholder="多个关键字请用空格分割"></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 0;">
          <el-button type="primary" icon="el-icon-search" @click="loadTableData" @submit.native.prevent>查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.limit"
      layout="total,prev,pager,next"
      :total="tableData.count"
      @current-change="PageChange">
    </el-pagination>
    <el-table
      v-loading="tableLoading"
      :data="tableData.data"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="动作" prop="operate">
      </el-table-column>
      <el-table-column label="应用" prop="app">
      </el-table-column>
      <el-table-column label="环境" prop="namespace">
      </el-table-column>
      <el-table-column label="集群" prop="cluster">
      </el-table-column>
      <el-table-column label="副本数调整">
        <template slot-scope="scope">
          {{scope.row.oldReplicas}} → {{scope.row.newReplicas}}
        </template>
      </el-table-column>
      <el-table-column label="操作时间" width="200px;" prop="createdTime">
      </el-table-column>
      <el-table-column label="内容" show-overflow-tooltip prop="remark">
      </el-table-column>
      <el-table-column
        label=""
        fixed="right" width="90px">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="podPage(scope.row)">实例管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

import {queryLogs} from "@/api/log";
import {searchScaleLog} from "@/api/k8s/scale";

export default {
  data() {
    return {
      tableData: {
        data: [],
        count: 0
      },
      tableLoading: false,
      searchForm: {
        keyword: "",
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query.keyword) {
      this.searchForm.keyword = this.$route.query.keyword
    }
    this.loadTableData();
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      this.$router.push({
        query: {"keyword": this.searchForm.keyword}
      });
      searchScaleLog(this.searchForm).then(response => {
        this.tableData = response.data;
        this.tableLoading = false;
      }).catch((e) => {
        this.$message.error(e.message);
        this.tableLoading = false;
      });
    },
    PageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    logContent(row) {
      let content = row.content;
      if (content.startsWith("[") || content.startsWith("{")) {
        try {
          content = JSON.stringify(JSON.parse(content), null, 2)
        } catch (e) {
          console.log("json parse fail")
        }
      }
      this.dialogContent = content
      this.dialogVisible = true
    },
    podPage(row) {
      let p = {
        "cluster": row.cluster,
        "namespace": row.namespace,
        "app": row.app,
      };
      this.$router.push({name: 'pod-index', query: p});
    },
  }
}
</script>


