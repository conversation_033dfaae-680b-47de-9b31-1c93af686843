<template>
  <div class="app-container">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData" @submit.native.prevent>
      <el-form-item label="集群">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" style="width: 100%;"
                   @change="clusterChange" filterable>
          <el-option
            v-for="item in clusterOptions"
            :key="item.name"
            :label="item.name + ' (' + item.description + ')'"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="环境">
        <el-select v-model="searchForm.namespace" placeholder="选择运行环境" style="width: 100%" filterable>
          <el-option
            key=""
            label="所有"
            value="">
          </el-option>
          <el-option
            v-for="item in namespaceOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用名">
        <el-select v-model="searchForm.app" filterable placeholder="请选择应用" style="width: 280px;" clearable>
          <el-option
            v-for="item in apps"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" icon="el-icon-circle-plus-outline" @click="showEditDialog(null)" style="margin-left: 20px;">新建</el-button>
        <export-button :table-ref="this.$refs.table001"></export-button>
      </el-form-item>
    </el-form>

    <table style="color: rgb(119, 119, 119);font-size: 12px;padding: 10px;">
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">扩容机制:</th>
        <td style="width: 1000px;">在扩容时间段之内，如果【当前运行副本数】小于【扩容副本数】，则一次性扩容到【扩容副本数】</td>
      </tr>
      <tr>
        <th style="width: 70px;text-align: left;vertical-align: top;">缩容机制:</th>
        <td>在扩容时间段之外，如果【当前运行副本数】大于【发布流程副本数】并且应用当前的CPU使用率小于80%，则缩容一个实例。五分钟后继续检测，满足条件则再缩容一个实例，直到【当前运行副本数】等于【发布流程副本数】</td>
      </tr>
    </table>
    <el-table
      ref="table001"
      v-loading="tableLoading"
      :data="tableData"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名"
                       sortable
                       prop="app">
      </el-table-column>
      <el-table-column label="运行环境"
                       prop="namespace">
      </el-table-column>
      <el-table-column label="所在集群"
                       prop="cluster">
      </el-table-column>
      <el-table-column label="扩容时间段" sortable prop="startTime">
        <template slot-scope="scope">
          {{ scope.row.startTime }} 至 {{ scope.row.endTime }} (每天）
        </template>
      </el-table-column>
      <el-table-column label="副本数" prop="replicas" align="center">
        <el-table-column label="发布流程" width="100" align="center" prop="pipeReplicas">
        </el-table-column>
        <el-table-column label="当前运行" width="100" align="center" prop="currReplicas">
        </el-table-column>
        <el-table-column label="扩容" width="100" align="center" prop="replicas">
        </el-table-column>
      </el-table-column>
      <el-table-column label="修改人"
                       prop="author">
      </el-table-column>
      <el-table-column label="操作"
                       width="240px">
        <template slot-scope="scope">
          <el-button
            style="margin-right: 5px"
            type="text"
            icon="el-icon-edit" i
            @click="showEditDialog(scope.row)"
            slot="reference">编辑
          </el-button>
          <el-popconfirm :title="'确定要删除吗？'" @confirm="deleteCron(scope.row)">
            <el-button
              style="margin-right: 5px"
              type="text"
              icon="el-icon-delete"
              slot="reference">删除
            </el-button>
          </el-popconfirm>
          <el-button
            type="text"
            class="el-icon-menu"
            @click="podPage(scope.row)">实例管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="创建/编辑扩缩容配置" :visible.sync="dialogVisible" width="700px">
      <el-form :model="editForm" ref="dialogEditForm" label-width="120px" :rules="editFormRules">
        <el-form-item label="应用" prop="app">
          <el-select v-model="editForm.app" filterable style="width: 100%" :disabled="editFormAppEditDisable">
            <el-option
              v-for="value in appOptions"
              :key="value"
              :label="value"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运行环境">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-form-item prop="cluster">
                <el-input v-model="editForm.cluster" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="namespace">
                <el-input v-model="editForm.namespace" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="扩容时间段">
          <el-time-select
            style="width: 160px"
            placeholder="起始时间"
            v-model="editForm.startTime"
            :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:00'
              }">
          </el-time-select>
          <span style="display: inline-block;padding: 0 5px;">至</span>
          <el-time-select
            placeholder="结束时间"
            style="width: 160px"
            v-model="editForm.endTime"
            :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:30',
                minTime: editForm.startTime
             }">
          </el-time-select>
        </el-form-item>
        <el-form-item label="副本数扩容到" prop="replicas">
          <el-input-number v-model.number="editForm.replicas"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createCron()" v-loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getAllAppName} from "@/api/app";
import {cloneObject} from "@/utils/my-util";
import {createCronScale, deleteCronScale, searchCronScale} from "@/api/k8s/scale";
import ExportButton from "@/views/components/export-button.vue";

export default {
  components: {ExportButton},
  mounted() {
    if(this.$route.query.cluster) {
      this.searchForm.cluster = this.$route.query.cluster;
    }else {
      this.searchForm.cluster = this.clusterOptions[0].name;
    }
    if(this.$route.query.namespace) {
      this.searchForm.namespace = this.$route.query.namespace;
    }
    if(this.$route.query.app) {
      this.searchForm.app = this.$route.query.app;
    }
    if(this.searchForm.app) {
      this.loadTableData();
    }
    getAllAppName().then(response => {
      this.apps = response.data;
    }).catch((e) => {
      this.$message.error("加载应用数据出错！ " + e.message);
    });
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    }
  },
  data() {
    return {
      searchEnv: "",
      apps:[],
      searchForm: {
        cluster: "",
        namespace: "",
        app: ""
      },
      editForm: {},
      editFormAppEditDisable: false,
      editFormRules: {
        app: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cluster: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        namespace: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        timeRange: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        replicas: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ]
      },
      tableData: [],
      tableLoading: false,
      dialogVisible: false,
      submitLoading: false,
      appOptions: []
    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchCronScale(this.searchForm).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    editFormReset() {
      this.editForm = {
        app: "",
        cluster: this.searchForm.cluster,
        namespace: this.searchForm.namespace,
        replicas: 2,
        startTime: "07:00",
        endTime: "13:00",
      }
    },
    showEditDialog(row) {
      if (this.appOptions.length < 1) {
        getAllAppName().then(response => {
          this.appOptions = response.data
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
      if (!row) {
        if (!this.searchForm.cluster || !this.searchForm.namespace) {
          this.$message.warning("请选选择集群和环境");
          return;
        }
        this.editFormReset();
        this.editFormAppEditDisable = false
      } else {
        this.editForm = cloneObject(row)
        this.editFormAppEditDisable = true
      }
      this.dialogVisible = true;
    },
    deleteCron(row) {
      deleteCronScale(row.id).then(response => {
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    clusterChange() {
      this.searchForm.namespace = ""
    },
    createCron() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        if (!this.editForm.startTime || !this.editForm.endTime) {
          this.$message.error("输入扩容时间段")
          return
        }
        this.submitLoading = true
        createCronScale(this.editForm).then(response => {
          this.dialogVisible = false
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.submitLoading = false
        })
      });
    },
    podPage(row) {
      let rou = this.$router.resolve({
        name: 'pod-index', query: {
          "cluster": row.cluster,
          "namespace": row.namespace,
          "app": row.app,
        }
      });
      window.open(rou.href, '_blank');
    }
  }
}
</script>
