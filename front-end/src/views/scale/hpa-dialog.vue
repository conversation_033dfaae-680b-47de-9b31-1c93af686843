<template>
  <el-dialog :title="title" :visible.sync="showDialog" v-if="showDialog">
    <el-form :model="form">
      <el-form-item label="应用" :label-width="formLabelWidth">
        <el-select v-model="appValue" filterable placeholder="请选择应用" @change="findPipelinesByApp">
          <el-option
            v-for="value in appOptions"
            :key="value"
            :label="value"
            :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发布流程" :label-width="formLabelWidth">
        <el-select v-model="pipelineValue" filterable placeholder="请选择">
          <el-option
            v-for="value in pipelineOptions"
            :key="value.id"
            :label="value.cluster + ' / ' + value.namespace + ' (' + value.namespace + ')'"
            :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="CPU使用率超过" :label-width="formLabelWidth">
        <el-input v-model.number="form.targetCPUUtilizationPercentage" placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item label="最大扩容到" :label-width="formLabelWidth">
        <el-input v-model.number="form.maxReplicas" placeholder="请输入内容"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="createHpa">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getAllAppName} from "@/api/app";
import {findPipelinesByApp} from "@/api/pipeline";
import {createHpa} from "@/api/k8s/hpa";

export default {
  name: "HpaDialog",
  props: {},
  data() {
    return {
      form: {
        targetCPUUtilizationPercentage: null,
        maxReplicas: null
      },
      formLabelWidth: "120px",
      // 父组件向子组件是单向传递，所以这里复制一下
      showDialog: false,
      title: "新增",
      appOptions: [],
      appValue: null,
      pipelineOptions: [],
      pipelineValue: null,
    }
  },
  mounted() {
  },
  methods: {
    open(id) {
      this.showDialog = true
      this.getApp()

      if (id !== undefined) {
        this.title = "编辑"
      }
    },
    getApp() {
      getAllAppName().then(response => {
        this.appOptions = response.data
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    findPipelinesByApp() {
      findPipelinesByApp(this.appValue).then(response => {
        this.pipelineOptions = response.data.pipelines
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    createHpa() {
      this.form.name = this.appValue
      this.form.cluster = this.pipelineValue.cluster
      this.form.namespace = this.pipelineValue.namespace
      createHpa(this.form).then(response => {
        this.$message.success("创建成功")
        this.$emit('loadTableData')
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(
        this.showDialog = false
      )
    }
  }
}
</script>

<style scoped>

</style>
