<template>
  <div class="app-container" v-loading="pageLoading">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData"
             @submit.native.prevent>
      <el-form-item label="集群">
        <el-select v-model="searchForm.cluster" placeholder="选择k8s集群" style="width: 100%;" @change="clusterChange"
                   filterable>
          <el-option v-for="item in clusterOptions" :key="item.name" :label="item.name + ' (' + item.description + ')'"
                     :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="环境">
        <el-select v-model="searchForm.namespace" placeholder="选择运行环境" style="width: 100%" filterable>
          <el-option key="" label="所有" value="">
          </el-option>
          <el-option v-for="item in namespaceOptions" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用名">
        <el-select v-model="searchForm.app" filterable placeholder="请选择应用" style="width: 280px;" clearable>
          <el-option v-for="item in apps" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="loadTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" icon="el-icon-circle-plus-outline" @click="showEditDialog(null)"
                   style="margin-left: 20px;">新建
        </el-button>
        <export-button :table-ref="this.$refs.table001"></export-button>
        <el-dropdown @command="handleMoreCommand" style="margin-left: 40px;">
          <span>
            更多操作<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="migrateConfig">迁移</el-dropdown-item>
            <el-dropdown-item command="addScaleForCoreAppDialog">补充核心服务配置</el-dropdown-item>
            <el-dropdown-item command="scaleUpAllHours">修改扩容时间为全天</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
    </el-form>

    <el-table ref="table001" v-loading="tableLoading" :data="tableData" element-loading-text="Loading" border fit
              highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名" prop="metadata.name">
      </el-table-column>
      <el-table-column label="运行环境" prop="metadata.namespace">
        <template slot-scope="scope">
          <div style="font-weight: bold;">
            {{ scope.row.metadata.namespace }}
          </div>
          <div style="font-size: 10px;color: #999;">
            集群：{{ scope.row.cluster }}
          </div>
          <div style="font-size: 10px;color: #999;"
               v-if="scope.row.metadata.annotations['fxiaoke.com/last-modify-user']">
            最后修改人 {{ scope.row.metadata.annotations['fxiaoke.com/last-modify-user'] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.spec.paused" type="success" size="mini" style="font-weight: bold;">
            启用
          </el-tag>
          <el-tag v-else type="warning" size="small" style="font-weight: bold;">
            停用
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="扩容触发器" width="180" prop="cpuTargetPercent">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 16px;">
            <div class="table-item">
              CPU使用率：{{ scope.row.spec.triggers[0].metadata.value }}%
            </div>
            <div class="table-item">
              持续时间(秒)：{{ scope.row.spec.scaleUp.stabilizationWindowSeconds }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="扩缩配置">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 16px;">
            <div class="table-item">
              最大副本：{{ scope.row.spec.maxReplicaCount }}
            </div>
            <div class="table-item">
              扩容步长：{{ scope.row.spec.scaleUp.replicaStep }}
            </div>
            <div class="table-item">
              时间窗口：{{ scope.row.spec.scaleUp.hourWindowDesc }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="缩容配置">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 16px;">
            <div class="table-item">
              最小副本：{{ scope.row.spec.minReplicaCount }}
            </div>
            <div class="table-item">
              缩容步长：{{ scope.row.spec.scaleDown.replicaStep }}
            </div>
            <div class="table-item">
              时间窗口：{{ scope.row.spec.scaleDown.hourWindowDesc }}
            </div>
            <div class="table-item">
              缩容间隔时长(秒)：{{ scope.row.spec.scaleDown.stabilizationWindowSeconds }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="执行状态">
        <template slot-scope="scope">
          <div style="font-size: 12px;line-height: 16px;">
            当前副本：{{ scope.row.status.currentReplica || '0' }}<br/>
            扩容次数：{{ scope.row.status.scaleUpCount || '0' }}<br/>
            缩容次数：{{ scope.row.status.scaleDownCount || '0' }}<br/>
            最近扩容时间： {{ scope.row.status.lastScaleUpTime || '--' }}<br/>
            最近缩容时间： {{ scope.row.status.lastScaleDownTime || '--' }}<br/>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200px">
        <template slot-scope="scope">
          <el-button style="font-size: 16px;color:#67c23a" type="text"
                     @click="monitorGrafanaPage(searchForm.cluster, scope.row.metadata.namespace, scope.row.metadata.name)"
                     size="mini">监控分析
          </el-button>
          <el-button type="text" style="margin-left: 0;" size="mini" @click="showEditDialog(scope.row)">编辑
          </el-button>
          <el-popconfirm :title="'确定要删除吗？'" @confirm="deleteItem(scope.row)">
            <el-button style="color: orangered" type="text" size="mini" slot="reference">删除
            </el-button>
          </el-popconfirm>
          <br/>
          <el-button type="text" @click="showDetailDialog(scope.row)" size="mini">配置详情
          </el-button>
          <router-link :to="{ name: 'pipeline-index', query: { 'app': scope.row.metadata.name } }" target="_blank">
            <i style="color:#409EFF;font-weight: 500;font-size: 12px;font-style:normal;">发布流程</i>
          </router-link>
          <router-link
            :to="{ name: 'pod-index', query: { 'cluster': scope.row.cluster, 'namespace': scope.row.metadata.namespace, 'app': scope.row.metadata.name } }"
            target="_blank">
            <i style="color:#409EFF;font-weight: 500;font-size: 12px;font-style:normal;">实例管理</i>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="扩缩容编辑" :visible.sync="dialogVisible" width="700px">
      <el-form :model="editForm" ref="dialogEditForm" label-width="140px" :rules="editFormRules"
               class="pod-auto-scaler-edit-form">
        <el-form-item label="禁用">
          <el-switch v-model="editForm.paused"></el-switch>
        </el-form-item>
        <el-form-item label="集群">
          <el-form-item prop="cluster">
            <el-select v-model="editForm.cluster" filterable style="width: 100%" :disabled="editFormAppEditDisable">
              <el-option v-for="item in clusterOptions" :key="item.name"
                         :label="item.name + ' (' + item.description + ')'" :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="命名空间" prop="namespace">
          <el-form-item prop="namespace">
            <el-select v-model="editForm.namespace" filterable style="width: 100%" :disabled="editFormAppEditDisable">
              <el-option v-for="item in namespaceOptions" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="应用" prop="app">
          <el-select v-model="editForm.app" filterable style="width: 100%" :disabled="editFormAppEditDisable">
            <el-option v-for="value in appOptions" :key="value" :label="value" :value="value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="cpu使用率阈值">
          <el-select v-model="editForm.cpuTargetPercent" style="width: 100%;">
            <el-option v-for="item in cpuPercentOptions" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="持续时间" prop="scaleUpStabilizationWindowSeconds">
          <el-input-number v-model="editForm.scaleUpStabilizationWindowSeconds" :min="30" :max="300"></el-input-number>
        </el-form-item>
        <el-form-item label="扩容步长" prop="scaleUpReplicaStep">
          <el-input-number v-model="editForm.scaleUpReplicaStep" :min="2" :max="20"></el-input-number>
        </el-form-item>
        <el-form-item label="最小副本数" prop="maxReplicas">
          <el-input-number v-model.number="editForm.minReplicas" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="最大副本数" prop="maxReplicas">
          <el-input-number v-model.number="editForm.maxReplicas" :min="4" :max="20"></el-input-number>
        </el-form-item>
        <el-form-item label="缩容步长">
          <el-input-number v-model="editForm.scaleDownReplicaStep" :min="1" :max="5" disabled></el-input-number>
          <span style="color: orangered">（暂由系统分配）</span>
        </el-form-item>
        <el-form-item label="扩容时间窗口">
          <el-input v-model="editForm.scaleUpHourWindow" disabled style="width: 180px;"></el-input>
          <span style="color: orangered">（暂由系统分配）</span>
        </el-form-item>
        <el-form-item label="缩容时间窗口">
          <el-input v-model="editForm.scaleDownHourWindow" disabled style="width: 180px;"></el-input>
          <span style="color: orangered">（暂由系统分配）</span>
        </el-form-item>
        <el-form-item label="缩容间隔时长(秒)">
          <el-input-number v-model="editForm.scaleDownStabilizationWindowSeconds" :min="120" :max="36000"
                           style="width: 180px;" :step="60"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createItem()" v-loading="pageLoading">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="detailDialog.title" :visible.sync="detailDialog.visible" width="50%" top="5vh">
      <div style="margin-top: -20px;overflow: auto;">
        <pre
          style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">
      {{ detailDialog.content }}</pre>
      </div>
    </el-dialog>

    <el-dialog title="修改时间窗口为全天的命令" :visible.sync="scaleUpAllHoursDialogVisible" width="50%">
      <div>
        <p>1. 修改单个服务</p>
        <div style="padding: 10px;background-color: #eee;">
          kubectl patch podautoscaler [app] -n [namesapce] --type='json'
          -p='[{"op":"replace","path":"/spec/scaleUp/hourWindow","value":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}]'
        </div>
        <p>2. 生成某个集群的批量修改命令</p>
        <div style="padding: 10px;background-color: #eee;">
          kubectl get pas --all-namespaces --no-headers | while read ns name _; do<br/>
          echo "kubectl patch podautoscaler -n $ns $name --type='json'
          -p='[{\"op\":\"replace\",\"path\":\"/spec/scaleUp/hourWindow\",\"value\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}]'"<br/>
          done
        </div>
      </div>
    </el-dialog>

    <el-dialog title="增加核心服务的自动扩缩容" :visible.sync="addScaleForCoreAppDialogVisible" width="40%">
      <div>
        <p style="line-height: 20px;color:orangered;margin-top:-30px;margin-left: 30px;">
          说明: 如果满足服务级别的应用在集群中没有自动扩缩容配置，则使用默认配置初始化扩缩容配置。只有管理员才有权限操作
        </p>
        <el-form :model="addScaleForCoreAppForm" ref="addScaleForCoreAppForm" label-width="120px">
          <el-form-item label="集群">
            <el-input v-model="addScaleForCoreAppForm.cluster" disabled></el-input>
          </el-form-item>
          <el-form-item label="命名空间">
            <el-input v-model="addScaleForCoreAppForm.namespace" disabled></el-input>
          </el-form-item>
          <el-form-item label="服务级别">
            <el-select v-model="addScaleForCoreAppForm.serviceLevel" style="width: 100%;">
              <el-option label="L0" value="L0"></el-option>
              <el-option label="L1" value="L1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="addScaleForCoreAppDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addScaleForCoreApp" v-loading="addScaleForCoreAppBtnLoading">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getAllAppName} from "@/api/app";
import {createPodAutoScaler, createPodAutoScalerForCoreApp, deletePodAutoScaler, migratePodAutoScaler, searchPodAutoScaler} from "@/api/k8s/scale";
import ExportButton from "@/views/components/export-button.vue";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  components: {ClipboardIcon, ExportButton},
  mounted() {
    if (this.$route.query.cluster) {
      this.searchForm.cluster = this.$route.query.cluster;
    } else {
      this.searchForm.cluster = this.clusterOptions[0].name;
    }
    if (this.$route.query.namespace) {
      this.searchForm.namespace = this.$route.query.namespace;
    }
    if (this.$route.query.app) {
      this.searchForm.app = this.$route.query.app;
    }
    if (this.searchForm.app) {
      this.loadTableData();
    }
    getAllAppName().then(response => {
      this.apps = response.data;
    }).catch((e) => {
      this.$message.error("加载应用数据出错！ " + e.message);
    });
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      if (this.searchForm.cluster) {
        for (let clu of this.$settings.clusters) {
          if (this.searchForm.cluster === clu.name) {
            return clu.namespaces;
          }
        }
      }
      return []
    },
    cpuPercentOptions: function () {
      let ret = [];
      for (let i = 50; i <= 100; i = i + 5) {
        ret.push({
          name: i + "%",
          value: i
        })
      }
      return ret;
    }
  },
  data() {
    return {
      searchEnv: "",
      apps: [],
      searchForm: {
        cluster: "",
        namespace: "",
        app: ""
      },
      editForm: {},
      detailDialog: {
        visible: false,
        title: "配置详情",
        content: "--",
      },
      editFormAppEditDisable: false,
      editFormRules: {
        app: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cluster: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        namespace: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        minReplicas: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        maxReplicas: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        cpuTargetPercent: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        scaleUpStabilizationWindowSeconds: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
        scaleUpReplicaStep: [
          {required: true, message: '值不能为空', trigger: 'blur'},
        ],
      },
      tableData: [],
      tableLoading: false,
      dialogVisible: false,
      pageLoading: false,
      appOptions: [],
      monitorLogVisible: false,
      monitorLogTableData: [],
      scaleUpAllHoursDialogVisible: false,
      addScaleForCoreAppDialogVisible: false,
      addScaleForCoreAppBtnLoading: false,
      addScaleForCoreAppForm: {
        cluster: "",
        namespace: "",
        serviceLevel: ""
      }

    }
  },
  methods: {
    loadTableData() {
      this.tableLoading = true;
      searchPodAutoScaler(this.searchForm).then(response => {
        let items = response.data
        for (let item of items) {
          item.cluster = this.searchForm.cluster;
        }
        this.tableData = items;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    editFormReset() {
      this.editForm = {
        app: "",
        cluster: this.searchForm.cluster,
        namespace: this.searchForm.namespace,
        paused: this.searchForm.paused,
        cpuTargetPercent: 70,
        scaleUpStabilizationWindowSeconds: 60,
        scaleUpReplicaStep: 3,
        scaleDownReplicaStep: 1,
        scaleUpHourWindow: [],
        scaleDownHourWindow: [],
        scaleDownStabilizationWindowSeconds: 600,
        minReplicas: 2,
        maxReplicas: 4,
      }
    },
    showDetailDialog(row) {
      let content = JSON.stringify(row, null, 2)
      this.detailDialog.content = content
      this.detailDialog.visible = true
    },
    monitorGrafanaPage(cluster, namespace, app) {
      let url = `/api/page/redirect?type=grafana&dashboard=pod-auto-scaler&cluster=${cluster}&app=${app}&namespace=${namespace}`
      window.open(url)
    },
    showEditDialog(row) {
      if (this.appOptions.length < 1) {
        getAllAppName().then(response => {
          this.appOptions = response.data
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
      if (!row) {
        this.editFormReset();
        this.editFormAppEditDisable = false
      } else {
        this.editForm = {
          app: row.metadata.name,
          cluster: row.cluster,
          namespace: row.metadata.namespace,
          paused: row.spec.paused,
          cpuTargetPercent: parseInt(row.spec.triggers[0].metadata.value),
          scaleUpStabilizationWindowSeconds: row.spec.scaleUp.stabilizationWindowSeconds,
          scaleUpReplicaStep: row.spec.scaleUp.replicaStep,
          minReplicas: row.spec.minReplicaCount,
          maxReplicas: row.spec.maxReplicaCount,
          scaleDownReplicaStep: row.spec.scaleDown.replicaStep,
          scaleUpHourWindow: row.spec.scaleUp.hourWindow,
          scaleDownHourWindow: row.spec.scaleDown.hourWindow,
          scaleDownStabilizationWindowSeconds: row.spec.scaleDown.stabilizationWindowSeconds,
        }
        this.editFormAppEditDisable = true
      }
      this.dialogVisible = true;
    },
    deleteItem(row) {
      let param = {
        cluster: row.cluster,
        namespace: row.metadata.namespace,
        app: row.metadata.name
      }
      deletePodAutoScaler(param).then(response => {
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    migrateConfig() {
      let clu = this.searchForm.cluster;
      this.$prompt('请输入集群', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: clu,
      }).then(({value}) => {
        this.pageLoading = true
        migratePodAutoScaler(value).then(response => {
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.pageLoading = false
        })
      }).catch(() => {

      });
    },
    scaleUpAllHours() {
      this.scaleUpAllHoursDialogVisible = true
    },
    addScaleForCoreAppDialog() {
      this.addScaleForCoreAppForm.cluster = this.searchForm.cluster
      this.addScaleForCoreAppForm.namespace = this.searchForm.namespace
      if (!this.addScaleForCoreAppForm.cluster || !this.addScaleForCoreAppForm.namespace) {
        this.$message.error("集群和环境不能为空")
        return
      }
      this.addScaleForCoreAppForm.serviceLevel = "L0"
      this.addScaleForCoreAppDialogVisible = true
    },
    addScaleForCoreApp() {
      this.addScaleForCoreAppBtnLoading = true
      createPodAutoScalerForCoreApp(this.addScaleForCoreAppForm).then(response => {
        this.addScaleForCoreAppDialogVisible = false
        this.$message.success("操作成功")
        this.loadTableData()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.addScaleForCoreAppBtnLoading = false
      })
    },
    handleMoreCommand(command) {
      switch (command) {
        case "migrateConfig":
          this.migrateConfig();
          break;
        case "addScaleForCoreAppDialog":
          this.addScaleForCoreAppDialog();
          break;
        case "scaleUpAllHours":
          this.scaleUpAllHours();
          break;
        default:
          this.$message.error('未知操作：' + command);
          break;
      }
    },
    clusterChange() {
      this.searchForm.namespace = ""
    },
    createItem() {
      this.$refs['dialogEditForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        // 处理scaleUpHourWindow，将字符串转换为数字数组
        if (typeof this.editForm.scaleUpHourWindow === 'string' && this.editForm.scaleUpHourWindow.trim() !== '') {
          try {
            // 通过逗号分割字符串，并将每个部分转换为数字
            this.editForm.scaleUpHourWindow = this.editForm.scaleUpHourWindow.split(',')
              .map(item => parseInt(item.trim(), 10))
              .filter(num => !isNaN(num)); // 过滤掉非数字的值
          } catch (error) {
            this.$message.warning('扩容时间窗口格式不正确，请使用逗号分隔的数字');
            return false;
          }
        }

        // 处理scaleDownHourWindow，将字符串转换为数字数组
        if (typeof this.editForm.scaleDownHourWindow === 'string' && this.editForm.scaleDownHourWindow.trim() !== '') {
          try {
            // 通过逗号分割字符串，并将每个部分转换为数字
            this.editForm.scaleDownHourWindow = this.editForm.scaleDownHourWindow.split(',')
              .map(item => parseInt(item.trim(), 10))
              .filter(num => !isNaN(num)); // 过滤掉非数字的值
          } catch (error) {
            this.$message.warning('缩容时间窗口格式不正确，请使用逗号分隔的数字');
            return false;
          }
        }
        this.pageLoading = true
        createPodAutoScaler(this.editForm).then(response => {
          this.dialogVisible = false
          this.$message.success("操作成功")
          this.loadTableData()
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.pageLoading = false
        })
      });
    }
  }
}
</script>

<style>
.pod-auto-scaler-edit-form .el-form-item {
  margin-bottom: 5px;
}
</style>
