<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="tabClick">
      <el-tab-pane label="集群列表" name="k8s-cluster" :lazy="true">
        <k8s-cluster></k8s-cluster>
      </el-tab-pane>
      <el-tab-pane label="宿主机资源使用" name="cluster-resource" :lazy="true">
        <cluster-resource></cluster-resource>
      </el-tab-pane>
      <el-tab-pane label="服务资源分配" name="app-resource" :lazy="true">
        <app-resource></app-resource>
      </el-tab-pane>
      <el-tab-pane label="Pod状态" name="pod-status" :lazy="true">
        <pod-status></pod-status>
      </el-tab-pane>
      <el-tab-pane label="事件列表" name="cluster-events" :lazy="true">
        <k8s-event></k8s-event>
      </el-tab-pane>
      <el-tab-pane label="收集预热镜像" name="image-preheat-manager" :lazy="true">
        <image-preheat-manager></image-preheat-manager>
      </el-tab-pane>
      <el-tab-pane label="镜像预热任务" name="image-preheat-job-list" :lazy="true">
        <image-preheat-job-list></image-preheat-job-list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ClusterResource from "@/views/cluster/cluster-resource";
import AppResource from "@/views/cluster/app-resource";
import PodStatus from "@/views/cluster/pod";
import K8sCluster from "@/views/cluster/k8s";
import K8sEvent from "@/views/cluster/event";
import {cloneObject} from "@/utils/my-util";
import ImagePreheatManager from "@/views/cluster/image-preheat-manager.vue";
import ImagePreheatJobList from "@/views/cluster/image-preheat-job-list.vue";

export default {
  components: {ImagePreheatJobList, ImagePreheatManager, K8sEvent, K8sCluster, PodStatus, ClusterResource, AppResource},
  mounted() {
    let tab = this.$route.query.activeTab;
    if (tab) {
      this.activeTab = tab;
    }
  },
  computed: {},
  data() {
    return {
      activeTab: "k8s-cluster"
    }
  },
  methods: {
    tabClick(tab) {
      let urlParam = cloneObject(this.$route.query)
      urlParam["activeTab"] = tab.name;
      this.$router.push({query: urlParam});
    }
  }
}
</script>
