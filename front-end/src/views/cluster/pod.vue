<template>
  <div v-loading="loading">
    <div>
      <env-selector-form :show-all-namespaces="false" @submitHandler="loadTableData" display="inline"></env-selector-form>
      <export-button :table-ref="this.$refs.table001"></export-button>
    </div>
    <el-card class="box-card">
      <el-table
        :data="this.tableData"
        row-key="name"
        :default-sort="{prop: 'statusDesc', order: 'ascending'}"
      >

        <el-table-column label="名称" prop="name" width="360" sortable>
        </el-table-column>
        <el-table-column label="状态" sortable sort-by="statusDesc" prop="statusDesc"
                         :filters="statusFilterData" :filter-method="filterStatus">
          <template slot-scope="scope">
            <span :class="podStatusClass(scope.row.statusDesc)"></span>
            {{ scope.row.statusDesc }}
          </template>
        </el-table-column>
        <el-table-column label="运行环境" prop="namespace">
        </el-table-column>
        <el-table-column label="运行版本" prop="deployTag" show-overflow-tooltip min-width="200">
        </el-table-column>
        <el-table-column label="重启数/最近重启" prop="restartCount" sortable>
          <template slot-scope="scope">
            <div>{{scope.row.restartCount}}</div>
            <div v-if="scope.row.restartCount > 0" style="font-size: 12px;color: #888;">{{scope.row.restartTime}}</div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" sortable prop="createTime" min-width="110">
        </el-table-column>
        <el-table-column width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              class="el-icon-menu"
              @click="podPage(scope.row)">实例管理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
<script>

import {getPodsByEnv} from "@/api/k8s/pod";
import EnvSelectorForm from "@/views/components/env-selector-form.vue";
import ExportButton from "@/views/components/export-button.vue";

export default {
  name: "podStatus",
  data() {
    return {
      loading: false,
      statusFilterData: [
        {"text": "调度中", value: "调度中"},
        {"text": "准备中", value: "准备中"},
        {"text": "启动中", value: "启动中"},
        {"text": "运行中", value: "运行中"},
        {"text": "关闭中", value: "关闭中"},
        {"text": "容器关闭中", value: "容器关闭中"},
        {"text": "不健康", value: "不健康"},
        {"text": "已关闭", value: "已关闭"},
        {"text": "未知状态", value: "未知状态"},
      ],
      tableData: []
    }
  },
  components: {ExportButton, EnvSelectorForm},
  mounted() {
  },
  computed: {
  },
  methods: {
    loadTableData: function (cluster, namespace) {
      this.loading = true;
      getPodsByEnv(cluster, namespace).then(response => {
        let newDate = []
        for(let it of response.data) {
          //过滤掉一些job创建的pod,避免形成干扰
          if(it.statusDesc === "已关闭") {
            continue
          }
          newDate.push(it)
        }
        this.tableData = newDate
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false;
      })
    },
    podPage(row) {
      let rou = this.$router.resolve({
        name: 'pod-index', query: {
          "cluster": row.cluster,
          "namespace": row.namespace,
          "app": row.labelApp,
        }
      });
      window.open(rou.href, '_blank');
    },
    filterStatus(value, row) {
      return row.statusDesc === value;
    },
    podStatusClass(podStatus) {
      if (podStatus) {
        if (podStatus === "运行中") {
          return "pod-status-green"
        } else if (["调度中", "准备中", "启动中"].includes(podStatus)) {
          return "pod-status-orange"
        }
      }
      return "pod-status-red"
    },
  }
}
</script>

<style>
.pod-status-green {
  background: #69c993;
  box-shadow: 0 4px 10px rgb(87 177 126 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-red {
  background: #d15352;
  box-shadow: 0 4px 10px rgb(209 83 82 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}

.pod-status-orange {
  background: #E6A23C;
  box-shadow: 0 4px 10px rgb(230 162 60 / 30%);
  margin-right: 3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block
}
</style>

