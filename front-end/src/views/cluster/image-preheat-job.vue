<template>
  <div class="image-preheat" v-loading="loading">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">{{ this.clusterObj.name }} | </span>
        <div style="display: inline-block;margin-left: 10px;">
          <span v-if="this.clusterObj.needImagePreheat" style="color: green;font-size: 12px;">需要预热</span>
          <span v-else style="color: red;font-size: 12px;">无需预热</span>
        </div>
        <el-tooltip placement="top" effect="light">
          <div slot="content" style="line-height: 20px;">
            <div>需要预热: 表示有自己独立的镜像缓存库,大版本发布需要做预热镜像</div>
            <div>无需预热: 表示使用公共镜像缓存库,大版本发布不需要做预热镜像</div>
          </div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <span style="float: right;">
          <el-button icon="el-icon-refresh" type="text" size="small" @click="loadTable">刷新</el-button>
          <el-button icon="el-icon-delete" type="text" size="small" @click="removeAllJobs">删除所有</el-button>
          <el-tooltip placement="top" effect="light" content="把本环境在用的镜像预热到镜像缓存库">
            <el-button type="text" size="small" @click="imagePreheatInCurrCluster">使用本环境镜像预热<i class="el-icon-warning-outline"></i></el-button>
          </el-tooltip>
        </span>
      </div>
      <div>
        <el-table
          :data="this.tableData"
          row-key="name"
          size="small"
        >
          <el-table-column label="name" prop="name" min-width="220"/>
          <el-table-column label="status" prop="status">
            <template slot-scope="scope">
                <span style="font-weight: bold;color:black">{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column label="restarts" prop="restarts"/>
          <el-table-column label="age" prop="age"/>
          <el-table-column label="" width="90">
            <template slot-scope="scope">
              <el-button type="text" @click="podDetailDialog(scope.row.name)">详情</el-button>
              <el-button type="text" @click="jobRemove(scope.row.jobName)" style="color:#E6A23C;margin-left: 0;">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <el-dialog
      :title="podDetail.name"
      :visible.sync="podDetailVisible"
      top="5vh">
      <div style="margin: 5px auto;text-align: center;">
        <clipboard-icon :text="podDetail.json">一键复制</clipboard-icon>
      </div>
      <vue-json-pretty
        v-loading="podDetailLoading"
        :data="podDetail.data"
        style="max-height: 600px; overflow-y: auto"
      >
      </vue-json-pretty>
    </el-dialog>
  </div>
</template>
<script>

import {getPodDetail} from "@/api/k8s/pod";
import VueJsonPretty from 'vue-json-pretty'
import ClipboardIcon from "@/views/components/clipboard-icon.vue";
import {deleteImagePreheatJobs, preheatWithCurrClusterImage, searchImagePreheatJobs} from "@/api/image_preheat";

export default {
  name: "image-preheat-job",
  props: {
    cluster: {
      type: String,
      required: true
    },
    namespace: {
      type: String,
      default: "image-preheat"
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      podDetailVisible: false,
      podDetailLoading: false,
      podDetail: {
        name: "",
        data: {},
        json: "",
      },
    }
  },
  computed: {
    clusterObj() {
      for (let it of this.$settings.clusters) {
        if (it.name === this.cluster) {
          return it;
        }
      }
      return {};
    }
  },
  components: {ClipboardIcon, VueJsonPretty},
  mounted() {
    this.loadTable();
  },
  methods: {
    loadTable() {
      this.loading = true;
      searchImagePreheatJobs(this.cluster, this.namespace).then(response => {
        this.tableData = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false;
      })
    },
    podDetailDialog(pod) {
      this.podDetailVisible = true;
      if (this.podDetail.name === pod && this.podDetail.data) {
        return;
      }
      this.podDetailLoading = true;
      getPodDetail(this.cluster, this.namespace, pod).then(response => {
        this.podDetailLoading = false;
        this.podDetail.name = pod;
        this.podDetail.data = response.data;
        this.podDetail.json = JSON.stringify(this.podDetail.data, null, 2);
      }).catch((e) => {
        this.$message.error(e.message);
        this.podDetailLoading = false;
      })
    },
    jobRemove(job) {
      this.$confirm(`确认删除? job：${job}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        const params = {
          cluster: this.cluster,
          namespace: this.namespace,
          names: [job]
        }
        deleteImagePreheatJobs(params).then(response => {
          this.$message.success('操作成功,将刷新数据');
          this.loadTable();
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false;
        })
      }).catch((e) => {

      });
    },
    imagePreheatInCurrCluster() {
      this.$confirm(`是否确认使用本环境镜像预热？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        preheatWithCurrClusterImage(this.cluster, 6).then(response => {
          this.$message.success('操作成功,将刷新数据');
          this.loadTable();
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false;
        })
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    removeAllJobs() {
      this.$confirm('确认删除所有Job?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteImagePreheatJobs({
          cluster: this.cluster,
          namespace: this.namespace,
          names: this.tableData.map(it => it.jobName)
        }).then(response => {
          this.$message.success('操作成功,将刷新数据');
          this.loadTable();
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading = false;
        })
      }).catch((e) => {

      });
    }
  }
}
</script>

<style>
.image-preheat .el-card__header {
  background-color: #ccc;
}
</style>

