<template>
  <div v-loading="loading">
    <div>
      <env-selector-form :show-all-namespaces="true" @submitHandler="loadEvents"></env-selector-form>
    </div>
    <el-card class="box-card">
      <el-timeline>
        <el-timeline-item
          placement="top"
          :hide-timestamp="true"
          :timestamp="item.lastTime ? item.lastTime: item.createTime"
          v-for="(item, index) in events"
          :key="index">
          <div>
            <span>{{ item.lastTime ? item.lastTime : item.createTime }}</span>
            <el-tag size="small" :type="item.type === 'Warning'? 'warning': 'info'" style="margin: 0 10px;">{{ item.type }}</el-tag>
            <span style="padding: 0 10px" v-if="showNamespace">{{ item.namespace }}</span>
            <span style="padding: 0 10px"> ( x{{ item.count }} )</span>
            <span style="padding-right: 10px;">{{ item.reason }}:</span> {{ item.message }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>
<script>

import {queryK8sEvents} from "@/api/k8s/event";
import EnvSelectorForm from "@/views/components/env-selector-form.vue";

export default {
  name: "k8s-event",
  data() {
    return {
      loading: false,
      showNamespace:false,
      events: []
    }
  },
  components: {EnvSelectorForm},
  mounted() {
  },
  computed: {},
  methods: {
    loadEvents: function (cluster, namespace) {
      this.loading = true;
      this.showNamespace = namespace === null || namespace === ""
      queryK8sEvents(cluster, namespace).then(response => {
        this.events = response.data
      }).catch((e) => {
        this.$message.error(e.message)
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style>

</style>

