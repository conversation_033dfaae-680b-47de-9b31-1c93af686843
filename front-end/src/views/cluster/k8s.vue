<template>
  <div class="k8s-cluster-container">
    <div class="show-field-wrapper">
      显示：
      <el-checkbox v-model="showFields.autoScaleV2">自动扩缩容</el-checkbox>
      <el-checkbox v-model="showFields.cronScale">定时扩缩容</el-checkbox>
      <el-checkbox v-model="showFields.imageRegistryProxy">镜像代理库</el-checkbox>
      <el-checkbox v-model="showFields.cloudCategory">云类型</el-checkbox>
      <el-checkbox v-model="showFields.namespaces">命名空间</el-checkbox>
      <el-checkbox v-model="showFields.prometheusDS">PrometheusDS</el-checkbox>
      <el-checkbox v-model="showFields.clickHouseDS">ClickHouseDS</el-checkbox>
      <el-checkbox v-model="showFields.pyroscopeDS">PyroscopeDS</el-checkbox>
      <export-button :table-ref="this.$refs.table001"></export-button>
    </div>
    <el-card class="box-card">
      <el-table
        ref="table001"
        :data="this.clusters"
        row-key="name"
        size="small"
      >
        <el-table-column
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="集群名" prop="name">
        </el-table-column>
        <el-table-column label="集群描述" prop="description">
        </el-table-column>
        <el-table-column label="版本号" prop="version">
        </el-table-column>
        <el-table-column label="状态" prop="disable" width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.enable" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else style="color: red;">关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="自动扩缩" v-if="showFields.autoScaleV2" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.autoScaleV2 === 'true'" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else-if="scope.row.autoScaleV2 === 'false'" style="color: red;">关闭</div>
            <div v-else>{{ scope.row.autoScaleV2 }}</div>
          </template>
        </el-table-column>
        <el-table-column label="定时扩缩" prop="cronScale" v-if="showFields.cronScale" width="90">
          <template slot-scope="scope">
            <div v-if="scope.row.cronScale" style="color: #14b614;font-weight: bold;">开启</div>
            <div v-else style="color: red;">关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="大版本预热?" prop="cronScale" width="90" v-if="showFields.imageRegistryProxy">
          <template slot-scope="scope">
            <div v-if="scope.row.needImagePreheat" style="color: #14b614;font-weight: bold;">需要</div>
            <div v-else style="color: red;">不需要</div>
          </template>
        </el-table-column>
        <el-table-column label="镜像库代理" prop="imageRegistryProxy" v-if="showFields.imageRegistryProxy">
        </el-table-column>
        <el-table-column label="云类型" prop="cloudCategory" v-if="showFields.cloudCategory">
        </el-table-column>
        <el-table-column label="PrometheusDS" prop="thirdServices.grafana.prometheusDS" v-if="showFields.prometheusDS">
        </el-table-column>
        <el-table-column label="ClickHouseDS" prop="thirdServices.grafana.clickHouseDS" v-if="showFields.clickHouseDS">
        </el-table-column>
        <el-table-column label="PyroscopeDS" prop="thirdServices.grafana.pyroscopeDS" v-if="showFields.pyroscopeDS">

        </el-table-column>
        <el-table-column label="命名空间" prop="namespaces" v-if="showFields.namespaces">
          <template slot-scope="scope">
            {{ scope.row.namespaces }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
<script>

import {cloneObject} from "@/utils/my-util";
import ExportButton from "@/views/components/export-button.vue";
import {getClustersAutoScalerV2} from "@/api/k8s/scale";

export default {
  name: "k8sCluster",
  data() {
    return {
      clusters: [],
      showFields: {
        autoScaleV2: true,
        cronScale: true,
        cloudCategory: false,
        namespaces: false,
        imageRegistryProxy: true,
        prometheusDS: false,
        clickHouseDS: false,
        pyroscopeDS: false,
      },
    }
  },
  components: {ExportButton},
  mounted() {
    for (let clu of this.$settings.clusters) {
      let item = cloneObject(clu);
      item["autoScaleV2"] = "--"
      this.clusters.push(item)
    }
    //加载集群的自动扩缩容V2状态
    getClustersAutoScalerV2().then(response => {
      for (let item of this.clusters) {
        if (response.data) {
          if (response.data[item.name]) {
            item["autoScaleV2"] = "true"
          } else {
            item["autoScaleV2"] = "false"
          }
        }
      }
    }).catch((e) => {
      console.error(e.message)
    }).finally(() => {
    });
  },
  computed: {},
  methods: {}
}
</script>

<style>
.k8s-cluster-container .show-field-wrapper {
  display: inline-block;
  margin-right: 10px;
  font-size: 12px;
}

.k8s-cluster-container .show-field-wrapper .el-checkbox {
  margin-right: 5px;
}

.k8s-cluster-container .show-field-wrapper .el-checkbox__label {
  padding-left: 2px;
  font-size: 12px;
}
</style>
