<template>
  <div class="help-doc">
    <div style="font-size: 14px;padding-bottom: 10px;">
      帮助文档
    </div>
    <div>
      <div class="help-doc-q">Q: 发布时，发布系统对发布版本号的处理机制是什么？</div>
      <div class="help-doc-a">
        A: 为了避免相同版本号的制品互相覆盖，遇到问题时能更好地回滚服务，发布系统对发布的版本号做了一些自动化的处理机制，机制如下图：<br/>
        <el-image
          style="max-width: 600px;"
          src="/images/deploy-version.svg"
          alt="发布版本号处理机制"
          :preview-src-list="['/images/deploy-version.svg']">
        </el-image>
      </div>
    </div>
    <div>
      <div class="help-doc-q">Q: 自动扩缩容机制</div>
      <div class="help-doc-a">
        <el-image
          style="max-width: 600px;"
          src="/images/auto-scale.svg"
          alt="自动扩缩容机制"
          :preview-src-list="['/images/auto-scale.svg']">
        </el-image>
      </div>
    </div>
    <div>
      <div class="help-doc-q">Q: 实例（pod）生命周期里的不同状态</div>
      <div class="help-doc-a">
        <el-image
          style="max-width: 600px;"
          src="/images/pod-status.svg"
          alt="pod状态"
          :preview-src-list="['/images/pod-status.svg']">
        </el-image>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'helpDoc',
  props: {},
  data() {
    return {}
  },

  computed: {},

  methods: {}
}
</script>

<style scoped>
.help-doc {
  line-height: 1em;
  padding: 20px 20px 10px 10px;
}

.help-doc-q {
  font-size: 14px;
  font-weight: bold;
  padding-top:20px;
  padding-bottom: 5px;
}
.help-doc-a {
  font-size: 12px;
}
</style>
