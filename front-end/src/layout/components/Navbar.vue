<template>
  <div class="navbar" :style="isProdEnv ? {backgroundColor: '#E6A23C'}:{}">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar"/>

    <breadcrumb class="breadcrumb-container" :style="isProdEnv ? {color: 'black'}:{}"/>
    <div class="prod-env-banner" v-if="isProdEnv">
      线上环境
    </div>
    <div class="right-menu">
      <el-button type="text" @click="docShow=true" style="font-size: 1.2em;margin-right: 30px">文档</el-button>
      <el-drawer
        :withHeader="false"
        :visible.sync="docShow"
        :wrapperClosable="true"
        size="100%"
        style="background:none;width: 100%;max-width: 1080px;left: auto;line-height: 1em;"
        direction="rtl">
        <el-button type="text" @click="docShow=false" style="position: absolute;right: 10px;">关闭</el-button>
        <help-doc></help-doc>
      </el-drawer>
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          {{ this.user.realName }} ({{ this.user.username }})
          <i class="el-icon-caret-bottom"/>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import HelpDoc from "@/layout/components/HelpDoc";

export default {
  components: {
    HelpDoc,
    Breadcrumb,
    Hamburger
  },
  data() {
    return {
      docShow: false,
    }
  },
  mounted() {
    this.$store.dispatch("user/sync")
  },
  computed: {
    ...mapGetters([
      'sidebar',
    ]),
    user: function () {
      return this.$store.state.user
    },
    isProdEnv() {
      console.log( window.location.host);
      return window.location.host.indexOf("foneshare") > -1;
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      top.location.href = "/"
    }
  }
}
</script>

<style lang="scss" scoped>

.prod-env-banner {
  position: fixed;
  margin: 0 auto;
  left: 0;
  right: 0;
  width: 160px;
  z-index: 9999;
  letter-spacing: 3px;
  text-align: center;
  padding-top: 12px;
  font-weight: bold;
  font-size: 24px;
  color: red;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

.hamburger-container {
  line-height: 46px;
  height: 100%;
  float: left;
  cursor: pointer;
  transition: background .3s;
  -webkit-tap-highlight-color: transparent;

&
:hover {
  background: rgba(0, 0, 0, .025)
}

}

.breadcrumb-container {
  float: left;
}

.right-menu {
  float: right;
  height: 100%;
  line-height: 50px;

&
:focus {
  outline: none;
}

.right-menu-item {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 18px;
  color: #5a5e66;
  vertical-align: text-bottom;

&
.hover-effect {
  cursor: pointer;
  transition: background .3s;

&
:hover {
  background: rgba(0, 0, 0, .025)
}

}
}

.avatar-container {
  margin-right: 30px;

.avatar-wrapper {
  margin-top: 5px;
  position: relative;

.user-avatar {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 10px;
}

.el-icon-caret-bottom {
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: 25px;
  font-size: 12px;
}

}
}
}
}
</style>
