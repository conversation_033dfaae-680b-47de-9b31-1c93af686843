/**
 * 拷贝数据对象
 * by wuzh
 * @param obj
 * @returns {any}
 */
export function cloneObject(obj) {
  return JSON.parse(JSON.stringify(obj))
}

export function timeFormat(time) {
  if (!time || !(time instanceof Date)) return "";
  let ret = time.getFullYear() + "-" + (time.getMonth() + 1) + "-" + time.getDate() + " " + time.getHours() + ":" + time.getMinutes() + ":" + time.getSeconds()
  return ret;
}

export function isBlank(str) {
  return (str === undefined || str == null || str.length <= 0);
}

export function pipelineOptionDesc(option) {
  if(option === "isCoreApp") {
    return "核心服务"
  }if(option === "onlyDeployTag") {
    return "只允许部署Tag"
  }if(option === "addSysctlKeepalive") {
    return "调整内核参数"
  }if(option === "skyWalkingAgent") {
    return "性能跟踪"
  }if(option === "appLogToKafka") {
    return "接入ClickHouse日志"
  }if(option === "buildUseRuntimeJDK") {
    return "镜像JDK版本编译代码"
  }if(option === "jvmGcLog") {
    return "GC日志"
  }else {
    return option
  }
}
