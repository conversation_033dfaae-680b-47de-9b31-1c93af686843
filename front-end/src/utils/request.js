import axios from 'axios'
import {Message} from 'element-ui'
import store from '@/store'
import {getToken} from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 300000 // request timeout， 当下载大文件时， 服务端的文件准备时间会比较长
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data;
    if (res.code !== 200) {
      console.log(res);
      if (res.code === 401) {
        axios.post('/api/shiro-cas/redirect-path?path=' + encodeURIComponent(window.location.href)).finally(() => {
          top.location.href = response.data.data;
        })
        if (res.data) {
          window.top.location.href = res.data
        }
      } else if (res.code === 301 || res.code === 302) {
        window.top.location.href = res.data
      }
      return Promise.reject(new Error(res.message || 'response code is not 200'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'warn',
      duration: 8 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
