import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/index.scss' // global css
import App from './App'
import store from './store'
import router from './router'

import '@/icons'
import axios from "axios";
// import '@/permission' // permission control

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const {mockXHR} = require('../mock')
//   mockXHR()
// }

// set ElementUI lang to EN
// Vue.use(ElementUI, {locale})
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI)

Vue.config.productionTip = false


axios.get('/api/setting').then(function (response) {
  if (response.data.code !== 200) {
    document.write("<h3 style='text-align: center;padding-top:20px;'>加载系统数据出错，请刷新页面尝试</h3>")
  } else {
    Vue.prototype.$settings = response.data.data
    Vue.prototype.$getClusterSettings = function (cluster) {
      let ret = null
      for (let it of this.$settings.clusters) {
        if (it.name === cluster) {
          ret = it
          break
        }
      }
      return ret
    }
    new Vue({
      el: '#app',
      router,
      store,
      render: h => h(App)
    });
    axios.get('/api/v1/user/info').then(function (response) {
      if (response.data.code === 401) {
        axios.post('/api/shiro-cas/redirect-path?path=' + encodeURIComponent(window.location.href)).finally(() => {
          top.location.href = response.data.data;
        })
      }
    })
  }
})








