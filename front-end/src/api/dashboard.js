import request from '@/utils/request'

export function getDashboardData() {
  return request({
    url: '/v1/dashboard/data',
    method: 'get',
  })
}
export function getResourcePool(cluster) {
  return request({
    url: '/v1/dashboard/resource-pool?cluster=' + cluster,
    method: 'get',
  })
}
export function getPodDraft(cluster) {
  return request({
    url: '/v1/dashboard/pod-draft?cluster=' + cluster,
    method: 'get',
  })
}

export function getAppDeployStatus() {
  return request({
    url: '/v1/dashboard/app-deploy-status',
    method: 'get',
  })
}

