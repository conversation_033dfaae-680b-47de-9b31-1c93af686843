import request from '@/utils/request'

export function getPods(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/pod/list',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app
    }
  })
}

export function getDeregisterPodByApp(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/pod/deregister/list-by-app',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app
    }
  })
}

export function getPodsByEnv(cluster, namespace) {
  return request({
    url: '/v1/k8s/pod/list-by-env',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace
    }
  })
}

export function getPodDetail(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/detail',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      pod: pod
    }
  })
}

export function getPodStdoutLog(cluster, namespace, pod, container, tailLines, previous) {
  return request({
    url: '/v1/k8s/pod/stdout',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      pod: pod,
      container: container,
      tailLines: tailLines,
      previous: previous
    }
  })
}

export function downloadPodStdoutLog(cluster, namespace, pod, container,tailLine) {
  let url = `/api/v1/k8s/pod/stdout/download?cluster=${cluster}&namespace=${namespace}&pod=${pod}&container=${container}&tailLines=${tailLine}&_time="` + new Date().getTime()
  window.open(url)
}

export function podDelete(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/delete',
    method: 'delete',
    params: {
      cluster: cluster,
      namespace: namespace,
      pod: pod
    }
  })
}

export function podDeregister(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/deregister',
    method: 'put',
    params: {
      cluster: cluster,
      namespace: namespace,
      pod: pod
    }
  })
}

export function podDeregisterListByCluster(cluster) {
  return request({
    url: '/v1/k8s/pod/deregister/list-by-cluster',
    method: 'get',
    params: { cluster: cluster}
  })
}

export function podVersionRetain(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/version/retain',
    method: 'put',
    params: {
      cluster: cluster,
      namespace: namespace,
      pod: pod
    }
  })
}

export function getPodFiles(params) {
  return request({
    url: '/v1/k8s/pod/file/list',
    method: 'get',
    params
  })
}

export function readyFile(data) {
  return request({
    url: '/v1/k8s/pod/file/ready',
    method: 'post',
    data
  })
}

export function downloadFile(fileId, fileName) {
  window.open('/api/v1/k8s/pod/file/download?fileId=' + fileId + "&fileName=" + fileName + "&_time=" + new Date().getTime())
}

export function archiveFiles(data) {
  return request({
    url: '/v1/k8s/pod/file/archive',
    method: 'post',
    data
  })
}

export function previewFile(fileId) {
  window.open('/api/v1/k8s/pod/file/preview?fileId=' + fileId)
}

export async function getPodOOM(pod) {
  return request({
    url: `/v1/k8s/pod/oom/${pod}`,
    method: 'get',
  })
}

export function queryCmsFiles(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/cms/list',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod
    }
  })
}


export function podEvents(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/events',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
    }
  })
}

export function podMetric(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/metric',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
    }
  })
}

export function openPyroscope(cluster, namespace, pod) {
  return request({
    url: '/v1/k8s/pod/open-pyroscope',
    method: 'post',
    data: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
    }
  })
}
