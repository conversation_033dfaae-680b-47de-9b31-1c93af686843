import request from '@/utils/request'

export function searchDeployment(cluster, namespace) {
  return request({
    url: '/v1/k8s/deployment/list',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace
    }
  })
}

export function deploymentDetail(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/deployment/detail',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app
    }
  })
}

export function updateDeploymentResource(data) {
  return request({
    url: '/v1/k8s/deployment/update-resource',
    method: 'post',
    data
  })
}

export function deploymentWholeDetail(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/deployment/detail/whole',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app
    }
  })
}

export function deploymentRedeploy(params) {
  return request({
    url: '/v1/k8s/deployment/redeploy',
    method: 'put',
    params
  })
}

export function useRecreateDeployStrategy(params) {
  return request({
    url: '/v1/k8s/deployment/use-recreate-deploy-strategy',
    method: 'put',
    params
  })
}

export function queryAppReplicaSet(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/deployment/replicaSet/list',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app
    }
  })
}

export function scaleReplicas(params) {
  return request({
    url: '/v1/k8s/deployment/scale',
    method: 'put',
    params
  })
}

export function rollback(cluster, namespace, app, revision, deployTag) {
  return request({
    url: '/v1/k8s/deployment/rollback',
    method: 'put',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app,
      "revision": revision,
      "deployTag": deployTag ? deployTag : "",
    }
  })
}



