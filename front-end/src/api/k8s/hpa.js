import request from "@/utils/request";


export function searchHpa(params) {
  return request({
    url: '/v1/k8s/scale/hpa',
    method: 'get',
    params
  })
}


export function createHpa(data) {
  return request({
    url: '/v1/k8s/scale/hpa',
    method: 'post',
    data
  })
}

export function deleteHpa(cluster, namespace, app) {
  return request({
    url: '/v1/k8s/scale/hpa',
    method: 'delete',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app
    }
  })
}
