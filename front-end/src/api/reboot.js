import request from "@/utils/request";


export function searchReboot(keyword) {
  return request({
    url: '/v1/reboot?keyword=' + keyword,
    method: 'get'
  })
}


export function createReboot(data) {
  return request({
    url: '/v1/reboot',
    method: 'post',
    data
  })
}

export function deleteReboot(id) {
  return request({
    url: '/v1/reboot',
    method: 'delete',
    params: {
      id: id
    }
  })
}
