import request from '@/utils/request'



export function createTempAuth(data) {
  return request({
    url: '/v1/temp-auth',
    method: 'post',
    data
  })
}

export function auditTempAuth(id, approved) {
  return request({
    url: `/v1/temp-auth/audit?id=${id}&approved=${approved}`,
    method: 'post',
  })
}

export function searchTempAuth(params) {
  return request({
    url: '/v1/temp-auth/search',
    method: 'get',
    params
  })
}

export function getTempAuthAdmins(params) {
  return request({
    url: '/v1/temp-auth/admins',
    method: 'get',
    params
  })
}

export function deleteTempAuth(id) {
  return request({
    url: '/v1/temp-auth',
    method: 'delete',
    params: {
      id: id
    }
  })
}
