import request from '@/utils/request'

export function queryEvents(key, lastId, limit) {
  return request({
    url: '/v1/event/query',
    method: 'get',
    params: {
      "key": key,
      "lastId": lastId,
      "limit": limit,
    }
  })
}

export function queryEventsByApp(cluster, namespace, app, lastId, limit) {
  return request({
    url: '/v1/event/query-by-app',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app,
      "lastId": lastId,
      "limit": limit,
    }
  })
}



