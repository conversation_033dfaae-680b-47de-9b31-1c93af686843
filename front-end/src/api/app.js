import request from '@/utils/request'

export function searchApp(params) {
  return request({
    url: '/v1/app/search',
    method: 'get',
    params
  })
}

export function groupByNamespace() {
  return request({
    url: '/v1/app/group-by-namespace',
    method: 'get'
  })
}
export function appsWithEnv() {
  return request({
    url: '/v1/app/apps-with-env',
    method: 'get'
  })
}

export function allApps() {
  return request({
    url: '/v1/app/all',
    method: 'get'
  })
}

export function getAllAppName() {
  return request({
    url: '/v1/app/names',
    method: 'get'
  })
}

export function findApp(name) {
  return request({
    url: '/v1/app/detail',
    method: 'get',
    params: {
      "name": name
    }
  })
}

export function createApp(data) {
  return request({
    url: '/v1/app',
    method: 'post',
    data
  })
}

export function editApp(data) {
  return request({
    url: '/v1/app',
    method: 'put',
    data
  })
}

export function deleteAppByName(name) {
  return request({
    url: '/v1/app/',
    method: 'delete',
    params: {
      "name": name
    }
  })
}

export function getAppAddress(cluster, namespace, app) {
  return request({
    url: '/v1/app/address',
    method: 'get',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "app": app
    }
  })
}

export function getGitTag(app) {
  return request({
    url: '/v1/app/git-tag',
    method: 'get',
    params: {
      app: app
    }
  })
}

export function createGitTag(data) {
  return request({
    url: '/v1/app/git-tag',
    method: 'post',
    data
  })
}

export function createBugfixBranch(data) {
  return request({
    url: '/v1/app/create-bugfix-branch',
    method: 'post',
    data
  })
}


export function batchDeleteGitTagAndBranch(data) {
  return request({
    url: '/v1/app/git-tag',
    method: 'delete',
    data
  })
}

export function findGitTag(gitUrl, searchName) {
  return request({
    url: '/v1/app/git-tag/find',
    method: 'get',
    params: {
      git_url: gitUrl,
      search_name: searchName,
    }
  })
}

export function updatePerm(app, orgs) {
  return request({
    url: '/v1/app/permission',
    method: 'put',
    data: {
      app: app,
      orgs: orgs
    }
  })
}

export function getGitModules(app, pipelineId) {
  return request({
    url: '/v1/app/git-modules',
    method: 'get',
    params: {
      app: app,
      pipelineId: pipelineId || ''
    }
  })
}

export function syncFromCRM() {
  return request({
    url: '/v1/app/sync-from-cmdb',
    method: 'get',
  })
}

export function countPipelines() {
  return request({
    url: '/v1/app/count-pipelines',
    method: 'get',
  })
}
