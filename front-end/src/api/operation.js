import request from '@/utils/request'

export function searchAppVersionHistory(params) {
  return request({
    url: '/operation/app-version-history',
    method: 'get',
    params
  })
}


export function searchCmdbOwner() {
  return request({
    url: '/operation/cmdb-owner',
    method: 'get',
  })
}

export function syncCmdbOwner() {
  return request({
    url: '/operation/cmdb-sync',
    method: 'get',
  })
}
