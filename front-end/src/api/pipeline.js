import request from '@/utils/request'

export function findPipelinesByApp(app) {
  return request({
    url: '/v1/pipeline/app/' + app,
    method: 'get',
  })
}

export function findPipelinesById(id) {
  return request({
    url: '/v1/pipeline',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function findPipelinesByEnv(cluster, namespace,app) {
  return request({
    url: '/v1/pipeline/find-by-env',
    method: 'get',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app
    }
  })
}

export function searchPipeline(params) {
  return request({
    url: '/v1/pipeline/search',
    method: 'get',
    params
  })
}

export function searchPipelineByProperties(data) {
  return request({
    url: '/v1/pipeline/search-by-properties',
    method: 'post',
    data
  })
}

export function allPipeline() {
  return request({
    url: '/v1/pipeline/all',
    method: 'get'
  })
}

export function findPipelinesByStatus(status) {
  return request({
    url: '/v1/pipeline/status',
    method: 'get',
    params: {
      status: status
    }
  })
}

export function initNewPipeline(app) {
  return request({
    url: '/v1/pipeline/init',
    method: 'post',
    params: {
      app: app
    }
  })
}


export function postPipeline(data) {
  return request({
    url: '/v1/pipeline',
    method: 'post',
    data
  })
}

export function removePipeline(id) {
  return request({
    url: '/v1/pipeline/offline',
    method: 'delete',
    params: {
      id: id
    }
  })
}

export function syncPipeline(data) {
  return request({
    url: '/v1/pipeline/sync',
    method: 'post',
    data
  })
}

export function getClonePipeline(sourceCluster, sourceNamespace, targetCluster, targetNamespace) {
  return request({
    url: '/v1/pipeline/clone/by-namespace',
    method: 'get',
    params: {
      sourceCluster: sourceCluster,
      sourceNamespace: sourceNamespace,
      targetCluster: targetCluster,
      targetNamespace: targetNamespace
    }
  })
}

export function clonePipeline(data) {
  return request({
    url: '/v1/pipeline/clone/by-namespace',
    method: 'post',
    data
  })
}

export function getDedicatedCloudPublishPipeline(sourceCluster, sourceNamespace, targetCluster, targetNamespace) {
  return request({
    url: '/v1/pipeline/publish-dedicated-cloud',
    method: 'get',
    params: {
      sourceCluster: sourceCluster,
      sourceNamespace: sourceNamespace,
      targetCluster: targetCluster,
      targetNamespace: targetNamespace
    }
  })
}

export function updatePipelineStatus(data) {
  return request({
    url: '/v1/pipeline/status',
    method: 'post',
    data
  })
}
