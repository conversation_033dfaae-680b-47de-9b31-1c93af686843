import request from '@/utils/request'


export function searchJob(data) {
  return request({
    url: '/v1/job/search',
    method: 'post',
    data
  })
}

export function getImageOptions(pipelineIds) {
  return request({
    url: '/v1/job/image-options',
    method: 'get',
    params: {
      pipelineIds
    }
  })
}

export function buildImage(data) {
  return request({
    url: '/v1/job/build-image',
    method: 'post',
    data
  })
}

export function deployApp(data) {
  return request({
    url: '/v1/job/deploy-app',
    method: 'post',
    data
  })
}

export function buildImageAndDeployApp(data) {
  return request({
    url: '/v1/job/build-and-deploy',
    method: 'post',
    data
  })
}

export function deployAppWithCurrentVersion(cluster, namespace, app, remark) {
  return request({
    url: '/v1/job/deploy-app-with-current-version',
    method: 'post',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app,
      remark: remark
    }
  })
}

export function buildImageWithCurrentVersion(cluster, namespace, app, remark) {
  return request({
    url: '/v1/job/build-image-with-current-version',
    method: 'post',
    params: {
      cluster: cluster,
      namespace: namespace,
      app: app,
      remark: remark
    }
  })
}

export function findJobById(id) {
  return request({
    url: '/v1/job/detail',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function findTaskByJobId(jobId) {
  return request({
    url: '/v1/job/tasks',
    method: 'get',
    params: {
      jobId: jobId
    }
  })
}

export function cancelJob(id) {
  return request({
    url: '/v1/job/cancel',
    method: 'put',
    data: {
      id: id
    }
  })
}

export function setJobStatus(id, status) {
  return request({
    url: `/v1/job/update-status?id=${id}&status=${status}`,
    method: 'put'
  })
}

export function redoJob(id) {
  return request({
    url: '/v1/job/redo',
    method: 'post',
    data: {
      id: id
    }
  })
}


// export function deployParamInit(pipelineIds) {
//   return request({
//     url: '/v1/job/deploy-param-init',
//     method: 'get',
//     params: {
//       pipelineIds
//     }
//   })
// }


