import request from '@/utils/request'

export function jacocoReset(cluster, namespace, pod) {
  return request({
    url: '/jacoco/reset',
    method: 'POST',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
    }
  })
}

export function jacocoReportTask(cluster, namespace, pod, jars) {
  return request({
    url: '/jacoco/report/task',
    method: 'POST',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
      "jars": jars,
    }
  })
}

export function jacocoListJars(cluster, namespace, pod) {
  return request({
    url: '/jacoco/list/jars',
    method: 'GET',
    params: {
      "cluster": cluster,
      "namespace": namespace,
      "pod": pod,
    }
  })
}




