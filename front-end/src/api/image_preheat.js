import request from '@/utils/request'

export function createImagePreheat(data) {
  return request({
    url: `/v1/image-preheat`,
    method: 'post',
    data
  })
}

export function searchImagePreheat(params) {
  return request({
    url: `/v1/image-preheat`,
    method: 'get',
    params
  })
}

export function deleteImagePreheat(id) {
  return request({
    url: `/v1/image-preheat?id=${id}`,
    method: 'delete',
  })
}

export function executeImagePreheat(data) {
  return request({
    url: `/v1/image-preheat/execute`,
    method: 'post',
    data
  })
}

export function searchImagePreheatJobs(cluster,namespace) {
  return request({
    url: `/v1/image-preheat/jobs?cluster=${cluster}&namespace=${namespace}`,
    method: 'get',
  })
}

export function deleteImagePreheatJobs(data) {
  return request({
    url: `/v1/image-preheat/jobs`,
    method: 'delete',
    data
  })
}

export function preheatWithCurrClusterImage(cluster,jobNumber) {
  return request({
    url: `/v1/image-preheat/preheat-with-curr-cluster-image?cluster=${cluster}&jobNumber=${jobNumber}`,
    method: 'post',
  })
}

export function downloadJobYaml(params) {
  const query = params
    ? '?' + Object.entries(params).map(([k, v]) => {
        if (Array.isArray(v)) {
          return v.map(item => `${encodeURIComponent(k)}=${encodeURIComponent(item)}`).join('&');
        }
        return `${encodeURIComponent(k)}=${encodeURIComponent(v)}`;
      }).join('&')
    : '';
  window.open(`/api/v1/image-preheat/download-job-yaml${query}`);
}
