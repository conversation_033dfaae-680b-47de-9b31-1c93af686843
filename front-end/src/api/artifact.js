import request from '@/utils/request'

export function getAllArtifact() {
  return request({
    url: '/v1/artifact/all',
    method: 'get'
  })
}

export function searchArtifact(params) {
  return request({
    url: '/v1/artifact/search',
    method: 'get',
    params
  })
}

export function analysisArtifact(params) {
  return request({
    url: '/v1/artifact/analysis',
    method: 'get',
    params
  })
}

export function createArtifact(data) {
  return request({
    url: '/v1/artifact',
    method: 'post',
    data
  })
}

export function deleteArtifact(id) {
  return request({
    url: '/v1/artifact',
    method: 'delete',
    params: {
      id: id
    }
  })
}

