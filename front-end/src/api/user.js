import request from '@/utils/request'

export function getUserInfo() {
  return request({
    url: '/v1/user/info',
    method: 'get',
  })
}

export function isAdmin() {
  return request({
    url: '/v1/user/is-admin',
    method: 'get',
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'get'
  })
}

export function getRoles() {
  return request({
    url: '/v1/role/all',
    method: 'get',
  })
}

export function listUserByRealName(realName) {
  return request({
    url: '/v1/user/list-by-real-name',
    method: 'get',
    params: {
      realName: realName
    }
  })
}

export function updateHistoryApp(app) {
  return request({
    url: '/v1/user/update-history-app?app=' + app,
    method: 'put',
  })
}

export function userNames() {
  return request({
    url: '/v1/user/names',
    method: 'get',
  })
}
