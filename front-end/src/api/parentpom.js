import request from '@/utils/request'

export function searchParentPom(params) {
  return request({
    url: '/v1/sys/parent-pom',
    method: 'get',
    params,
  })
}

export function saveParentPom(data) {
  return request({
    url: '/v1/sys/parent-pom',
    method: 'post',
    data,
  })
}

export function modifyUpdateTime(data) {
  return request({
    url: '/v1/sys/parent-pom/update-time',
    method: 'put',
    data,
  })
}

export function deleteParentPom(params) {
  return request({
    url: '/v1/sys/parent-pom',
    method: 'delete',
    params,
  })
}
