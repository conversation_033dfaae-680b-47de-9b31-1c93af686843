import {getUserInfo, logout} from '@/api/user'

const getDefaultState = () => {
  return {
    username: "",
    realName: "",
    email: "",
    employId: "",
    roles: [],
    permissions: [],
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_STATE: (state, user) => {
    Object.assign(state, user)
  }
}

const actions = {
  sync({commit, state}) {
    return new Promise((resolve, reject) => {
      getUserInfo().then(response => {
        const {data} = response
        commit('SET_STATE', data)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({commit, state}) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

