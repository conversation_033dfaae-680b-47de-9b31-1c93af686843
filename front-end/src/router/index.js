import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: Layout,
    children: [
      {
        path: '',
        name: '404',
        component: () => import('@/views/404'),
        meta: {title: '404'}
      }
    ],
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: 'cicd/app-deploy',
    hide: true,
  },
  {
    path: '/dashboard',
    component: Layout,
    children: [
      {
        path: 'home',
        name: 'dashboard-home',
        component: () => import('@/views/dashboard/index'),
        meta: {title: '首页看板', icon: 'dashboard'}
      },
    ]
  },
  {
    path: '/app',
    component: Layout,
    meta: {title: '应用和模块', icon: 'app'},
    redirect: 'noRedirect',
    children: [
      {
        path: 'list',
        name: 'app-list',
        component: () => import('@/views/app/index'),
        meta: {title: '应用和模块', icon: 'app'}
      },
      {
        path: 'permission',
        name: 'app-permission',
        hidden: true,
        component: () => import('@/views/app/permission'),
        meta: {title: '应用权限', activeMenu: '/app/list'}
      },
      {
        path: 'artifact/list',
        name: 'app-artifact-list',
        hidden: true,
        component: () => import('@/views/app/artifact-list'),
        meta: {title: '部署模块', icon: 'module2'}
      },
      {
        path: 'git-tag',
        hidden: true,
        component: () => import('@/views/app/git-tag'),
        name: 'git-tag',
        meta: {title: '打基线', activeMenu: '/app/pipeline/list'}
      },
      {
        path: 'git-tag/batch-delete',
        hidden: true,
        component: () => import('@/views/app/git-tag-batch-delete'),
        name: 'git-tag-batch-delete',
        meta: {title: '批量删除分支或Tag', activeMenu: '/app/pipeline/list'}
      }
    ]
  },
  {
    path: '/cicd',
    component: Layout,
    meta: {title: 'CI/CD', icon: 'cicd'},
    children: [
      {
        path: 'app-deploy',
        name: 'cicd-app-deploy',
        component: () => import('@/views/cicd/app-deploy.vue'),
        meta: {title: '应用发布', icon: 'guide'},
      },
      {
        path: 'app-deploy-history',
        name: 'cicd-app-deploy-history',
        component: () => import('@/views/cicd/app-deploy-history'),
        meta: {title: '发布历史', icon: 'record'},
      },
      {
        path: 'app-deploy-detail',
        name: 'cicd-app-deploy-detail',
        hidden: true,
        component: () => import('@/views/cicd/app-deploy-detail'),
        meta: {title: '发布详情', icon: 'application',activeMenu: '/cicd/app-deploy-history'},
      },
      {
        path: 'image-build',
        name: 'cicd-image-build',
        component: () => import('@/views/cicd/image-build.vue'),
        meta: {title: '镜像构建', icon: 'image'},
      },
      {
        path: 'image-build-history',
        name: 'cicd-image-build-history',
        component: () => import('@/views/cicd/image-build-history.vue'),
        meta: {title: '构建历史', icon: 'record'},
      },
      {
        path: 'build-detail',
        name: 'cicd-image-build-detail',
        hidden: true,
        component: () => import('@/views/cicd/image-build-detail'),
        meta: {title: '构建详情', icon: 'image',activeMenu: '/cicd/image-build'},
      },
      {
        path: 'image-list',
        name: 'cicd-image-list',
        component: () => import('@/views/cicd/image-list.vue'),
        meta: {title: '镜像列表', icon: 'image'},
      },
      {
        path: 'app-version-history',
        name: 'app-version-history',
        component: () => import('@/views/cicd/app-version-history.vue'),
        meta: {title: '应用历史版本查询', icon: ''},
      },
      {
        path: 'edit',
        hidden: true,
        component: () => import('@/views/pipeline/edit'),
        name: 'app-pipeline-edit',
        meta: {title: '发布流程编辑', activeMenu: '/app/pipeline/list'}
      },
      {
        path: 'sync-config',
        hidden: true,
        component: () => import('@/views/pipeline/sync-config-to-other'),
        name: 'app-pipeline-sync-config',
        meta: {title: '同步配置到其他发布流程', activeMenu: '/app/pipeline/list'}
      }
    ]
  },
  {
    path: '/pod',
    component: Layout,
    redirect: 'noRedirect',
    meta: {title: '实例管理', icon: 'application'},
    children: [
      {
        path: 'index',
        name: 'pod-index',
        component: () => import('@/views/pod/index3'),
        meta: {title: '实例管理', icon: 'application'}
      },
      {
        path: 'file',
        name: 'pod-file-page',
        hidden: true,
        component: () => import('@/views/pod/pod-file-page'),
        meta: {title: 'Pod文件', icon: 'files'}
      }
    ]
  },
  {
    path: '/app/scale',
    component: Layout,
    meta: {title: '扩缩容', icon: 'more'},
    children: [
      {
        path: 'auto-v2',
        name: 'pod-auto-scaler',
        component: () => import('@/views/scale/pod-auto-scaler'),
        meta: {title: '自动扩缩容', icon: ''}
      },
      {
        path: 'cron',
        name: 'app-scale-cron',
        component: () => import('@/views/scale/cron_scale'),
        meta: {title: '定时扩缩容', icon: ''}
      },
      {
        path: 'log',
        name: 'app-scale-log',
        component: () => import('@/views/scale/scale_log'),
        meta: {title: '日志', icon: ''}
      }
    ]
  },
  {
    path: '/auth',
    component: Layout,
    meta: {title: '权限管理', icon: 'lock'},
    children: [
      {
        path: 'org',
        name: 'auth-org',
        component: () => import('@/views/auth/org'),
        meta: {title: '部门管理', icon: 'peoples'}
      },
      {
        path: 'temp-path',
        name: 'auth-temp-auth',
        component: () => import('@/views/auth/temp-auth'),
        meta: {title: '临时授权', icon: 'key'}
      },
    ]
  },
  {
    path: '/analysis',
    component: Layout,
    meta: {title: '数据分析', icon: 'chart'},
    children: [
      {
        path: 'pipelines',
        name: 'analysis-pipeline',
        component: () => import('@/views/analysis/app-analysis'),
        meta: {title: '应用部署分析', icon: ''}
      },
      {
        path: 'sys-logs',
        name: 'analysis-sys-logs',
        component: () => import('@/views/analysis/sys-logs'),
        meta: {title: '系统运行日志', icon: ''}
      },
      {
        path: 'charts/resources',
        name: 'analysis-charts-resources',
        component: () => import('@/views/charts/resources'),
        meta: {title: '资源分配图表', icon: ''}
      },
      {
        path: 'charts/event',
        name: 'analysis-charts-event',
        component: () => import('@/views/charts/event'),
        meta: {title: '事件分析图表', icon: ''}
      },
      {
        path: 'charts/restart',
        name: 'analysis-charts-restart',
        component: () => import('@/views/charts/restart'),
        meta: {title: '重启次数', icon: ''}
      }
    ]
  },
  {
    path: '/tool',
    component: Layout,
    meta: {title: '辅助工具', icon: 'tool'},
    children: [
      {
        path: 'address/lookup',
        name: 'tool-address-lookup',
        component: () => import('@/views/tool/find-app-by-address'),
        meta: {title: '地址查应用', icon: ''}
      },
      {
        path: 'jar/scan',
        name: 'tool-jar-scan',
        component: () => import('@/views/tool/scan-jar'),
        meta: {title: 'Jar包扫描', icon: ''}
      },
      {
        path: 'tomcat/version/scan',
        name: 'tool-tomcat-version-scan',
        component: () => import('@/views/tool/scan-tomcat-version'),
        meta: {title: 'Tomcat版本扫描', icon: ''}
      },
      {
        path: 'app/manage',
        name: 'tool-app-manage',
        component: () => import('@/views/tool/app-manage-tab'),
        meta: {title: '服务运维', icon: ''},
      },
      {
        path: 'app/gc',
        name: 'tool-app-gc',
        component: () => import('@/views/tool/app-gc'),
        meta: {title: '应用清理', icon: ''},
        hidden: true,
      },
      {
        path: 'app/reboot',
        name: 'tool-app-reboot',
        component: () => import('@/views/tool/app-cron-reboot'),
        meta: {title: '服务定时重启', icon: ''},
        hidden: true,
      },
      {
        path: 'app/restart',
        name: 'tool-app-restart',
        component: () => import('@/views/tool/app-restart'),
        meta: {title: '服务批量重启', icon: ''},
        hidden: true,
      },
      {
        path: 'app/redeploy',
        name: 'tool-app-redeploy',
        component: () => import('@/views/tool/app-redeploy'),
        meta: {title: '服务批量重发', icon: ''},
        hidden: true,
      },
      {
        path: 'app/redeploy-v2',
        name: 'tool-app-redeploy-v2',
        component: () => import('@/views/tool/app-redeploy-v2'),
        meta: {title: '服务批量重发V2', icon: ''},
        hidden: true,
      },
      {
        path: 'app/deploy',
        name: 'tool-app-deploy',
        component: () => import('@/views/tool/app-deploy'),
        meta: {title: '服务批量发布', icon: ''},
        hidden: true,
      },
      {
        path: 'app/build-v2',
        name: 'tool-app-build-v2',
        component: () => import('@/views/tool/app-build-v2'),
        meta: {title: '服务批量构建', icon: ''},
        hidden: true,
      },
      {
        path: 'tool-app-version-snapshot',
        name: 'tool-app-version-snapshot',
        component: () => import('@/views/tool/app-version-snapshot/index.vue'),
        meta: {title: '应用版本快照', icon: ''},
      },
      {
        path: 'not-java-app',
        name: 'not-java-app',
        component: () => import('@/views/tool/not-java-app/index.vue'),
        meta: {title: '非Java应用', icon: ''}
      },
      {
        path: 'pipeline/clone/by-namespace',
        name: 'tool-pipeline-clone-by-namespace',
        component: () => import('@/views/tool/clone-pipeline'),
        meta: {title: '发布流程克隆', icon: ''},
        hidden: true,
      },
      {
        path: 'pipeline/resource-update',
        name: 'tool-pipeline-resource-update',
        component: () => import('@/views/tool/pipeline-resource-update'),
        meta: {title: '发布流程资源批量修改', icon: ''},
        hidden: true,
      },
      {
        path: 'cms-config-migrate',
        name: 'tool-cms-config-migrate',
        component: () => import('@/views/tool/cms-config-migrate'),
        meta: {title: '配置文件迁移', icon: ''},
        hidden: true,
      },
      {
        path: 'pod-deregister-manage',
        name: 'tool-pod-deregister-manage',
        component: () => import('@/views/tool/pod-deregister-manage'),
        meta: {title: '摘除Pod管理', icon: ''},
        hidden: true,
      },
      {
        path: 'dedicated-cloud-publish-helper',
        name: 'tool-dedicated-cloud-publish-helper',
        component: () => import('@/views/tool/dedicated-cloud-publish-helper'),
        meta: {title: '专属云应用发布助手', icon: ''},
        hidden: true,
      },
      {
        path: 'yaml-export',
        name: 'tool-yaml-export',
        component: () => import('@/views/tool/yaml-export'),
        meta: {title: 'Yaml导出', icon: ''},
        hidden: true,
      },
      {
        path: 'helm-chart-build',
        name: 'tool-helm-chart-build',
        component: () => import('@/views/tool/helm-chart-build'),
        meta: {title: 'helm chart构建', icon: ''},
        hidden: true,
      },
      {
        path: 'dedicated-cloud-publish-status',
        name: 'dedicated-cloud-publish-status',
        component: () => import('@/views/tool/dedicated-cloud-publish-status'),
        hidden: true,
      },
      {
        path: 'pipeline-migrate-index',
        name: 'pipeline-migrate-index',
        component: () => import('@/views/tool/cluster-migrate-index'),
        meta: {title: '集群迁移', icon: ''}
      },
      {
        path: 'app-address-query',
        name: 'tool-app-address-query',
        component: () => import('@/views/tool/app-address-query'),
        hidden: true,
        meta: {title: '应用地址查询', icon: ''}
      }
    ]
  },
  {
    path: '/cluster',
    component: Layout,
    meta: {title: 'k8s集群', icon: 'kubernetes'},
    children: [
      {
        path: 'k8s-cluster',
        name: 'cluster-k8s-cluster',
        component: () => import('@/views/cluster/index'),
        meta: {title: 'k8s集群', icon: ''}
      }
    ]
  },
  {
    path: '/devops',
    component: Layout,
    children: [
      {
        path: 'event',
        name: 'devops-event',
        component: () => import('@/views/tool/devops-event'),
        meta: {title: '平台事件流', icon: 'operation'}
      },
    ]
  },
  {
    path: '/audit',
    component: Layout,
    meta: {title: '发布流程审批', icon: 'audit'},
    children: [
      {
        path: 'pipeline',
        name: 'audit-pipeline',
        component: () => import('@/views/pipeline/audit-list'),
        meta: {title: '发布流程审批', icon: 'audit'}
      }
    ]
  },
  {
    path: '/log',
    component: Layout,
    meta: {title: '操作日志', icon: 'audit-log'},
    children: [
      {
        path: 'list',
        name: 'log-list',
        component: () => import('@/views/log/list'),
        meta: {title: '操作日志', icon: 'audit-log'}
      }
    ]
  },
  {
    path: '/sys',
    component: Layout,
    children: [
      {
        path: 'settings',
        name: 'sys-settings',
        component: () => import('@/views/sys/settings'),
        meta: {title: '系统配置', icon: 'settings'}
      },
    ]
  },
  // 404 page must be placed at the end !!!
  {path: '*', redirect: '/404', hidden: true}
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({y: 0}),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
