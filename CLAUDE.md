# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Kubernetes Application Manager (fs-k8s-app-manager) consisting of a Go backend API server and Vue.js frontend for managing containerized applications across multiple Kubernetes clusters.

## Architecture

- **Backend**: Go (Gin framework) REST API server in `back-end/`
- **Frontend**: Vue.js 2.6.10 with Element UI in `front-end/`
- **Database**: PostgreSQL with GORM
- **External Integrations**: Jenkins (CI/CD), GitLab (SCM), Harbor (registry), Redis, Kafka, ClickHouse

## Build Commands

### Frontend (front-end/)
```bash
# Development server (port 8081 with proxy to backend)
npm run dev

# Production build (outputs to ../back-end/templates)
npm run build:prod

# Staging build
npm run build:stage

# Linting
npm run lint

# Tests
npm run test:unit
npm run test:ci
```

### Backend (back-end/)
```bash
# Run the application
go run main.go

# Build binary
go build -o fs-k8s-app-manager main.go

# Run tests
go test ./...
```

### Docker Build
```bash
# Standard build
docker build -t fs-k8s-app-manager .

# With Node.js frontend build (if network allows)
docker build -f with-nodejs-build.Dockerfile -t fs-k8s-app-manager .
```

## Development Setup

### Prerequisites
- Go 1.19+
- Node.js >=8.9 (note: Node >16 requires `export NODE_OPTIONS=--openssl-legacy-provider`)
- Docker
- kubectl (multiple versions 1.13-1.32 supported)
- Helm 3.11.3

### Configuration
- Backend config: `back-end/conf/config.json`
- Kubernetes configs: `back-end/conf/kubeconf/`
- Frontend builds output to: `back-end/templates/`

## Key Modules

### Backend Services (back-end/service/)
- `app_service/`: Application lifecycle management
- `k8s_service/`: Kubernetes cluster operations
- `pipeline_service/`: Deployment pipeline management
- `job_service/`: CI/CD job orchestration
- `gitlab_service/`: Git repository integration

### API Routes (back-end/web/api/v1/)
- `app/`: Application CRUD and Git operations
- `k8s/`: Pod, deployment, scaling operations
- `pipeline/`: Pipeline management
- `tool/`: Batch operations and utilities

### Kubernetes Templates (back-end/k8s-templates/templates/)
- `deployment.yaml.tmpl`: Standard deployments
- `service.yaml.tmpl`: Service definitions
- `ingress.yaml.tmpl`: Ingress configurations
- `scale-hpa.yaml.tmpl`: Horizontal Pod Autoscaler
- `fs-pod-autoscaler.yaml.tmpl`: Custom autoscaling

### Frontend Views (front-end/src/views/)
- `dashboard/`: Resource usage overview
- `app/`: Application management interface
- `cicd/`: Build and deployment pipelines
- `cluster/`: Pod monitoring and management
- `tool/`: Administrative utilities

## Development Notes

### Jenkins Integration
- Requires specific plugins: Least Load, Pipeline Utility Steps, Jacoco, Docker Pipeline
- Set Jenkins quiet period to 0 to avoid queue issues
- Anonymous users need read permissions on job resources

### Common Issues
- Git lock files: `rm -rf /var/jenkins_home/caches/*`
- Node.js SSL issues: `export NODE_OPTIONS=--openssl-legacy-provider`
- HTTPS required for Jenkins iframe auto-refresh

### Template System
Uses Go text/template with Sprig functions for Kubernetes manifest generation. Templates are in `k8s-templates/templates/` with parameter definitions in `k8s-templates/params.go`.

### Multi-cluster Support
Configuration supports multiple Kubernetes clusters with different kubeconfig files stored in `back-end/conf/kubeconf/`.