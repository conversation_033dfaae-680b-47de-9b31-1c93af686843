package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"time"
)

type Config struct {
	ReloadTime     string         `json:"reloadTime"`
	Hostname       string         `json:"hostname"`
	App            App            `json:"app"`
	Postgres       Postgres       `json:"postgres"`
	<PERSON>        Jenkins        `json:"jenkins"`
	Gitlab         Gitlab         `json:"gitlab"`
	CMS            CMS            `json:"cms"`
	Harbor         Harbor         `json:"harbor"`
	CAS            CAS            `json:"cas"`
	QiXin          QiXin          `json:"qiXin"`
	Sms            SMS            `json:"sms"`
	FsPaas         FsPaas         `json:"fsPaas"`
	MetadataServer MetadataServer `json:"metadataServer"`
	K8sAppManager  K8sAppManager  `json:"k8sAppManager"`
	War            War            `json:"war"`
	Eolinker       Eolinker       `json:"eolinker"`
	Redis          Redis          `json:"redis"`
	Kafka          Kafka          `json:"kafka"`
	Clickhouse     Clickhouse     `json:"clickhouse"`
	OpenApiTokens  []string       `json:"openApiTokens"`
}
type App struct {
	RunMode       string        `json:"runMode"`
	HttpPort      int           `json:"httpPort"`
	ReadTimeout   time.Duration `json:"readTimeout"`
	WriteTimeout  time.Duration `json:"writeTimeout"`
	CacheCategory string        `json:"cacheCategory"`
	RuntimeDir    string        `json:"runtimeDir"`
	UploadDir     string        `json:"uploadDir"`
	DownloadDir   string        `json:"downloadDir"`
	KubeConfDir   string        `json:"kubeConfDir"`
}

type Postgres struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Database string `json:"database"`
}

type Jenkins struct {
	Host                   string `json:"host"`
	Username               string `json:"username"`
	Password               string `json:"password"`
	JobBuildImage          string `json:"jobBuildImage"`
	JobBuildPackageToImage string `json:"jobBuildPackageToImage"`
	JobJacoco              string `json:"jobJacoco"`
	EnableCRSF             bool   `json:"enableCRSF"`
	MavenImage             string `json:"mavenImage"`
}

type QiXin struct {
	Host                string `json:"host"`
	ReceiverEI          []int  `json:"receiverEI"`
	PublishAppId        string `json:"publishAppId"`
	ImportantAlertAppId string `json:"importantAlertAppId"`
}

type SMS struct {
	Host string `json:"host"`
}

type FsPaas struct {
	Host                string `json:"host"`
	ObjectId            string `json:"objectId"`
	EnterpriseId        int    `json:"enterpriseId"`
	SysUserId           int    `json:"sysUserId"`
	QiXinSessionFieldId string `json:"qiXinSessionFieldId"`
}
type MetadataServer struct {
	Host            string `json:"host"`
	UserObj         string `json:"userObj"`
	DeptObj         string `json:"deptObj"`
	ServerModuleObj string `json:"serverModuleObj"`
}
type K8sAppManager struct {
	Host string `json:"host"`
}
type Harbor struct {
	Host              string `json:"host"`
	AppProject        string `json:"appProject"`
	HelmChartProject  string `json:"helmChartProject"`
	Https             bool   `json:"https"`
	Username          string `json:"username"`
	Password          string `json:"password"`
	ArtifactBaseImage string `json:"artifactBaseImage"`
	ArtifactProject   string `json:"artifactProject"`
}

type Gitlab struct {
	Host  string `json:"host"`
	Token string `json:"token"`
}

type CMS struct {
	WebHost             string `json:"webHost"`
	ApiHost             string `json:"apiHost"`
	Token               string `json:"token"`
	CmdbConfigPath      string `json:"cmdbConfigPath"`
	ServiceConfigPath   string `json:"serviceConfigPath"`
	ServiceConfigPathV2 string `json:"serviceConfigPathV2"`
}

type CAS struct {
	LoginPath    string `json:"loginPath"`
	LogoutPath   string `json:"logoutPath"`
	ValidatePath string `json:"validatePath"`
}

type War struct {
	Host     string `json:"host"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type Eolinker struct {
	Enable         bool   `json:"enable"`
	Host           string `json:"host"`
	EoSecretKey    string `json:"eoSecretKey"`
	SpaceIdDefault string `json:"spaceIdDefault"`
	ReportHost     string `json:"reportHost"`
}

type Redis struct {
	Addr     string `json:"addr"`
	Password string `json:"password"`
	DB       int    `json:"db"`
}

type Kafka struct {
	Addr []string `json:"addr"`
}

type Clickhouse struct {
	Addr     []string `json:"addr"`
	User     string   `json:"user"`
	Password string   `json:"password"`
}

var _config_ = Config{}

func GetConfig() Config {
	return _config_
}

func init() {
	err := Reload()
	if err != nil {
		panic(err)
	}
}

func Reload() error {
	fmt.Println("config.json reload")
	fp := "conf/config.json"
	file, _ := os.ReadFile(fp)
	if err := json.Unmarshal(file, &_config_); err != nil {
		return errors.New(fmt.Sprintf("config.json parse fail, err: %v", err))
	}
	_config_.App.ReadTimeout = _config_.App.ReadTimeout * time.Second
	_config_.App.WriteTimeout = _config_.App.WriteTimeout * time.Second
	_config_.ReloadTime = time.Now().Format("2006-01-02 15:04:05")
	_config_.Hostname, _ = os.Hostname()
	return nil
}
