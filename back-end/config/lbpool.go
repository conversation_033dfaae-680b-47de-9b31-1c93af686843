package config

import (
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/pkg/util/strslice"
	log "github.com/sirupsen/logrus"
	"os"
)

type LBPools []LBPool

type LBPool struct {
	Cluster       string    `json:"cluster"`
	DefaultLBAddr string    `json:"defaultLBAddr"`
	LBAllocEnable bool      `json:"lbAllocEnable"`
	LBAllocList   []LBAlloc `json:"lbAllocList"`
}

type LBAlloc struct {
	Addr    string   `json:"addr"`
	AppList []string `json:"appList"`
	Name    string   `json:"name"`
	Remark  string   `json:"remark"`
}

func (pools LBPools) GetLBPool(cluster string) (LBPool, error) {
	for _, pool := range pools {
		if pool.Cluster == cluster {
			return pool, nil
		}
	}
	return LBPool{}, fmt.Errorf("lb pool not found, cluster: %s", cluster)
}

func (pool LBPool) GetLBAlloc(addr string) (LBAlloc, error) {
	for _, alloc := range pool.LBAllocList {
		if alloc.Addr == addr {
			return alloc, nil
		}
	}
	return LBAlloc{}, fmt.Errorf("lb alloc not found, addr: %s", addr)
}

func (alloc LBAlloc) AppHit(namespace, app string) bool {
	if namespace == "" || app == "" {
		return false
	}

	patterns := []string{
		fmt.Sprintf("%s/%s", namespace, app),
		fmt.Sprintf("%s/*", namespace),
		fmt.Sprintf("*/%s", app),
	}

	for _, pattern := range patterns {
		if strslice.Find(alloc.AppList, pattern) {
			return true
		}
	}
	return false
}

func (pools LBPools) GetClusterByLBAddr(lbAddr string) (cluster string) {
	for _, pool := range pools {
		if pool.DefaultLBAddr == lbAddr {
			return pool.Cluster
		}
		for _, alloc := range pool.LBAllocList {
			if alloc.Addr == lbAddr {
				return pool.Cluster
			}
		}
	}
	return ""
}

func (pools LBPools) GetAppLBAddr(cluster, namespace, app string) string {
	if cluster == "" || namespace == "" || app == "" {
		return ""
	}
	pool, err := pools.GetLBPool(cluster)
	if err != nil {
		log.Errorf("get lvs pool fail, %s", err.Error())
		return "-lvs-pool-not-found-"
	}
	if pool.LBAllocEnable {
		for _, alloc := range pool.LBAllocList {
			if alloc.AppHit(namespace, app) {
				return alloc.Addr
			}
		}
	}
	return pool.DefaultLBAddr
}

var _lbPools_ LBPools

func init() {
	if err := LBPoolsReload(); err != nil {
		panic(err)
	}
	for poolIdx, pool := range _lbPools_ {
		for allocIdx, alloc := range pool.LBAllocList {
			if alloc.AppList == nil {
				_lbPools_[poolIdx].LBAllocList[allocIdx].AppList = []string{}
			}
		}
	}
}

func LBPoolsReload() error {
	fmt.Println("lb-pools.json reload")
	fp := "conf/lb-pools.json"
	file, _ := os.ReadFile(fp)
	if err := json.Unmarshal(file, &_lbPools_); err != nil {
		return fmt.Errorf("lb-pools.json parse fail, err: %v", err)
	}
	return nil
}

func GetLBPools() LBPools {
	return _lbPools_
}
