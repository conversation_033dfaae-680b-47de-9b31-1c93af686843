package config

import (
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/pkg/util/strslice"
	"os"
	"strings"
)

type ScaleUpConfig struct {
	MinCPUAvailableCore  int `json:"minCPUAvailableCore"`  // Core
	MinMemoryAvailableGB int `json:"minMemoryAvailableGB"` // GB
}

type OnCallConfig struct {
	Enable    bool          `json:"enable"`
	ScaleUp   ScaleUpConfig `json:"scaleUp"`
	AlertList []Alert       `json:"alertList"`
}

type Alert struct {
	Name    string       `json:"name"`
	Desc    string       `json:"desc"`
	DryRun  bool         `json:"dryRun"`
	AppList ResourceList `json:"appList"`
}

type ResourceList struct {
	Whitelist []string `json:"whitelist"`
	Blacklist []string `json:"blacklist"`
}

func (o OnCallConfig) GetAlert(alertName string) Alert {
	//告警名称里可能会包含环境名称，需要对这类告警进行兼容处理
	//比如：jvm-thread-blocked (k8s0)、pod-cpu-throttled (ale)
	for _, f := range []string{"(", "（"} {
		if strings.Contains(alertName, f) {
			alertName = alertName[0:strings.Index(alertName, f)]
			alertName = strings.TrimSpace(alertName)
		}
	}

	for _, item := range o.AlertList {
		if item.Name == alertName {
			return item
		}
	}
	return Alert{
		Name:    alertName,
		DryRun:  true,
		Desc:    "",
		AppList: ResourceList{},
	}
}

func (o Alert) AppAllow(cluster, namespace, app string) bool {
	bl := o.AppList.Blacklist
	wl := o.AppList.Whitelist
	patterns := []string{
		"*/*/*",
		fmt.Sprintf("%s/*/*", cluster),
		fmt.Sprintf("*/%s/*", namespace),
		fmt.Sprintf("*/*/%s", app),
		fmt.Sprintf("%s/%s/*", cluster, namespace),
		fmt.Sprintf("%s/*/%s", cluster, app),
		fmt.Sprintf("*/%s/%s", namespace, app),
		fmt.Sprintf("%s/%s/%s", cluster, namespace, app),
	}

	// Check blacklist first
	if len(bl) > 0 {
		for _, pattern := range patterns {
			if strslice.Find(bl, pattern) {
				return false
			}
		}
	}
	// Check whitelist
	if len(wl) > 0 {
		for _, pattern := range patterns {
			if strslice.Find(wl, pattern) {
				return true
			}
		}
	}
	// default deny
	return false
}

var _oncallConf_ = OnCallConfig{}

func GetOncallConfig() OnCallConfig {
	return _oncallConf_
}

func init() {
	err := OncallReload()
	if err != nil {
		panic(err)
	}
	for idx, _ := range _oncallConf_.AlertList {
		item := &_oncallConf_.AlertList[idx]
		if item.AppList.Whitelist == nil {
			item.AppList.Whitelist = make([]string, 0)
		}
		if item.AppList.Blacklist == nil {
			item.AppList.Blacklist = make([]string, 0)
		}
	}
}

func OncallReload() error {
	fmt.Println("oncall.json reload")
	fp := "conf/oncall.json"
	file, _ := os.ReadFile(fp)
	if err := json.Unmarshal(file, &_oncallConf_); err != nil {
		return fmt.Errorf("oncall.json parse fail, err: %v", err)
	}
	return nil
}
