package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models/data"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/util/strslice"
	"os"
	"strings"
	"time"
)

type CloudCategory string

const (
	FxiaokeCloud   CloudCategory = "fxiaokeCloud"
	PublicCloud    CloudCategory = "publicCloud"
	DedicatedCloud CloudCategory = "dedicatedCloud"
	HybirdCloud    CloudCategory = "hybirdCloud"
)

type setting struct {
	ReloadTime                   string                         `json:"reloadTime"`
	Hostname                     string                         `json:"hostname"`
	Maintain                     Maintain                       `json:"maintain"`
	TimeWindow                   TimeWindow                     `json:"timeWindow"`
	EnvConfirmText               string                         `json:"envConfirmText"`
	MaxSurgeForceFull            bool                           `json:"maxSurgeForceFull"`
	EolinkerTestDefault          bool                           `json:"eolinkerTestDefault"`
	IngressReloadAllowInDay      bool                           `json:"ingressReloadAllowInDay"`
	Clusters                     []Cluster                      `json:"clusters"`
	MavenImages                  []string                       `json:"mavenImages"`
	ThirdServices                ThirdServices                  `json:"thirdServices"`
	AppSuffix                    map[string]string              `json:"appSuffix"`
	PipelineDefault              PipelineDefault                `json:"pipelineDefault"`
	DeployStrategies             []DeployStrategy               `json:"deployStrategies"`
	CheckVersionOfDedicatedCloud []CheckVersionOfDedicatedCloud `json:"checkVersionOfDedicatedCloud"`
	HelmChart                    HelmChart                      `json:"helmChart"`
	OncallScaleUp                OncallScaleUp                  `json:"oncallScaleUp"`
	BaseImageGray                BaseImageGray                  `json:"baseImageGray"`
}

type Cluster struct {
	Name               string               `json:"name"`
	Description        string               `json:"description"`
	Version            string               `json:"version"`
	PodIPPrefix        string               `json:"podIPPrefix"`
	IngressTmpl        string               `json:"ingressTmpl"`
	IngressParentHost  string               `json:"ingressParentHost"`
	SchedulerName      string               `json:"schedulerName"`
	Enable             bool                 `json:"enable"`
	CronScale          bool                 `json:"cronScale"`
	ShowLBAddr         bool                 `json:"showLBAddr"`
	ScaleMaxReplicas   int32                `json:"scaleMaxReplicas"`
	CloudCategory      CloudCategory        `json:"cloudCategory"`
	OpsDeploy          bool                 `json:"opsDeploy"`
	Labels             []string             `json:"labels"`
	Namespaces         []string             `json:"namespaces"`
	BaseImages         []string             `json:"baseImages"`
	ThirdServices      ClusterThirdServices `json:"thirdServices"`
	Nodes              []Node               `json:"nodes"`
	EmptyDir           EmptyDir             `json:"emptyDir"`
	MallocArena        MallocArena          `json:"mallocArena"`
	Apm                Apm                  `json:"apm"`
	PrometheusMonitor  PrometheusMonitor    `json:"prometheusMonitor"`
	ImageRegistryProxy string               `json:"imageRegistryProxy"`
	DnsConfigSearches  []string             `json:"dnsConfigSearches"`
	NeedImagePreheat   bool                 `json:"needImagePreheat"` //大版本期间是否需要镜像预热； 有些专属云共享一个镜像代理库，其中只需要一个集群做镜像预热即可
	ImagePullPolicy    string               `json:"imagePullPolicy"`
}

type Maintain struct {
	CI MaintainItem `json:"ci"`
	CD MaintainItem `json:"cd"`
}

type MaintainItem struct {
	Open bool   `json:"open"`
	Desc string `json:"desc"`
}

type HelmChart struct {
	//导出helm chart时版本前缀，比如860/870
	VersionPrefix string `json:"versionPrefix"`
}

type TimeWindow struct {
	Open              bool     `json:"open"`
	ExcludeNamespaces []string `json:"excludeNamespaces"`
}

type EmptyDir struct {
	TomcatLog bool `json:"tomcatLog"`
	AppLogs   bool `json:"appLogs"`
}

type MallocArena struct {
	Enable bool `json:"enable"`
	Max    uint `json:"max"`
}
type Node struct {
	Name   string `json:"name"`
	Value  string `json:"value"`
	Remark string `json:"remark"`
}

type Apm struct {
	Enable       bool   `json:"enable"`
	SkyWalkingUI string `json:"skyWalkingUI"`
}

// PrometheusMonitor prometheus PrometheusMonitor
type PrometheusMonitor struct {
	Enable bool `json:"enable"`
	//Prometheus release name
	Prometheus      string `json:"prometheus"`
	MetricsInterval string `json:"metricsInterval"`
	ScrapeTimeout   string `json:"scrapeTimeout"`
	MetricsPath     string `json:"metricsPath"`
}

type PipelineDefault struct {
	Status               string                  `json:"status" form:"status" binding:"required"`
	Cluster              string                  `json:"cluster" form:"cluster" binding:"required"`
	Namespace            string                  `json:"namespace" form:"namespace" binding:"required"`
	BaseImage            string                  `json:"baseImage" form:"baseImage" binding:"required"`
	Replicas             uint                    `json:"replicas" form:"replicas"`
	DeployStrategy       constant.DeployStrategy `json:"deployStrategy" form:"deployStrategy" binding:"required"`
	Resources            data.Resources          `json:"resources" form:"resources" binding:"required"`
	LivenessProbe        data.Probe              `json:"livenessProbe" form:"livenessProbe"`
	ReadinessProbe       data.Probe              `json:"readinessProbe" form:"readinessProbe"`
	StartupProbe         data.Probe              `json:"startupProbe" form:"startupProbe"`
	Schedule             data.Schedule           `json:"schedule" form:"schedule"`
	PVC                  data.PVC                `json:"pvc" form:"pvc"`
	Envs                 datatype.Envs           `json:"envs" form:"envs"`
	Ports                datatype.Ports          `json:"ports" form:"ports"`
	Options              data.PipelineOption     `json:"options" form:"options"`
	PartnerApps          datatype.StrList        `json:"partnerApps" form:"partnerApps"`
	ExclusiveApps        datatype.StrList        `json:"exclusiveApps" form:"exclusiveApps"`
	PreStopWebhook       string                  `json:"preStopWebhook" form:"preStopWebhook"`
	PreStopRetainSeconds uint                    `json:"preStopRetainSeconds" form:"preStopRetainSeconds"`
	EolinkerIDs          datatype.EolinkerIDs    `json:"eolinkerIDs" form:"eolinkerIDs"`
	Remark               string                  `json:"remark" form:"remark"`
	ExtInitContainer     string                  `json:"extInitContainer" form:"extInitContainer"`
}
type ThirdServices struct {
	WebShellHost   string `json:"webShellHost"`
	DevopsEventUrl string `json:"devopsEventUrl"`
}
type ClusterThirdServices struct {
	DubboHelperUrl string      `json:"dubboHelperUrl"`
	NacosHelperUrl string      `json:"nacosHelperUrl"`
	NacosServerUrl string      `json:"nacosServerUrl"`
	PrometheusHost string      `json:"prometheusHost"`
	Grafana        Grafana     `json:"grafana"`
	ClickVisual    ClickVisual `json:"clickVisual"`
	OOMReportUrl   string      `json:"oomReportUrl"`
}

type Grafana struct {
	Host         string `json:"host"`
	PrometheusDS string `json:"prometheusDS"`
	ClickHouseDS string `json:"clickHouseDS"`
	PyroscopeDS  string `json:"pyroscopeDS"`
}

type ClickVisual struct {
	Host string `json:"host"`
}

type DeployStrategy struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type CheckVersionOfDedicatedCloud struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
}

type OncallScaleUp struct {
	AppList []string `json:"appList"`
}

func (o OncallScaleUp) AppContains(cluster, namespace, app string) bool {
	return strslice.Find(o.AppList, "*/*/*") ||
		strslice.Find(o.AppList, fmt.Sprintf("*/*/%s", app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("*/%s/%s", namespace, app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("%s/*/%s", cluster, app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("%s/%s/%s", cluster, namespace, app))
}

type BaseImageGray struct {
	ImageTagSuffix string   `json:"imageTagSuffix"`
	AppList        []string `json:"appList"`
}

func (o BaseImageGray) AppContains(cluster, namespace, app string) bool {
	return strslice.Find(o.AppList, "*/*/*") ||
		strslice.Find(o.AppList, fmt.Sprintf("%s/*/*", cluster)) ||
		strslice.Find(o.AppList, fmt.Sprintf("*/*/%s", app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("*/%s/%s", namespace, app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("%s/*/%s", cluster, app)) ||
		strslice.Find(o.AppList, fmt.Sprintf("%s/%s/%s", cluster, namespace, app))
}

func (s setting) GetCluster(name string) *Cluster {
	for _, item := range s.Clusters {
		if item.Name == name {
			return &item
		}
	}
	return nil
}

func (s setting) GetClusterNamespaces(cluster string) []string {
	clu := s.GetCluster(cluster)
	if clu == nil {
		return []string{}
	}
	return clu.Namespaces
}

func (s setting) ClusterIndexOf(name string) int {
	for idx, it := range s.Clusters {
		if it.Name == name {
			return idx
		}
	}
	return -1
}

// GetEnvSortValue 获取环境的排序值,排序越靠后则返回的值越大
func (s setting) GetEnvSortValue(cluster, namespace string) int {
	ret := 0
	cluName := cluster
	for idx, it := range s.Clusters {
		cluIdx := idx
		//纷享云排在最前面，且相同环境放一起
		if it.IsFxiaokeCloud() {
			cluIdx = -1
		}
		if it.Name == cluName {
			ret = ret + cluIdx*100
			for nsIdx, ns := range it.Namespaces {
				if ns == namespace {
					ret = ret + nsIdx
					break
				}
			}
			break
		}
	}
	return ret
}

func (s setting) GetClusterByLBAddr(addr string) *Cluster {
	cluName := GetLBPools().GetClusterByLBAddr(addr)
	if cluName != "" {
		return s.GetCluster(cluName)
	}
	return nil
}

// todo: 需要考虑不同集群使用了相同pod ip段的情况
func (s setting) GetClusterByPodIP(podIP string) *Cluster {
	for _, item := range s.Clusters {
		if strings.HasPrefix(podIP, item.PodIPPrefix) {
			return &item
		}
	}
	return nil
}
func (s setting) GetClusterByNamespace(namespace string) []string {
	ret := make([]string, 0, 10)
	for _, item := range s.Clusters {
		for _, ns := range item.Namespaces {
			if ns == namespace {
				ret = append(ret, item.Name)
				break
			}
		}
	}
	return ret
}

func (s setting) FirstCluster() *Cluster {
	if len(s.Clusters) > 0 {
		return &s.Clusters[0]
	}
	return nil
}
func (s setting) GetProcessName(namespace, app string) string {
	processName := app
	if suffix, ok := s.AppSuffix[namespace]; ok {
		processName = processName + suffix
	}
	return processName
}

func (c Cluster) NormalImage2ProxyImage(imageFullName string) string {
	if c.ImageRegistryProxy == "" {
		return imageFullName
	}
	return strings.Replace(imageFullName, GetConfig().Harbor.Host, c.ImageRegistryProxy, 1)
}

func (c Cluster) ProxyImage2NormalImage(imageFullName string) string {
	if c.ImageRegistryProxy == "" {
		return imageFullName
	}
	return strings.Replace(imageFullName, c.ImageRegistryProxy, GetConfig().Harbor.Host, 1)
}

func (c Cluster) IsDedicatedCloud() bool {
	return c.CloudCategory == DedicatedCloud
}

func (c Cluster) IsFxiaokeCloud() bool {
	return c.CloudCategory == FxiaokeCloud
}

func (c Cluster) IsHybridCloud() bool {
	return c.CloudCategory == HybirdCloud
}

func (c Cluster) CloudCategoryDesc() string {
	if c.CloudCategory == FxiaokeCloud {
		return "纷享云"
	} else if c.CloudCategory == PublicCloud {
		return "公有云"
	} else if c.CloudCategory == DedicatedCloud {
		return "专属云"
	} else if c.CloudCategory == HybirdCloud {
		return "混合云"
	} else {
		return "未知"
	}
}

func (s setting) ClusterVersion(cluster string) string {
	for _, it := range s.Clusters {
		if cluster == it.Name {
			return it.Version
		}
	}
	return ""
}

var _set_ = setting{}

func init() {
	err := ReloadSettings()
	if err != nil {
		panic(err)
	}
}
func ReloadSettings() error {
	fmt.Println("settings.json reload")
	fp := "conf/settings.json"
	file, _ := os.ReadFile(fp)
	if err := json.Unmarshal(file, &_set_); err != nil {
		return errors.New(fmt.Sprintf("settings.json parse fail, err: %v", err))
	}
	_set_.ReloadTime = time.Now().Format("2006-01-02 15:04:05")
	_set_.Hostname, _ = os.Hostname()
	return nil
}

func GetSetting() setting {
	return _set_
}

//func UpdateSetting(jsonData string) error {
//	if err := json.Unmarshal([]byte(jsonData), &_set_); err != nil {
//		return err
//	}
//}

func (tw TimeWindow) IsSkip(namespace string) bool {
	return strslice.Find(tw.ExcludeNamespaces, namespace)
}
