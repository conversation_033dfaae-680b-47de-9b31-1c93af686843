apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{`{{DEPLOY_APP_NAME}}`}}
  namespace: {{`{{DEPLOY_K8S_NAMESPACE}}`}}
  labels:
    app: {{`{{DEPLOY_APP_NAME}}`}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
    fxiaoke.com/language: {{`{{DEPLOY_APP_LANGUAGE}}`}}
spec:
  replicas: {{`{{DEPLOY_APP_REPLICAS}}`}}
  selector:
    matchLabels:
      app: {{`{{DEPLOY_APP_NAME}}`}}
      version: v0
  template:
    metadata:
      labels:
        app: {{`{{DEPLOY_APP_NAME}}`}}
        version: v0
      annotations:
        fxiaoke.com/managed-by: gitlab-ci
        fxiaoke.com/language: "{{`{{DEPLOY_APP_LANGUAGE}}`}}"
        fxiaoke.com/commit-sha: "{{`{{CI_COMMIT_SHORT_SHA}}`}}"
    spec:
      containers:
        - name: {{`{{DEPLOY_APP_NAME}}`}}
          image: "{{`{{DEPLOY_APP_DOCKER_IMAGE}}`}}"
          imagePullPolicy: Always
          volumeMounts:
            - name: app-conf
              mountPath: /etc/app-conf
          ports:
            {{- range .Ports}}
            - containerPort: {{.Port}}
            {{- end}}
          resources:
            requests:
              cpu: {{`{{DEPLOY_APP_REQUESTS_CPU}}`}}
              memory: {{`{{DEPLOY_APP_REQUESTS_MEMORY}}`}}
            limits:
              cpu: {{`{{DEPLOY_APP_LIMITS_CPU}}`}}
              memory: {{`{{DEPLOY_APP_LIMITS_MEMORY}}`}}
          env:
            - name: TZ
              value: Asia/Shanghai
      volumes:
        - name: app-conf
          configMap:
            name: {{`{{DEPLOY_APP_NAME}}`}}-config
            items:
                - key: app-conf
                  path: app-conf