apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{`{{DEPLOY_APP_NAME}}`}}
  namespace: {{`{{DEPLOY_K8S_NAMESPACE}}`}}
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
  labels:
    app.kubernetes.io/managed-by: gitlab-ci
    app.kubernetes.io/name: {{`{{DEPLOY_APP_NAME}}`}}
spec:
  rules:
    - host: "{{`{{DEPLOY_APP_INGRESS_HOST}}`}}"
      http:
        paths:
          - backend:
              serviceName: {{`{{DEPLOY_APP_NAME}}`}}
              servicePort: 80
