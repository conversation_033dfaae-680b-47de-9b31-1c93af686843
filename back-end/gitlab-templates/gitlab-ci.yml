stages:
  - build
  - deploy
  - deploy-online
variables:
  DEPLOY_APP_NAME: "{{.App}}"
  DEPLOY_APP_DOCKER_IMAGE: "reg.firstshare.cn/app/{{.App}}:${CI_COMMIT_REF_NAME}"
  DEPLOY_APP_DOCKER_IMAGE_FONESHARE: "reg.foneshare.cn/app/{{.App}}:${CI_COMMIT_REF_NAME}"

build_image:
  stage: build
  tags:
    - docker-build
  only:
    - web
  script:
    - docker pull ${DEPLOY_APP_DOCKER_IMAGE} || true
    - docker build --pull -t ${DEPLOY_APP_DOCKER_IMAGE} .
    - docker push ${DEPLOY_APP_DOCKER_IMAGE}
    - docker tag ${DEPLOY_APP_DOCKER_IMAGE} ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}
    - docker push ${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}

  {{range $i, $v := .DeployEnvList}}
deploy_{{$v.Namespace}}:
  extends: .deploy_template
  {{- if $v.IsOnline}}
  stage: deploy-online
  tags:
    - k8s-deploy-foneshare
  when: manual
  {{- end}}
  before_script:
    - export DEPLOY_APP_DOCKER_IMAGE="{{if $v.IsOnline }}${DEPLOY_APP_DOCKER_IMAGE_FONESHARE}{{else}}${DEPLOY_APP_DOCKER_IMAGE}{{end}}"
  variables:
    DEPLOY_K8S_CLUSTER: "{{$v.RuntimeEnv}}-{{$v.Cluster}}"
    DEPLOY_K8S_NAMESPACE: "{{$v.Namespace}}"
    DEPLOY_K8S_CONFIGMAP_NAME: "configmap-{{$v.Namespace}}.yml"
    DEPLOY_APP_INGRESS_HOST: "{{$v.App}}.{{$v.Namespace}}.{{$v.IngressParentHost}}"
    DEPLOY_APP_LANGUAGE: "{{$v.Language}}"
    DEPLOY_APP_REPLICAS: "{{$v.Replicas}}"
    DEPLOY_APP_REQUESTS_CPU: "{{$v.RequestCPU}}"
    DEPLOY_APP_REQUESTS_MEMORY: "{{$v.RequestMemory}}"
    DEPLOY_APP_LIMITS_CPU: "{{$v.LimitCPU}}"
    DEPLOY_APP_LIMITS_MEMORY: "{{$v.LimitMemory}}"
  environment:
    name: "{{$v.Namespace}}/{{$v.Cluster}}"
    url: "https://k8s-app.{{- if $v.IsOnline }}foneshare.cn{{else}}firstshare.cn{{end}}/#/pod/list?cluster={{$v.Cluster}}&namespace=${DEPLOY_K8S_NAMESPACE}&app=${DEPLOY_APP_NAME}"
  {{end}}

.deploy_template:
  stage: deploy
  tags:
    - k8s-deploy
  only:
    - web
  image: reg.firstshare.cn/base/fs-kubectl:v2.0
  script:
    - "echo === deploy config: ${DEPLOY_K8S_CONFIGMAP_NAME}"
    - kubectl-proxy apply -f k8s-manifest/config/${DEPLOY_K8S_CONFIGMAP_NAME}
    - "echo === deploy deployment.yml"
    - kubectl-proxy apply --print-manifest -f k8s-manifest/templates/deployment.yml
    - "echo === deploy service.yml"
    - kubectl-proxy apply --print-manifest -f k8s-manifest/templates/service.yml
    - "echo === deploy ingress.yml"
    - kubectl-proxy apply --print-manifest -f k8s-manifest/templates/ingress.yml
