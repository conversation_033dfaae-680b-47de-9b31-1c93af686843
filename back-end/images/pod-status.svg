<svg id="SvgjsSvg1725" width="793.5078125" height="361" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"><defs id="SvgjsDefs1726"><marker id="SvgjsMarker1736" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1737" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1746" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1747" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1768" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1769" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1772" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1773" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1776" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1777" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1780" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1781" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1794" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1795" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker><marker id="SvgjsMarker1852" markerWidth="12" markerHeight="8" refX="9" refY="4" viewBox="0 0 12 8" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1853" d="M0,0 L12,4 L0,8 L0,0" fill="#9e9e9e" stroke="#9e9e9e" stroke-width="1"></path></marker></defs><rect id="svgbackgroundid" width="793.5078125" height="361" fill="#ffffff"></rect><g id="SvgjsG1728" transform="translate(126.23667417440663,171)"><path id="SvgjsPath1729" d="M 0 17C 0 -5.666666666666667 66 -5.666666666666667 66 17C 66 39.666666666666664 0 39.666666666666664 0 17Z" stroke="rgba(224,224,224,1)" stroke-width="1" fill-opacity="1" fill="#f5f5f5"></path><g id="SvgjsG1730"><text id="SvgjsText1731" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="46px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="6.375" transform="rotate(0)"><tspan id="SvgjsTspan1732" dy="16" x="33"><tspan id="SvgjsTspan1733" style="text-decoration:;">调度</tspan></tspan></text></g></g><g id="SvgjsG1734"><path id="SvgjsPath1735" d="M90.31226917004119 188L107.77447167222391 188L107.77447167222391 188L124.43667417440662 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1736)"></path></g><g id="SvgjsG1738" transform="translate(233.1942503590808,171)"><path id="SvgjsPath1739" d="M 0 17C 0 -5.666666666666667 66 -5.666666666666667 66 17C 66 39.666666666666664 0 39.666666666666664 0 17Z" stroke="rgba(224,224,224,1)" stroke-width="1" fill-opacity="1" fill="#f5f5f5"></path><g id="SvgjsG1740"><text id="SvgjsText1741" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="46px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="6.375" transform="rotate(0)"><tspan id="SvgjsTspan1742" dy="16" x="33"><tspan id="SvgjsTspan1743" style="text-decoration:;">准备</tspan></tspan></text></g></g><g id="SvgjsG1744"><path id="SvgjsPath1745" d="M192.73667417440663 188L212.71546226674377 188L212.71546226674377 188L231.3942503590808 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1746)"></path></g><g id="SvgjsG1748" transform="translate(340.5441176470599,171)"><path id="SvgjsPath1749" d="M 0 17C 0 -5.666666666666667 66 -5.666666666666667 66 17C 66 39.666666666666664 0 39.666666666666664 0 17Z" stroke="rgba(224,224,224,1)" stroke-width="1" fill-opacity="1" fill="#f5f5f5"></path><g id="SvgjsG1750"><text id="SvgjsText1751" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="46px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="6.375" transform="rotate(0)"><tspan id="SvgjsTspan1752" dy="16" x="33"><tspan id="SvgjsTspan1753" style="text-decoration:;">启动</tspan></tspan></text></g></g><g id="SvgjsG1754" transform="translate(596.3613393935054,171)"><path id="SvgjsPath1755" d="M 0 17C 0 -5.666666666666667 66 -5.666666666666667 66 17C 66 39.666666666666664 0 39.666666666666664 0 17Z" stroke="rgba(224,224,224,1)" stroke-width="1" fill-opacity="1" fill="#f5f5f5"></path><g id="SvgjsG1756"><text id="SvgjsText1757" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="46px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="6.375" transform="rotate(0)"><tspan id="SvgjsTspan1758" dy="16" x="33"><tspan id="SvgjsTspan1759" style="text-decoration:;">运行</tspan></tspan></text></g></g><g id="SvgjsG1760" transform="translate(698.7226787870106,171)"><path id="SvgjsPath1761" d="M 0 17C 0 -5.666666666666667 66 -5.666666666666667 66 17C 66 39.666666666666664 0 39.666666666666664 0 17Z" stroke="rgba(224,224,224,1)" stroke-width="1" fill-opacity="1" fill="#f5f5f5"></path><g id="SvgjsG1762"><text id="SvgjsText1763" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="46px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="6.375" transform="rotate(0)"><tspan id="SvgjsTspan1764" dy="16" x="33"><tspan id="SvgjsTspan1765" style="text-decoration:;">关闭</tspan></tspan></text></g></g><g id="SvgjsG1766"><path id="SvgjsPath1767" d="M299.6942503590808 188L319.86918400307036 188L319.86918400307036 188L338.7441176470599 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1768)"></path></g><g id="SvgjsG1770"><path id="SvgjsPath1771" d="M407.0441176470599 188L430.45656938047415 188L430.45656938047415 188L452.56902111388837 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1772)"></path></g><g id="SvgjsG1774"><path id="SvgjsPath1775" d="M662.8613393935054 188L680.542009090258 188L680.542009090258 188L696.9226787870107 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1776)"></path></g><g id="SvgjsG1778"><path id="SvgjsPath1779" d="M559.5 188L577.6806696967527 188L577.6806696967527 188L594.5613393935055 188" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1780)"></path><rect id="SvgjsRect1782" width="8" height="16" x="568.0764687877269" y="180" fill="#ffffff"></rect><text id="SvgjsText1783" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="8px" fill="#323232" font-weight="400" align="top" lineHeight="16px" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="177.375" transform="rotate(0)"><tspan id="SvgjsTspan1784" dy="16" x="572.0764687877269"><tspan id="SvgjsTspan1785" style="text-decoration:;">Y</tspan></tspan></text></g><g id="SvgjsG1786" transform="translate(25,168)"><path id="SvgjsPath1787" d="M 0 0L 64.31226917004119 0L 64.31226917004119 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1788"><text id="SvgjsText1789" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="65px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1790" dy="16" x="32.5"><tspan id="SvgjsTspan1791" style="text-decoration:;">Pod创建</tspan></tspan></text></g></g><g id="SvgjsG1792"><path id="SvgjsPath1793" d="M506.68451055694413 214.5L506.68451055694413 244L373.5441176470599 244L373.5441176470599 206.8" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1794)"></path><rect id="SvgjsRect1796" width="11" height="16" x="501.18451055694413" y="221" fill="#ffffff"></rect><text id="SvgjsText1797" font-family="微软雅黑" text-anchor="middle" font-size="13px" width="11px" fill="#323232" font-weight="400" align="top" lineHeight="16px" anchor="middle" family="微软雅黑" size="13px" weight="400" font-style="" opacity="1" y="218.375" transform="rotate(0)"><tspan id="SvgjsTspan1798" dy="16" x="506.68451055694413"><tspan id="SvgjsTspan1799" style="text-decoration:;">N</tspan></tspan></text></g><g id="SvgjsG1800" transform="translate(89.31226917004119,97)"><path id="SvgjsPath1801" d="M 0 0L 139.06422780212097 0L 139.06422780212097 40L 0 40Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1802"><text id="SvgjsText1803" font-family="微软雅黑" text-anchor="start" font-size="12px" width="120px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="2.5" transform="rotate(0)"><tspan id="SvgjsTspan1804" dy="15" x="10"><tspan id="SvgjsTspan1805" style="text-decoration:;">选择一个满足资源和调</tspan></tspan><tspan id="SvgjsTspan1806" dy="15" x="10"><tspan id="SvgjsTspan1807" style="text-decoration:;">度条件的宿主机</tspan></tspan></text></g></g><g id="SvgjsG1808"><path id="SvgjsPath1809" d="M159.23667417440663 170.5L159.23667417440663 154L158.84438307110167 154L158.84438307110167 137.5" stroke-dasharray="3,3" stroke="#9e9e9e" stroke-width="1" fill="none"></path></g><g id="SvgjsG1810" transform="translate(214.92819340855056,30)"><path id="SvgjsPath1811" d="M 0 0L 102.53211390106048 0L 102.53211390106048 41L 0 41Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1812"><text id="SvgjsText1813" font-family="微软雅黑" text-anchor="start" font-size="12px" width="83px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="3" transform="rotate(0)"><tspan id="SvgjsTspan1814" dy="15" x="10"><tspan id="SvgjsTspan1815" style="text-decoration:;">· 拉取镜像</tspan></tspan><tspan id="SvgjsTspan1816" dy="15" x="10"><tspan id="SvgjsTspan1817" style="text-decoration:;">· 分配pod ip</tspan></tspan></text></g></g><g id="SvgjsG1818"><path id="SvgjsPath1819" d="M266.1942503590808 170.5L266.1942503590808 121L266.1942503590808 121L266.1942503590808 71.5" stroke-dasharray="3,3" stroke="#9e9e9e" stroke-width="1" fill="none"></path></g><g id="SvgjsG1820" transform="translate(326.3161764705899,98)"><path id="SvgjsPath1821" d="M 0 0L 94.45588235294008 0L 94.45588235294008 38L 0 38Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1822"><text id="SvgjsText1823" font-family="微软雅黑" text-anchor="start" font-size="12px" width="75px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="9" transform="rotate(0)"><tspan id="SvgjsTspan1824" dy="15" x="10"><tspan id="SvgjsTspan1825" style="text-decoration:;">启动业务进程</tspan></tspan></text></g></g><g id="SvgjsG1826"><path id="SvgjsPath1827" d="M373.5441176470599 170.5L373.5441176470599 153.5L373.5441176470599 153.5L373.5441176470599 136.5" stroke-dasharray="3,3" stroke="#9e9e9e" stroke-width="1" fill="none"></path></g><g id="SvgjsG1828" transform="translate(694.5047300690617,99)"><path id="SvgjsPath1829" d="M 0 0L 74.43589743589791 0L 74.43589743589791 36L 0 36Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1830"><text id="SvgjsText1831" font-family="微软雅黑" text-anchor="start" font-size="12px" width="55px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="8" transform="rotate(0)"><tspan id="SvgjsTspan1832" dy="15" x="10"><tspan id="SvgjsTspan1833" style="text-decoration:;">删除pod</tspan></tspan></text></g></g><g id="SvgjsG1834"><path id="SvgjsPath1835" d="M731.7226787870106 170.5L731.7226787870106 153L731.7226787870106 153L731.7226787870106 135.5" stroke-dasharray="3,3" stroke="#9e9e9e" stroke-width="1" fill="none"></path></g><g id="SvgjsG1836" transform="translate(529.197213064188,25)"><path id="SvgjsPath1837" d="M 0 0L 200.32825265863494 0L 200.32825265863494 68L 0 68Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1838"><text id="SvgjsText1839" font-family="微软雅黑" text-anchor="start" font-size="12px" width="181px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="1.5" transform="rotate(0)"><tspan id="SvgjsTspan1840" dy="15" x="10"><tspan id="SvgjsTspan1841" style="text-decoration:;">业务进程正常启动且通过就绪健康</tspan></tspan><tspan id="SvgjsTspan1842" dy="15" x="10"><tspan id="SvgjsTspan1843" style="text-decoration:;">检测</tspan></tspan><tspan id="SvgjsTspan1844" dy="15" x="10"><tspan id="SvgjsTspan1845" style="text-decoration:;">[备注]: 只有在当前状态下才会接</tspan></tspan><tspan id="SvgjsTspan1846" dy="15" x="10"><tspan id="SvgjsTspan1847" style="text-decoration:;">收http流量 </tspan></tspan></text></g></g><g id="SvgjsG1848"><path id="SvgjsPath1849" d="M629.3613393935054 170.5L629.3613393935054 132L629.3613393935055 132L629.3613393935055 93.5" stroke-dasharray="3,3" stroke="#9e9e9e" stroke-width="1" fill="none"></path></g><g id="SvgjsG1850"><path id="SvgjsPath1851" d="M629.3613393935054 205.5L629.3613393935054 267L373.5441176470599 267L373.5441176470599 206.8" stroke="#9e9e9e" stroke-width="1" fill="none" marker-end="url(#SvgjsMarker1852)"></path></g><g id="SvgjsG1854" transform="translate(373.3673412665049,273)"><path id="SvgjsPath1855" d="M 0 0L 254.63265873349508 0L 254.63265873349508 63L 0 63Z" stroke-dasharray="3,4" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1856"><text id="SvgjsText1857" font-family="微软雅黑" text-anchor="start" font-size="12px" width="235px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="start" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="-1" transform="rotate(0)"><tspan id="SvgjsTspan1858" dy="15" x="10"><tspan id="SvgjsTspan1859" style="text-decoration:;">· 程序异常退出</tspan></tspan><tspan id="SvgjsTspan1860" dy="15" x="10"><tspan id="SvgjsTspan1861" style="text-decoration:;">· 内存溢出（OOM)</tspan></tspan><tspan id="SvgjsTspan1862" dy="15" x="10"><tspan id="SvgjsTspan1863" style="text-decoration:;">· 存活健康检测失败导致重启</tspan></tspan><tspan id="SvgjsTspan1864" dy="15" x="10"><tspan id="SvgjsTspan1865" style="text-decoration:;">· 其他类错误</tspan></tspan></text></g></g><g id="SvgjsG1866" transform="translate(454.3690211138884,162)"><path id="SvgjsPath1867" d="M 0 26L 52.31548944305581 0L 104.63097888611162 26L 52.31548944305581 52Z" stroke="rgba(158,158,158,1)" stroke-width="1" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1868"><text id="SvgjsText1869" font-family="微软雅黑" text-anchor="middle" font-size="12px" width="85px" fill="#616161" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="微软雅黑" size="12px" weight="400" font-style="" opacity="1" y="16.259999999999998" transform="rotate(0)"><tspan id="SvgjsTspan1870" dy="15" x="52.5"><tspan id="SvgjsTspan1871" style="text-decoration:;">是否启动成功?</tspan></tspan></text></g></g></svg>