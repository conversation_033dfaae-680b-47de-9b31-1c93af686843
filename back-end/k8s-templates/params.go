package k8s_templates

import (
	"fs-k8s-app-manager/models/data"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
)

type Artifact struct {
	GitUrl        string
	Module        string
	ContextPath   string
	Tag           string
	CommitID      string
	ArtifactImage string
	ArtifactSrc   string
	ArtifactDst   string
}

type DeploymentParam struct {
	Cluster              string                  `yaml:"Cluster,omitempty"`
	Namespace            string                  `yaml:"Namespace,omitempty"`
	App                  string                  `yaml:"App"`
	AppImage             string                  `yaml:"AppImage"`
	ImagePullPolicy      string                  `yaml:"ImagePullPolicy"`
	Replicas             uint                    `yaml:"Replicas"`
	MaxSurge             string                  `yaml:"MaxSurge"`
	DeployId             string                  `yaml:"DeployId"`
	Annotations          map[string]string       `yaml:"Annotations"`
	Labels               map[string]string       `yaml:"Labels"`
	PodAnnotations       map[string]string       `yaml:"PodAnnotations"`
	Resources            data.Resources          `yaml:"Resources"`
	DeployStrategy       constant.DeployStrategy `yaml:"DeployStrategy"`
	LivenessProbe        data.Probe              `yaml:"LivenessProbe"`
	ReadinessProbe       data.Probe              `yaml:"ReadinessProbe"`
	StartupProbe         data.Probe              `yaml:"StartupProbe"`
	Schedule             data.Schedule           `yaml:"Schedule"`
	SchedulerName        string                  `yaml:"SchedulerName"`
	PVC                  data.PVC                `yaml:"PVC"`
	Envs                 datatype.Envs           `yaml:"Envs"`
	Ports                datatype.Ports          `yaml:"Ports"`
	PartnerApps          []string                `yaml:"PartnerApps"`
	ExclusiveApps        []string                `yaml:"ExclusiveApps"`
	PreStopWebhook       string                  `yaml:"PreStopWebhook"`
	PreStopRetainSeconds uint                    `yaml:"PreStopRetainSeconds"`
	AddSysctlKeepalive   bool                    `yaml:"AddSysctlKeepalive"`
	Privileged           bool                    `yaml:"Privileged"`
	Artifacts            []Artifact              `yaml:"Artifacts"`
	ExtInitContainers    []Artifact              `yaml:"ExtInitContainers"`
	TomcatLogEmptyDir    bool                    `yaml:"TomcatLogEmptyDir"`
	AppLogsEmptyDir      bool                    `yaml:"AppLogsEmptyDir"`
	Port80To8080         bool                    `yaml:"Port80To8080"` // 是否开启端口跳转（80转8080）, 主要用在tomcat容器 由root用户 转 非root用户 的改造过程的兼容处理
	FailRollout          bool                    `yaml:"FailRollout"`
	RevisionHistoryLimit int                     `yaml:"RevisionHistoryLimit"`
	DNSConfigSearches    []string                `yaml:"DNSConfigSearches"`
}

type ServiceParam struct {
	App       string
	Namespace string
	Ports     []Port
}

type PrometheusMonitorParam struct {
	App        string
	Namespace  string
	Prometheus string
	Endpoints  []Endpoint
}

type Port struct {
	Name          string
	Port          int32
	TargetPortStr string
	NodePort      int32
}

type Endpoint struct {
	Path          string
	Port          string
	Interval      string
	ScrapeTimeout string
}

type IngressParam struct {
	App         string
	Namespace   string
	IngressHost string
}

type HpaParam struct {
	App                            string
	Namespace                      string
	MinReplicas                    int32
	MaxReplicas                    int32
	TargetCPUUtilizationPercentage int32
	Annotations                    map[string]string
}
