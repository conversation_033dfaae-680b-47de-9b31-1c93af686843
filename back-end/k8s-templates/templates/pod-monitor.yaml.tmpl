apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  labels:
    app.kubernetes.io/component: metrics
    release: {{.Prometheus}}
  name: {{.App}}
  namespace: {{.Namespace}}
spec:
  podMetricsEndpoints:
    {{- range .Endpoints}}
    - interval: {{.Interval}}
      scrapeTimeout: {{.ScrapeTimeout}}
      path: {{.Path}}
      port: {{.Port}}
    {{- end}}
  jobLabel: {{.App}}
  podTargetLabels:
    - app
  selector:
    matchLabels:
      app: {{.App}}
