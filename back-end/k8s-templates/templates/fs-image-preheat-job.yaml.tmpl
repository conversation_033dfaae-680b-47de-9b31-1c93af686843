{{- range .Jobs }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: "{{.JobName}}"
  namespace: image-preheat
  labels:
    app: "{{.AppName}}"
spec:
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: "{{.AppName}}"
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - "{{.AppName}}"
                topologyKey: kubernetes.io/hostname
              weight: 100
      restartPolicy: OnFailure
      initContainers:
        {{- range .InitContainers }}
        - name: "{{.Name}}"
          image: "{{.Image}}"
          imagePullPolicy: "Always"
          command: ["echo", "success"]
        {{- end }}
      containers:
        - name: "{{.AppName}}"
          image: reg.foneshare.cn/docker.io/alpine:latest
          command:
            - echo
            - success
          resources:
            limits:
              cpu: 30m
              memory: 20Mi
            requests:
              cpu: 30m
              memory: 20Mi
{{- end }}