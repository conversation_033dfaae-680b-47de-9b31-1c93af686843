apiVersion: autoscaling.fxiaoke.com/v1
kind: PodAutoScaler
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
  labels:
    app: {{.App}}
    {{- range $key, $value := .Labels }}
    "{{$key}}": "{{$value}}"
    {{- end }}
  annotations:
    {{- range $key, $value := .Annotations}}
    "{{$key}}": "{{$value}}"
    {{- end}}
spec:
  app: {{.App}}
  paused: {{.Paused}}
  minReplicaCount: {{.MinReplicas}}
  maxReplicaCount: {{.MaxReplicas}}
  scaleUp:
    initialDelaySeconds: {{.ScaleUpInitialDelaySeconds}}
    stabilizationWindowSeconds: {{.ScaleUpStabilizationWindowSeconds}}
    replicaStep: {{.ScaleUpReplicaStep}}
    hourWindow:
    {{- range $it := .ScaleUpHourWindow }}
      - {{ $it }}
    {{- end }}
  scaleDown:
    stabilizationWindowSeconds: {{.ScaleDownStabilizationWindowSeconds}}
    replicaStep: {{.ScaleDownReplicaStep}}
    hourWindow:
    {{- range $it := .ScaleDownHourWindow }}
      - {{ $it }}
    {{- end }}
  triggers:
    - type: cpu
      metadata:
        metricType: Utilization
        value: "{{.TriggerCpuUtilization}}"


