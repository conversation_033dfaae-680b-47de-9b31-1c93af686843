apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 10m
spec:
  rules:
    - host: "{{.App}}.{{.IngressHost}}"
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: {{.App}}
                port:
                  name: http