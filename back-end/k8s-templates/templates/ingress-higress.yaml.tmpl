apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 10m
  name: {{.App}}
  namespace: {{.Namespace}}
spec:
  ingressClassName: higress
  rules:
    - host: "{{.App}}.{{.IngressHost}}"
      http:
        paths:
          - backend:
              service:
                name:  {{.App}}
                port:
                  name: http
            path: /
            pathType: Prefix
