apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
  annotations:
    {{- range $key, $value := .Annotations}}
    "{{$key}}": "{{$value}}"
    {{- end}}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{.App}}
  minReplicas: {{.MinReplicas}}
  maxReplicas: {{.MaxReplicas}}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{.TargetCPUUtilizationPercentage}}