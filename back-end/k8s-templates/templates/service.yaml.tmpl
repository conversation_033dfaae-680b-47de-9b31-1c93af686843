apiVersion: v1
kind: Service
metadata:
  name: {{.App}}
  namespace: {{.Namespace}}
  labels:
    app: {{.App}}
  annotations:
    prometheus.io/metrics-type: jvm
    prometheus.io/path: /metrics
    prometheus.io/port: "8090"
    prometheus.io/scrape: "true"
spec:
  type: NodePort
  selector:
    app: {{.App}}
  ports:
    {{- range .Ports}}
    - name: {{.Name}}
      port: {{.Port}}
      targetPort: {{.TargetPortStr}}
      {{- if .NodePort}}
      nodePort: {{.NodePort}}
      {{- end}}
    {{- end}}
