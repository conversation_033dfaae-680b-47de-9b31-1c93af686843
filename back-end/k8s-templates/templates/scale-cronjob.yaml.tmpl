apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: {{.Name}}
  namespace: {{.Namespace}}
  labels:
    app: {{.App}}
spec:
  schedule: "{{.Schedule}}"
  concurrencyPolicy: Replace
  successfulJobsHistoryLimit: 6
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: fs-k8s-scaler
              image: reg.firstshare.cn/base/fs-k8s-scaler:v0.1
              args:
                - -appName={{.APP}}
                - -namespace={{.Namespace}}
                - -replicas={{.Replicas}}
          restartPolicy: Never