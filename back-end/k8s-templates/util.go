package k8s_templates

import (
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/file"
	"fs-k8s-app-manager/pkg/util/template"
)

func BuildDeployment(p DeploymentParam) (string, error) {
	return template.ParseTemplateFile(getFilePath("deployment.yaml.tmpl"), p)
}
func BuildDeploymentWithName(p DeploymentParam, filename string) (string, error) {
	return template.ParseTemplateFile(getFilePath(filename), p)
}
func BuildService(p ServiceParam) (string, error) {
	return template.ParseTemplateFile(getFilePath("service.yaml.tmpl"), p)
}

func BuildPodMonitor(p PrometheusMonitorParam) (string, error) {
	return template.ParseTemplateFile(getFilePath("pod-monitor.yaml.tmpl"), p)
}

func BuildIngress(p IngressParam) (string, error) {
	return template.ParseTemplateFile(getFilePath("ingress.yaml.tmpl"), p)
}
func BuildIngressWithName(p IngressParam, filename string) (string, error) {
	return template.ParseTemplateFile(getFilePath(filename), p)
}
func BuildHpa(p HpaParam) (string, error) {
	return template.ParseTemplateFile(getFilePath("scale-hpa.yaml.tmpl"), p)
}

func BuildPodAutoScaler(p dto.PodAutoScalerDTO) (string, error) {
	return template.ParseTemplateFile(getFilePath("fs-pod-autoscaler.yaml.tmpl"), p)
}

func GetFileContent(filename string) (string, error) {
	return file.Read(getFilePath(filename))
}

func BuildContentWithFilename(filename string, param interface{}) (string, error) {
	return template.ParseTemplateFile(getFilePath(filename), param)
}

func getFilePath(name string) string {
	return file.AbsPath("k8s-templates/templates/" + name)
}
