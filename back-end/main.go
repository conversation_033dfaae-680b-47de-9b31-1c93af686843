package main

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cronjob"
	"fs-k8s-app-manager/pkg/logger"
	"fs-k8s-app-manager/pkg/watcher"
	"fs-k8s-app-manager/routers"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
)

// @title			k8s应用管理系统API
// @version		1.0
// @description	k8s应用管理系统API接口文档
// @description  ◉ 线下地址：k8s-app.firstshare.cn
// @description  ◉ 线上地址：k8s-app.foneshare.cn
// @securityDefinitions.basic	BasicAuth
// @securityDefinitions.apikey	ApiKeyAuth
// @in							header
// @name						Authorization
func main() {
	logger.Setup()
	watcher.Setup()
	gin.SetMode(config.GetConfig().App.RunMode)
	routersInit := routers.InitRouter()
	readTimeout := config.GetConfig().App.ReadTimeout
	writeTimeout := config.GetConfig().App.WriteTimeout
	endPoint := fmt.Sprintf(":%d", config.GetConfig().App.HttpPort)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	log.Printf("[info] start http server listening %s", endPoint)
	cronjob.Run()
	server.ListenAndServe()
}
