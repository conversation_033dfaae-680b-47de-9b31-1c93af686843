package web

import (
	"fmt"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"net/http"
)

// Response
// @Description 响应结构体
type Response struct {
	Code    int         `json:"code" example:"200"`        // 状态码
	Message string      `json:"message" example:"success"` // 状态信息描述
	Data    interface{} `json:"data"`                      // 数据
}

const (
	CODE_SUCCESS         = 200
	CODE_CLIENT_ERROR    = 400
	CODE_REDIRECT        = 301
	CODE_PERMISSION_DENY = 403
	CODE_SERVER_ERROR    = 500
)

//func Success(data interface{}) (ret Response) {
//	ret = Response{
//		Code: SUCCESS,
//		Msg:  GetMsg(SUCCESS),
//		Data: data,
//	}
//	return
//}
//func ServerError(msg string) (ret Response) {
//	ret = Response{
//		Code: SERVER_ERROR,
//		Msg:  firstNotEmpty(msg, GetMsg(SERVER_ERROR)),
//	}
//	return
//}
//func ClientError(msg string) (ret Response) {
//	ret = Response{
//		Code: CLIENT_ERROR,
//		Msg:  firstNotEmpty(msg, GetMsg(CLIENT_ERROR)),
//	}
//	return
//}

func FailJson(c *gin.Context, code int, Message string) {
	_ = FailJson2(c, code, Message)
}

func FailJson2(c *gin.Context, code int, Message string) Response {
	log.Warn(fmt.Sprintf("Response is not success, code: %d, Message: %s ", code, Message))
	repo := Response{
		Code:    code,
		Message: Message,
		Data:    nil,
	}
	c.JSON(http.StatusOK, repo)
	return repo
}

func SuccessJson(c *gin.Context, data interface{}) {
	_ = SuccessJson2(c, data)
}

func SuccessJson2(c *gin.Context, data interface{}) Response {
	repo := Response{
		Code:    CODE_SUCCESS,
		Message: "success",
		Data:    data,
	}
	c.JSON(http.StatusOK, repo)
	return repo
}

func FailJsonWithFailStatus(c *gin.Context, code int, Message string) {
	_ = FailJsonWithFailStatus2(c, code, Message)
}

func FailJsonWithFailStatus2(c *gin.Context, code int, Message string) Response {
	log.Warn(fmt.Sprintf("Response is not success, code: %d, Message: %s ", code, Message))
	repo := Response{
		Code:    code,
		Message: Message,
		Data:    nil,
	}
	c.JSON(code, repo)
	return repo
}

func RedirectJson(c *gin.Context, data interface{}) {
	_ = RedirectJson2(c, data)
}

func RedirectJson2(c *gin.Context, data interface{}) Response {
	repo := Response{
		Code:    CODE_REDIRECT,
		Message: "redirect",
		Data:    data,
	}
	c.JSON(http.StatusOK, repo)
	return repo
}

//func ReturnJson(c *gin.Context, code int, msg string, data interface{}) {
//	if code != CODE_SUCCESS {
//		logging.Warn(fmt.Sprintf("Response is not success, code: %d, msg: %s ", code, msg))
//	}
//	repo := Response{
//		Code: code,
//		Msg:  msg,
//		Data: data,
//	}
//	c.JSON(http.StatusOK, repo)
//}

//func firstNotEmpty(first, last string) string {
//	if len(first) > 0 {
//		return first
//	}
//	return last
//}
