package parentpom

import (
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/parent_pom_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/web"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type ParentPomSaveParam struct {
	ID        uint   `form:"id"`
	Name      string `form:"name"`
	Remark    string `form:"remark"`
	IsArchive bool   `form:"isArchive"`
	Namespaces string `form:"namespaces"`
	Enable    bool   `form:"enable"`
	ShowName  string `form:"showName"`
	SortRank  int64  `form:"sortRank"`
}

func Search(c *gin.Context) {
	keyword := c.Query("keyword")
	ret, err := parent_pom_service.Search(keyword)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, ret)
}

func Delete(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id is required")
		return
	}
	// Convert id from string to uint
	uid, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "invalid id")
		return
	}
	err = parent_pom_service.Delete(uint(uid))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Save(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		panic("只有管理员才有权限操作")
	}
	var param ParentPomSaveParam
	if err := c.ShouldBindJSON(&param); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if param.Name != "fxiaoke-parent-pom" && !strings.HasPrefix(param.Name, "fxiaoke-parent-pom-") {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "父pom名称只能为 fxiaoke-parent-pom 或以 fxiaoke-parent-pom- 开头")
		return
	}

	model := models.ParentPom{}
	if param.ID != 0 {
		var err error
		model, err = parent_pom_service.FindById(param.ID)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	} else {
		_, err := parent_pom_service.FindByName(param.Name)
		if err == nil {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "父pom已存在, 名称："+param.Name)
			return
		}
	}
	model.Name = param.Name
	model.Remark = param.Remark
	model.IsArchive = param.IsArchive
	model.Enable = param.Enable
	model.ShowName = param.ShowName
	model.SortRank = param.SortRank
	model.Namespaces = param.Namespaces
	err := parent_pom_service.Save(&model)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, model)
}
