package grafana

import (
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/notify_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"io"
	"strconv"
	"strings"
	"time"
)

func PhoneCallTest(c *gin.Context) {
	phone := c.Query("phone")
	msg := c.Query("msg")
	if phone == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "phone is required")
		return
	}
	err := notify_service.PhoneCall(phone, 500, msg)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}
func Alert(c *gin.Context) {
	reqBody, _ := io.ReadAll(c.Request.Body)
	logContent := make(map[string]interface{})
	logContent["reqBody"] = string(reqBody)
	body, _ := parseBody(string(reqBody))
	logContent["parseBody"] = body

	alerts := make(map[string][]metric)
	for _, alert := range body.Alerts {
		metrics := make([]metric, 0, 10)
		for _, met := range alert.parseMetrics() {
			metrics = append(metrics, met)
			//过滤掉DatasourceError、DatasourceNoData类的错误
			if alert.Status == "firing" && alert.Labels["alertname"] != "DatasourceError" && alert.Labels["alertname"] != "DatasourceNoData" {
				if met.Pod() != "" {
					sendQixinAlert(alert, met)
				} else {
					sendQixinAlertWithoutApp(alert, met)
				}
				if strings.EqualFold("true", alert.Labels["phoneNotify"]) && alert.Labels["phones"] != "" {
					phoneAlert(alert.Labels["phones"], alert.Labels["ttlCode"], alert.Labels["alertname"])
				}
			}

		}
		alerts[alert.Labels["alertname"]] = metrics
	}
	logContent["parseAlerts"] = alerts
	log_service.CreateBySys("Grafana-告警", "params", logContent)
	web.SuccessJson(c, nil)
}

func phoneAlert(phones string, ttlCode string, message string) {
	code, err := strconv.Atoi(ttlCode)
	if err != nil {
		log.Warnf("tts code is not a number,ttlCode: %s", ttlCode)
		code = 500
	}
	if err := notify_service.PhoneCall(phones, code, message); err != nil {
		log.Warn("Grafana语音告警发送失败，" + err.Error())
	}
}

func sendQixinAlertWithoutApp(a alert, m metric) {
	qixin := make([]string, 0, 10)
	title := a.Labels["alertname"]
	qixin = append(qixin, "--- "+title+" ---")
	summary := a.Annotations["summary"]

	if m.BizName() != "" {
		qixin = append(qixin, fmt.Sprintf("【BizName】：%s", m.BizName()))
	}
	val := fmt.Sprintf("%.2f", m.Value())
	if strings.HasSuffix(val, ".00") {
		val = val[:len(val)-3]
	}
	qixin = append(qixin, fmt.Sprintf("【当前值】：%s", val))
	if summary != "" {
		qixin = append(qixin, fmt.Sprintf("【描述】：%s", summary))
	}
	//qixin = append(qixin, fmt.Sprintf("【负责人】：%s", "@"+cmdb_service.GetOwnerNames(app, " @")))

	qixin = append(qixin, fmt.Sprintf("【监控图】：%s", a.PanelURL))

	if err := notify_service.SendQiXinToImportantAlertSession(make([]int, 0, 0), strings.Join(qixin, "\n")); err != nil {
		log.Warn("Grafana企信告警发送失败，" + err.Error())
	}
}
func sendQixinAlert(a alert, m metric) {
	qixin := make([]string, 0, 10)
	title := a.Labels["alertname"]
	qixin = append(qixin, "--- "+title+" ---")
	pod := m.Pod()
	app := m.App()
	if app == "" {
		app = k8s_util.GetAppName(pod)
	}
	namespace := m.Namespace()
	summary := a.Annotations["summary"]

	qixin = append(qixin, fmt.Sprintf("【应用】：%s", app))
	if namespace != "" {
		qixin = append(qixin, fmt.Sprintf("【环境】：%s", namespace))
	}
	qixin = append(qixin, fmt.Sprintf("【Pod】：%s", pod))
	val := fmt.Sprintf("%.2f", m.Value())
	if strings.HasSuffix(val, ".00") {
		val = val[:len(val)-3]
	}
	qixin = append(qixin, fmt.Sprintf("【当前值】：%s", val))
	if summary != "" {
		qixin = append(qixin, fmt.Sprintf("【描述】：%s", summary))
	}
	qixin = append(qixin, fmt.Sprintf("【负责人】：%s", "@"+strings.Join(app_service.GetAppMainOwnersWithCache(app), "@")))
	if namespace != "" {
		qixin = append(qixin, fmt.Sprintf("【监控图】：%s&var-namespace=%s&var-pod=%s", a.PanelURL, namespace, pod))
	} else {
		qixin = append(qixin, fmt.Sprintf("【监控图】：%s&var-namespace=All&var-pod=%s", a.PanelURL, pod))
	}
	alertT := alertType(title)
	cluster := ""
	for _, clu := range config.GetSetting().GetClusterByNamespace(namespace) {
		if ret, err := k8s_service.PodDetail(clu, namespace, pod); ret != nil && err == nil {
			cluster = clu
			break
		}
	}
	if alertT == "thread-block" || alertT == "thread-count-limit" {
		if cluster != "" {
			if dumpFile, err := k8s_service.ThreadDump(cluster, namespace, pod, false); err != nil {
				log.Warnf("thread dump fail, %v", err)
			} else {
				qixin = append(qixin, fmt.Sprintf("【线程dump】："+dumpFile))
			}
			go func() {
				_, _ = k8s_service.ThreadDump(cluster, namespace, pod, true)
				_, _ = k8s_service.ThreadDumpByGstack(cluster, namespace, pod)
			}()
		}
	}
	if alertT == "thread-block" {
		if cluster != "" && namespace != "" && app != "" {
			err := scaleUp(cluster, namespace, app, 3)
			if err != nil {
				log.Warnf("scale up fail, %v", err)
				qixin = append(qixin, fmt.Sprintf("【自动扩容】：扩容失败，"+err.Error()))
			} else {
				qixin = append(qixin, fmt.Sprintf("【自动扩容】：扩容了 %d 个副本", 3))
				event_service.Create("Alert", event_service.BuildAppKey(cluster, namespace, app),
					fmt.Sprintf("%s 告警触发扩容，扩容了 %d 个副本", alertT, 3))
			}
		}
	}
	if err := notify_service.SendQiXinToImportantAlertSession(app_service.GetMainOwnerIdWithCache(app), strings.Join(qixin, "\n")); err != nil {
		log.Warn("Grafana企信告警发送失败，" + err.Error())
	}
}

func alertType(title string) string {
	if strings.Contains(title, "BLOCKED") && strings.Contains(title, "线程") {
		return "thread-block"
	} else if strings.Contains(title, "线程总数") {
		return "thread-count-limit"
	} else {
		return ""
	}
}

func scaleUp(cluster, namespace, app string, addReplicas int32) error {
	dep, err := k8s_service.GetDeploymentDTO(cluster, namespace, app)
	if err != nil {
		return err
	}
	if dep == nil {
		return errors.New("deployment not found")
	}
	if dep.Replicas >= 30 {
		return errors.New("副本数已达上限（30个）")
	}
	return kubectl.Scale(cluster, namespace, app, dep.Replicas+addReplicas)
}
func deregister(cluster, namespace, app, pod string) error {
	return nil
}

func scaleUpAndDeregister() {

}

func parseBody(jsonStr string) (ret webhookBody, err error) {
	err = json.Unmarshal([]byte(jsonStr), &ret)
	return
}

type webhookBody struct {
	Receiver          string            `json:"receiver"`
	Status            string            `json:"status"`
	Alerts            []alert           `json:"alerts"`
	GroupLabels       map[string]string `json:"groupLabels"`
	CommonLabels      map[string]string `json:"commonLabels"`
	CommonAnnotations map[string]string `json:"commonAnnotations"`
	ExternalURL       string            `json:"externalURL"`
	Version           string            `json:"version"`
	GroupKey          string            `json:"groupKey"`
	TruncatedAlerts   int               `json:"truncatedAlerts"`
	OrgID             int               `json:"orgId"`
	Title             string            `json:"title"`
	State             string            `json:"state"`
	Message           string            `json:"message"`
}
type alert struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     time.Time         `json:"startsAt"`
	EndsAt       time.Time         `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
	Fingerprint  string            `json:"fingerprint"`
	SilenceURL   string            `json:"silenceURL"`
	DashboardURL string            `json:"dashboardURL"`
	PanelURL     string            `json:"panelURL"`
	ValueString  string            `json:"valueString"`
}

func (a alert) parseMetrics() []metric {
	ret := make([]metric, 0, 10)
	metrics := strings.Split(a.ValueString, "],")
	metricsRep := strings.NewReplacer("[", " ", "]", " ", "{", " ", "}", " ", ",", " ", "'", "", "\"", "")

	for _, labels := range metrics {
		item := make(map[string]string)
		labels = metricsRep.Replace(labels)
		for _, label := range strings.Fields(labels) {
			parts := strings.Split(label, "=")
			if len(parts) == 2 {
				item[parts[0]] = parts[1]
			}
		}
		ret = append(ret, item)
	}
	return ret
}

type metric map[string]string

func (m metric) Value() float64 {
	val := m["value"]
	ret, _ := strconv.ParseFloat(val, 64)
	return ret
}
func (m metric) App() string {
	return m["app_name"]
}
func (m metric) Pod() string {
	ret := m["pod_name"]
	if ret == "" {
		ret = m["pod"]
	}
	return ret
}
func (m metric) Namespace() string {
	return m["namespace"]
}

func (m metric) BizName() string {
	return m["bizName"]
}
