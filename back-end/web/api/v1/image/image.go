package image

import (
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/client/harbor"
	"fs-k8s-app-manager/pkg/util/times"
	"fs-k8s-app-manager/service/parent_pom_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/tool/harbor_tool"
	"github.com/dustin/go-humanize"
	"github.com/gin-gonic/gin"
	"sort"
)

type ArtifactImageSearch struct {
	GitUrl     string   `form:"gitUrl" binding:"required"`
	GitModule  string   `form:"gitModule"`
	ParentPoms []string `form:"parentPoms[]"`
	tag        string   `form:"tag"`
	Page       int      `form:"page,default=1" `
	Limit      int      `form:"limit,default=20"`
}

func getParentPoms() ([]string, error) {
	ret := make([]string, 0, 3)
	parentPoms, err := parent_pom_service.FindAll()
	if err != nil {
		return ret, err
	}
	for _, it := range parentPoms {
		ret = append(ret, it.Name)
	}
	return ret, nil
}

func Search(c *gin.Context) {
	var p ArtifactImageSearch
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if len(p.ParentPoms) < 1 {
		if v, err := getParentPoms(); err == nil {
			p.ParentPoms = v
		} else {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	}
	imageTags := make([]map[string]interface{}, 0, 200)
	var count int64
	for _, parentPom := range p.ParentPoms {
		repo := harbor_tool.BuildRepository(p.GitUrl, p.GitModule, parentPom)
		if a, co, err := harbor.Create().GetArtifacts(config.GetConfig().Harbor.ArtifactProject, repo, p.Page, p.Limit); err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		} else {
			imageTags = append(imageTags, parseImageTagDTOs(a, config.GetConfig().Harbor.ArtifactProject, repo)...)
			count += co
		}
	}
	sort.Slice(imageTags, func(i, j int) bool {
		return imageTags[i]["createTime"].(string) > imageTags[j]["createTime"].(string)
	})
	data := make(map[string]interface{})
	data["count"] = count
	data["data"] = imageTags
	web.SuccessJson(c, data)
}

func parseImageTagDTOs(artifacts []harbor.Artifact, project, reposotry string) []map[string]interface{} {
	var ret []map[string]interface{}
	for _, artifact := range artifacts {
		for _, tag := range artifact.Tags {
			createTime := tag.PushTime
			if t, err := times.ConvertUTCToChinaTime(tag.PushTime); err == nil {
				createTime = t.Format("2006-01-02 15:04:05")
			}
			ret = append(ret, map[string]interface{}{
				"projectId":    artifact.ProjectId,
				"repositoryId": artifact.RepositoryId,
				"project":      project,
				"repository":   reposotry,
				"dockerTag":    tag.Name,
				"size":         artifact.Size,
				"sizeDesc":     humanize.Bytes(uint64(artifact.Size)),
				"digest":       artifact.Digest,
				"createTime":   createTime,
				"remark":       artifact.ExtraAttrs.Config.BuildRemark(),
				"gitTag":       artifact.ExtraAttrs.Config.GitTag(),
				"author":       artifact.ExtraAttrs.Author,
			})
		}

	}
	return ret
}
