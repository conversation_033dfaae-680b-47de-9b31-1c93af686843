package org

import (
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/org_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"strings"
)

func All(c *gin.Context) {
	entities, err := org_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, dto.ToOrgList(entities))
}

func Create(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有管理员才有【部门新增】权限，请联系管理员操作")
		return
	}

	var p dto.Org
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(auth.GetRealName(c), "部门-创建", p.Name, p)
	entity := p.ToModel()
	if err := org_service.Create(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Edit(c *gin.Context) {
	var p dto.Org
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	dbEntity, err := org_service.FindByID(p.ID)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) && !strslice.Find(dbEntity.Users, user.RealName) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "无操作权限，请联系当前部门成员进行修改，成员列表： "+strings.Join(dbEntity.Users, ","))
		return
	}
	removeUsers := strslice.FindStringsNotInArray(dbEntity.Users, p.Users)
	addUsers := strslice.FindStringsNotInArray(p.Users, dbEntity.Users)
	desc := p.Name
	if len(addUsers) > 0 {
		desc = desc + " | 增加：" + strings.Join(addUsers, ",")
	}
	if len(removeUsers) > 0 {
		desc = desc + " | 删除：" + strings.Join(removeUsers, ",")
	}

	log_service.Create(auth.GetRealName(c), "部门-修改", desc, p)
	dbEntity.Name = p.Name
	dbEntity.Remark = p.Remark
	dbEntity.Users = p.Users
	err = org_service.Update(&dbEntity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	perm_service.ClearOrgsCache()
	web.SuccessJson(c, nil)
}
