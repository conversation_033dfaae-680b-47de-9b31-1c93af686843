package eolinker

import (
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/eolinker"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
)

func ListProjects(c *gin.Context) {
	if !config.GetConfig().Eolinker.Enable {
		web.SuccessJson(c, []string{})
		return
	}
	data, err := eolinker.Default().ListProject()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func ListTimedTask(c *gin.Context) {
	if !config.GetConfig().Eolinker.Enable {
		web.SuccessJson(c, []string{})
		return
	}
	projectID := c.Query("project_id")
	data, err := eolinker.Default().ListTimedTask(projectID)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
