package dashboard

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/middleware/auth"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	v1 "k8s.io/api/core/v1"
	"time"
)

type ResourcePools struct {
}

func Data(c *gin.Context) {
	web.SuccessJson(c, nil)
}

func ResourcePool(c *gin.Context) {
	clu := c.Query("cluster")
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("capacity@@%s", clu))
	if data, found := cache.GetStruct(cacheKey, []map[string]interface{}{}); found {
		web.SuccessJson(c, data)
		return
	}
	cluster := config.GetSetting().GetCluster(clu)
	capacity, err := k8s_service.GetClusterCapacity(cluster.Name, "")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data := make([]map[string]interface{}, 0, 30)
	for _, it := range cluster.Nodes {
		pool := capacity.Filter(it.Value)
		item := make(map[string]interface{})
		item["name"] = it.Name
		item["cpuCapacity"] = pool.CPUCapacity / 1000
		item["cpuLimit"] = pool.CPULimit / 1000
		item["cpuRequire"] = pool.CPURequire / 1000
		item["memCapacity"] = pool.MemoryCapacity / 1024
		item["memLimit"] = pool.MemoryLimit / 1024
		item["memRequire"] = pool.MemoryRequire / 1024
		data = append(data, item)
	}
	if err := cache.SetStruct(cacheKey, data, 30*time.Minute); err != nil {
		log.Warn(err.Error())
	}
	web.SuccessJson(c, data)
}

func parseShouldPool(pod v1.Pod) string {
	if pod.Spec.Affinity == nil || pod.Spec.Affinity.NodeAffinity == nil || pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution == nil {
		return ""
	}
	for _, affi := range pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution {
		if affi.Preference.MatchExpressions == nil {
			continue
		}
		for _, expr := range affi.Preference.MatchExpressions {
			if expr.Key == "fxiaoke.com/dedicated" {
				return expr.Values[0]
			}
		}
	}
	return ""
}

func PodDrift(c *gin.Context) {
	clu := c.Query("cluster")
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("podDrift@@%s", clu))
	if data, found := cache.GetStruct(cacheKey, []map[string]interface{}{}); found {
		web.SuccessJson(c, data)
		return
	}
	cluster := config.GetSetting().GetCluster(clu)

	data := make([]map[string]interface{}, 0, 30)
	for _, ns := range cluster.Namespaces {
		pods, err := k8s.GetPodList(cluster.Name, ns, "")
		if err != nil {
			log.Infof("get pods fail, %s,%s", cluster.Name, ns)

		}
		for _, pod := range pods.Items {
			runPod := k8s_service.GetResourcePool(clu, pod.Spec.NodeName)
			shouldPool := parseShouldPool(pod)
			//历史原因，暂且代码兼容 Common 和 空字符串
			if runPod == "" {
				runPod = "Common"
			}
			if shouldPool == "" {
				shouldPool = "Common"
			}
			if shouldPool == "Common" || runPod == shouldPool {
				continue
			}

			item := make(map[string]interface{})
			item["cluster"] = clu
			item["namespace"] = pod.Namespace
			item["app"] = k8s_util.GetAppName(pod.Name)
			item["pod"] = pod.Name
			item["nodeName"] = pod.Spec.NodeName
			item["runPool"] = runPod
			item["shouldPool"] = shouldPool

			data = append(data, item)
		}
	}

	if err := cache.SetStruct(cacheKey, data, 30*time.Minute); err != nil {
		log.Warn(err.Error())
	}
	web.SuccessJson(c, data)
}

func AppDeployStatus(c *gin.Context) {
	user, _ := auth.GetUser(c)
	apps := user.RecentApps
	data := make([]map[string]interface{}, 0, len(apps))

	for _, app := range apps {
		item := make(map[string]interface{})
		item["app"] = app
		appStatusList := make([]*k8s_service.AppStatus, 0, 50)
		for _, clu := range config.GetSetting().Clusters {
			//if strslice.Find([]string{"k8s0"}, clu.Name) {
			//	continue
			//}
			for _, ns := range clu.Namespaces {
				appStatusList = append(appStatusList, k8s_service.GetAppStatus(clu.Name, ns, app))
			}
		}
		item["status"] = appStatusList
		data = append(data, item)
	}
	web.SuccessJson(c, data)
}
