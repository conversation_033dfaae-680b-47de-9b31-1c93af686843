package event

import (
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
)

type PageSearch struct {
	Key    string `form:"key"`
	LastId int    `form:"lastId,default=-1" `
	Limit  int    `form:"limit,default=20"`
}

type PageSearchByApp struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
	App       string `form:"app" binding:"required"`
	LastId    int    `form:"lastId,default=-1" `
	Limit     int    `form:"limit,default=20"`
}

func Query(c *gin.Context) {
	var p PageSearch
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	data, err := QueryByKey(p.LastId, p.<PERSON>, p.<PERSON>)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.<PERSON>rror())
		return
	}
	web.SuccessJson(c, data)
}

func QueryByApp(c *gin.Context) {
	var p PageSearchByApp
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	data, err := QueryByKey(p.LastId, p.Limit, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func QueryByKey(lastId, limit int, key string) ([]map[string]interface{}, error) {
	entities, err := event_service.Query(lastId, limit, key)
	if err != nil {
		return nil, err
	}

	ret := make([]map[string]interface{}, 0, len(entities))
	for _, it := range entities {
		item := make(map[string]interface{})
		item["id"] = it.ID
		item["key"] = it.Key
		item["content"] = it.Content
		item["author"] = it.Author
		item["created"] = it.CreatedAt.Format("2006-01-02 15:04:05")
		ret = append(ret, item)
	}
	return ret, nil
}
