package log

import (
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"github.com/gin-gonic/gin"
)

type SearchParam struct {
	param.PageSearch
	Author  string `form:"author"`
	Operate string `form:"operate"`
	Target  string `form:"target"`
}

func Search(c *gin.Context) {
	var p SearchParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	if p.Limit > 1000 {
		user, _ := auth.GetUser(c)
		if !perm_service.IsAdmin(user) {
			web.FailJson(c, web.CODE_SERVER_ERROR, "只有管理员才能查询超过1000条日志!")
			return
		}
	}
	entities, err := log_service.Search(p.Author, p.Operate, p.Target, p.Page, p.Limit)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make(map[string]interface{})
	data["count"] = log_service.Count(p.Author, p.Operate, p.Target)
	data["data"] = dto.ParseLogList(entities)
	web.SuccessJson(c, data)
}
