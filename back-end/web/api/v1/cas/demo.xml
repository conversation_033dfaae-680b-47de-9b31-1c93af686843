<cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>
    <cas:authenticationSuccess>
        <cas:user>***********</cas:user>
        <cas:attributes>
            <cas:isFromNewLogin>false</cas:isFromNewLogin>
            <cas:authenticationDate>2020-11-20T11:18:13.130+08:00[Asia/Shanghai]</cas:authenticationDate>
            <cas:roles>qa,dataplatform,admin,cms-create</cas:roles>
            <cas:successfulAuthenticationHandlers>RestAuthenticationHandler</cas:successfulAuthenticationHandlers>
            <cas:accounttype>fxiaoke</cas:accounttype>
            <cas:employeeId>5548</cas:employeeId>
            <cas:userId>605</cas:userId>
            <cas:realname>吴志辉</cas:realname>
            <cas:credentialType>UsernamePasswordCredential</cas:credentialType>
            <cas:samlAuthenticationStatementAuthMethod>urn:oasis:names:tc:SAML:1.0:am:password</cas:samlAuthenticationStatementAuthMethod>
            <cas:authenticationMethod>RestAuthenticationHandler</cas:authenticationMethod>
            <cas:permissions>wuzh-test,devtools,console,bt,upm,bigfile,normalRoute,grayRoute,qixin,sms,warehouse,thirdpush,oms,er</cas:permissions>
            <cas:longTermAuthenticationRequestTokenUsed>false</cas:longTermAuthenticationRequestTokenUsed>
            <cas:id>605</cas:id>
            <cas:department>平台运营-平台架构组,北研业务线-平台运营部</cas:department>
            <cas:email><EMAIL></cas:email>
            <cas:username>***********</cas:username>
        </cas:attributes>
    </cas:authenticationSuccess>
</cas:serviceResponse>