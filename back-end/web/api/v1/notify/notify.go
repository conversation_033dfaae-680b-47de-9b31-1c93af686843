package notify

import (
	"fmt"
	"fs-k8s-app-manager/service/notify_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
)

type qiXinParam struct {
	SessionType string `form:"sessionType" binding:""`
	UserIds     []int  `form:"userIds" binding:"required"`
	Content     string `form:"content" binding:"required"`
}

func SendQiXin(c *gin.Context) {
	var p qiXinParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if p.SessionType == "" {
		p.SessionType = "publish"
	}
	var err error
	if p.SessionType == "publish" {
		err = notify_service.SendQiXinToPublishSession(p.UserIds, p.Content)
	} else if p.SessionType == "alert" {
		err = notify_service.SendQiXinToImportantAlertSession(p.UserIds, p.Content)
	} else {
		err = fmt.Errorf("unknown session type: %s", p.SessionType)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)

}
