package scale

import (
	"fmt"
	"fs-k8s-app-manager/config"
	k8s_templates "fs-k8s-app-manager/k8s-templates"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/client/kubectl/pascli"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/cron_scale_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/scale_log_service"
	"fs-k8s-app-manager/service/scale_monitor_log_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/k8s"
	"fs-k8s-app-manager/web/param"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
	"time"
)

type HpaSearchParam struct {
	k8s.NamespaceParam
	Keyword string `form:"keyword"`
}

type ScaleSearchParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace"`
	App       string `form:"app"`
}

type HpaDeleteParam struct {
	k8s.NamespaceParam
	App string `form:"app" binding:"required"`
}

func hpaFilter(d []dto.Hpa, filter func(dto dto.Hpa) bool) []dto.Hpa {
	ret := d[:0]
	for _, item := range d {
		if filter(item) {
			ret = append(ret, item)
		}
	}
	return ret
}

func ListHpa(c *gin.Context) {
	var p HpaSearchParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	data, err := k8s_service.ListHpa(p.Cluster, p.Namespace)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if p.Keyword != "" {
		data = hpaFilter(data, func(dto dto.Hpa) bool {
			return strings.Contains(dto.Name, p.Keyword)
		})
	}
	web.SuccessJson(c, data)
}

func CreateHpa(c *gin.Context) {
	var p dto.Hpa
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.Name) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有应用权限")
		return
	}
	pipeline, err := pipeline_service.FirstInEnv(p.Cluster, p.Namespace, p.Name)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("当前环境下没有部署应用: %s", p.Name))
		return
	}
	p.MinReplicas = int32(pipeline.Replicas)
	if p.MinReplicas >= p.MaxReplicas {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("扩容实例数[%d]必须大于发布流程配置的实例数[%d]", p.MaxReplicas, p.MinReplicas))
		return
	}

	tplParam := k8s_templates.HpaParam{
		App:                            p.Name,
		Namespace:                      p.Namespace,
		MinReplicas:                    p.MinReplicas,
		MaxReplicas:                    p.MaxReplicas,
		TargetCPUUtilizationPercentage: p.TargetCPUUtilizationPercentage,
		Annotations:                    make(map[string]string),
	}

	iYaml, err := k8s_templates.BuildHpa(tplParam)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "自动扩缩容-编辑", p.Name, p)
	log_service.Create(auth.GetRealName(c), "K8S-Hpa-Apply", p.Name, iYaml)
	if err := kubectl.Apply(p.Cluster, p.Namespace, iYaml); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func DeleteHpa(c *gin.Context) {
	var p HpaDeleteParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		panic("你没有应用权限")
	}
	log_service.Create(auth.GetRealName(c), "自动扩缩容-删除", p.App, p)
	if err := k8s_service.DeleteHpa(p.Cluster, p.Namespace, p.App); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
	return
}

func ListCronScale(c *gin.Context) {
	var p ScaleSearchParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	entities, err := cron_scale_service.Search(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	dtos := dto.ToCronScaleList(entities)
	for i := 0; i < len(dtos); i++ {
		it := dtos[i]
		if dep, err := k8s_service.GetDeploymentDTO(it.Cluster, it.Namespace, it.App); err == nil {
			dtos[i].CurrReplicas = strconv.Itoa(int(dep.Replicas))
		}
		if pipe, err := pipeline_service.FirstInEnv(it.Cluster, it.Namespace, it.App); err == nil {
			dtos[i].PipeReplicas = strconv.Itoa(int(pipe.Replicas))
		}
	}
	web.SuccessJson(c, dtos)
}

func DeleteCronScale(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	entity, err := cron_scale_service.FindById(uint(id))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, entity.App) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有应用权限")
		return
	}
	if err := cron_scale_service.DeleteById(entity.ID); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(user.RealName, "定时扩缩容-删除", entity.App, entity.AppFullName())
	web.SuccessJson(c, nil)

}
func CreateCronScale(c *gin.Context) {
	var p dto.CronScale
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有应用权限")
		return
	}

	if pipeline, err := pipeline_service.FirstInEnv(p.Cluster, p.Namespace, p.App); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "应用必须在当前环境已经存在发布流程")
		return
	} else if p.Replicas <= pipeline.Replicas {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("【扩容副本数（%d）】必须要大于 【发布流程配置的副本数（%d）】", p.Replicas, pipeline.Replicas))
		return
	}

	var entity = p.ToModel()
	entity.Author = user.RealName
	if entity.ID == 0 {
		if db, _ := cron_scale_service.FirstInEnv(entity.Cluster, entity.Namespace, entity.App); db.ID != 0 {
			web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("当前应用已经存在一个自动扩缩容配置：%s/%s/%s", entity.Cluster, entity.Namespace, entity.App))
			return
		}
	}

	if err := cron_scale_service.SaveOrUpdate(entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if p.ID == 0 {
		log_service.Create(user.RealName, "定时扩缩容-新建", p.App, p)
	} else {
		log_service.Create(user.RealName, "定时扩缩容-修改", p.App, p)
	}
	web.SuccessJson(c, nil)
}

func GetAllScaleByApp(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	if cluster == "" || namespace == "" || app == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数!")
		return
	}

	clu := config.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, cluster+" 集群不存在!")
		return
	}

	data := make(map[string]interface{})
	var cronScales = make([]models.CronScale, 0)
	var err error

	if clu.CronScale {
		cronScales, err = cron_scale_service.FindAll()
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for _, it := range cronScales {
			if it.Cluster == cluster && it.Namespace == namespace && it.App == app {
				data["cronScale"] = it
				break
			}
		}
	}
	if podAutoscalers, err := pascli.ListPodAutoScalerFromK8sWithCache(cluster, "", 30*time.Minute); err == nil {
		for _, it := range podAutoscalers.Items {
			if it.Namespace == namespace && it.Name == app {
				data["autoScaleV2"] = it
				break
			}
		}
	}
	web.SuccessJson(c, data)
}

func SearchScaleMonitorLog(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	entities, err := scale_monitor_log_service.SearchToday(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, dto.ParseScaleLogMonitorList(entities))
}

func SearchScaleLog(c *gin.Context) {
	var p param.PageSearch
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	p.Keyword = strings.TrimSpace(p.Keyword)
	keywords := strings.Fields(p.Keyword)
	entities, err := scale_log_service.Search(keywords, p.Page, p.Limit)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make(map[string]interface{})
	data["count"] = scale_log_service.Count(keywords)
	data["data"] = dto.ParseScaleLogList(entities)
	web.SuccessJson(c, data)
}
