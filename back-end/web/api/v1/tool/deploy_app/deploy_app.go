package deploy_app

import (
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/tool"
	"github.com/gin-gonic/gin"
	"io"
	"strings"
	"time"
)

var cacheKey = key.Pre().TOOL.Key("deployAppJob")

func authCheck(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		panic("只有管理员才有权限操作")
	}
}

func Create(c *gin.Context) {
	authCheck(c)
	user, _ := auth.GetUser(c)
	if v, found := cache.GetStruct(cacheKey, tool.Job{}); found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("已经有任务正在进行中, start time: %s", v.StartTime))
		return
	}

	var items []string
	var err error
	reqBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	typee := c.Query("type")
	if typee != "deploy" && typee != "redeploy" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "type参数错误")
		return
	}

	forceCodeCompile := c.Query("forceCodeCompile") == "true"
	dependencyCheck := c.Query("dependencyCheck") == "true"
	parentPom := c.Query("parentPom")
	fixVersion := c.Query("fixVersion")
	suffixVersion := c.Query("suffixVersion")
	message := c.Query("message")
	err = json.Unmarshal(reqBody, &items)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	job := tool.Job{
		StartTime: time.Now(),
		Items:     make([]tool.JobItem, 0, 200),
		Status:    0,
		Remark:    "",
	}
	for _, it := range items {
		job.Items = append(job.Items, tool.JobItem{
			Name:   it,
			Result: "wait",
		})
	}
	err = cache.SetStruct(cacheKey, job, 24*time.Hour)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(user.RealName, "应用-批量发布", "", map[string]interface{}{
		"forceCodeCompile": forceCodeCompile,
		"dependencyCheck":  dependencyCheck,
		"parentPom":        parentPom,
		"type":             typee,
		"fixVersion":       fixVersion,
		"suffixVersion":    suffixVersion,
		"message":          message,
		"items":            items,
	})
	jobItems := job.Items
	go func() {
		for idx, it := range jobItems {
			for {
				if v, found := cache.GetStruct(cacheKey, tool.Job{}); found {
					job = v
					if job.Status == 0 {
						time.Sleep(5 * time.Second)
						continue
					}
				} else {
					return
				}
				break
			}

			parts := strings.Split(it.Name, "/")
			if len(parts) != 3 {
				job.Items[idx].Result = "skip"
				_ = cache.SetStruct(cacheKey, job, 24*time.Hour)
				continue
			}
			cluster := parts[0]
			namespace := parts[1]
			app := parts[2]
			log_service.Create(user.RealName, "应用-重发", it.Name, "")
			job.Items[idx].StartTime = time.Now()
			if typee == "deploy" {
				err = deployWithFixVersion(cluster, namespace, app, forceCodeCompile, dependencyCheck, user, fixVersion, message, parentPom)
			} else if typee == "redeploy" {
				err = deployWithSuffixVersion(cluster, namespace, app, forceCodeCompile, dependencyCheck, user, suffixVersion, message, parentPom)
			} else {
				err = fmt.Errorf("type参数错误")
			}
			if err != nil {
				job.Items[idx].Result = "fail, " + err.Error()
				_ = cache.SetStruct(cacheKey, job, 24*time.Hour)
				continue
			}
			job.Items[idx].Result = "success"
			_ = cache.SetStruct(cacheKey, job, 24*time.Hour)
			time.Sleep(60 * time.Second)
		}
	}()
	web.SuccessJson(c, nil)
}

func deployWithFixVersion(cluster, namespace, app string, forceCodeCompile, dependencyCheck bool, user models.User, version, message string, parentPom string) (err error) {
	return errors.New("暂不支持")

}

func deployWithSuffixVersion(cluster, namespace, app string, forceCodeCompile, dependencyCheck bool, user models.User, suffixVersion, message string, parentPom string) (err error) {
	return errors.New("暂不支持")
}
func Stop(c *gin.Context) {
	authCheck(c)
	job, found := cache.GetStruct(cacheKey, tool.Job{})
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "任务不存在")
		return
	}
	job.Status = 0
	_ = cache.SetStruct(cacheKey, job, 24*time.Hour)
	web.SuccessJson(c, nil)
}
func Start(c *gin.Context) {
	authCheck(c)
	job, found := cache.GetStruct(cacheKey, tool.Job{})
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "任务不存在")
		return
	}
	job.Status = 1
	_ = cache.SetStruct(cacheKey, job, 24*time.Hour)
	web.SuccessJson(c, nil)
}
func Remove(c *gin.Context) {
	authCheck(c)
	job, found := cache.GetStruct(cacheKey, tool.Job{})
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "任务不存在")
		return
	}
	if job.Status == 1 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "请先暂停任务")
		return
	}
	cache.Delete(cacheKey)
	web.SuccessJson(c, nil)
}
func Output(c *gin.Context) {
	authCheck(c)
	if v, found := cache.GetStruct(cacheKey, tool.Job{}); found {
		web.SuccessJson(c, v)
		return
	}
	web.SuccessJson(c, nil)
	return
}
