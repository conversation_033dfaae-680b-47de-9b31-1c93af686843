package git

import "strings"

var gitUrlReplacer = strings.NewReplacer("http://", "", "https://", "", "git@", "", ".git", "")

func GetProjectGroup(gitUrl string) string {
	paths := parsePaths(gitUrl)
	//gitlab允许子group
	if len(paths) > 3 {
		return strings.Join(paths[1:len(paths)-2], "/")

	}
	return paths[len(paths)-2]
}
func GetProjectName(gitUrl string) string {
	paths := parsePaths(gitUrl)
	return paths[len(paths)-1]
}

func parsePaths(gitUrl string) []string {
	pathStr := gitUrlReplacer.Replace(gitUrl)
	return strings.Split(pathStr, "/")
}
