package tool

type ScanJarParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace"`
	JarPrefix string `form:"jarPrefix" binding:"required"`
}

type YamlExportItem struct {
	Key       string `form:"cluster"`
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
	App       string `form:"app" binding:"required"`
}

type YamlExportResultItem struct {
	YamlExportItem
	Deployment string
	Service    string
}
