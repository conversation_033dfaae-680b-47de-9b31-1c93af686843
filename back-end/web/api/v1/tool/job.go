package tool

import (
	"encoding/json"
	"fmt"
	"time"
)

type JobItem struct {
	Name      string
	StartTime time.Time
	Result    string
}

func (j JobItem) Output() string {
	if j.StartTime.IsZero() {
		return fmt.Sprintf("%s -- %s", j.<PERSON>, j.Result)
	}
	return fmt.Sprintf("%s: %s -- %s", j.StartTime.Format("2006/01/02 15:04:05"), j.Name, j.Result)
}

func (j JobItem) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Name      string    `json:"name"`
		StartTime time.Time `json:"startTime"`
		Result    string    `json:"result"`
		Output    string    `json:"output"`
	}{
		Name:      j.Name,
		StartTime: j.StartTime,
		Result:    j.Result,
		Output:    j.Output(),
	})
}

type Job struct {
	StartTime time.Time `json:"name"`
	Items     []JobItem `json:"items"`
	Status    int       `json:"status"`
	Remark    string    `json:"remark"`
}

//type JobController struct {
//	cacheKey string
//}

//func (jc JobController) GetJob() (Job, bool) {
//	return cache.GetStruct(jc.cacheKey, Job{})
//}
//func (jc JobController) Start() (Job, bool) {
//	return cache.GetStruct(jc.cacheKey, Job{})
//}
//func (jc JobController) Stop() (Job, bool) {
//	return cache.GetStruct(jc.cacheKey, Job{})
//}
//
//func (jc JobController) Remove() error{
//
//}
