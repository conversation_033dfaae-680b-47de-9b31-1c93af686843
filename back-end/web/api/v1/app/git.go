package app

import (
	"fmt"
	"fs-k8s-app-manager/pkg/client/gitlab"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"time"
)

type CreateBugfixBranchParam struct {
	GitURL    string `form:"gitUrl" binding:"required"`
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
	App       string `form:"app" binding:"required"`
}

func CreateGitTag(c *gin.Context) {
	var p dto.CreateGitTag
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "GitTag-创建", p.GitURL, p)
	if err := app_service.CreateGitTag(p); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func CreateBugfixBranch(c *gin.Context) {
	var p CreateBugfixBranchParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	dep, err := k8s_service.DeploymentDetail(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	modules, err := k8s_service.GetDeployModules(dep)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	gitRef := ""
	for _, m := range modules {
		if m.GitUrl == p.GitURL {
			gitRef = m.Tag
			break
		}
	}
	if gitRef == "" {
		web.FailJson(c, web.CODE_SERVER_ERROR, "未找到gitUrl对应的tag")
		return
	}

	branchName := fmt.Sprintf("%s--bugfix-%s", p.Namespace, time.Now().Format("200601021504"))
	err = gitlab.CreateBranch(p.GitURL, branchName, gitRef)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "创建Bugfix分支", p.GitURL, p)
	web.SuccessJson(c, branchName)
}

func GetGitTag(c *gin.Context) {
	app := c.Query("app")
	if app == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "app为空")
		return
	}
	data, err := app_service.FindGitUrlAndTagByApp(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func BatchDeleteGitTagAndBranch(c *gin.Context) {
	var p dto.BranchDeleteGitTagAndBranch
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if len(p.Tags) > 0 {
		log_service.Create(auth.GetRealName(c), "GitTag-批量删除", p.GitURL, p)
	}
	if len(p.Branches) > 0 {
		log_service.Create(auth.GetRealName(c), "Git分支-批量删除", p.GitURL, p)
	}
	if err := app_service.BatchDeleteTagAndBranch(p); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func FindTagByGitUrl(c *gin.Context) {
	gitUrl := c.Query("git_url")
	searchName := c.Query("search_name")
	if gitUrl == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "git_url为空")
		return
	}
	data, err := app_service.FindTagByGitUrl(gitUrl, searchName)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
