package operation

import (
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"io"
	"strings"
)

type appLevelUpdateParam struct {
	App   string `json:"app"`
	Level string `json:"level"`
}

func AppUpdate(c *gin.Context) {
	op := c.Query("op")
	var output interface{}
	var err error
	if op == "updateAppTimeWindow" {
		output, err = updateAppTimeWindow()
	} else if op == "updateAppLevel" {
		output, err = updateAppLevel(c)
	} else {
		err = errors.New("unknown op: " + op)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, output)
}

func updateAppTimeWindow() (interface{}, error) {
	output := make([]string, 0, 500)
	apps, err := app_service.FindAll()
	if err != nil {
		return nil, err
	}
	items := make([]models.App, 0, len(apps))
	for _, it := range apps {
		if strings.Contains(it.Name, "-console") || strings.Contains(it.Name, "-admin") {
			continue
		}
		items = append(items, it)
	}
	for _, it := range items {
		it.TimeWindow = make([]datatype.TimePeriod, 0, 2)
		it.TimeWindow = append(it.TimeWindow, datatype.TimePeriod{
			DaysOfWeek: []int{1, 2, 3, 4, 5, 6, 7},
			TimeRange:  datatype.TimeRange{"23:00", "23:59"},
		})
		it.TimeWindow = append(it.TimeWindow, datatype.TimePeriod{
			DaysOfWeek: []int{1, 2, 3, 4, 5, 6, 7},
			TimeRange:  datatype.TimeRange{"00:00", "06:00"},
		})
		if err := app_service.Update(&it); err != nil {
			output = append(output, fmt.Sprintf("%s fail, err: %v", it.Name, err))
		} else {
			output = append(output, fmt.Sprintf("%s success", it.Name))
		}
	}
	log_service.CreateBySys(fmt.Sprintf("刷数据-应用发布时间窗口"), "app", output)
	return output, nil
}

func updateAppLevel(c *gin.Context) (interface{}, error) {
	output := make([]string, 0, 500)
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}
	var params []appLevelUpdateParam
	err = json.Unmarshal(body, &params)
	if err != nil {
		return nil, err
	}

	for _, param := range params {
		app, err := app_service.FindByName(param.App)
		if err != nil {
			output = append(output, fmt.Sprintf("%s fail, err: %v", param.App, err))
			continue
		}
		if app.Level == param.Level {
			output = append(output, fmt.Sprintf("%s skipped, level is already %s", param.App, param.Level))
			continue
		}
		app.Level = param.Level
		if err := app_service.Update(&app); err != nil {
			output = append(output, fmt.Sprintf("%s fail, err: %v", param.App, err))
		} else {
			output = append(output, fmt.Sprintf("%s level updated to %s", param.App, app.Level))
		}
	}
	log_service.CreateBySys(fmt.Sprintf("刷数据-应用服务等级"), "app", output)
	return output, nil
}

//func updateUserPinyin() (interface{}, error) {
//	output := make([]string, 0, 500)
//	items, err := user_service.All()
//	if err != nil {
//		return nil, err
//	}
//	for _, it := range items {
//		it.PinYin = pinyin.To(it.RealName)
//		if err := user_service.Save(&it); err != nil {
//			output = append(output, it.RealName+" err: "+err.Error())
//		} else {
//			output = append(output, it.RealName+" "+it.PinYin)
//		}
//	}
//	return output, nil
//}
