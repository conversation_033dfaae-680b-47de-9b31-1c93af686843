package gitlab

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/client/gitlab"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/gitlab_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

type GitTagParam struct {
	GitUrls []string `json:"gitUrls" binding:"required"`
}

func CreateGitlabCIFile(c *gin.Context) {
	var p CreateCIParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "Gitlab部署文件-下载", p.App, p)
	deployEnvList := make([]gitlab_service.DeployEnvParam, 0, 0)
	//todo: 不要写死在代码里，改用配置方式
	for _, item := range p.Namespace {
		runtimeEnv := "firstshare"
		cluster := "k8s1"
		ingressParentHost := "k8s1.firstshare.cn"
		isOnline := false
		if strings.Contains(item, "foneshare") {
			runtimeEnv = "foneshare"
			cluster = "k8s1"
			ingressParentHost = "k8s1.foneshare.cn"
			isOnline = true
		}
		deployEnvList = append(deployEnvList, gitlab_service.DeployEnvParam{
			App:               p.App,
			RuntimeEnv:        runtimeEnv,
			Cluster:           cluster,
			Namespace:         item,
			Replicas:          p.Replicas,
			IngressParentHost: ingressParentHost,
			Language:          p.Language,
			RequestCPU:        fmt.Sprintf("%.2f", p.Cpu/2),
			RequestMemory:     fmt.Sprintf("%dMi", p.Memory/2),
			LimitCPU:          fmt.Sprintf("%.2f", p.Cpu),
			LimitMemory:       fmt.Sprintf("%dMi", p.Memory),
			IsOnline:          isOnline,
		})
	}
	ports := p.Ports

	file, err := gitlab_service.CreateCIFile(deployEnvList, ports)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	fileId := filepath.Base(file)
	downloadFile := filepath.Join(config.GetConfig().App.DownloadDir, fileId)
	err = os.Rename(file, downloadFile)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, fileId)
}
func DownloadGitlabCIFile(c *gin.Context) {
	fileId, found := c.GetQuery("fileId")
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺失参数")
		return
	}
	fileName, _ := c.GetQuery("fileName")
	if len(fileName) < 1 {
		fileName = fileId
	}
	file := filepath.Join(config.GetConfig().App.DownloadDir, fileId)
	c.FileAttachment(file, fileName)
}

func GetGitTag(c *gin.Context) {
	var p GitTagParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data := make(map[string]interface{})
	wg := sync.WaitGroup{}
	wg.Add(len(p.GitUrls))
	for _, gitUrl := range p.GitUrls {
		go func(gitUrl string) {
			tags, _ := gitlab.SearchTag(gitUrl, 200, nil)
			var branches []*dto.GitBranch
			if true {
				branches, _ = gitlab.SearchBranch(gitUrl, 100, nil)
			}
			data[gitUrl] = map[string]interface{}{
				"tagOptions":    tags,
				"branchOptions": branches,
			}
			wg.Done()
		}(gitUrl)
	}
	wg.Wait()
	web.SuccessJson(c, data)
}
