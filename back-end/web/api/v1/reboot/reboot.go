package reboot

import (
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/reboot_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"strconv"
)

type CreateParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
	App       string `form:"app" binding:"required"`
}

func List(c *gin.Context) {
	keyword := c.Query("keyword")

	entities, err := reboot_service.Search(keyword)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	dtos := dto.ToRebootList(entities)
	web.SuccessJson(c, dtos)
}

func Delete(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	entity, err := reboot_service.FindById(uint(id))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if err := reboot_service.DeleteById(uint(id)); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(user.RealName, "服务定时重启-删除", entity.App, entity)
	web.SuccessJson(c, nil)
}

func Create(c *gin.Context) {
	var p dto.Reboot
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, p.App) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "你没有应用权限")
		return
	}

	if _, err := reboot_service.FirstInEnv(p.Cluster, p.Namespace, p.App); err == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "当前环境已经存在一个定时重启任务")
		return
	}
	var entity = p.ToModel()
	entity.Author = user.RealName

	if err := reboot_service.SaveOrUpdate(entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(user.RealName, "服务定时重启-新建", p.App, p)
	web.SuccessJson(c, nil)
}
