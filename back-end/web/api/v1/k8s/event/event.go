package event

import (
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/k8s"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func PodEvents(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBind<PERSON>uery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	data, err := k8s_service.PodEventDTOs(p.Cluster, p.Namespace, p.Pod, 30)
	if err != nil {
		log.Warn("can't found pod event, ", err)
	}
	web.SuccessJson(c, data)
}
func Search(c *gin.Context) {
	var p SearchParam
	if err := c.ShouldBind(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.<PERSON>rror())
		return
	}
	data, err := k8s_service.ListEvent(p.Cluster, p.Namespace, p.Type, p.Reason)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
