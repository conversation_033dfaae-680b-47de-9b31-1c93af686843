package k8s

type NamespaceParam struct {
	Cluster   string `form:"cluster" binding:"required"`
	Namespace string `form:"namespace" binding:"required"`
}

type DeploymentParam struct {
	NamespaceParam
	App string `form:"app" binding:"required"`
}

type RedeployParam struct {
	DeploymentParam
	MaxSurge int `form:"maxSurge" binding:"required,min=1,max=100"`
}
type PodParam struct {
	NamespaceParam
	Pod string `form:"pod" binding:"required"`
}
