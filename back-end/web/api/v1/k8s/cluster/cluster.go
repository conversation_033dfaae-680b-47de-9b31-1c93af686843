package cluster

import (
	"encoding/json"
	"fs-k8s-app-manager/pkg/client/kubecap"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
)

func Capacity(c *gin.Context) {
	var p SearchParam
	if err := c.ShouldBind(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	capacity, err := k8s_service.GetClusterCapacity(p.Cluster, p.NodeType)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, capacity)
}
func PodCapacity(c *gin.Context) {
	cluster := c.Query("cluster")
	node := c.Query("node")
	if cluster == "" || node == "" {
		web.FailJson(c, web.CODE_SERVER_ERROR, "please choose cluster and node")
		return
	}
	out, err := kubecap.Pod(cluster, node)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data, err := json2Map(out)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func json2Map(str string) (map[string]interface{}, error) {
	m := make(map[string]interface{})
	err := json.Unmarshal([]byte(str), &m)
	if err != nil {
		return nil, err
	}
	return m, nil
}
