package pod

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	k8s_cli "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/client/zip"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	file2 "fs-k8s-app-manager/pkg/util/file"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pyroscope_service"
	"fs-k8s-app-manager/service/user_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/k8s"
	log "github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	"net/http"
	netUrl "net/url"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// todo: 这段代码转移到 api/user.go 里面去
func updateRecentPod(c *gin.Context, service string) {
	ret := []string{service}
	if user, f := auth.GetUser(c); f {
		if user.RecentPods != nil {
			for _, it := range user.RecentPods {
				if !strslice.Find(ret, it) {
					ret = append(ret, it)
				}
			}
			if len(ret) > 40 {
				ret = ret[:40]
			}
		}
		user.RecentPods = ret
		if err := auth.UpdateUserInfo(c, user); err != nil {
			log.Warn("session update user fail, name:", user.Username, err.Error())
		}
		if err := user_service.UpdateRecentPod(user.Username, ret); err != nil {
			log.Warn("user recent pod update fail, name: ", user.Username, err.Error())
		}
	}
}

func ListDeregisterByApp(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	data := k8s_service.ListDeregister(cluster, namespace, app, true)
	web.SuccessJson(c, data)
}
func List(c *gin.Context) {
	var p k8s.DeploymentParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	pods, err := k8s_service.ListPod(p.Cluster, p.Namespace, p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	//podMetrics := k8s_service.GetAppMetrics(p.Cluster, p.Namespace, p.App)
	//
	//pods := make([]dto.Pod, 0, len(data))
	//for _, it := range data {
	//	if m, ok := podMetrics[it.Name]; ok {
	//		it.AvgMemory = m.AvgMemory
	//		it.MaxMemory = m.MaxMemory
	//		it.MaxCpuUsage = m.MaxCpuUsage
	//		it.AvgCpuUsage = m.AvgCpuUsage
	//	}
	//	pods = append(pods, it)
	//}

	if pods != nil {
		updateRecentPod(c, fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App))
	}
	if pods == nil {
		pods = make([]dto.Pod, 0, 0)
	}
	if len(pods) > 0 {
		if metrics, err := kubectl.GetPodsMetrics(p.Cluster, p.Namespace, p.App); err == nil {
			for i := 0; i < len(pods); i++ {
				for _, metric := range metrics {
					if pods[i].Name == metric.Name {
						pods[i].CpuUsage = metric.CPU
						pods[i].MemoryUsage = metric.Memory
						pods[i].CpuUsagePercent = int(pods[i].CpuUsage * 100 / pods[i].LimitCPU)
						//metrics下内存单位是Mb
						pods[i].MemoryUsagePercent = int(pods[i].MemoryUsage * 1024 * 1024 * 100 / pods[i].LimitMemory)
						break
					}
				}
			}
		}

		for i := 0; i < len(pods); i++ {
			pods[i].ResourcePool = k8s_service.GetResourcePool(p.Cluster, pods[i].NodeName)
		}
	}
	web.SuccessJson(c, pods)
}

func ListByEnv(c *gin.Context) {
	var p k8s.NamespaceParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	pods, err := k8s_service.ListPod(p.Cluster, p.Namespace, "")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if pods == nil {
		pods = make([]dto.Pod, 0, 0)
	}
	web.SuccessJson(c, pods)
}

func Metric(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	metric, err := kubectl.GetPodMetric(p.Cluster, p.Namespace, p.Pod)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, metric)
}

func Files(c *gin.Context) {
	var p FileParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}

	data, err := k8s_service.ListPodFiles(p.Cluster, p.Namespace, p.Pod, p.Path)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	sortedData := *data
	sort.SliceStable(sortedData, func(i, j int) bool {
		if sortedData[i].IsDir {
			return true
		} else {
			return false
		}
	})
	if p.Path != "/" {
		sortedData = append([]dto.PodFile{{
			Name:         "..",
			IsDir:        true,
			Size:         0,
			HumanizeSize: "-",
			ModifyTime:   "-",
		}}, sortedData...)
	}
	web.SuccessJson(c, sortedData)
}

func ReadyFile(c *gin.Context) {
	var p FileParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "实例文件-下载", p.Pod, p)

	fileName := filepath.Base(p.Path)
	//路径里带上时间信息，避免不同人下载时互相影响
	destFile := file2.AbsPath(filepath.Join(config.GetConfig().App.DownloadDir, p.Pod, time.Now().Format("0102150405"), fileName))
	err := k8s_service.CopyFileFromPod(p.Cluster, p.Namespace, p.Pod, p.Path, destFile)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, base64.StdEncoding.EncodeToString([]byte(destFile)))
}

func Archive(c *gin.Context) {
	var p ArchiveParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "实例文件-归档", p.Pod, p)
	destFile := time.Now().Format("2006-01-02-15-04-05") + ".zip"
	err := k8s_service.ArchiveFiles(p.Cluster, p.Namespace, p.Pod, p.Dir, p.Files, destFile)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, destFile)
}
func DownloadFile(c *gin.Context) {
	fileId, found := c.GetQuery("fileId")
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "文件找不到! ")
		return
	}
	bs, err := base64.StdEncoding.DecodeString(fileId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "can't base64 decode fileId: "+fileId)
	}
	file := string(bs)
	//对大文件做压缩处理，节省网络流量
	if !strslice.Find([]string{".zip", ".rar", ".gz", ".tgz"}, path.Ext(file)) {
		if s, _ := file2.GetSize(file); s > 50*1024*1024 {
			srcFile := filepath.Base(file)
			workDir := filepath.Dir(file)
			zipFile := srcFile + ".zip"
			if output, err := zip.ZipFile(workDir, zipFile, srcFile); err == nil {
				file = file + ".zip"
			} else {
				log.Warn("zip file fail,", output, err.Error())
			}
		}
	}
	fileName := filepath.Base(file)
	c.FileAttachment(file, fileName)
}

func PreviewFile(c *gin.Context) {
	fileId, found := c.GetQuery("fileId")
	if !found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺失参数 ")
		return
	}
	srcFile, err := base64.StdEncoding.DecodeString(fileId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "can't base64 decode fileId:  "+fileId)
	}
	file := string(srcFile)
	content, _ := os.ReadFile(file)
	fileExt := path.Ext(file)
	if strings.ToLower(fileExt) == ".svg" {
		c.Data(200, "image/svg+xml", content)
	} else if strings.ToLower(fileExt) == ".htm" || strings.ToLower(fileExt) == ".html" {
		c.Data(200, "text/html;charset=UTF-8", content)
	} else {
		c.Data(200, "text/plain", content)
	}
}

func UploadFile(c *gin.Context) {
	cluster := c.PostForm("cluster")
	namespace := c.PostForm("namespace")
	pod := c.PostForm("pod")
	podPath := c.PostForm("path")

	file, err := c.FormFile("file")
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	_ = file2.MkDir(config.GetConfig().App.UploadDir)
	filename := filepath.Base(file.Filename)
	if strings.Contains(filename, " ") {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "文件名包含了空格")
		return
	}
	srcFile := file2.AbsPath(filepath.Join(config.GetConfig().App.UploadDir, filename))
	if err := c.SaveUploadedFile(file, srcFile); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	tgtFile := strings.ReplaceAll(filepath.Join(podPath, filename), "\\", "/")

	err = k8s_service.CopyFileToPod(cluster, namespace, pod, tgtFile, srcFile)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "实例文件-上传", pod, fmt.Sprintf("%s/%s", pod, filename))
	web.SuccessJson(c, nil)

}
func Detail(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data, err := k8s_service.PodDetail(p.Cluster, p.Namespace, p.Pod)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}

func StdoutLog(c *gin.Context) {
	var p StdoutLogParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if p.TailLines < 1 {
		p.TailLines = 1000
	}
	data, err := k8s_service.PodLog(p.Cluster, p.Namespace, p.Pod, p.Container, p.TailLines, p.Previous)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, data)
}
func StdoutLogDownload(c *gin.Context) {
	var p StdoutLogParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if p.TailLines < 1 {
		p.TailLines = 1000
	}
	data, err := k8s_service.PodLog(p.Cluster, p.Namespace, p.Pod, p.Container, p.TailLines, p.Previous)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	file, err := file2.CreateTempFile(data)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	c.FileAttachment(file, p.Pod+"-stdout.log")
}

func Delete(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if err := k8s_service.PodDelete(p.Cluster, p.Namespace, p.Pod); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	k8s_service.DeleteDeregisterPodCache(p.Cluster, p.Namespace, k8s_util.GetAppName(p.Pod))
	user := auth.GetRealName(c)
	log_service.Create(user, "实例-删除", p.Pod, p)
	event_service.Create(user, event_service.BuildAppKey(p.Cluster, p.Namespace, k8s_util.GetAppName(p.Pod)), "pod删除，pod名："+p.Pod)
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      user,
		ResourceType: "app",
		ResourceId:   k8s_util.GetAppName(p.Pod),
		Title:        "pod删除",
		Message:      fmt.Sprintf("%s 手动删除了pod: %s", user, p.Pod),
		Level:        "warn",
		Extra:        "pod被摘除后，系统会自动创建一个新的pod",
	})
	time.Sleep(1 * time.Second)
	web.SuccessJson(c, "success")
}

// Deregister Pod流量摘除
func Deregister(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	//流量摘除注意顺序，建议顺序： MQ > dubbo & nacos > k8s http
	//摘除RocketMQ consumer/dubbo/nacos
	deErr := kubectl.DeregisterService(p.Cluster, p.Namespace, p.Pod)
	// 摘除k8s http
	//todo: 如果pod摘除失败了，应该如何处理？是否应该要回滚MQ/dubbo的摘除？
	err := k8s_service.PodDeregister(p.Cluster, p.Namespace, p.Pod)

	msg := ""
	if deErr != nil {
		msg += deErr.Error()
	}
	if err != nil {
		msg += " " + err.Error()
	}
	if deErr != nil || err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, msg)
		return
	}
	user := auth.GetRealName(c)
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      user,
		ResourceType: "app",
		ResourceId:   k8s_util.GetAppName(p.Pod),
		Title:        "pod摘除",
		Message:      fmt.Sprintf("%s 手动摘除了pod: %s", user, p.Pod),
		Level:        "warn",
		Extra:        "pod被摘除后，系统会自动创建一个新的pod",
	})
	event_service.Create(user, event_service.BuildAppKey(p.Cluster, p.Namespace, k8s_util.GetAppName(p.Pod)), "pod摘除，pod名："+p.Pod)
	log_service.Create(user, "实例-摘除", p.Pod, p)
	time.Sleep(2 * time.Second)
	web.SuccessJson(c, "success")
}

func DeregisterListByCluster(c *gin.Context) {
	cluster := c.Query("cluster")
	if cluster == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少请求参数")
		return
	}

	clu := config.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "集群不存在")
		return
	}
	data := make([]corev1.Pod, 0)
	for _, ns := range clu.Namespaces {
		pods, err := k8s_cli.GetPodList(cluster, ns, "")
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for _, pod := range pods.Items {
			if strings.HasSuffix(pod.Labels["app"], "-close") {
				data = append(data, pod)
			}
		}
	}
	web.SuccessJson(c, data)
}

// VersionRetain 测试场景使用，比如需要测试不同版本应用间的资源使用情况
func VersionRetain(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "实例-版本保留", p.Pod, p)
	event_service.Create(auth.GetRealName(c), event_service.BuildAppKey(p.Cluster, p.Namespace, k8s_util.GetAppName(p.Pod)), "pod版本保留，pod名："+p.Pod)
	if err := k8s_service.PodVersionRetain(p.Cluster, p.Namespace, p.Pod); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	time.Sleep(2 * time.Second)
	web.SuccessJson(c, "success")
}

func ListCMSFile(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	cacheKey := fmt.Sprintf("CMSFILE##%s##%s##%s", p.Cluster, p.Namespace, p.Pod)

	if data, found := cache.GetStruct(key.Pre().POD.Key(cacheKey), []string{}); found {
		web.SuccessJson(c, data)
		return
	}
	data, err := k8s_service.ListJavaCmsFile(p.Cluster, p.Namespace, p.Pod)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	sort.Strings(data)
	cache.SetStruct(key.Pre().POD.Key(cacheKey), data, time.Hour)
	web.SuccessJson(c, data)
}

func WebShell(c *gin.Context) {
	podIP := c.Query("podIP")
	cluster := config.GetSetting().GetClusterByPodIP(podIP)
	if cluster == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "can't found cluster, podIp: "+podIP)
		return
	}
	items := make([]map[string]string, 0, 5)
	for _, ns := range cluster.Namespaces {
		pod, err := k8s_service.GetPodByIP(cluster.Name, ns, podIP)
		if err == nil {
			item := make(map[string]string)
			item["cluster"] = cluster.Name
			item["namespace"] = pod.Namespace
			item["pod"] = pod.Name
			item["container"] = pod.LabelApp
			items = append(items, item)
			break
		}
	}
	param, _ := json.Marshal(items)
	url := fmt.Sprintf("%s/?param=%s",
		config.GetSetting().ThirdServices.WebShellHost, netUrl.QueryEscape(string(param)))
	c.Redirect(http.StatusFound, url)
}
func EventReport(c *gin.Context) {
	var p EventReportParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	log_service.Create("", "Pod事件-上报", fmt.Sprintf(" %s(%s)", p.Pod, p.Reason), p)
	if len(p.Type) < 1 {
		p.Type = dto.EventTypeWarning
	}
	if len(p.Action) < 1 {
		p.Action = "Undefined"
	}
	if len(p.Message) < 1 {
		p.Message = fmt.Sprintf("%s : %s", p.Reason, p.Pod)
	}

	eventDTO := dto.Event{
		Namespace:          p.Namespace,
		InvolvedObjectType: "Pod",
		InvolvedObjectName: p.Pod,
		Type:               p.Type,
		Reason:             p.Reason,
		Message:            p.Message,
		Action:             p.Action,
	}
	if err := k8s_service.CreateEvent(p.Cluster, &eventDTO); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if p.Reason == "JvmOOM" {
		_ = k8s_service.UpdatePodRestartEvent(p.Cluster, p.Namespace, p.Pod, time.Now().Format("2006-01-02 15:04:05"), p.Reason)
	}
	web.SuccessJson(c, nil)
}

func OpenPyroscope(c *gin.Context) {
	var p k8s.PodParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	clu := config.GetSetting().GetCluster(p.Cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "集群不存在")
		return
	}
	if clu.ThirdServices.Grafana.PyroscopeDS == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "当前环境暂不支持持续性能分析")
		return
	}

	configFile, err := pyroscope_service.OpenPyroscope(p.Cluster, p.Namespace, p.Pod)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "实例-开启持续性能分析", p.Pod, p)
	event_service.Create(auth.GetRealName(c), event_service.BuildAppKey(p.Cluster, p.Namespace, k8s_util.GetAppName(p.Pod)), "开启持续性能分析，pod名："+p.Pod)
	web.SuccessJson(c, configFile)
}
