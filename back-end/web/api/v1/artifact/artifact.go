package artifact

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/artifact_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

const allArtifactCacheKey = "AllArtifact"

func Search(c *gin.Context) {
	var p param.PageSearch
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	data := make(map[string]interface{})
	if entities, err := artifact_service.Search(p.Keyword, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = dto.ToArtifactList(entities)
		data["count"] = artifact_service.Count(p.Keyword)
	}
	web.SuccessJson(c, data)
}

func Analysis(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	var pipes []models.Pipeline
	var err error
	if cluster == "" {
		pipes, err = pipeline_service.FindAll()
	} else if namespace == "" {
		pipes, err = pipeline_service.FindByCluster(cluster)
	} else {
		pipes, err = pipeline_service.FindByClusterAndNamespace(cluster, namespace)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	pipelineArtifacts := make(map[string][]string)
	for _, p := range pipes {
		for _, ar := range p.AppModules {
			key := fmt.Sprintf("%s@@%s", ar.GitUrl, ar.Module)
			if pipelineArtifacts[key] == nil {
				pipelineArtifacts[key] = make([]string, 0, 100)
			}
			pipelineArtifacts[key] = append(pipelineArtifacts[key], p.FullName())
		}
	}

	entities, err := artifact_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data := dto.ToArtifactList(entities)
	for idx, ar := range data {
		key := fmt.Sprintf("%s@@%s", ar.GitUrl, ar.Module)
		extraAttr := make(map[string]interface{})
		extraAttr["refCount"] = len(pipelineArtifacts[key])
		extraAttr["ref"] = pipelineArtifacts[key]
		data[idx].ExtraAttr = extraAttr
	}
	web.SuccessJson(c, data)
}

func All(c *gin.Context) {
	if data, found := cache.GetStruct(key.Pre().ARTIFACT.Key(allArtifactCacheKey), []dto.Artifact{}); found {
		web.SuccessJson(c, data)
		return
	}
	entities, err := artifact_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data := dto.ToArtifactList(entities)
	cache.SetStruct(key.Pre().ARTIFACT.Key(allArtifactCacheKey), data, 5*time.Minute)
	web.SuccessJson(c, data)
}

func Delete(c *gin.Context) {
	idStr := c.Query("id")
	log_service.Create(auth.GetRealName(c), "部署模块-删除", idStr, "")
	id, _ := strconv.Atoi(idStr)
	if err := artifact_service.DeleteById(uint(id)); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Create(c *gin.Context) {
	var p dto.Artifact
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if _, err := artifact_service.FindByUrlAndModule(p.GitUrl, p.Module); err == nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, "【Git地址】和【模块】值相同的部署模块应存在")
		return
	}

	entity := p.ToModel()
	entity.Author = auth.GetRealName(c)
	if err := artifact_service.Create(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(entity.Author, "部署模块-创建", p.GitUrl, p)
	cache.Delete(key.Pre().ARTIFACT.Key(allArtifactCacheKey))
	web.SuccessJson(c, nil)
}
