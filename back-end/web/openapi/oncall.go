package openapi

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/web"
	log "github.com/sirupsen/logrus"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// scaleUpLock 添加应用扩容锁
func scaleUpLock(cluster, namespace, app string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// scaleUpIsLocked 检查应用扩容锁
func scaleUpIsLocked(cluster, namespace, app string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("scale-up-locker:%s/%s/%s", cluster, namespace, app))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// nodeDrainLock 添加节点驱逐锁
func nodeDrainLock(cluster, node string, expire time.Duration) {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	_ = cache.SetStruct(cacheKey, "true", expire)
}

// nodeDrainIsLocked 检查节点驱逐锁
func nodeDrainIsLocked(cluster, node string) bool {
	cacheKey := key.Pre().ONCALL.Key(fmt.Sprintf("node-drain-locker:%s/%s", cluster, node))
	if _, found := cache.GetStruct(cacheKey, interface{}(nil)); found {
		return true
	}
	return false
}

// OncallWebhookParam
// @Description Oncall服务Webhook参数
type OncallWebhookParam struct {
	// 告警环境
	AlertEnv string `json:"alert_env" binding:"required" example:"k8s0"`
	// 告警等级
	AlertLevel string `json:"alert_level" binding:"-" example:"WARN"`
	// 告警来源
	AlertSource string `json:"alert_source" binding:"-" example:"GRAFANA"`
	// 告警状态
	AlertStatus string `json:"alert_status" binding:"required" example:"FIRING"`
	// 告警名称
	AlertName string `json:"alertname" binding:"required" example:"pod-cpu-throttled"`
	// 运行环境
	Namespace string `json:"namespace" binding:"required" example:"foneshare"`
	// 资源ID
	ResourceID string `json:"resource_id" binding:"required" example:"fs-k8s-tomcat-test-5d8874c9dd-6bg6q"`
	// 资源名称
	ResourceName string `json:"resource_name" binding:"required" example:"fs-k8s-tomcat-test"`
	// 资源类型
	ResourceType string `json:"resource_type" binding:"required" example:"app"`
}

// OncallWebhook oncall webhook API
// @Summary 提供给Oncall系统的回调接口
// @Description 根据 operate 请求参数来指定操作类型
// @Tags oncall
// @Accept json
// @Produce octet-stream
// @Param RequestBody	body		OncallWebhookParam	true "json格式的请求体"
// @Param operate	query		string	false	"操作类型（可选值：ScaleUp | ThreadDump | PodDeregister | NodeDrain ）" default()
// @Success 200 {object} web.Response "success"
// @Failure	400	{object}	web.Response  "请求参数错误，错误信息在message字段中。"
// @Failure	500	{object}	web.Response  "服务器内部错误，错误信息在message字段中。"
// @Router /openapi/oncall/webhook [post]
func OncallWebhook(c *gin.Context) {
	var p OncallWebhookParam
	var response web.Response
	defer func(request *OncallWebhookParam, response *web.Response) {
		operate := fmt.Sprintf("OnCall-WebHook")
		operator := fmt.Sprintf("%s(%s)", request.AlertName, request.AlertStatus)
		target := fmt.Sprintf("%s/%s/%s", request.AlertEnv, request.Namespace, request.ResourceName)
		log_service.Create(operator, operate, target, map[string]interface{}{
			"request":  *request,
			"response": *response,
		})
	}(&p, &response)

	if err := c.ShouldBindBodyWith(&p, binding.JSON); err != nil {
		response = web.FailJson2(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if !strings.EqualFold(p.AlertStatus, "FIRING") {
		response = web.SuccessJson2(c, "skipped, because alert_status is not FIRING")
		return
	}
	if strings.EqualFold(p.Namespace, "jacoco") {
		response = web.SuccessJson2(c, "skipped, jacoco namespace ignore")
		return
	}
	conf := config.GetOncallConfig()
	if !conf.Enable {
		response = web.FailJson2(c, web.CODE_SERVER_ERROR, "oncall webhook is not enabled")
		return
	}

	operate := c.Query("operate")
	var data interface{}
	var err error
	if operate == "ScaleUp" {
		data, err = scaleUp(p)
	} else if operate == "ThreadDump" {
		data, err = threadDump(p)
	} else if operate == "PodDeregister" {
		data, err = podDeregister(p)
	} else if operate == "NodeDrain" {
		data, err = nodeDrain(p)
	} else {
		response = web.FailJson2(c, web.CODE_CLIENT_ERROR, "operate ["+operate+"] is not supported")
		return
	}

	if err != nil {
		response = web.FailJson2(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	response = web.SuccessJson2(c, data)
}

func scaleUp(p OncallWebhookParam) (interface{}, error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName
	addReplica := int32(3)
	scaleUpConfig := config.GetOncallConfig().ScaleUp

	if capacity, er := k8s_service.GetClusterAllocatableCapacity(cluster); er == nil {
		if capacity.CPUAvailable < int64(scaleUpConfig.MinCPUAvailableCore)*1000 {
			return nil, fmt.Errorf("skipped, cluster cpu available is less than %s Core", strconv.Itoa(scaleUpConfig.MinCPUAvailableCore))
		}
		if capacity.MemoryAvailable < int64(scaleUpConfig.MinMemoryAvailableGB)*1024*1024*1024 {
			return nil, fmt.Errorf("skipped, cluster memory available is less than %d GB", scaleUpConfig.MinMemoryAvailableGB)
		}
	}

	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		return nil, fmt.Errorf("skipped, app is not in alert whitelist")
	}

	dryRun := config.GetOncallConfig().GetAlert(p.AlertName).DryRun
	beforeReplicas, afterReplicas, err := scaleUpExecute(cluster, namespace, app, addReplica, dryRun)
	if err != nil {
		return nil, err
	}

	if dryRun {
		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp(DryRun)",
			fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, beforeReplicas, afterReplicas))
		return nil, nil
	}

	//该日志给 scaleDownOncallScaledApps 定时任务使用。参考：jobs.go 下的 scaleDownOncallScaledApps
	log_service.Create(p.AlertName, "oncall-scale-up", fmt.Sprintf("%s/%s/%s", cluster, namespace, app), "")

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp",
		fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, beforeReplicas, afterReplicas))

	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
		fmt.Sprintf("【告警自愈】服务扩容，副本数: %d → %d", beforeReplicas, afterReplicas))

	return nil, nil
}

func scaleUpExecute(cluster, namespace, app string, addReplica int32, dryRun bool) (beforeReplicas, afterReplicas int32, err error) {
	if scaleUpIsLocked(cluster, namespace, app) {
		return -1, -1, fmt.Errorf("skipped, app scale up is locked because of has been scaled up recently")
	}
	dep, err := k8s_service.GetDeploymentDTO(cluster, namespace, app)
	if err != nil {
		return -1, -1, err
	}
	if dep.Replicas == 0 {
		return -1, -1, fmt.Errorf("skipped, current replicas is 0")
	}
	if dep.Replicas == 1 {
		return -1, -1, fmt.Errorf("skipped, current replicas is 1, maybe the service can only run with a single replica")
	}

	allowedMaxReplicas := int32(20)
	if clu := config.GetSetting().GetCluster(cluster); clu != nil {
		allowedMaxReplicas = clu.ScaleMaxReplicas
	}
	if dep.Replicas >= allowedMaxReplicas {
		return -1, -1, fmt.Errorf("skipped, current replicas is greater than allowed max replicas %d", allowedMaxReplicas)
	}
	newReplicas := dep.Replicas + addReplica
	scaleUpLock(cluster, namespace, app, 15*time.Minute)

	if dryRun {
		return dep.Replicas, newReplicas, nil
	}
	err = kubectl.Scale(cluster, namespace, app, newReplicas)
	return dep.Replicas, newReplicas, err
}

func podDeregister(p OncallWebhookParam) (interface{}, error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName

	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		return nil, fmt.Errorf("skipped, app is not in alert whitelist")
	}

	dryRun := config.GetOncallConfig().GetAlert(p.AlertName).DryRun
	err := podDeregisterExecute(cluster, namespace, p.ResourceID, dryRun)
	if err != nil {
		return nil, err
	}
	if dryRun {
		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "Deregister(DryRun)",
			fmt.Sprintf("Pod %s 因 %s 告警触发了自动摘除", p.ResourceID, p.AlertName))
		return nil, nil
	}

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "Deregister",
		fmt.Sprintf("Pod %s 因 %s 告警触发了自动摘除", p.ResourceID, p.AlertName))
	//待改进： 记录摘除状态
	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
		fmt.Sprintf("【告警自愈】Pod摘除，名称: %s", p.ResourceID))

	return nil, nil
}

func podDeregisterExecute(cluster, namespace, pod string, dryRun bool) error {
	if podDetail, err := k8s_service.PodDetail(cluster, namespace, pod); podDetail != nil && err == nil {
		//对于启动不久的pod，关闭摘除。避免Pod启动阶段被摘除
		if time.Now().Unix()-podDetail.CreationTimestamp.Unix() < 900 {
			return fmt.Errorf("skipped, pod is created less than 15 minutes")
		}
	} else {
		return fmt.Errorf("skipped, resource is not pod, path: %s/%s/%s", cluster, namespace, pod)
	}
	if dryRun {
		return nil
	}
	err := kubectl.DeregisterService(cluster, namespace, pod)
	if err != nil {
		return err
	}
	err = k8s_service.PodDeregister(cluster, namespace, pod)
	return err
}

func nodeDrain(p OncallWebhookParam) (interface{}, error) {
	cluster := p.AlertEnv
	node := p.ResourceID
	if strings.EqualFold(p.AlertEnv, "k8s1") {
		node = ipToVlnxName(node)
	}
	alert := config.GetOncallConfig().GetAlert(p.AlertName)
	data, err := nodeDrainExecute(cluster, node, alert)
	if err != nil {
		return nil, err
	}
	for _, item := range data {
		if strings.EqualFold(item["status"], "success") {
			selfHealingType := "Eviction"
			if alert.DryRun {
				selfHealingType = "Eviction(DryRun)"
			}
			sendSelfHealingEvent(cluster, item["namespace"], "app", item["app"], alert.Name, selfHealingType,
				fmt.Sprintf("Pod %s 因 %s 告警触发了自动驱逐", item["pod"], alert.Name))
			if !alert.DryRun {
				event_service.Create(alert.Name, event_service.BuildAppKey(cluster, item["namespace"], item["app"]),
					fmt.Sprintf("【告警自愈】Pod驱逐，名称: %s", item["pod"]))
			}
		}
	}
	return data, nil
}

func nodeDrainExecute(cluster, node string, alert config.Alert) ([]map[string]string, error) {
	podList, err := k8s.ListPodByNode(cluster, node)
	if err != nil {
		return nil, err
	}

	drainPodList := make([]corev1.Pod, 0, 5)
	for _, pod := range podList.Items {
		app := k8s_util.GetAppName(pod.Name)
		if alert.AppAllow(cluster, pod.Namespace, app) {
			drainPodList = append(drainPodList, pod)
		}
	}
	//最多驱逐3个pod
	if len(drainPodList) > 3 {
		drainPodList = drainPodList[:3]
	}

	if len(drainPodList) > 0 {
		if nodeDrainIsLocked(cluster, node) {
			return nil, fmt.Errorf("skipped, node drain is locked")
		}
		nodeDrainLock(cluster, node, 10*time.Minute)
	}

	data := make([]map[string]string, 0, len(drainPodList))
	dryRun := alert.DryRun
	for _, pod := range drainPodList {
		status := "success"
		app := k8s_util.GetAppName(pod.Name)
		if !dryRun {
			if e := k8s_service.PodDelete(cluster, pod.Namespace, pod.Name); e != nil {
				status = "failed. err: " + e.Error()
			}
		}
		data = append(data, map[string]string{
			"cluster":      cluster,
			"namespace":    pod.Namespace,
			"node":         node,
			"app":          app,
			"pod":          pod.Name,
			"server_level": pod.Annotations["fxiaoke.com/server-level"],
			"status":       status,
		})
	}
	return data, nil
}

func threadDump(p OncallWebhookParam) (interface{}, error) {
	cluster := p.AlertEnv
	namespace := p.Namespace
	app := p.ResourceName
	pod := p.ResourceID

	_, err := k8s_service.PodDetail(cluster, namespace, pod)

	if err != nil {
		return nil, err
	}
	if !config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, namespace, app) {
		err = fmt.Errorf("skipped, app is not in alert whitelist")
		return nil, err
	}

	if config.GetOncallConfig().GetAlert(p.AlertName).DryRun {
		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ThreadDump(DryRun)",
			fmt.Sprintf("Pod %s 因 %s 告警触发了线程Dump，文件地址：%s", pod, p.AlertName, "--"))
		return "filepath: --", nil
	}

	dumpFile, err := k8s_service.ThreadDump(cluster, namespace, pod, false)
	if err != nil {
		return nil, err
	}
	_, _ = k8s_service.ThreadDumpByGstack(cluster, namespace, pod)
	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, k8s_util.GetAppName(pod)),
		fmt.Sprintf("【告警自愈】线程Dump，文件路径: %s", dumpFile))

	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ThreadDump",
		fmt.Sprintf("Pod %s 因 %s 告警触发了线程Dump，文件地址：%s", pod, p.AlertName, dumpFile))

	return "filepath: " + dumpFile, nil
}

func ipToVlnxName(ip string) string {
	if !strings.HasPrefix(ip, "172.17") {
		return ip
	}

	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return ip
	}

	third, err1 := strconv.Atoi(parts[2])
	fourth, err2 := strconv.Atoi(parts[3])
	if err1 != nil || err2 != nil {
		return ip
	}

	return fmt.Sprintf("vlnx%03d%03d", third, fourth)
}

func sendSelfHealingEvent(cluster, namespace, resourceType, resourceId, alertName, selfHealingType, message string) {
	if err := kafka.SendDevopsEvent(kafka.DevopsEvent{
		Creator:      "fs-k8s-app-manager",
		Level:        "warn",
		Title:        "告警自愈",
		Cluster:      cluster,
		Profile:      namespace,
		ResourceType: resourceType,
		ResourceId:   resourceId,
		Message:      fmt.Sprintf("%s/%s", alertName, selfHealingType),
		Extra:        message,
	}); err != nil {
		log.Warn("send self healing event fail, " + err.Error())
	}
}
