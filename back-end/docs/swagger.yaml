definitions:
  dto.DeployModule:
    properties:
      commitId:
        type: string
      commitIdShort:
        type: string
      contextPath:
        type: string
      gitUrl:
        type: string
      module:
        type: string
      tag:
        type: string
    type: object
  dto.Deployment:
    properties:
      cluster:
        type: string
      container0Image:
        description: 第一个容器的镜像
        type: string
      container0ImageTag:
        type: string
      container0Name:
        type: string
      createTime:
        type: string
      deployModules:
        items:
          $ref: '#/definitions/dto.DeployModule'
        type: array
      deployRemark:
        type: string
      deployTag:
        type: string
      deployTime:
        type: string
      deployUser:
        type: string
      language:
        type: string
      lastModifyUser:
        type: string
      limitCpu:
        type: integer
      limitMemory:
        type: integer
      name:
        type: string
      namespace:
        type: string
      replicas:
        type: integer
      requestCpu:
        type: integer
      requestMemory:
        type: integer
      revision:
        type: string
      updateTime:
        type: string
    type: object
  openapi.AppAddressResp:
    properties:
      app:
        type: string
      cluster:
        type: string
      clusterInnerAddress:
        items:
          type: string
        type: array
      clusterOuterAddress:
        items:
          type: string
        type: array
      name:
        type: string
      namespace:
        type: string
      nodePort:
        type: string
      port:
        type: string
      protocol:
        type: string
      targetPort:
        type: string
    type: object
  openapi.AppModuleVersionResponse:
    description: 响应结构体
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      data:
        description: 数据
        items:
          $ref: '#/definitions/openapi.AppModuleVersionResponseData'
        type: array
      message:
        description: 状态信息描述
        example: success
        type: string
    type: object
  openapi.AppModuleVersionResponseData:
    description: 服务版本信息查询返回结构体
    properties:
      app:
        description: 发布系统上的应用名
        example: fs-k8s-tomcat-test
        type: string
      cluster:
        description: k8s集群名
        example: k8s1
        type: string
      gitCommitId:
        description: 代码提交ID
        example: 51e1001334abc0257a340e5791fbf578b06bb627
        type: string
      gitRef:
        description: 代码分支或标签
        example: master
        type: string
      gitSubDir:
        description: 代码仓库子目录
        example: fs-k8s-tomcat-test-biz
        type: string
      gitUrl:
        description: 代码仓库地址
        example: https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git
        type: string
      namespace:
        description: k8s命名空间 （运行环境）
        example: fstest
        type: string
    type: object
  openapi.AppRestartParam:
    properties:
      app:
        example: fs-k8s-tomcat-test
        type: string
      cluster:
        example: k8s1
        type: string
      namespace:
        example: fstest
        type: string
      token:
        example: "1234567890"
        type: string
    required:
    - app
    - cluster
    - namespace
    - token
    type: object
  openapi.AppServerDetailResponse:
    description: 响应结构体
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      data:
        allOf:
        - $ref: '#/definitions/dto.Deployment'
        description: 数据
      message:
        description: 状态信息描述
        example: success
        type: string
    type: object
  openapi.DownloadCoverageParam:
    description: 覆盖率下载参数结构体
    properties:
      app:
        description: 发布系统上的应用名
        example: fs-crm-sfa
        type: string
      appModule:
        description: 应用的部署模块， 格式为：git地址@模块名
        example: https://git.firstshare.cn/sfa/fs-crm-sfa.git@fs-crm-web
        type: string
      cluster:
        description: k8s集群名
        example: k8s0
        type: string
      jars:
        description: 需要下载的jar包
        example:
        - fs-metadata-common.jar
        - fs-metadata-core.jar
        items:
          type: string
        type: array
      namespace:
        description: k8s命名空间 （运行环境）
        example: jacoco
        type: string
    required:
    - app
    - appModule
    - cluster
    - namespace
    type: object
  openapi.GitProjectVersionResponse:
    description: 响应结构体
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      data:
        description: 数据
        items:
          $ref: '#/definitions/openapi.GitProjectVersionResponseData'
        type: array
      message:
        description: 状态信息描述
        example: success
        type: string
    type: object
  openapi.GitProjectVersionResponseData:
    description: Git项目版本信息查询返回结构体
    properties:
      apps:
        description: 被引用的应用列表
        example:
        - fs-k8s-tomcat-test-biz
        items:
          type: string
        type: array
      appsWithEnv:
        example:
        - k8s1/fstest/fs-k8s-tomcat-test-biz
        items:
          type: string
        type: array
      gitCommitId:
        description: 代码提交ID
        example: 51e1001334abc0257a340e5791fbf578b06bb627
        type: string
      gitRef:
        description: 代码提交标签
        example: master
        type: string
      gitUrl:
        example: https://git.firstshare.cn/devops/fs-k8s-tomcat-test.git
        type: string
      modules:
        description: 被应用引用的模块列表。空值表示该项目的根模块
        example:
        - fs-k8s-tomcat-test-biz
        - fs-k8s-tomcat-test-provider
        items:
          type: string
        type: array
      owners:
        description: 被引用的应用的所有负责人信息
        example:
        - 吴志辉
        - 刘全胜
        items:
          type: string
        type: array
    type: object
  openapi.OncallWebhookParam:
    description: Oncall服务Webhook参数
    properties:
      alert_env:
        description: 告警环境
        example: k8s0
        type: string
      alert_level:
        description: 告警等级
        example: WARN
        type: string
      alert_source:
        description: 告警来源
        example: GRAFANA
        type: string
      alert_status:
        description: 告警状态
        example: FIRING
        type: string
      alertname:
        description: 告警名称
        example: pod-cpu-throttled
        type: string
      namespace:
        description: 运行环境
        example: foneshare
        type: string
      resource_id:
        description: 资源ID
        example: fs-k8s-tomcat-test-5d8874c9dd-6bg6q
        type: string
      resource_name:
        description: 资源名称
        example: fs-k8s-tomcat-test
        type: string
      resource_type:
        description: 资源类型
        example: app
        type: string
    required:
    - alert_env
    - alert_status
    - alertname
    - namespace
    - resource_id
    - resource_name
    - resource_type
    type: object
  openapi.ResetCoverageParam:
    description: 重置覆盖率参数结构体
    properties:
      app:
        description: 发布系统上的应用名
        example: fs-crm-sfa
        type: string
      cluster:
        description: k8s集群名
        example: k8s0
        type: string
      namespace:
        description: k8s命名空间 （运行环境）
        example: jacoco
        type: string
    required:
    - app
    - cluster
    - namespace
    type: object
  web.Response:
    description: 响应结构体
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      data:
        description: 数据
      message:
        description: 状态信息描述
        example: success
        type: string
    type: object
info:
  contact: {}
  description: |-
    k8s应用管理系统API接口文档
    ◉ 线下地址：k8s-app.firstshare.cn
    ◉ 线上地址：k8s-app.foneshare.cn
  title: k8s应用管理系统API
  version: "1.0"
paths:
  /jacoco/coverage/download:
    post:
      consumes:
      - application/json
      description: |-
        下载的文件名为：{podName}-jacoco.zip， 解压后的文件结构为：
        <div>--------------------</div>
        <pre>
        classes              (存放class文件里目录，包含了部署模块的class文件）
        jacoco.exec         （jacoco覆盖率文件）
        commitID.txt        （部署模块运行的CommitID）
        gitRef.txt          （部署模块运行的分支或者tag名）
        jars                （存放jar包的目录，里面存放请求参数里指定的jar包）
        └─jar001-1.0.0.jar  （示例的jar文件）
        └─jar002-1.1.0.jar  （示例的jar文件）
        </pre>
        <div>--------------------</div>
      parameters:
      - description: json格式的请求体
        in: body
        name: RequestBody
        required: true
        schema:
          $ref: '#/definitions/openapi.DownloadCoverageParam'
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 下载的文件
          schema:
            type: file
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 生成覆盖率文件并下载，此接口耗时较长，建议调用方配置超时时间为2分钟以上
      tags:
      - jacoco
  /jacoco/coverage/reset:
    post:
      consumes:
      - application/json
      parameters:
      - description: json格式的请求体
        in: body
        name: RequestBody
        required: true
        schema:
          $ref: '#/definitions/openapi.ResetCoverageParam'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/web.Response'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 重置覆盖率
      tags:
      - jacoco
  /openapi/app/address:
    get:
      parameters:
      - description: 应用名称
        in: query
        name: app
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/web.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/openapi.AppAddressResp'
                  type: array
              type: object
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 查询应用地址
      tags:
      - app
  /openapi/app/git-project/version:
    get:
      parameters:
      - description: 运行环境
        in: query
        name: namespace
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/openapi.GitProjectVersionResponse'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 查询应用所使用Git项目的版本信息
      tags:
      - app
  /openapi/app/module/version:
    get:
      parameters:
      - description: 运行环境
        in: query
        name: namespace
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/openapi.AppModuleVersionResponse'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 查询服务部署模的版本信息
      tags:
      - app
  /openapi/app/restart:
    post:
      parameters:
      - description: json格式的请求体
        in: body
        name: RequestBody
        required: true
        schema:
          $ref: '#/definitions/openapi.AppRestartParam'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            $ref: '#/definitions/web.Response'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 重启应用
      tags:
      - app
  /openapi/app/server/detail:
    get:
      parameters:
      - description: k8s集群名
        in: query
        name: cluster
        required: true
        type: string
      - description: 运行环境
        in: query
        name: namespace
        required: true
        type: string
      - description: 应用名
        in: query
        name: app
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            $ref: '#/definitions/openapi.AppServerDetailResponse'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 获取应用服务信息
      tags:
      - app
  /openapi/oncall/webhook:
    post:
      consumes:
      - application/json
      description: 根据 operate 请求参数来指定操作类型
      parameters:
      - description: json格式的请求体
        in: body
        name: RequestBody
        required: true
        schema:
          $ref: '#/definitions/openapi.OncallWebhookParam'
      - default: ""
        description: 操作类型（可选值：ScaleUp | ThreadDump | podDeregister | NodeDrain ）
        in: query
        name: operate
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: success
          schema:
            $ref: '#/definitions/web.Response'
        "400":
          description: 请求参数错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
        "500":
          description: 服务器内部错误，错误信息在message字段中。
          schema:
            $ref: '#/definitions/web.Response'
      summary: 提供给Oncall系统的回调接口
      tags:
      - oncall
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
  BasicAuth:
    type: basic
swagger: "2.0"
