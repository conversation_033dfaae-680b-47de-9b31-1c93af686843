package perm_service

import (
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/org_service"
	log "github.com/sirupsen/logrus"
	"time"
)

const orgAdmin = "发布系统-管理员"
const timeWindowAdmin = "发版时间窗口-管理员"
const tempAuthAdmin = "临时授权-管理员"
const opsDeployAdmin = "私有云发布-管理员"

func IsAdmin(user models.User) bool {
	org := adminOrg()
	if org != nil {
		return strslice.Find(org.Users, user.RealName)
	}
	return false
}

func IsAdmin2(userRealName string) bool {
	org := adminOrg()
	if org != nil {
		return strslice.Find(org.Users, userRealName)
	}
	return false
}

func adminOrg() *models.Org {
	if v, found := GetOrgsWithCache()[orgAdmin]; found {
		return &v
	}
	return nil
}

func IsTimeWindowAdmin(user models.User) bool {
	if v, found := GetOrgsWithCache()[timeWindowAdmin]; found {
		return strslice.Find(v.Users, user.RealName)
	}
	return false
}

func IsTempAuthAdmin(user models.User) bool {
	if v, found := GetOrgsWithCache()[tempAuthAdmin]; found {
		return strslice.Find(v.Users, user.RealName)
	}
	return false
}
func ListTempAuthAdmin() []string {
	if v, found := GetOrgsWithCache()[tempAuthAdmin]; found {
		return v.Users
	}
	return make([]string, 0)
}

func IsOpsDeployAdmin(user models.User) bool {
	if v, found := GetOrgsWithCache()[opsDeployAdmin]; found {
		return strslice.Find(v.Users, user.RealName)
	}
	return false
}
func ListOpsDeployAdmin() []string {
	if v, found := GetOrgsWithCache()[opsDeployAdmin]; found {
		return v.Users
	}
	return make([]string, 0)
}

// InOrg 用户是否组内成员
func InOrg(user models.User, orgName string) bool {
	org, found := GetOrgsWithCache()[orgName]
	if !found {
		return false
	}
	return strslice.Find(org.Users, user.RealName)
}

func ClearOrgsCache() {
	cache.Delete(key.Pre().PERM.Key("all-org-cache"))
}
func GetOrgsWithCache() map[string]models.Org {
	cacheKey := "all-org-cache"
	if data, found := cache.GetStruct(key.Pre().PERM.Key(cacheKey), map[string]models.Org{}); found {
		return data
	}
	data := make(map[string]models.Org)
	if orgs, err := org_service.FindAll(); err == nil {
		for _, org := range orgs {
			data[org.Name] = org
		}
		cache.SetStruct(key.Pre().PERM.Key(cacheKey), data, 5*time.Minute)
	} else {
		log.Warn("org_service.FindAll() fail," + err.Error())
	}
	return data
}

func HasAppPerm(user models.User, app string) bool {
	return HasAppPerm2(user.RealName, app)
}

// 是否为应用管理员
//func IsAppAdmin(user models.User, app string) bool {
//	if IsAdmin2(user.RealName) {
//		return true
//	}
//	return IsAppAdmin2(user.RealName, app)
//}
//func IsAppAdmin2(userRealName, app string) bool {
//	return strslice.Find(GetAppAdmins(app), userRealName)
//}

func IsAppOwner(userRealName, app string) bool {
	return strslice.Find(app_service.GetAppAllOwnersWithCache(app), userRealName)
}

func IsAppMainOwner(userRealName, app string) bool {
	return strslice.Find(app_service.GetAppMainOwnersWithCache(app), userRealName)
}

func HasAppPerm2(userRealName, app string) bool {
	if IsAdmin2(userRealName) {
		return true
	}
	if IsAppOwner(userRealName, app) {
		return true
	}

	appOrgs := app_service.GetAppOrgsWithCache(app)
	//当app没有配置权限时， 则默认为所有人都有权限
	if len(appOrgs) < 1 {
		return true
	}
	orgs := GetOrgsWithCache()
	for _, appOrg := range appOrgs {
		if org, found := orgs[appOrg]; found {
			if strslice.Find(org.Users, userRealName) {
				return true
			}
		}
	}
	return false
}
