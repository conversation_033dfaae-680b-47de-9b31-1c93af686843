package scale_log_service

import (
	"fmt"
	"fs-k8s-app-manager/models"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"strings"
)

func Create(operate, cluster, namespace, app, author, remark string, oldReplicas, newReplicas int32) {
	if err := Save(&models.ScaleLog{
		Model:       gorm.Model{},
		Operate:     operate,
		App:         app,
		Cluster:     cluster,
		Namespace:   namespace,
		OldReplicas: oldReplicas,
		NewReplicas: newReplicas,
		Author:      author,
		Remark:      remark,
	}); err != nil {
		log.Warn("log save db fail ", err)
	}
}
func CreateBySys(operate, cluster, namespace, app, remark string, oldReplicas, newReplicas int32) {
	Create(operate, cluster, namespace, app, "system", remark, oldReplicas, newReplicas)
}
func Save(entity *models.ScaleLog) (err error) {
	return models.DB().Create(entity).Error
}

func Search(keywords []string, page, limit int) (ret []models.ScaleLog, err error) {
	db := models.DB().Model(&models.ScaleLog{})
	conditions := make([]string, 0, 20)
	for _, key := range keywords {
		cond := fmt.Sprintf(" (operate LIKE '%%%s%%' or app LIKE '%%%s%%' OR remark LIKE '%%%s%%') ", key, key, key)
		conditions = append(conditions, cond)
	}
	if len(conditions) > 0 {
		db.Where(strings.Join(conditions, " AND "))
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}

func Count(keywords []string) int64 {
	var count int64
	db := models.DB().Model(models.ScaleLog{})
	conditions := make([]string, 0, 20)
	for _, key := range keywords {
		cond := fmt.Sprintf(" (operate LIKE '%%%s%%' or app LIKE '%%%s%%' OR remark LIKE '%%%s%%') ", key, key, key)
		conditions = append(conditions, cond)
	}
	if len(conditions) > 0 {
		db.Where(strings.Join(conditions, " AND "))
	}
	db.Count(&count)
	return count
}
