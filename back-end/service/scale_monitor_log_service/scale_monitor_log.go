package scale_monitor_log_service

import (
	"fs-k8s-app-manager/models"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"time"
)

func Create(cluster, namespace, app, remark string) {
	if err := Save(&models.ScaleMonitorLog{
		Model:     gorm.Model{},
		App:       app,
		Cluster:   cluster,
		Namespace: namespace,
		Remark:    remark,
	}); err != nil {
		log.Warn("log save db fail ", err)
	}
}

func Save(entity *models.ScaleMonitorLog) (err error) {
	return models.DB().Create(entity).Error
}

func SearchToday(cluster, namespace, app string) (ret []models.ScaleMonitorLog, err error) {
	db := models.DB().Model(&models.ScaleMonitorLog{})
	n := time.Now()
	today := time.Date(n.Year(), n.Month(), n.Day(), 0, 0, 0, 0, n.Location())
	db.Where("created_at > ? AND cluster = ? AND namespace = ? AND app = ?", today, cluster, namespace, app)
	err = db.Order("created_at desc").Find(&ret).Error
	return
}

func Count() int64 {
	var count int64
	models.DB().Model(models.Log{}).Count(&count)
	return count
}

func DeleteBeforeTime(time time.Time) error {
	return models.DB().Unscoped().Where("created_at < ?", time).Delete(&models.ScaleMonitorLog{}).Error
}
