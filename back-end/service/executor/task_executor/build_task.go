package task_executor

import (
	"context"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/client/jenkins"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/task_service"
	"github.com/mitchellh/mapstructure"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"strings"
	"time"
)

type BuildParam struct {
	JenkinsJob        string `json:"jenkinsJob"`
	MavenImage        string `json:"mavenImage"`
	UnitTest          bool   `json:"unitTest"`
	ForceCodeCompile  bool   `json:"forceCodeCompile"`
	DependencyCheck   bool   `json:"dependencyCheck"`
	ParentPom         string `json:"parentPom"`
	GitUrl            string `json:"gitUrl"`
	GitModule         string `json:"gitModule"`
	GitTag            string `json:"gitTag"`
	CommitId          string `json:"commitId"`
	ArtifactBaseImage string `json:"artifactBaseImage"`
	ArtifactImage     string `json:"artifactImage"`
}

type BuildTaskExecutor struct {
	TaskAbstractExecutor
	Params BuildParam
}

func (s BuildTaskExecutor) Build(t *models.Task) (TaskExecutor, error) {
	if err := mapstructure.Decode(t.Params, &s.Params); err != nil {
		return s, err
	}
	s.Task = t
	return s, nil
}

func (s BuildTaskExecutor) Run() {
	defer s.catchPanic()
	if s.Task.IsSkip() {
		panic("task is skip")
	}
	if !s.Task.IsWait() {
		panic("task is not wait")
	}
	if err := s.Start(); err != nil {
		_ = s.Fail()
		panic(err.Error())
	}
	if err := s.process(); err != nil {
		panic(err.Error())
	}
	if !s.Task.IsCancel() {
		if err := s.Success(); err != nil {
			panic(err.Error())
		}
	}
}

func (s BuildTaskExecutor) process() error {
	start := time.Now().Unix()
	jc, err := jenkins.CreateJenkinsClientDefault(s.Params.JenkinsJob)
	if err != nil {
		return err
	}
	params := make(map[string]string)
	params["unitTest"] = bool2YesNo(s.Params.UnitTest)
	params["forceBuild"] = bool2YesNo(s.Params.ForceCodeCompile)
	params["dependencyCheck"] = bool2YesNo(s.Params.DependencyCheck)
	params["parentPom"] = s.Params.ParentPom
	//短时间内使用相同参数调用jenkins api时，会存在状态码303的问题，导致无法批量发版。
	//这里使用uuid来实现每次的请求参数不一致
	params["uuid"] = uuid.NewV4().String()
	params["mavenImage"] = s.Params.MavenImage
	params["gitUrl"] = s.Params.GitUrl
	params["gitModule"] = s.Params.GitModule
	params["gitTag"] = s.Params.GitTag
	params["commitId"] = s.Params.CommitId
	params["artifactBaseImage"] = s.Params.ArtifactBaseImage
	params["artifactImage"] = s.Params.ArtifactImage
	params["artifactRemark"] = strslice.FirstNotEmpty(s.Task.Remark, "--")
	params["author"] = s.Task.Author

	queueId, err := jc.BuildWithParameters(params)
	if err != nil {
		return err
	}
	if queueId == 0 {
		return errors.New("jenkins创建Job实例失败，可能是由于Job中有实例在排队等待中，请通过重试来尝试解决")
	}
	buildId, err := jc.GetBuildIdByQueueItemId(queueId, 120)
	if err != nil {
		return err
	}
	_ = task_service.AppendAttribute(s.Task.ID, "jenkinsJob", s.Params.JenkinsJob)
	_ = task_service.AppendAttribute(s.Task.ID, "jenkinsBuildId", buildId)
	build, err := jc.GetBuild(buildId)
	if err != nil {
		return err
	}
	var loopErr error = nil
	for {
		_ = s.ReloadTask()
		if s.Task.IsCancel() {
			_ = s.AppendOutput("The task was canceled")
			if _, e := build.Stop(context.Background()); e != nil {
				log.Warn("Jenkins build stop fail, ", e.Error())
			}
			break
		}
		if time.Now().Unix()-start > int64(s.Task.TimeoutSeconds) {
			loopErr = errors.New(fmt.Sprintf("task timeout [%d seconds]", s.Task.TimeoutSeconds))
			break
		}
		time.Sleep(5 * time.Second)
		output := build.GetConsoleOutput(context.Background())
		if err := s.ChangeOutput(output); err != nil {
			log.Warn("task output update fail,", err.Error())
		}

		if _, err := build.Poll(context.Background()); err != nil {
			log.Warn(err.Error())
			continue
		}
		if !build.Raw.Building {
			switch strings.ToUpper(build.Raw.Result) {
			case jenkins.BUILD_RESULT_ABORTED:
				_ = s.Cancel()
			case jenkins.BUILD_RESULT_SUCCESS:
				fmt.Println("jenkins task success")
			default:
				loopErr = errors.New("jenkins task fail")
			}
			break
		}
	}
	return loopErr
}

func bool2YesNo(v bool) string {
	if v {
		return "yes"
	} else {
		return "no"
	}
}
