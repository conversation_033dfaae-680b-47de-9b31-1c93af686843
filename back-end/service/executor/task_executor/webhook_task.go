package task_executor

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models"
	"github.com/go-resty/resty/v2"
	"github.com/mitchellh/mapstructure"
	uuid "github.com/satori/go.uuid"
)

type WebhookParam struct {
	Url    string                 `json:"url"`
	Method string                 `json:"method"`
	Body   map[string]interface{} `json:"body"`
}

type WebhookTaskExecutor struct {
	TaskAbstractExecutor
	Params WebhookParam
}

func (s WebhookTaskExecutor) Build(t *models.Task) (TaskExecutor, error) {
	if err := mapstructure.Decode(t.Params, &s.Params); err != nil {
		return s, err
	}
	s.Task = t
	return s, nil
}

func (s WebhookTaskExecutor) Run() {
	defer s.catchPanic()
	if s.Task.IsSkip() {
		panic("task is skip")
	}
	if !s.Task.IsWait() {
		panic("task is not wait")
	}
	if err := s.Start(); err != nil {
		_ = s.Fail()
		panic(err.Error())
	}
	if err := s.process(); err != nil {
		panic(err.Error())
	}
	if !s.Task.IsCancel() {
		if err := s.Success(); err != nil {
			panic(err.Error())
		}
	}
}

func (s WebhookTaskExecutor) process() error {
	cli := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	//cli.SetBasicAuth("","")
	var repo *resty.Response
	reqId := uuid.NewV4().String()

	reqBody := ""
	if v, err := json.Marshal(s.Params.Body); err != nil {
		return err
	} else {
		reqBody = string(v)
	}
	if s.Params.Method != "POST" {
		return errors.New("currently only the post method is supported")
	}
	_ = s.AppendOutput(fmt.Sprintf("Request Url: %s", s.Params.Url))
	_ = s.AppendOutput(fmt.Sprintf("Request Method: %s", s.Params.Method))
	_ = s.AppendOutput(fmt.Sprintf("Request body: %s", reqBody))

	repo, err := cli.R().
		SetHeader("x-request-id", reqId).
		SetHeader("Content-Type", "application/json").
		SetBody(reqBody).
		Post(s.Params.Url)
	if err != nil {
		return err
	}
	_ = s.AppendOutput(fmt.Sprintf("Response Status: %d", repo.StatusCode()))
	_ = s.AppendOutput(fmt.Sprintf("Response Body: %s", string(repo.Body())))
	if 200 < repo.StatusCode() && repo.StatusCode() < 300 {
		return errors.New("http status code are not between 200 and 300")
	}
	return nil
}
