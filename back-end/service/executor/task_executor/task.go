package task_executor

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/service/task_service"
	"strings"
	"time"
)

type TaskExecutor interface {
	Run()
	Build(t *models.Task) (TaskExecutor, error)
	GetTask() models.Task
}

type TaskAbstractExecutor struct {
	Task *models.Task
}

func (t TaskAbstractExecutor) GetTask() models.Task {
	return *t.Task
}

func (t TaskAbstractExecutor) catchPanic() {
	success := true
	if r := recover(); r != nil {
		_ = t.AppendOutput(fmt.Sprintf("%v", r))
		success = false
	}
	_ = t.ReloadTask()
	if !t.Task.IsEnd() {
		if success {
			_ = t.Success()
		} else {
			_ = t.Fail()
		}
	}
	_ = task_service.UpdateEndTime(t.Task.ID, time.Now())
}

func (t TaskAbstractExecutor) Start() error {
	t.Task.StartTime = models.DBTime(time.Now())
	t.Task.Status = models.TASK_PHASE_RUNNING
	return models.DB().Save(t.Task).Error
}
func (t TaskAbstractExecutor) Cancel() error {
	t.Task.Status = models.TASK_PHASE_CANCEL
	return models.DB().Save(t.Task).Error
}
func (t TaskAbstractExecutor) Success() error {
	t.Task.Status = models.TASK_PHASE_SUCCESS
	return models.DB().Save(t.Task).Error
}
func (t TaskAbstractExecutor) Fail() error {
	t.Task.Status = models.TASK_PHASE_FAIL
	return models.DB().Save(t.Task).Error
}
func (t TaskAbstractExecutor) ReloadTask() error {
	return models.DB().Where(models.Task{BaseModel: models.BaseModel{ID: t.Task.ID}}).Take(&t.Task).Error
}

func (t TaskAbstractExecutor) AppendOutput(output string) error {
	if strings.TrimSpace(output) == "" {
		return nil
	}
	if t.Task.Output != "" {
		t.Task.Output += "\n"
	}
	t.Task.Output += output
	return models.DB().Save(t.Task).Error
}

func (t TaskAbstractExecutor) ChangeOutput(output string) error {
	t.Task.Output = output
	return models.DB().Save(t.Task).Error
}
