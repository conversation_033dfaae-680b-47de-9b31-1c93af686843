package user_service

import (
	"encoding/json"
	"fs-k8s-app-manager/models"
	"io"
	"net/http"
	"strings"
	"time"
)

func All() (ret []models.User, err error) {
	err = models.DB().Find(&ret).Error
	return
}
func FindByName(name string) (ret models.User, err error) {
	err = models.DB().Where("username = ?", name).Take(&ret).Error
	return
}
func FindByRealName(realName string) (ret models.User, err error) {
	err = models.DB().Where("real_name = ?", realName).Take(&ret).Error
	return
}

func ListByRealName(realName string) (ret []models.User, err error) {
	err = models.DB().Where("real_name like ?", "%"+realName+"%").Find(&ret).Error
	return
}
func PreLikeByRealName(realName string) (ret models.User, err error) {
	err = models.DB().Where("real_name like ?", realName+"%").Take(&ret).Error
	return
}

func UpdateRecentApp(username string, apps []string) error {
	return models.DB().Model(&models.User{}).
		Where("username", username).
		Select("RecentApps").Updates(models.User{RecentApps: apps}).Error
}

func UpdateRecentPod(username string, service []string) error {
	return models.DB().Model(&models.User{}).
		Where("username", username).
		Select("RecentPods").Updates(models.User{RecentPods: service}).Error
}

func Create(entity *models.User) (err error) {
	return models.DB().Create(entity).Error
}

func Save(entity *models.User) (err error) {
	return models.DB().Save(entity).Error
}

type employeeResponse struct {
	EmployeeDto employeeDto `json:"employeeDto"`
}

type employeeDto struct {
	Status string `json:"status"`
}

var resignedUserCache = make(map[string]bool)
var resignedUserCacheTime = time.Now()

func UserIsResigned(userName string) (bool, error) {
	if time.Now().Sub(resignedUserCacheTime) > 12*time.Hour {
		resignedUserCache = make(map[string]bool)
		resignedUserCacheTime = time.Now()
	}
	if v, ok := resignedUserCache[userName]; ok {
		return v, nil
	}
	url := "http://172.17.4.196:12282/fs-organization-provider/EmployeeProviderService/getEmployeesByName"
	headers := map[string]string{
		"x-fs-ei":      "1",
		"Content-Type": "application/json; charset=UTF-8",
	}

	requestData := map[string]interface{}{
		"name":         userName,
		"enterpriseId": 1,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return false, err
	}

	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		return false, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}

	var result employeeResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		return false, err
	}
	ret := strings.EqualFold(result.EmployeeDto.Status, "STOP")
	resignedUserCache[userName] = ret
	return ret, nil
}
