package notify_service

import (
	"crypto/tls"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/util/numslice"
	"fs-k8s-app-manager/pkg/util/template"
	"fs-k8s-app-manager/service/log_service"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
	"strconv"
	"strings"
)

func SendQiXinToImportantAlertSession(userIds []int, content string) error {
	return sendQiXin(userIds, config.GetConfig().QiXin.ImportantAlertAppId, content, false)
}
func SendQiXinToPublishSession(userIds []int, content string) error {
	return sendQiXin(userIds, config.GetConfig().QiXin.PublishAppId, content, true)
}
func sendQiXin(userIds []int, appId, content string, innerApp bool) error {
	body := make(map[string]interface{})
	body["appId"] = appId
	body["innerApp"] = innerApp
	body["ea"] = "fs"
	body["messageType"] = "T"
	body["messageContent"] = content

	if userIds == nil {
		userIds = make([]int, 0, 3)
	}
	if config.GetConfig().QiXin.ReceiverEI != nil {
		for _, it := range config.GetConfig().QiXin.ReceiverEI {
			if _, b := numslice.FindInt(userIds, it); !b {
				userIds = append(userIds, it)
			}
		}
	}
	body["userIds"] = userIds

	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R().SetHeader("Content-Type", "application/json")
	log.Debug("=== qixin message body ===")
	log.Debug(body)
	req.SetBody(body)
	resp, err := req.Post(fmt.Sprintf("%s/sendMessageProxy/sendMessage?topic=TOPIC_QIXIN_ALARM&flag=3", config.GetConfig().QiXin.Host))
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 && resp.StatusCode() != 201 {
		return fmt.Errorf("send qixin fail, %s, %s", resp.Status(), string(resp.Body()))
	}
	log_service.CreateBySys("企信消息-发送", "AppId: "+appId, body)
	return nil
}

// PhoneCall 拨打电话语音, phones： 电话号码，多个之间用逗号分割
func PhoneCall(phones string, ttsCode int, message string) error {
	if config.GetConfig().Sms.Host == "" {
		return errors.New("sms host is empty")
	}
	body := make(map[string]interface{})

	body["phones"] = strings.Split(phones, ",")
	body["bizName"] = "k8s-app-manager"
	body["templateId"] = ttsCode
	body["templateParams"] = []string{message}
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R().SetHeader("Content-Type", "application/json")
	log.Debug("=== sms tts message body ===")
	log.Debug(body)
	req.SetBody(body)
	resp, err := req.Post(fmt.Sprintf("%s/api/v2/sms/tts", config.GetConfig().Sms.Host))
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 && resp.StatusCode() != 201 {
		return fmt.Errorf("phone call fail, %s, %s", resp.Status(), string(resp.Body()))
	}
	log_service.CreateBySys("语音电话-拨打", phones, body)
	return nil
}

func SendPaasMessage(p PaasMessageParam) error {
	paasConf := config.GetConfig().FsPaas
	body, _ := buildPaasMessageBodyTemplate(p)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-fs-ei", strconv.Itoa(paasConf.EnterpriseId)).
		SetHeader("x-fs-userinfo", strconv.Itoa(paasConf.SysUserId))

	req.SetBody(body)
	url := fmt.Sprintf("%s/v3/object_data/%s/create", config.GetConfig().FsPaas.Host, config.GetConfig().FsPaas.ObjectId)
	log.Debug("=== paas message body ===")
	log.Debug(body)
	log.Debug("=== paas message body ===")
	log.Debug(body)
	resp, err := req.Post(url)
	if err != nil {
		return err
	}

	if resp.StatusCode() != 200 && resp.StatusCode() != 201 {
		return fmt.Errorf("send paas message fail, %s, %s", resp.Status(), string(resp.Body()))
	}
	//log_service.CreateBySys("PAAS消息-发送", "--", body)
	return nil
}

func buildPaasMessageBodyTemplate(p PaasMessageParam) (string, error) {
	tepl := `
    {
      "object_data": {
        "record_type": "default__c",
            "owner":["{{.OwnerId}}"],
            "package":"CRM",
            "create_by":"{{.SysUserId}}",
            "object_describe_api_name":"{{.ObjectId}}",
            "field_x2x27__c":{{.StartTime}},
            "field_8hnir__c":{{.EndTime}},
            "field_MpSoh__c":{{.BuildTimeCost}},
            "field_jH9sx__c":{{.DeployTimeCost}},
            "field_o1mw6__c":"{{.QiXinSessionFieldId}}",
            "field_32Xb1__c":"{{.App}}",
            "field_u4zfH__c":"{{.Tag}}",
            "field_nSHhq__c":"{{.Cluster}}",
            "field_cN0wa__c":"{{.Namespace}}",
            "field_S32Ra__c":"{{.PublishStatus}}",
            "field_4eytB__c":"{{.DeployDetailLink}}",
            "field_82bZk__c":"{{.PublishRemark}}"
      },
      "optionInfo": {
        "useValidationRule": false,
        "isDuplicateSearch": false,
        "skipFuncValidate": true
      }
    }
`
	return template.ParseTemplate(tepl, p)
}

type PaasMessageParam struct {
	OwnerId             int
	SysUserId           int
	ObjectId            string
	StartTime           int64
	EndTime             int64
	BuildTimeCost       int
	DeployTimeCost      int
	QiXinSessionFieldId string
	App                 string
	Tag                 string
	Cluster             string
	Namespace           string
	DeployDetailLink    string
	PublishStatus       string
	PublishRemark       string
}
