package cron_scale_service

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

func SaveOrUpdate(entity models.CronScale) (err error) {
	if entity.ID == 0 {
		return models.DB().Create(&entity).Error
	} else {
		return models.DB().Omit("created_at").Save(&entity).Error
	}
}
func FindById(id uint) (ret models.CronScale, err error) {
	err = models.DB().Where(models.CronScale{Model: gorm.Model{ID: id}}).Take(&ret).Error
	return
}

func FirstInEnv(cluster, namespace, app string) (ret models.CronScale, err error) {
	err = models.DB().Where(models.CronScale{
		Cluster:   cluster,
		Namespace: namespace,
		App:       app,
	}).First(&ret).Error
	return
}
func DeleteById(id uint) error {
	return models.DB().Where("id = ?", id).Unscoped().Delete(&models.CronScale{}).Error
}

func FindAll() (ret []models.CronScale, err error) {
	err = models.DB().Find(&ret).Error
	return
}

func Search(cluster, namespace, app string) (ret []models.CronScale, err error) {
	db := models.DB().Model(&models.CronScale{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if app != "" {
		db.Where("app = ?", app)
	}
	err = db.Order("id desc").Find(&ret).Error
	return
}
