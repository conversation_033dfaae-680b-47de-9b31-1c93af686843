package parent_pom_service

import (
	"fs-k8s-app-manager/models"
)

func Search(keyword string) (ret []models.ParentPom, err error) {
	db := models.DB()
	if keyword != "" {
		db = db.Where("name like ?", "%"+keyword+"%")
		db = db.Or("remark like ?", "%"+keyword+"%")
	}
	err = db.Order("sort_rank desc").Find(&ret).Error
	return
}

func FindAllEnable() (ret []models.ParentPom, err error) {
	err = models.DB().Where("enable = ?", true).Order("sort_rank desc").Find(&ret).Error
	return
}

func FindAll() (ret []models.ParentPom, err error) {
	err = models.DB().Order("sort_rank desc").Find(&ret).Error
	return
}

func FindById(id uint) (ret models.ParentPom, err error) {
	err = models.DB().Where("id = ?", id).First(&ret).Error
	return
}

func FindByName(name string) (ret models.ParentPom, err error) {
	err = models.DB().Where("name = ?", name).Take(&ret).Error
	return
}

func Save(entity *models.ParentPom) (err error) {
	return models.DB().Save(entity).Error
}

func Delete(id uint) (err error) {
	return models.DB().Delete(&models.ParentPom{}, id).Error
}
