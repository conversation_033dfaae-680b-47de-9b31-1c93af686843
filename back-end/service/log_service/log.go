package log_service

import (
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/models"
	log "github.com/sirupsen/logrus"
	"reflect"
	"time"
)

func Create(author, operate, target string, jsonAble interface{}) {
	var content string
	if str, ok := jsonAble.(string); ok {
		content = str
	} else if v, err := json.Marshal(jsonAble); err != nil {
		content = fmt.Sprintf("audit log save db fail, obj can't json serializable, obj type: %s, err: %s", reflect.TypeOf(jsonAble).String(), err.Error())
		log.Warn(content)
	} else {
		content = string(v)
	}
	if err := Save(&models.Log{
		Author:  author,
		Operate: operate,
		Target:  target,
		Content: content,
	}); err != nil {
		log.Warn("audit log save db fail ", err)
	}
}
func CreateBySys(operate, target string, jsonAble interface{}) {
	Create("system", operate, target, jsonAble)
}
func Save(entity *models.Log) (err error) {
	return models.DB().Create(entity).Error
}

func Search(user, operate, target string, page, limit int) (ret []models.Log, err error) {
	db := models.DB().Model(&models.Log{})
	if operate != "" {
		db.Where("operate = ?", operate)
	}
	if target != "" {
		db.Where("target LIKE ?", "%"+target+"%")
	}
	if user != "" {
		db.Where("author LIKE ?", "%"+user+"%")
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}

func Count(user, operate, target string) int64 {
	var count int64
	db := models.DB().Model(models.Log{})
	if operate != "" {
		db.Where("operate = ?", operate)
	}
	if target != "" {
		db.Where("target LIKE ?", "%"+target+"%")
	}
	if user != "" {
		db.Where("author LIKE ?", "%"+user+"%")
	}
	db.Count(&count)
	return count
}

func CountAll() int64 {
	var count int64
	db := models.DB().Model(models.Log{})
	db.Count(&count)
	return count
}

func DeleteBeforeTime(time time.Time) error {
	return models.DB().Unscoped().Where("created_at < ?", time).Delete(&models.Log{}).Error
}
