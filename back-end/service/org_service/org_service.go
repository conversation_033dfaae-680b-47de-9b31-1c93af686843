package org_service

import (
	"fs-k8s-app-manager/models"
)

func FindByID(id uint) (ret models.Org, err error) {
	err = models.DB().Where("id = ?", id).Take(&ret).Error
	return
}

func FindAll() (ret []models.Org, err error) {
	err = models.DB().Order("id desc").Find(&ret).Error
	return
}
func Search(where models.Org) (ret []models.Org, err error) {
	err = models.DB().Model(&models.Org{}).Where(where).Order("id desc").Find(&ret).Error
	return
}

func Create(entity *models.Org) (err error) {
	return models.DB().Create(entity).Error
}

func Update(entity *models.Org) (err error) {
	return models.DB().Save(entity).Error
}
