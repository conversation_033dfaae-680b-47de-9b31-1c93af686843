package crm_service

import (
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/dto"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

type ServerRecord struct {
	ID           string   `json:"_id"`
	Name         string   `json:"name"`
	DisplayName  string   `json:"display_name"`
	Level        string   `json:"importance_level__c"`
	Category     string   `json:"service_category__c"`
	Info         string   `json:"field_U6pmo__c"`
	MainOwner    []string `json:"owner"`
	Owners       []string `json:"field_8qntr__c"`
	EnableStatus string   `json:"field_HwozY__c"`
	Departments  []string `json:"data_own_department"`
	IsDeleted    bool     `json:"is_deleted"`
}

type UserRecord struct {
	UserId string `json:"user_id"`
	Name   string `json:"name"`
}

type DeptRecord struct {
	DeptId string `json:"dept_id"`
	Name   string `json:"name"`
}

type CRMBaseResponse struct {
	ErrCode      int         `json:"errCode"`
	ErrMessage   string      `json:"errMessage"`
	SearchSource interface{} `json:"searchSource"`
}

type UserResponse struct {
	CRMBaseResponse
	Result []UserRecord `json:"result"`
}

type DeptResponse struct {
	CRMBaseResponse
	Result []DeptRecord `json:"result"`
}

type ServerResponse struct {
	CRMBaseResponse
	Result []ServerRecord `json:"result"`
}

// Generic interfaces and types for abstraction

// CRMRecord represents any record type that can be fetched from CRM
type CRMRecord interface {
	ServerRecord | UserRecord | DeptRecord
}

// CRMResponse represents any response type from CRM API
type CRMResponse[T CRMRecord] interface {
	GetErrCode() int
	GetErrMessage() string
	GetResult() []T
}

func (r ServerResponse) GetErrCode() int           { return r.ErrCode }
func (r ServerResponse) GetErrMessage() string     { return r.ErrMessage }
func (r ServerResponse) GetResult() []ServerRecord { return r.Result }

func (r UserResponse) GetErrCode() int         { return r.ErrCode }
func (r UserResponse) GetErrMessage() string   { return r.ErrMessage }
func (r UserResponse) GetResult() []UserRecord { return r.Result }

func (r DeptResponse) GetErrCode() int         { return r.ErrCode }
func (r DeptResponse) GetErrMessage() string   { return r.ErrMessage }
func (r DeptResponse) GetResult() []DeptRecord { return r.Result }

// FetchConfig holds configuration for different types of CRM data fetching
type FetchConfig struct {
	ObjectAPIName string
	FieldList     []string
	LogName       string // For logging purposes
}

var _metadataConf = config.GetConfig().MetadataServer

// Predefined configurations for different record types
var (
	ServerConfig = FetchConfig{
		ObjectAPIName: _metadataConf.ServerModuleObj,
		FieldList: []string{
			"_id",
			"name",
			"display_name",
			"importance_level__c",
			"service_category__c",
			"field_U6pmo__c",
			"owner",
			"field_8qntr__c",
			"field_HwozY__c",
			"data_own_department",
			"is_deleted",
		},
		LogName: "Servers",
	}

	UserConfig = FetchConfig{
		ObjectAPIName: _metadataConf.UserObj,
		FieldList: []string{
			"user_id",
			"name",
		},
		LogName: "Users",
	}

	DeptConfig = FetchConfig{
		ObjectAPIName: _metadataConf.DeptObj,
		FieldList: []string{
			"dept_id",
			"name",
		},
		LogName: "Departments",
	}
)

// Generic pagination function that works with any CRM record type
func fetchWithPagination[T CRMRecord](config FetchConfig, unmarshalFunc func([]byte) ([]T, int, string, error)) ([]T, error) {
	log.Infof("Starting %s fetch job", config.LogName)

	const maxRecordsPerRequest = 500
	const apiEndpoint = "/fs-metadata-rest/paas/metadata/data/find/by/template/projection"

	var allRecords []T
	offset := 0
	totalFetched := 0
	maxRequests := 200

	client := resty.New().SetHostURL(_metadataConf.Host).SetTimeout(120 * time.Second)

	for {
		maxRequests--
		if maxRequests < 0 {
			log.Warn("Max requests reached, stopping")
			break
		}
		templateJSON := map[string]string{
			"limit":  strconv.Itoa(maxRecordsPerRequest),
			"offset": strconv.Itoa(offset),
		}

		templateJSONBytes, err := json.Marshal(templateJSON)
		if err != nil {
			log.Errorf("Failed to marshal template JSON: %v", err)
			return nil, fmt.Errorf("failed to marshal template JSON: %w", err)
		}

		request := map[string]interface{}{
			"fieldList":             config.FieldList,
			"tenantId":              "1",
			"userId":                "-10000",
			"objectDescribeAPIName": config.ObjectAPIName,
			"templateJson":          string(templateJSONBytes),
		}

		log.Infof("Fetching %s batch: offset=%d, limit=%d", config.LogName, offset, maxRecordsPerRequest)

		// Make API request
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(request).
			Post(apiEndpoint)

		if err != nil {
			log.Errorf("Failed to make API request: %v", err)
			return nil, fmt.Errorf("failed to make API request: %w", err)
		}

		if resp.StatusCode() != 200 {
			log.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
			return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
		}

		// Parse response using the provided unmarshal function
		records, errCode, errMessage, err := unmarshalFunc(resp.Body())
		if err != nil {
			log.Errorf("Failed to unmarshal response: %v", err)
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		// Check for API errors
		if errCode != 0 {
			log.Errorf("API returned error: code=%d, message=%s", errCode, errMessage)
			return nil, fmt.Errorf("API error: code=%d, message=%s", errCode, errMessage)
		}

		// Add records to collection
		batchSize := len(records)
		allRecords = append(allRecords, records...)
		totalFetched += batchSize

		log.Infof("Fetched %d records in this batch, total so far: %d", batchSize, totalFetched)

		// Check if we've reached the end (no more records)
		if batchSize < maxRecordsPerRequest {
			log.Infof("Reached end of data. Fetched %d records in this batch (less than limit of %d)", batchSize, maxRecordsPerRequest)
			break
		}

		// Update offset for next iteration
		offset += maxRecordsPerRequest

		// Add a small delay to avoid overwhelming the API
		time.Sleep(200 * time.Millisecond)
	}

	log.Infof("%s fetch completed successfully. Total records fetched: %d", config.LogName, totalFetched)
	return allRecords, nil
}

// Unmarshal helper functions for different response types
func unmarshalCRMDataResponse(data []byte) ([]ServerRecord, int, string, error) {
	var response ServerResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

func unmarshalUserResponse(data []byte) ([]UserRecord, int, string, error) {
	var response UserResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

func unmarshalDeptResponse(data []byte) ([]DeptRecord, int, string, error) {
	var response DeptResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

// 获取所有服务器数据
func fetchServersDatas() ([]ServerRecord, error) {
	return fetchWithPagination(ServerConfig, unmarshalCRMDataResponse)
}

// 获取所有用户数据
func fetchUsersFromCRM() ([]UserRecord, error) {
	cacheKey := key.Pre().CMDB.Key("crm#users")
	if cachedData, found := cache.GetStruct(cacheKey, []UserRecord{}); found {
		log.Infof("Using cached users data, count: %d", len(cachedData))
		return cachedData, nil
	}

	users, err := fetchWithPagination(UserConfig, unmarshalUserResponse)
	if err != nil {
		return nil, err
	}

	if err := cache.SetStruct(cacheKey, users, 24*time.Hour); err != nil {
		log.Warnf("Failed to cache users data: %v", err)
	} else {
		log.Infof("Cached users data successfully, count: %d, expire in %v", len(users), 24*time.Hour)
	}
	return users, nil
}

// 获取所有部门数据
func fetchDeptFromCRM() ([]DeptRecord, error) {
	if DeptConfig.ObjectAPIName == "" {
		log.Warnf("No object API name specified for dept, skipping")
		return make([]DeptRecord, 0), nil
	}
	cacheKey := key.Pre().CMDB.Key("crm#dept")
	if cachedData, found := cache.GetStruct(cacheKey, []DeptRecord{}); found {
		log.Infof("Using cached dept data, count: %d", len(cachedData))
		return cachedData, nil
	}

	depts, err := fetchWithPagination(DeptConfig, unmarshalDeptResponse)
	if err != nil {
		return nil, err
	}

	if err := cache.SetStruct(cacheKey, depts, 24*time.Hour); err != nil {
		log.Warnf("Failed to cache dept data: %v", err)
	} else {
		log.Infof("Cached dept data successfully, count: %d, expire in %v", len(depts), 24*time.Hour)
	}
	return depts, nil
}

func serverLevelConvert(level string) string {
	switch level {
	case "lv0_service":
		return "L0"
	case "lv1_service":
		return "L1"
	case "lv2_service":
		return "L2"
	case "lv3_service":
		return "L3"
	default:
		return level
	}
}

func serverCategoryConvert(category string) string {
	switch category {
	case "backend_service":
		return "后台异步服务"
	case "frontend_service":
		return "前台服务"
	case "merged_service":
		return "合并部署服务"
	default:
		return category
	}
}
func serverStatusConvert(status string) string {
	switch status {
	case "option1":
		return "启用"
	case "option2":
		return "停用"
	default:
		return status
	}
}

func BuildCmsCmdbRecords() ([]dto.CmsCmdbRecord, error) {
	if _metadataConf.Host == "" || _metadataConf.ServerModuleObj == "" || _metadataConf.UserObj == "" {
		return nil, fmt.Errorf("invalid metadata server config: %v", _metadataConf)

	}
	services, err := fetchServersDatas()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch servers data: %v", err)
	}
	users, err := fetchUsersFromCRM()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch users data: %v", err)
	}
	depts, err := fetchDeptFromCRM()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch departments data: %v", err)
	}
	usersMap := make(map[string]UserRecord)
	for _, it := range users {
		usersMap[it.UserId] = it
	}
	deptsMap := make(map[string]DeptRecord)
	for _, it := range depts {
		deptsMap[it.DeptId] = it
	}
	cmdbServerRecords := make([]dto.CmsCmdbRecord, 0, len(services))
	for _, it := range services {
		if it.IsDeleted {
			continue
		}
		mainOwner := make([]string, 0, 2)
		for _, it := range it.MainOwner {
			if u, ok := usersMap[it]; ok {
				mainOwner = append(mainOwner, u.Name)
			}
		}
		owners := make([]string, 0, 10)
		for _, it := range it.Owners {
			if u, ok := usersMap[it]; ok {
				owners = append(owners, u.Name)
			}
		}
		dept := make([]string, 0, 10)
		for _, it := range it.Departments {
			if d, ok := deptsMap[it]; ok {
				dept = append(dept, d.Name)
			}
		}
		cmdbServerRecords = append(cmdbServerRecords, dto.CmsCmdbRecord{
			CrmID:       it.ID,
			Service:     it.DisplayName,
			Level:       serverLevelConvert(it.Level),
			Category:    serverCategoryConvert(it.Category),
			Info:        it.Info,
			MainOwner:   strings.Join(mainOwner, ","),
			Owner:       strings.Join(owners, ","),
			Status:      serverStatusConvert(it.EnableStatus),
			Departments: strings.Join(dept, ","),
		})
	}
	sort.Slice(cmdbServerRecords, func(i, j int) bool {
		return cmdbServerRecords[i].Service < cmdbServerRecords[j].Service
	})
	// 在cmdbServerRecords的第一个位置插入数据
	cmdbServerRecords = append([]dto.CmsCmdbRecord{
		{
			CrmID:       "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Service:     "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Level:       "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			MainOwner:   "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Owner:       "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Category:    "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Status:      "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Departments: "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
			Info:        "提示：此配置内容同步自CRM对象，修改请前往CRM: https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c",
		},
	}, cmdbServerRecords...)
	return cmdbServerRecords, nil
}
