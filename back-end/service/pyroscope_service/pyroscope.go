package pyroscope_service

import (
	"bytes"
	"fmt"
	"fs-k8s-app-manager/pkg/client/cms"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/service/k8s_service"
	"regexp"
	"strings"

	log "github.com/sirupsen/logrus"

	"gopkg.in/ini.v1"
)

const (
	cmsConfigName         = "fs-eye-profiling"
	pyroscopeOpenKey      = "pyroscope_open"
	scopeNamespaceListKey = "scope_namespace_list"
	scopePodListKey       = "scope_pod_list"
)

type PyroscopeConfig struct {
	Profile string
	Config  string
}

// OpenPyroscope enables Pyroscope profiling for a specific pod
func OpenPyroscope(cluster, namespace, pod string) (configFile string, err error) {
	app := k8s_util.GetAppName(pod)

	podObj, err := k8s_service.PodDetail(cluster, namespace, pod)
	if err != nil {
		return "", err
	}

	profiles := []string{namespace}

	javaOpts := ""
	for _, env := range podObj.Spec.Containers[0].Env {
		if env.Name == "JAVA_OPTS" {
			javaOpts = env.Value
			break
		}
	}
	profileCandidates := getProfileCandidates(javaOpts)
	if len(profileCandidates) > 0 {
		profiles = append(profiles, profileCandidates...)
	}

	pyroConfig, err := findValidPyroscopeConfig(profiles)
	if err != nil {
		return "", err
	}
	if strings.Contains(pyroConfig.Config, pod) {
		return "", fmt.Errorf("pod %s 已经开启了持续性能分析，Config file: %s/%s",
			pod, pyroConfig.Profile, cmsConfigName)
	}

	// Update configuration
	updatedConfig, err := updateConfiguration(pyroConfig, app, namespace, pod)
	if err != nil {
		return "", err
	}

	// Save updated configuration
	if _, err := cms.UpdateConfig(pyroConfig.Profile, cmsConfigName, updatedConfig); err != nil {
		return "", fmt.Errorf("failed to update %s/%s config fail: %w", pyroConfig.Profile, cmsConfigName, err)
	}

	return fmt.Sprintf("%s/%s", pyroConfig.Profile, cmsConfigName), nil
}

// getProfileCandidates extracts profile candidates from pipeline environment
func getProfileCandidates(javaOpts string) []string {
	profileCandidates := make([]string, 0, 3)
	if javaOpts != "" {
		re := regexp.MustCompile(`-Dprocess\.profile\.candidates=([^\s]+)`)
		matches := re.FindStringSubmatch(javaOpts)
		if len(matches) == 2 {
			for _, candidate := range strings.Split(matches[1], ",") {
				if candidate != "" {
					profileCandidates = append(profileCandidates, candidate)
				}
			}
		}
	}
	return profileCandidates
}

// findValidPyroscopeConfig finds the first valid Pyroscope configuration from candidates
func findValidPyroscopeConfig(candidates []string) (*PyroscopeConfig, error) {
	for _, profile := range candidates {
		config, err := cms.GetConfig(profile, cmsConfigName)
		if err != nil {
			log.Warnf("get cms config fail for profile %s: %s", profile, err.Error())
			continue
		}
		if config != "" {
			return &PyroscopeConfig{Profile: profile, Config: config}, nil
		}
	}
	return nil, fmt.Errorf("找不到需要修改的配置文件，查找环境：%v", candidates)
}

// updateConfiguration updates the Pyroscope configuration for a specific app
func updateConfiguration(pyroConfig *PyroscopeConfig, app, namespace, pod string) (string, error) {
	ini.PrettyFormat = false
	ini.PrettyEqual = false

	cfg, err := ini.Load([]byte(pyroConfig.Config))
	if err != nil {
		return "", fmt.Errorf("failed to load config: %w", err)
	}

	appSection := cfg.Section(app)
	if appSection == nil {
		appSection, err = cfg.NewSection(app)
		if err != nil {
			return "", fmt.Errorf("failed to create new section for app %s: %w", app, err)
		}
	}
	updateAppSection(appSection, namespace, pod)

	var buf bytes.Buffer
	if _, err := cfg.WriteTo(&buf); err != nil {
		return "", fmt.Errorf("failed to write config: %w", err)
	}

	return buf.String(), nil
}

// updateAppSection updates the application section in the configuration
func updateAppSection(section *ini.Section, namespace, pod string) {
	if !section.HasKey(pyroscopeOpenKey) {
		_, _ = section.NewKey(pyroscopeOpenKey, "true")
		_, _ = section.NewKey(scopeNamespaceListKey, namespace)
		_, _ = section.NewKey(scopePodListKey, pod)
		return
	}

	section.Key(pyroscopeOpenKey).SetValue("true")

	updateScopeList(section, scopeNamespaceListKey, namespace)
	updateScopeList(section, scopePodListKey, pod)
}

// updateScopeList updates a scope list in the configuration
func updateScopeList(section *ini.Section, key, value string) {
	currentValue := section.Key(key).Value()
	if currentValue == "" {
		section.Key(key).SetValue(value)
		return
	}

	values := strings.Split(currentValue, ",")
	for _, v := range values {
		if v == value {
			return
		}
	}
	section.Key(key).SetValue(currentValue + "," + value)
}
