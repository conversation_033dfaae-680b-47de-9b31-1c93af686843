package app_service

import (
	"fs-k8s-app-manager/pkg/client/gitlab"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/math"
	"fs-k8s-app-manager/service/pipeline_service"
	"regexp"
	"strconv"
	"strings"
)

func FindGitUrlAndTagByApp(app string) ([]dto.GitTagProperty, error) {
	var data []dto.GitTagProperty
	pipelines, err := pipeline_service.FindByApp(app)
	if err != nil {
		return nil, err
	}
	gitUrlMap := make(map[string]dto.GitTagProperty)
	for _, p := range pipelines {
		for _, m := range p.AppModules {
			if _, ok := gitUrlMap[m.GitUrl]; !ok {
				branches, err := gitlab.SearchBranch(m.GitUrl, 100, nil)
				if err != nil {
					return nil, err
				}
				tags, err := gitlab.SearchTag(m.GitUrl, 200, nil)
				if err != nil {
					return nil, err
				}
				branchDef := "master"
				for _, it := range branches {
					if it.Name == "main" {
						branchDef = "main"
						break
					}
				}
				gitUrlMap[m.GitUrl] = dto.GitTagProperty{
					GitURL:   m.GitUrl,
					Branches: branches,
					Tags:     tags,
					Branch:   branchDef,
					NextTag:  getNextTag(tags),
				}
			}
		}
	}

	for _, v := range gitUrlMap {
		data = append(data, v)
	}
	return data, nil
}

func getNextTag(tags []*dto.GitTag) string {
	reg := regexp.MustCompile(`(\d+)\.(\d+)\.(\d+)`)
	strList := make([]string, 0)
	for i := 0; i < math.Min(len(tags), 5); i++ {
		if !reg.MatchString(tags[i].Name) {
			continue
		}
		strList = reg.FindStringSubmatch(tags[i].Name)
		break
	}
	if len(strList) != 4 {
		return ""
	}
	nums := strList[1:]
	num3, _ := strconv.Atoi(nums[2])
	nums[2] = strconv.Itoa(num3 + 1)
	return "v" + strings.Join(nums, ".")
}

func CreateGitTag(t dto.CreateGitTag) error {
	return gitlab.CreateTag(t.GitURL, t.Tag, t.Branch, t.Msg)
}

func BatchDeleteTagAndBranch(data dto.BranchDeleteGitTagAndBranch) error {
	for _, v := range data.Branches {
		// TODO 并发删除
		if err := gitlab.DeleteBranch(data.GitURL, v); err != nil {
			return err
		}
	}

	for _, v := range data.Tags {
		// TODO 并发删除
		if err := gitlab.DeleteTag(data.GitURL, v); err != nil {
			return err
		}
	}
	return nil
}

func FindTagByGitUrl(gitUrl, searchName string) (dto.GitTagProperty, error) {
	var data dto.GitTagProperty
	var err error
	data.Tags, err = gitlab.SearchTag(gitUrl, 2000, &searchName)

	if err != nil {
		return dto.GitTagProperty{}, err
	}
	data.Branches, err = gitlab.SearchBranch(gitUrl, 2000, &searchName)
	if err != nil {
		return dto.GitTagProperty{}, err
	}
	// 去除master分支
	//for i, v := range data.Branches {
	//	if v.Name == "master" {
	//		data.Branches = append(data.Branches[0:i], data.Branches[i+1:]...)
	//		break
	//	}
	//}
	return data, err
}
