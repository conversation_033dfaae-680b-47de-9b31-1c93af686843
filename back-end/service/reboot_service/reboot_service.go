package reboot_service

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
	"time"
)

func SaveOrUpdate(entity models.Reboot) (err error) {
	if entity.ID == 0 {
		return models.DB().Create(&entity).Error
	} else {
		entity.UpdatedAt = time.Now()
		return models.DB().Omit("created_at").Save(&entity).Error
	}
}

func FindById(id uint) (ret models.Reboot, err error) {
	err = models.DB().Where(models.Reboot{Model: gorm.Model{ID: id}}).Take(&ret).Error
	return
}

func FirstInEnv(cluster, namespace, app string) (ret models.Reboot, err error) {
	err = models.DB().Where(models.Reboot{
		Cluster:   cluster,
		Namespace: namespace,
		App:       app,
	}).First(&ret).Error
	return
}

func DeleteById(id uint) error {
	return models.DB().Where("id = ?", id).Unscoped().Delete(&models.Reboot{}).Error
}

func Search(keyword string) (ret []models.Reboot, err error) {
	db := models.DB().Model(&models.Reboot{})
	if keyword != "" {
		db.Where(fmt.Sprintf("(cluster LIKE '%%%s%%' or namespace LIKE '%%%s%%' OR app LIKE '%%%s%%')",
			keyword, keyword, keyword))
	}
	err = db.Order("id desc").Find(&ret).Error
	return
}
