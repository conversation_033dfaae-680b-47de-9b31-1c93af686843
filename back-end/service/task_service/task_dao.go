package task_service

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"time"
)

func Create(entity *models.Task) (err error) {
	return models.DB().Create(entity).Error
}

func FindById(id uint) (ret models.Task, err error) {
	err = models.DB().Where(models.Task{BaseModel: models.BaseModel{ID: id}}).Take(&ret).Error
	return
}

func FindByJob(jobId uint) (ret []models.Task, err error) {
	err = models.DB().Order("id").Where(models.Task{JobID: jobId}).Find(&ret).Error
	return
}

func UpdateStatus(id uint, status models.TaskPhase) error {
	return models.DB().Model(&models.Task{BaseModel: models.BaseModel{ID: id}}).
		Update("status", status).Error
}
func UpdateStartTime(id uint, startTime time.Time) error {
	return models.DB().Model(&models.Task{BaseModel: models.BaseModel{ID: id}}).
		Update("start_time", startTime).Error
}

func UpdateEndTime(id uint, endTime time.Time) error {
	return models.DB().Model(&models.Task{BaseModel: models.BaseModel{ID: id}}).
		Update("end_time", endTime).Error
}

func UpdateOutput(id uint, output string) error {
	return models.DB().Model(&models.Task{BaseModel: models.BaseModel{ID: id}}).
		Update("output", output).Error
}
func AppendOutput(id uint, output string) error {
	entity, err := FindById(id)
	if err != nil {
		return nil
	}
	var v string
	if len(entity.Output) > 0 {
		v = fmt.Sprintf("%s\n%s", entity.Output, output)
	} else {
		v = output
	}
	return UpdateOutput(id, v)
}
func AppendAttribute(id uint, key string, value interface{}) error {
	entity, err := FindById(id)
	if err != nil {
		return nil
	}
	v := entity.Attributes
	if v == nil {
		v = make(map[string]interface{})
	}
	v[key] = value
	return models.DB().Model(&models.Task{}).Where("id = ?", id).Update("attributes", v).Error
}

func SearchAfterTime(typee string, status []string, startTime time.Time) (ret []models.Task, err error) {
	db := models.DB().Model(&models.Task{})
	if typee != "" {
		db.Where("type = ?", typee)
	}
	if len(status) > 0 {
		db.Where("status IN ?", status)
	}
	db.Where("created_at > ?", startTime)
	err = db.Order("id desc").Find(&ret).Error
	return
} //func LastByType(typee models.TaskType, params map[string]interface{}) (ret models.Task, err error) {
//	db := models.DB().Model(&models.Task{})
//	if typee != "" {
//		db.Where("type = ?", typee)
//	}
//	if len(params) > 0 {
//		for k, v := range params {
//			switch v := v.(type) {
//			case string:
//				db = db.Where("params->>'"+k+"' = ?", v)
//			case int, int32, int64, uint, uint32, uint64:
//				db = db.Where("CAST(params->>'"+k+"' AS INTEGER) = ?", v)
//			case float32, float64:
//				db = db.Where("CAST(params->>'"+k+"' AS DOUBLE PRECISION) = ?", v)
//			case bool:
//				db = db.Where("CAST(params->>'"+k+"' AS BOOLEAN) = ?", v)
//			default:
//				err = errors.New(fmt.Sprintf("params query not support type: %v", v))
//				return
//			}
//			db.Where("params->>'"+k+"' = ?", v)
//		}
//	}
//	err = db.Order("id desc").Find(&ret).Error
//	return
//}
