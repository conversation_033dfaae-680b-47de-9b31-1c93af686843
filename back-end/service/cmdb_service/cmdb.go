package cmdb_service

import (
	"encoding/json"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/cms"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/crm_service"
	log "github.com/sirupsen/logrus"
	"strings"
)

var cacheKey = "service-owners"

func ClearServiceOwnersCache() {
	cache.Delete(key.Pre().CMDB.Key(cacheKey))
}

func GetDataFromCms() ([]dto.CmsCmdbRecord, error) {
	configPaths := strings.Split(config.GetConfig().CMS.CmdbConfigPath, "/")
	content, err := cms.GetConfig(configPaths[0], configPaths[1])
	if err != nil {
		return nil, err
	}

	var ret []dto.CmsCmdbRecord
	err = json.Unmarshal([]byte(content), &ret)
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func SyncCmdbFromCRM() {
	configPaths := strings.Split(config.GetConfig().CMS.CmdbConfigPath, "/")
	if len(configPaths) != 2 || configPaths[0] == "" || configPaths[1] == "" {
		log.Errorf("Invalid cmdb config path: %s", config.GetConfig().CMS.CmdbConfigPath)
	}
	cmsProfile, cmsConfig := configPaths[0], configPaths[1]
	cmdbServerRecords, err := crm_service.BuildCmsCmdbRecords()
	if err != nil {
		log.Errorf("%v", err)
		return
	}
	jsonData, err := json.MarshalIndent(cmdbServerRecords, "", "  ")
	if err != nil {
		log.Errorf("Failed to marshal cmdb data: %v", err)
		return
	}
	_, err = cms.UpdateConfig(cmsProfile, cmsConfig, string(jsonData))
	if err != nil {
		log.Errorf("Failed to update cmdb data: %v", err)
		return
	}
}
