package event_service

import (
	"fmt"
	"fs-k8s-app-manager/models"
	log "github.com/sirupsen/logrus"
)

func BuildAppKey(cluster, namespace, app string) string {
	return fmt.Sprintf("%s@%s@%s", cluster, namespace, app)
}
func Create(author, key, content string) {
	CreateWithLevel(author, key, content, 0)
}
func CreateBySys(key, content string) {
	Create("system", key, content)
}
func CreateWithLevel(author, key, content string, level int) {
	if err := Save(&models.Event{
		Author:  author,
		Key:     key,
		Level:   level,
		Content: content,
	}); err != nil {
		log.Warn("event save db fail ", err)
	}
}

func Save(entity *models.Event) (err error) {
	return models.DB().Create(entity).Error
}

func Query(startId, limit int, key string) (ret []models.Event, err error) {
	db := models.DB().Model(&models.Event{})
	//conditions := make([]string, 0, 20)
	//if key != "" {
	//
	//}
	//	cond := fmt.Sprintf(" (author LIKE '%%%s%%' OR operate LIKE '%%%s%%' or target LIKE '%%%s%%' OR content LIKE '%%%s%%') ", key, key, key, key)
	//	conditions = append(conditions, cond)
	//if len(conditions) > 0 {
	//	db.Where(strings.Join(conditions, " AND "))
	//}

	if key != "" {
		db.Where("key = ?", key)
	}
	if startId > 0 {
		db.Where("id < ?", startId)
	}
	err = db.Limit(limit).Order("id desc").Find(&ret).Error
	return
}
