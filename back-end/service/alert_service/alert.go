package alert_service

import (
	"fs-k8s-app-manager/models"
	log "github.com/sirupsen/logrus"
	"time"
)

func Create(namespace, app, pod, users, content string) {
	if err := Save(&models.Alert{
		App:       app,
		Namespace: namespace,
		Pod:       pod,
		Content:   content,
		Users:     users,
	}); err != nil {
		log.Warn("alert save db fail ", err)
	}
}

func Save(entity *models.Alert) (err error) {
	return models.DB().Create(entity).Error
}

func DeleteBeforeTime(time time.Time) error {
	return models.DB().Unscoped().Where("created_at < ?", time).Delete(&models.Alert{}).Error
}
