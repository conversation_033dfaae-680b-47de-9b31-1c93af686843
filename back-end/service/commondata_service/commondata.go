package commondata_service

import (
	"errors"
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

func Create(owner, dataType, content string, columns ...string) error {
	entity := &models.CommonData{
		Owner:    owner,
		DataType: dataType,
		Content:  content,
	}
	for idx, v := range columns {
		order := idx + 1
		if order == 1 {
			entity.Column01 = v
		} else if order == 2 {
			entity.Column02 = v
		} else if order == 3 {
			entity.Column03 = v
		} else if order == 4 {
			entity.Column04 = v
		} else if order == 5 {
			entity.Column05 = v
		} else {
			return errors.New("columns length should be less than 6")
		}
	}
	return models.DB().Create(entity).Error
}

func Search(dataType models.CommonDataType, page, limit int, columns ...string) (ret []models.CommonData, err error) {
	db := models.DB().Model(&models.CommonData{})
	db.Where("data_type = ?", dataType)
	for idx, v := range columns {
		order := idx + 1
		if order == 1 {
			db.Where("column01 = ?", v)
		} else if order == 2 {
			db.Where("column02 = ?", v)
		} else if order == 3 {
			db.Where("column03 = ?", v)
		} else if order == 4 {
			db.Where("column04 = ?", v)
		} else if order == 5 {
			db.Where("column05 = ?", v)
		} else {
			return nil, errors.New("columns length should be less than 6")
		}
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}

func FindById(id uint) (ret models.CommonData, err error) {
	err = models.DB().Where(models.CommonData{Model: gorm.Model{ID: id}}).Take(&ret).Error
	return
}
func DeleteById(id uint) error {
	return models.DB().Delete(&models.CommonData{}, id).Error
}
