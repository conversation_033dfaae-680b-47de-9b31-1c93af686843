package gitlab_service

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/util/file"
	"fs-k8s-app-manager/pkg/util/template"
	"fs-k8s-app-manager/pkg/util/zip"
	"os"
	"path/filepath"
	"time"
)

const timeFormat = "20060102150405"

type PortParam struct {
	Name string `json:"name"`
	Port int16  `json:"port"`
}

type DeployEnvParam struct {
	App               string
	RuntimeEnv        string
	Cluster           string
	Namespace         string
	Replicas          int16
	IngressParentHost string
	Language          string
	RequestCPU        string
	RequestMemory     string
	LimitCPU          string
	LimitMemory       string
	IsOnline          bool
}

func CreateCIFile(deployEnvList []DeployEnvParam, ports []PortParam) (zipFilePath string, err error) {
	entries := make([]zip.FileEntry, 0)
	app := deployEnvList[0].App

	//创建好需要的目录
	nowFormat := time.Now().Format(timeFormat)
	workDir := filepath.Join(config.GetConfig().App.DownloadDir, "gitlab_ci", nowFormat)
	err = os.MkdirAll(workDir, os.ModePerm)
	if err != nil {
		return
	}
	err = os.MkdirAll(filepath.Join(workDir, "k8s-manifest", "templates"), os.ModePerm)
	if err != nil {
		return
	}
	err = os.MkdirAll(filepath.Join(workDir, "k8s-manifest", "config"), os.ModePerm)
	if err != nil {
		return
	}

	file := ".gitlab-ci.yml"
	content, err := createGitlabCiFile(filepath.Join(workDir, file), app, deployEnvList)
	if err != nil {
		return
	}
	entries = append(entries, zip.FileEntry{Name: file, Body: content})

	file = "k8s-manifest/templates/deployment.yml"
	content, err = createDeploymentFile(filepath.Join(workDir, file), ports)
	if err != nil {
		return
	}
	entries = append(entries, zip.FileEntry{Name: file, Body: content})

	file = "k8s-manifest/templates/service.yml"
	content, err = createServiceFile(filepath.Join(workDir, file), ports)
	if err != nil {
		return
	}
	entries = append(entries, zip.FileEntry{Name: file, Body: content})

	file = "k8s-manifest/templates/ingress.yml"
	content, err = createIngressFile(filepath.Join(workDir, file))
	if err != nil {
		return
	}
	entries = append(entries, zip.FileEntry{Name: file, Body: content})

	for _, item := range deployEnvList {
		file = filepath.Join("k8s-manifest/config", fmt.Sprintf("configmap-%s.yml", item.Namespace))
		content, err = createConfigMapFile(filepath.Join(workDir, file))
		if err != nil {
			return
		}
		entries = append(entries, zip.FileEntry{Name: file, Body: content})
	}

	zipFilePath = filepath.Join(workDir, fmt.Sprintf("%s-gitlab-ci-%s.zip", app, nowFormat))
	err = zip.ZipArchive(zipFilePath, entries)
	if err != nil {
		return "", err
	}
	return
}

func createGitlabCiFile(filepath, app string, deployEnvList []DeployEnvParam) (content string, err error) {
	data := make(map[string]interface{})
	data["DeployEnvList"] = deployEnvList
	data["App"] = app

	content, err = template.ParseTemplateFile("gitlab-templates/gitlab-ci.yml", data)
	if err != nil {
		return
	}
	err = file.Write(filepath, content)
	return
}

func createConfigMapFile(filepath string) (content string, err error) {
	content, err = template.ParseTemplateFile("gitlab-templates/k8s-manifest/config/configmap.yml", nil)
	if err != nil {
		return
	}
	err = file.Write(filepath, content)
	return
}

func createDeploymentFile(filepath string, ports []PortParam) (content string, err error) {
	data := make(map[string]interface{})
	data["Ports"] = ports
	content, err = template.ParseTemplateFile("gitlab-templates/k8s-manifest/templates/deployment.yml", data)
	if err != nil {
		return
	}
	err = file.Write(filepath, content)
	return
}

func createServiceFile(filepath string, ports []PortParam) (content string, err error) {
	data := make(map[string]interface{})
	data["Ports"] = ports
	content, err = template.ParseTemplateFile("gitlab-templates/k8s-manifest/templates/service.yml", data)
	if err != nil {
		return
	}
	err = file.Write(filepath, content)
	return
}

func createIngressFile(filepath string) (content string, err error) {
	content, err = template.ParseTemplateFile("gitlab-templates/k8s-manifest/templates/ingress.yml", nil)
	if err != nil {
		return
	}
	err = file.Write(filepath, content)
	return
}
