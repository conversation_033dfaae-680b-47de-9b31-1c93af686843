package artifact_image_service

import (
	"fs-k8s-app-manager/models"
	"time"
)

func FindByImage(image string) (ret models.ArtifactImage, err error) {
	err = models.DB().Where("image = ?", image).Last(&ret).Error
	return
}

func Create(entity *models.ArtifactImage) (err error) {
	return models.DB().Create(entity).Error
}

func DeleteBeforeTime(time time.Time) error {
	return models.DB().Unscoped().Where("created_at < ?", time).Delete(&models.ArtifactImage{}).Error
}
