package artifact_service

import (
	"fs-k8s-app-manager/models"
)

func FindAll() (ret []models.Artifact, err error) {
	err = models.DB().Find(&ret).Error
	return
}
func Search(keyword string, page, limit int) (ret []models.Artifact, err error) {
	db := models.DB().Model(&models.Artifact{})
	if keyword != "" {
		db.Where("git_url LIKE ?", "%"+keyword+"%").
			Or("module LIKE ?", "%"+keyword+"%")
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}
func Count(keyword string) int64 {
	var count int64
	db := models.DB().Model(&models.Artifact{})
	if keyword != "" {
		db.Where("git_url LIKE ?", "%"+keyword+"%").
			Or("module LIKE ?", "%"+keyword+"%")
	}
	db.Count(&count)
	return count
}
func FindById(id uint) (ret models.Artifact, err error) {
	err = models.DB().Where("id = ?", id).Take(&ret).Error
	return
}

func FindByUrlAndModule(gitUrl, module string) (ret models.Artifact, err error) {
	err = models.DB().Where(models.Artifact{
		GitUrl: gitUrl,
		Module: module,
	}).Take(&ret).Error
	return
}
func DeleteById(id uint) error {
	return models.DB().Unscoped().Where("id = ?", id).Delete(&models.Artifact{}).Error
}

func Create(entity *models.Artifact) (err error) {
	return models.DB().Create(entity).Error
}

func Update(entity *models.Artifact) (err error) {
	return models.DB().Save(entity).Error
}
