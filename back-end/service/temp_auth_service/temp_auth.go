package temp_auth_service

import (
	"fs-k8s-app-manager/models"
)

func Create(entity *models.TempAuth) (err error) {
	return models.DB().Create(entity).Error
}

func Search(keyword string, approved *bool, page, limit int) (ret []models.TempAuth, err error) {
	db := models.DB().Model(&models.TempAuth{})
	if keyword != "" {
		db.Where("user LIKE ?", "%"+keyword+"%").
			Or("authorizer LIKE ?", "%"+keyword+"%")
	}
	if approved != nil {
		db.Where("approved = ?", *approved)
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}

func Count(keyword string) int64 {
	var count int64
	db := models.DB().Model(&models.TempAuth{})
	if keyword != "" {
		db.Where("user LIKE ?", "%"+keyword+"%").
			Or("authorizer LIKE ?", "%"+keyword+"%")
	}
	db.Count(&count)
	return count
}

func Update(entity *models.TempAuth) (err error) {
	return models.DB().Save(entity).Error
}

func DeleteById(id uint) error {
	return models.DB().Unscoped().Where("id = ?", id).Delete(&models.TempAuth{}).Error
}

func FindByAppAndUser(app, user string) (ret models.TempAuth, err error) {
	err = models.DB().Where(models.TempAuth{App: app, User: user}).Order("id desc").Take(&ret).Error
	return
}
func FindByAppAndUserAndOperate(app, user, operate string) (ret models.TempAuth, err error) {
	err = models.DB().Where(models.TempAuth{App: app, User: user, Operate: operate}).Order("id desc").Take(&ret).Error
	return
}

func FindById(id uint) (ret models.TempAuth, err error) {
	err = models.DB().Where("id = ?", id).Take(&ret).Error
	return
}
