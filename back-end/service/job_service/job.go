package job_service

import (
	"fs-k8s-app-manager/models"
	"time"
)

func Create(entity *models.Job) (err error) {
	return models.DB().Create(entity).Error
}

func FindById(id uint) (ret models.Job, err error) {
	err = models.DB().Where(models.Job{BaseModel: models.BaseModel{ID: id}}).Take(&ret).Error
	return
}

func LastByApp(app string, typee models.JobType) (ret models.Job, err error) {
	db := models.DB().Model(&models.Job{})
	if typee != "" {
		db.Where("type = ?", typee)
	}
	err = db.Where(models.Job{App: app}).Last(&ret).Error
	return
}

func LastByPipelineId(pipelineId uint, typee models.JobType) (ret models.Job, err error) {
	db := models.DB().Model(&models.Job{})
	if typee != "" {
		db.Where("type = ?", typee)
	}
	err = db.Where(models.Job{PipelineId: pipelineId}).Last(&ret).Error
	return
}

func FindOnPage(page, limit int) (ret []models.Job, err error) {
	err = models.DB().Model(&models.Job{}).Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}
func DeleteById(id uint) error {
	return models.DB().Delete(&models.Job{}, id).Error
}

func UpdateStatus(id uint, status models.JobPhase) error {
	return models.DB().Model(&models.Job{BaseModel: models.BaseModel{ID: id}}).Update("status", status).Error
}

func UpdateStartTime(id uint, startTime time.Time) error {
	return models.DB().Model(&models.Job{BaseModel: models.BaseModel{ID: id}}).
		Update("start_time", startTime).Error
}

func LastByType(typee models.JobType, params map[string]string) (ret models.Job, err error) {
	db := models.DB().Model(&models.Job{})
	if typee != "" {
		db.Where("type = ?", typee)
	}
	if len(params) > 0 {
		for k, v := range params {
			if len(v) == 0 {
				continue
			}
			db.Where("params->>'"+k+"' = ?", v)
		}
	}
	err = db.Order("id desc").Find(&ret).Error
	return
}

func UpdateEndTime(id uint, endTime time.Time) error {
	return models.DB().Model(&models.Job{BaseModel: models.BaseModel{ID: id}}).
		Update("end_time", endTime).Error
}
func Search(typee string, status []string, app, author string, params map[string]string, page, limit int) (count int64, ret []models.Job, err error) {
	db := models.DB().Model(&models.Job{})
	if typee != "" {
		db.Where("type = ?", typee)
	}
	if len(status) > 0 {
		db.Where("status IN ?", status)
	}
	if len(author) > 0 {
		db.Where("author = ?", author)
	}
	if len(app) > 0 {
		db.Where("app = ?", app)
	}
	if len(params) > 0 {
		for k, v := range params {
			if len(v) == 0 {
				continue
			}
			db.Where("params->>'"+k+"' = ?", v)
		}
	}
	db.Count(&count)
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}

//func LastInEnv(cluster, namespace, app string) (ret models.Job, err error) {
//	err = models.DB().Where(models.Job{
//		Cluster:   cluster,
//		Namespace: namespace,
//		App:       app,
//	}).Last(&ret).Error
//	return
//}
//
//func LastSuccessInEnv(cluster, namespace, app string) (ret models.Job, err error) {
//	err = models.DB().Where(models.Job{
//		Cluster:   cluster,
//		Namespace: namespace,
//		App:       app,
//		Status:    constant.Job_STATUS_SUCCESS,
//	}).Last(&ret).Error
//	return
//}
