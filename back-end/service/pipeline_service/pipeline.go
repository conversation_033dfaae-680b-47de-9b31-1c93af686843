package pipeline_service

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

func filterByValidCluster(pipes []models.Pipeline) []models.Pipeline {
	ret := make([]models.Pipeline, 0, len(pipes))
	if pipes != nil {
		for _, it := range pipes {
			if v := config.GetSetting().GetCluster(it.Cluster); v != nil && v.Enable {
				ret = append(ret, it)
			}
		}
	}
	return ret
}

func clusterIsValid(cluster string) bool {
	v := config.GetSetting().GetCluster(cluster)
	return v != nil && v.Enable
}

func FindByApp(app string) (ret []models.Pipeline, err error) {
	err = models.DB().Where(models.Pipeline{App: app}).Find(&ret).Error
	if err == nil {
		ret = filterByValidCluster(ret)
	}
	return
}

//func FindByApps(apps ...string) (ret []models.Pipeline, err error) {
//	err = models.DB().Where("app in ?", apps).Find(&ret).Error
//	if err == nil {
//		ret = filterByValidCluster(ret)
//	}
//	return
//}

func FindAll() (ret []models.Pipeline, err error) {
	err = models.DB().Find(&ret).Error
	if err == nil {
		ret = filterByValidCluster(ret)
	}
	return
}

func FindByStatus(status string) (ret []models.Pipeline, err error) {
	err = models.DB().Where("status = ?", status).Find(&ret).Error
	if err == nil {
		ret = filterByValidCluster(ret)
	}
	return
}

func FindById(id uint) (ret models.Pipeline, err error) {
	err = models.DB().Where(models.Pipeline{Model: gorm.Model{ID: id}}).Take(&ret).Error
	if err == nil && !clusterIsValid(ret.Cluster) {
		err = fmt.Errorf("cluster is invalid")
	}
	return
}
func FindByIds(ids ...uint) (ret []models.Pipeline, err error) {
	err = models.DB().Where(ids).Find(&ret).Error
	if err == nil {
		ret = filterByValidCluster(ret)
	}
	return
}

func SaveOrUpdate(entity *models.Pipeline) (err error) {
	if entity.ID == 0 {
		return models.DB().Create(entity).Error
	} else {
		return models.DB().Omit("created_at").Save(entity).Error
	}
}
func ExistInEnv(cluster, namespace, app string) bool {
	return models.DB().Where(models.Pipeline{
		Cluster:   cluster,
		Namespace: namespace,
		App:       app,
	}).First(&models.Pipeline{}).Error == nil
}
func FirstInEnv(cluster, namespace, app string) (ret models.Pipeline, err error) {
	err = models.DB().Where(models.Pipeline{
		Cluster:   cluster,
		Namespace: namespace,
		App:       app,
	}).First(&ret).Error
	return
}

func FindByCluster(cluster string) (ret []models.Pipeline, err error) {
	err = models.DB().Where(models.Pipeline{Cluster: cluster}).Find(&ret).Error
	return
}

func DeleteById(id uint) error {
	return models.DB().Where("id = ?", id).Delete(&models.Pipeline{}).Error
}

func FindByClusterAndNamespace(cluster, namespace string) (ret []models.Pipeline, err error) {
	err = models.DB().Where("cluster = ? AND namespace = ?", cluster, namespace).Find(&ret).Error
	return
}

func Search(cluster, namespace, keyword string, page, limit int) (count int64, ret []models.Pipeline, err error) {
	db := models.DB().Model(&models.Pipeline{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if keyword != "" {
		db.Where("app LIKE ? or base_image LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	db.Count(&count)
	err = db.Offset((page - 1) * limit).Limit(limit).Order("app asc").Find(&ret).Error
	return
}

func Search2(cluster, namespace, app, image, javaOpts, status string, page, limit int) (count int64, ret []models.Pipeline, err error) {
	db := models.DB().Model(&models.Pipeline{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if app != "" {
		db.Where("app LIKE ?", "%"+app+"%")
	}
	if image != "" {
		db.Where("base_image LIKE ?", "%"+image+"%")
	}
	if javaOpts != "" {
		db.Where("envs LIKE ?", "%"+javaOpts+"%")
	}
	if status != "" {
		db.Where("status = ?", status)
	}
	db.Count(&count)
	err = db.Offset((page - 1) * limit).Limit(limit).Order("app asc").Find(&ret).Error
	return
}

func UpdateStatus(id uint, status string) error {
	return models.DB().Model(&models.Pipeline{}).Where("id = ?", id).Update("status", status).Error
}
