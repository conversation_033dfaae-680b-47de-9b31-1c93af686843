package k8s_service

import (
	"fmt"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/dto"
	log "github.com/sirupsen/logrus"
	"time"
)

func GetResourcePool(cluster, node string) string {
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("nodeDtoMap@@%s", cluster))
	if data, found := cache.GetStruct(cacheKey, map[string]dto.Node{}); found {
		return data[node].DedicatedName
	}
	nodeMap, err := GetNodeMap(cluster)
	if err != nil {
		log.Warn(err)
		return ""
	}
	if err := cache.SetStruct(cacheKey, nodeMap, 8*time.Hour); err != nil {
		log.Warn(err.Error())
	}
	return nodeMap[node].DedicatedName
}

func GetNodeMap(cluster string) (map[string]dto.Node, error) {
	nodeDTOs, err := ListNode(cluster)
	if err != nil {
		return nil, err
	}
	f := func(items []dto.Node) map[string]dto.Node {
		m := make(map[string]dto.Node)
		for _, it := range items {
			m[it.Name] = it
		}
		return m
	}
	return f(nodeDTOs), nil
}
