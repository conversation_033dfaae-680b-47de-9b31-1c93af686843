package k8s_service

import (
	"fmt"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"time"
)

func ThreadDump(cluster, namespace, pod string, withNativeMethod bool) (filePath string, err error) {
	filePath = "/opt/tomcat/logs/jstack-" + time.Now().Format("20060102150405") + ".txt"
	params := ""
	if withNativeMethod {
		filePath = "/opt/tomcat/logs/jstack-with-native-method-" + time.Now().Format("20060102150405") + ".txt"
		params = "-m"
	}
	cmd := fmt.Sprintf("jstack %s $(pgrep java) > %s 2>&1", params, filePath)
	_, err = kubectl.PodCmd(cluster, namespace, pod, cmd, 20*time.Second)
	return
}
func ThreadDumpByGstack(cluster, namespace, pod string) (filePath string, err error) {
	filePath = "/opt/tomcat/logs/gstack-" + time.Now().Format("20060102150405") + ".txt"
	cmd := fmt.Sprintf("gstack $(pgrep java) > %s 2>&1", filePath)
	_, err = kubectl.PodCmd(cluster, namespace, pod, cmd, 20*time.Second)
	return
}
