package k8s_service

import (
	"encoding/json"
	"fs-k8s-app-manager/pkg/client/kubecap"
	"github.com/prometheus/common/log"

	"strconv"
	"strings"
)

func parseCPU(val string) (int64, error) {
	if strings.HasSuffix(strings.To<PERSON><PERSON>er(val), "m") {
		val = val[:len(val)-1]
	}
	return strconv.ParseInt(val, 10, 64)
}
func parseMemory(val string) (ret int64, err error) {
	if strings.HasSuffix(strings.ToLower(val), "gi") {
		val = val[:len(val)-2]
		ret, err = strconv.ParseInt(val, 10, 64)
		ret = ret * 1024
	} else if strings.HasSuffix(strings.ToLower(val), "mi") {
		val = val[:len(val)-2]
		ret, err = strconv.ParseInt(val, 10, 64)
	} else {
		ret, err = strconv.ParseInt(val, 10, 64)
	}
	return
}

func GetClusterCapacity(cluster string, dedicatedName string) (ret ClusterCapacity, err error) {
	out, err := kubecap.Node(cluster, dedicatedName)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(out), &ret)
	if err != nil {
		log.Warn(err)
		return
	}

	nodeMap, err := GetNodeMap(cluster)
	if err != nil {
		log.Warn(err)
		return
	}
	for idx, _ := range ret.Nodes {
		it := &ret.Nodes[idx]
		if v, found := nodeMap[it.Name]; found {
			it.Schedulable = v.Schedulable
			it.DedicatedName = v.DedicatedName
			it.CPUCapacity = v.CPUCapacity
			it.MemoryCapacity = v.MemoryCapacity
			it.CPULimit, _ = parseCPU(it.CPU.Limits)
			it.CPURequire, _ = parseCPU(it.CPU.Requests)
			it.MemoryLimit, _ = parseMemory(it.Memory.Limits)
			it.MemoryRequire, _ = parseMemory(it.Memory.Requests)
		}
	}
	return
}

type Resource struct {
	Limits             string `json:"limits"`
	LimitsPercent      string `json:"limitsPercent"`
	Requests           string `json:"requests"`
	RequestsPercent    string `json:"requestsPercent"`
	Utilization        string `json:"utilization"`
	UtilizationPercent string `json:"utilizationPercent"`
}

type ClusterTotals struct {
	CPU    Resource `json:"cpu"`
	Memory Resource `json:"memory"`
}

type Node struct {
	Name           string   `json:"name"`
	Schedulable    bool     `json:"schedulable"`
	DedicatedName  string   `json:"dedicatedName"`
	CPU            Resource `json:"cpu"`
	Memory         Resource `json:"memory"`
	CPUCapacity    int64    `json:"cpuCapacity"`
	MemoryCapacity int64    `json:"memoryCapacity"`
	CPULimit       int64    `json:"cpuLimit"`
	CPURequire     int64    `json:"cpuRequire"`
	MemoryLimit    int64    `json:"memoryLimit"`
	MemoryRequire  int64    `json:"memoryRequire"`
}

type ClusterCapacity struct {
	ClusterTotals ClusterTotals `json:"clusterTotals"`
	Nodes         []Node        `json:"nodes"`
}

type NodePoolCapacity struct {
	CPUCapacity    int64  `json:"cpuCapacity"`
	MemoryCapacity int64  `json:"memoryCapacity"`
	CPULimit       int64  `json:"cpuLimit"`
	CPURequire     int64  `json:"cpuRequire"`
	MemoryLimit    int64  `json:"memoryLimit"`
	MemoryRequire  int64  `json:"memoryRequire"`
	Nodes          []Node `json:"nodes"`
}

func (c ClusterCapacity) Filter(dedicatedName string) NodePoolCapacity {
	ret := NodePoolCapacity{
		Nodes: make([]Node, 0, 100),
	}

	for _, it := range c.Nodes {
		if it.DedicatedName == dedicatedName {
			ret.Nodes = append(ret.Nodes, it)
			ret.CPULimit += it.CPULimit
			ret.CPURequire += it.CPURequire
			ret.CPUCapacity += it.CPUCapacity
			ret.MemoryLimit += it.MemoryLimit
			ret.MemoryRequire += it.MemoryRequire
			ret.MemoryCapacity += it.MemoryCapacity
		}
	}
	return ret
}
