package k8s_service

import (
	"fmt"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	log "github.com/sirupsen/logrus"
	"time"
)

type AppStatus struct {
	Cluster    string    `json:"name"`
	Namespace  string    `json:"namespace"`
	App        string    `json:"app"`
	Version    string    `json:"version"`
	Replicas   int32     `json:"replicas"`
	UpdateTime time.Time `json:"updateTime"`
	UpdateUser string    `json:"updateUser"`
	Tags       []string  `json:"tags"`
}

func GetAppStatus(cluster, namespace, app string) *AppStatus {
	status := allAppStatus(cluster, namespace)
	if status == nil {
		return nil
	}
	return status[fmt.Sprintf("%s@%s@%s", cluster, namespace, app)]
}
func allAppStatus(cluster, namespace string) map[string]*AppStatus {
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("appStatus@@%s@@%s", cluster, namespace))

	if data, found := cache.GetStruct(cacheKey, map[string]*AppStatus{}); found {
		return data
	}
	deploys, err := ListDeployment(cluster, namespace)
	if err != nil {
		log.Warn(err)
		return nil
	}
	data := make(map[string]*AppStatus)
	for _, dep := range deploys {
		item := AppStatus{
			Cluster:    cluster,
			Namespace:  dep.Namespace,
			App:        dep.Name,
			Version:    dep.DeployTag,
			Replicas:   dep.Replicas,
			UpdateTime: time.Time{},
			UpdateUser: "-",
			Tags:       nil,
		}
		data[fmt.Sprintf("%s@%s@%s", item.Cluster, item.Namespace, item.App)] = &item
	}
	if err := cache.SetStruct(cacheKey, data, 20*time.Minute); err != nil {
		log.Warn(err.Error())
	}
	return data
}
