package image_preheat_service

import (
	"fs-k8s-app-manager/models"
)

func FindAll() (ret []models.ImagePreheat, err error) {
	err = models.DB().Find(&ret).Error
	return
}

func FindByID(id uint) (ret models.ImagePreheat, err error) {
	err = models.DB().Where("id = ?", id).Take(&ret).Error
	return
}

func Save(entity *models.ImagePreheat) (err error) {
	return models.DB().Save(entity).Error
}

func Delete(id uint) error {
	return models.DB().Unscoped().Where("id = ?", id).Delete(&models.ImagePreheat{}).Error
}

// Search image preheat tasks by keyword (searches Name field)
func Search(keyword string) ([]models.ImagePreheat, error) {
	var ret []models.ImagePreheat
	db := models.DB().Model(&models.ImagePreheat{})
	if keyword != "" {
		db = db.Where("name LIKE ?", "%"+keyword+"%")
	}
	err := db.Order("id desc").Find(&ret).Error
	return ret, err
}
