package app_version_history_service

import (
	"fs-k8s-app-manager/models"
	"time"
)

func Save(entity *models.AppVersionHistory) (err error) {
	return models.DB().Create(entity).Error
}

func Search(cluster, namespace, app, recordDate string, page, limit int) (ret []models.AppVersionHistory, err error) {
	db := models.DB().Model(&models.AppVersionHistory{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if app != "" {
		db.Where("app = ?", app)
	}
	if recordDate != "" {
		db.Where("record_date = ?", recordDate)
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("app desc").Find(&ret).Error
	return
}

func Count(cluster, namespace, app, recordDate string) int64 {
	var count int64
	db := models.DB().Model(&models.AppVersionHistory{})
	if cluster != "" {
		db.Where("cluster = ?", cluster)
	}
	if namespace != "" {
		db.Where("namespace = ?", namespace)
	}
	if app != "" {
		db.Where("app = ?", app)
	}
	if recordDate != "" {
		db.Where("record_date = ?", recordDate)
	}
	db.Count(&count)
	return count
}

func CountAll() int64 {
	var count int64
	db := models.DB().Model(models.AppVersionHistory{})
	db.Count(&count)
	return count
}

func DeleteBeforeTime(time time.Time) error {
	return models.DB().Unscoped().Where("created_at < ?", time).Delete(&models.AppVersionHistory{}).Error
}
