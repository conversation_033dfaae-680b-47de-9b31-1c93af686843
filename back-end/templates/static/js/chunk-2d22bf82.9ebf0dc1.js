(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22bf82"],{f0df:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"260px"},model:{value:e.searchForm.keyword,callback:function(t){e.$set(e.searchForm,"keyword","string"===typeof t?t.trim():t)},expression:"searchForm.keyword"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog()}}},[e._v("新建\n      ")])],1)],1),e._v(" "),e._m(0),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"所在集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启时间点",align:"center",prop:"rebootHour",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        每天的 "),a("b",[e._v(e._s(t.row.rebootHour))]),e._v(" 点\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",align:"center",prop:"author"}}),e._v(" "),a("el-table-column",{attrs:{label:"备注",prop:"remark","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return e.deleteReboot(t.row)}}},[a("el-button",{staticStyle:{"margin-right":"5px"},attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[e._v("删除\n          ")])],1)]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"创建服务重启任务",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-input",{model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app","string"===typeof t?t.trim():t)},expression:"editForm.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"k8s集群"}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.clusterChange},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}},e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"重启时间点"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.rebootHour,callback:function(t){e.$set(e.editForm,"rebootHour",t)},expression:"editForm.rebootHour"}},e._l([23,0,1,2,3,4,5,6],(function(e){return a("el-option",{key:e,attrs:{label:e+"点",value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{model:{value:e.editForm.remark,callback:function(t){e.$set(e.editForm,"remark","string"===typeof t?t.trim():t)},expression:"editForm.remark"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.submitLoading,expression:"submitLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.create()}}},[e._v("确 定")])],1)],1)],1)},o=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("定时重启")]),e._v(" "),a("td",{staticStyle:{width:"1000px"}},[e._v("\n        服务会在配置的时间点上进行重启，按批重启所有实例，每批启动25%的实例\n      ")])])])}],i=(a("7f7f"),a("2d63")),l=a("b775");function n(e){return Object(l["a"])({url:"/v1/reboot?keyword="+e,method:"get"})}function s(e){return Object(l["a"])({url:"/v1/reboot",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/v1/reboot",method:"delete",params:{id:e}})}var u={components:{},mounted:function(){this.loadTableData()},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.editForm.cluster){var e,t=Object(i["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.editForm.cluster===a.name)return a.namespaces}}catch(r){t.e(r)}finally{t.f()}}return[]}},data:function(){return{searchForm:{keyword:""},editForm:{},editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],rebootHour:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,submitLoading:!1,appOptions:[]}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,n(this.searchForm.keyword).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:"",namespace:"",rebootHour:23,remark:""}},clusterChange:function(){this.editForm.namespace=""},showEditDialog:function(){this.editFormReset(),this.dialogVisible=!0},deleteReboot:function(e){var t=this;c(e.id).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},create:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;e.submitLoading=!0,s(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))}))}}},d=u,m=a("2877"),p=Object(m["a"])(d,r,o,!1,null,null,null);t["default"]=p.exports}}]);