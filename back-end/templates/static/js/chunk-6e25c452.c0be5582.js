(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e25c452"],{2352:function(t,e,a){"use strict";a("a242")},5147:function(t,e,a){var i=a("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[i]=!1,!"/./"[t](e)}catch(r){}}return!0}},"530d":function(t,e,a){"use strict";a.d(e,"d",(function(){return r})),a.d(e,"e",(function(){return o})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return c}));var i=a("b775");function r(){return Object(i["a"])({url:"/v1/artifact/all",method:"get"})}function o(t){return Object(i["a"])({url:"/v1/artifact/search",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/v1/artifact",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},a242:function(t,e,a){},aae3:function(t,e,a){var i=a("d3f4"),r=a("2d95"),o=a("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},acaa:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")])],1)]),t._v(" "),a("el-col",{attrs:{span:20}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1)],1)])],1)],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,prev,pager,next",total:t.tableData.count},on:{"current-change":t.PageChange}}),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"Git地址",sortable:"",prop:"gitUrl"}}),t._v(" "),a("el-table-column",{attrs:{label:"模块",sortable:"",prop:"module"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建人",width:"120px",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",width:"140px",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popconfirm",{attrs:{title:"删除部署模块不应影响到应用和发布流程，确定继续删除吗？"},on:{confirm:function(a){return t.deleteRow(e.$index,e.row)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1)]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:"新建",visible:t.dialogEditVisible,width:"50%","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[a("el-form-item",{attrs:{label:"Git地址",prop:"gitUrl"}},[a("el-input",{model:{value:t.dialogEditForm.gitUrl,callback:function(e){t.$set(t.dialogEditForm,"gitUrl","string"===typeof e?e.trim():e)},expression:"dialogEditForm.gitUrl"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"模块"}},[a("el-input",{model:{value:t.dialogEditForm.module,callback:function(e){t.$set(t.dialogEditForm,"module","string"===typeof e?e.trim():e)},expression:"dialogEditForm.module"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}})],1)],1),t._v(" "),a("div",{staticStyle:{"padding-left":"120px","line-height":"1.5em"}},[t._v("\n      【Git地址】: 项目的Git地址，必须为 https 开头"),a("br"),t._v("\n      【模块】: 项目的子模块，如果没有则保留为空\n    ")]),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create()}}},[t._v("确 定")])],1)],1)],1)},r=[],o=(a("aef6"),a("f559"),a("530d")),n={name:"artifactList",data:function(){var t=function(t,e,a){e.startsWith("https://")&&e.endsWith(".git")?a():a(new Error("地址必须为https://xxx.git格式"))};return{searchForm:{keyword:"",page:1,limit:10},tableData:[],tableLoading:!1,dialogEditVisible:!1,dialogEditForm:{id:0,gitUrl:"",module:""},dialogEditFormRules:{gitUrl:[{required:!0,message:"请输入项目Git地址",trigger:"blur"},{validator:t,message:"Git地址必须为https://xxx.git格式",trigger:"blur"}]}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(o["e"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.gitUrl="",this.dialogEditForm.module=""},PageChange:function(t){this.searchForm.page=t,this.loadTableData()},createPage:function(){this.dialogEditVisible=!0,this.resetEditForm()},deleteRow:function(t,e){var a=this;Object(o["c"])(e.id).then((function(e){a.tableData.data.splice(t,1)})).catch((function(t){a.$message.error(t.message)}))},create:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;Object(o["b"])(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))}}},l=n,c=(a("2352"),a("2877")),s=Object(c["a"])(l,i,r,!1,null,"6cf89c18",null);e["default"]=s.exports},aef6:function(t,e,a){"use strict";var i=a("5ca1"),r=a("9def"),o=a("d2c8"),n="endsWith",l=""[n];i(i.P+i.F*a("5147")(n),"String",{endsWith:function(t){var e=o(this,t,n),a=arguments.length>1?arguments[1]:void 0,i=r(e.length),c=void 0===a?i:Math.min(r(a),i),s=String(t);return l?l.call(e,s,c):e.slice(c-s.length,c)===s}})},d2c8:function(t,e,a){var i=a("aae3"),r=a("be13");t.exports=function(t,e,a){if(i(e))throw TypeError("String#"+a+" doesn't accept regex!");return String(r(t))}},f559:function(t,e,a){"use strict";var i=a("5ca1"),r=a("9def"),o=a("d2c8"),n="startsWith",l=""[n];i(i.P+i.F*a("5147")(n),"String",{startsWith:function(t){var e=o(this,t,n),a=r(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),i=String(t);return l?l.call(e,i,a):e.slice(a,a+i.length)===i}})}}]);