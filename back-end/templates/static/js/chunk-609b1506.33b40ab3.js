(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-609b1506"],{2273:function(t,n,e){},"24fd":function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return i}));var a=e("b775");function r(){return Object(a["a"])({url:"/v1/org/all",method:"get"})}function u(t){return Object(a["a"])({url:"/v1/org",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/v1/org",method:"put",data:t})}},"74cc":function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container app-permission"},[e("div",{staticClass:"app_info_panel",staticStyle:{"padding-bottom":"20px"}},[e("b",[t._v("应用")]),e("span",[t._v(t._s(t.app.name))]),t._v(" "),e("b",[t._v("第一负责人")]),e("span",[t._v(t._s(t.app.mainOwner))]),t._v(" "),e("b",[t._v("负责人")]),e("span",[t._v(t._s(t.app.owners.join(",")))]),t._v(" "),e("b",[t._v("描述")]),e("span",[t._v(t._s(t.app.remark))])]),t._v(" "),e("el-transfer",{attrs:{data:t.orgOptions,filterable:"",titles:["可选角色","应用的角色"],"filter-method":t.filterOrg,"filter-placeholder":"请输入搜索关键字"},model:{value:t.app.orgs,callback:function(n){t.$set(t.app,"orgs",n)},expression:"app.orgs"}}),t._v(" "),e("div",{staticStyle:{"text-align":"center",margin:"20px 80px 0 0"}},[e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.submitBtnLoading,expression:"submitBtnLoading"}],staticStyle:{padding:"12px 30px"},attrs:{type:"primary",disabled:this.submitBtnDisable},on:{click:t.submitPerm}},[t._v("提 交\n    ")])],1)],1)},r=[],u=(e("7f7f"),e("2d63")),i=e("b562"),o=e("24fd"),c={data:function(){return{loading:!0,submitBtnLoading:!1,submitBtnDisable:!1,app:{name:"",orgs:[],mainOwner:"",owners:[]},orgOptions:[],tableData:[],tableLoading:!1,leftCheckedOrg:[],rightCheckedOrg:[],filterOrg:function(t,n){return!t||n.label.indexOf(t)>-1}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.$route.query.app?(this.loading=!0,Object(i["j"])(this.$route.query.app).then((function(n){t.app=n.data})).catch((function(n){t.submitBtnDisable=!0,t.$message.error(n.message)})).finally((function(){t.loading=!1})),Object(o["a"])().then((function(n){var e,a=[],r=Object(u["a"])(n.data);try{for(r.s();!(e=r.n()).done;){var i=e.value;a.push({key:i.name,label:i.name+" (成员:"+i.users.join(",")+")",name:i.name,remark:i.remark,disabled:!1})}}catch(o){r.e(o)}finally{r.f()}t.orgOptions=a})).catch((function(n){t.submitBtnDisable=!0,t.$message.error("加载角色数据出错"+n.message)}))):this.$message.error("缺少应用参数")},submitPerm:function(){var t=this;this.submitBtnLoading=!0,Object(i["r"])(this.app.name,this.app.orgs).then((function(n){t.$message.success("操作成功"),t.$router.push({name:"app-list",query:{app:t.app.name}})})).catch((function(n){t.$message.error(n.message)})).finally((function(){t.submitBtnLoading=!1}))}}},p=c,s=(e("a772"),e("2877")),d=Object(s["a"])(p,a,r,!1,null,null,null);n["default"]=d.exports},a772:function(t,n,e){"use strict";e("2273")},b562:function(t,n,e){"use strict";e.d(n,"p",(function(){return r})),e.d(n,"b",(function(){return u})),e.d(n,"a",(function(){return i})),e.d(n,"l",(function(){return o})),e.d(n,"j",(function(){return c})),e.d(n,"e",(function(){return p})),e.d(n,"i",(function(){return s})),e.d(n,"h",(function(){return d})),e.d(n,"m",(function(){return l})),e.d(n,"o",(function(){return m})),e.d(n,"g",(function(){return f})),e.d(n,"f",(function(){return b})),e.d(n,"c",(function(){return g})),e.d(n,"k",(function(){return h})),e.d(n,"r",(function(){return v})),e.d(n,"n",(function(){return O})),e.d(n,"q",(function(){return j})),e.d(n,"d",(function(){return _}));var a=e("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function u(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function o(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function c(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function p(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function d(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function l(t,n,e){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:n,app:e}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function h(t,n){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:n}})}function v(t,n){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:n}})}function O(t,n){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:n||""}})}function j(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}function _(){return Object(a["a"])({url:"/v1/app/count-pipelines",method:"get"})}}}]);