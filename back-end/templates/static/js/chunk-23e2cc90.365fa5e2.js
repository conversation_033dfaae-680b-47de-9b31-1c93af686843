(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-23e2cc90"],{"4f2b":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[n("el-alert",{attrs:{title:"应用批量构建",type:"info",description:"使用当前运行的版本重新构建镜像。","show-icon":""}}),t._v(" "),n("el-form",{staticStyle:{"max-width":"800px"},attrs:{"label-width":"120px"}},[n("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"查询条件"}},[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1),t._v(" "),n("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"构建备注"}},[n("el-input",{model:{value:t.buildRemark,callback:function(e){t.buildRemark=e},expression:"buildRemark"}})],1),t._v(" "),n("el-form-item",{staticStyle:{margin:"3px","text-align":"right"}},[n("el-button",{staticStyle:{margin:"3px"},attrs:{type:"primary",size:"small"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1),t._v(" "),n("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[n("el-table",{attrs:{data:t.tableData,border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"构建过？",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.extraAttr.reDeployed)+"\n        ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.reBuild(e.row)}}},[t._v("使用当前版本构建\n            ")]),t._v(" "),n("br"),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-image-build-history",query:{}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n                历史\n              ")])],1),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n                发布流程\n              ")])],1)],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",prop:"app"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticStyle:{"font-size":"10px"}},[n("div",[t._v(t._s(e.row.app))]),t._v(" "),n("div",{staticStyle:{size:"10px",color:"#999"}},[t._v(t._s(e.row.namespace)+" ("+t._s(e.row.cluster)+")")])])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"状态",prop:"status",width:"100px"}}),t._v(" "),n("el-table-column",{attrs:{label:"模块数",prop:"status",width:"80px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",[t._v(t._s(e.row.appModules.length))]),t._v(" "),n("div",[n("el-button",{attrs:{type:"text"},on:{click:function(n){return t.contentShow(e.row)}}},[t._v("查看\n            ")])],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"模块镜像"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticStyle:{"font-size":"10px"}},[n("div",[t._v(t._s(e.row.extraAttr.deployModuleImages))])])]}}])})],1)],1),t._v(" "),n("el-dialog",{attrs:{title:t.contentDialog.title,visible:t.contentDialog.visible,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.contentDialog,"visible",e)}}},[n("div",[n("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(this.contentDialog.content))])])])],1)},r=[],o=n("2d63"),i=n("51a9"),u=n("8504"),c=n("76fe"),l={components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,searchForm:"",buildRemark:"",tableData:[],contentDialog:{visible:!1,title:"",content:""}}},methods:{loadTableData:function(){var t=this;this.pageLoading=!0,this.tableData=[],Object(i["m"])(this.searchForm).then((function(e){var n,a=Object(o["a"])(e.data);try{for(a.s();!(n=a.n()).done;){var r=n.value;r.extraAttr.deployModuleImages="?",r.extraAttr.reDeployed="NO",r.extraAttr.deployModules="?",t.findDeployment(r)}}catch(i){a.e(i)}finally{a.f()}t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},findDeployment:function(t){null!==t?Object(u["c"])(t.cluster,t.namespace,t.app).then((function(e){if(e.data.spec.template.spec.initContainers.length>0){var n,a=[],r=Object(o["a"])(e.data.spec.template.spec.initContainers);try{for(r.s();!(n=r.n()).done;){var i=n.value;a.push(i.image)}}catch(u){r.e(u)}finally{r.f()}t.extraAttr.deployModuleImages=a.join("<br/>"),t.extraAttr.deployModules=e.data.spec.template.spec.initContainers}})).catch((function(t){console.log(t.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reBuild:function(t){var e=this;Object(c["c"])(t.cluster,t.namespace,t.app,this.buildRemark).then((function(n){e.$message.success("操作成功"),t.extraAttr.reDeployed="YES"})).catch((function(t){e.$message.error(t.message)}))},contentShow:function(t){this.contentDialog.content=t.extraAttr.deployModules,this.contentDialog.title="应用："+t.app,this.contentDialog.visible=!0}}},s=l,p=n("2877"),d=Object(p["a"])(s,a,r,!1,null,null,null);e["default"]=d.exports},"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"e",(function(){return o})),n.d(e,"d",(function(){return i})),n.d(e,"l",(function(){return u})),n.d(e,"m",(function(){return c})),n.d(e,"a",(function(){return l})),n.d(e,"f",(function(){return s})),n.d(e,"i",(function(){return p})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return m})),n.d(e,"n",(function(){return f})),n.d(e,"g",(function(){return b})),n.d(e,"b",(function(){return v})),n.d(e,"h",(function(){return h})),n.d(e,"o",(function(){return g}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function i(t,e,n){return Object(a["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function u(t){return Object(a["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function l(){return Object(a["a"])({url:"/v1/pipeline/all",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(a["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(a["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function f(t){return Object(a["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function b(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function v(t){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function h(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function g(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return r})),n.d(e,"i",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"e",(function(){return u})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return l})),n.d(e,"c",(function(){return s})),n.d(e,"g",(function(){return p})),n.d(e,"h",(function(){return d})),n.d(e,"d",(function(){return m})),n.d(e,"l",(function(){return f})),n.d(e,"j",(function(){return b}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function i(t){return Object(a["a"])({url:"/v1/job/build-image",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function l(t,e,n,r){return Object(a["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function s(t,e,n,r){return Object(a["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function p(t){return Object(a["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(a["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(a["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function f(t,e){return Object(a["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function b(t){return Object(a["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return l})),n.d(e,"d",(function(){return s})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function s(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,o){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:o||""}})}}}]);