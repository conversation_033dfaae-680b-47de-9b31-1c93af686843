(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52428f3e"],{"51a9":function(e,t,n){"use strict";n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return i})),n.d(t,"l",(function(){return s})),n.d(t,"m",(function(){return u})),n.d(t,"a",(function(){return c})),n.d(t,"f",(function(){return o})),n.d(t,"i",(function(){return p})),n.d(t,"j",(function(){return f})),n.d(t,"k",(function(){return m})),n.d(t,"n",(function(){return d})),n.d(t,"g",(function(){return h})),n.d(t,"b",(function(){return g})),n.d(t,"h",(function(){return v})),n.d(t,"o",(function(){return b}));var r=n("b775");function l(e){return Object(r["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function a(e){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function i(e,t,n){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:n}})}function s(e){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function u(e){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function o(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function f(e){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function d(e){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function h(e,t,n,l){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:l}})}function g(e){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function v(e,t,n,l){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:l}})}function b(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"6a0a":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticStyle:{width:"600px",margin:"0 auto"}},[n("el-steps",{attrs:{"finish-status":"success"}},[n("el-step",{attrs:{title:"选择集群参数","process-status":"error"}}),e._v(" "),n("el-step",{attrs:{title:"开始克隆"}})],1),e._v(" "),n("el-button",{staticStyle:{"margin-top":"12px"},attrs:{loading:e.loading},on:{click:e.next}},[e._v(e._s(e.nextBtnText))])],1),e._v(" "),n("el-divider"),e._v(" "),"query-pipelines"===e.active?n("div",[n("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.form}},[n("el-form-item",{attrs:{label:"从",prop:"sourceCluster"}},[n("el-select",{attrs:{"value-key":"id",placeholder:"源集群",filterable:""},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.clusterOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"克隆到",prop:"targetCluster"}},[n("el-select",{attrs:{"value-key":"id",placeholder:"目标集群",filterable:""},model:{value:e.form.targetCluster,callback:function(t){e.$set(e.form,"targetCluster",t)},expression:"form.targetCluster"}},e._l(e.clusterOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1)],1)],1):e._e(),e._v(" "),"clone-pipelines"===e.active?n("div",[n("el-alert",{attrs:{title:e.form.sourceCluster.cluster+"/"+e.form.sourceCluster.namespace+"--\x3e"+e.form.targetCluster.cluster+"/"+e.form.targetCluster.namespace,type:"warning",center:""}}),e._v(" "),n("el-table",{ref:"multipleTable",attrs:{data:e.clonePipelineTableData,size:"mini"},on:{"selection-change":e.handleClonePipelineTableSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),e._v(" "),n("el-table-column",{attrs:{property:"app",label:"所属应用"}}),e._v(" "),n("el-table-column",{attrs:{property:"status",label:"发布流程状态"}})],1)],1):e._e()],1)},l=[],a=(n("7f7f"),n("2d63")),i=n("51a9"),s={name:"clone-pipeline",components:{},data:function(){var e=function(e,t,n){null===t.cluster&&null===t.namespace?n(new Error("请选择集群")):n()};return{active:"query-pipelines",form:{sourceCluster:{cluster:null,namespace:null},targetCluster:{cluster:null,namespace:null}},rules:{sourceCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}],targetCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}]},clonePipelineTableData:[],clonePipelineTableMultipleSelection:[],loading:!1}},computed:{nextBtnText:function(){return"query-pipelines"===this.active?"查询发布流程":"clone-pipelines"===this.active?"克隆发布流程":"未知操作"},clusterOptions:function(){var e,t=[],n=0,r=Object(a["a"])(this.$settings.clusters);try{for(r.s();!(e=r.n()).done;){var l,i=e.value,s=Object(a["a"])(i.namespaces);try{for(s.s();!(l=s.n()).done;){var u=l.value,c={};c.cluster=i.name,c.namespace=u,c.id=n,t.push(c),n++}}catch(o){s.e(o)}finally{s.f()}}}catch(o){r.e(o)}finally{r.f()}return t}},methods:{next:function(){var e=this;switch(this.active){case"query-pipelines":this.$refs["ruleForm"].validate((function(t){if(!t)return!1;e.loading=!0,e.loadClonePipeline()}));break;case"clone-pipelines":if(this.clonePipelineTableMultipleSelection.length<=0)return void this.$message({message:"未选择克隆的应用",type:"warning"});this.loading=!0,this.clonePipeline()}},loadClonePipeline:function(){var e=this;Object(i["g"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.targetCluster.cluster,this.form.targetCluster.namespace).then((function(t){e.active="clone-pipelines",e.clonePipelineTableData=t.data,e.$nextTick((function(){e.$refs.multipleTable.toggleAllSelection()}))})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},handleClonePipelineTableSelectionChange:function(e){this.clonePipelineTableMultipleSelection=e},clonePipeline:function(){var e,t=this,n=[],r=Object(a["a"])(this.clonePipelineTableMultipleSelection);try{for(r.s();!(e=r.n()).done;){var l=e.value;n.push(l.id)}}catch(u){r.e(u)}finally{r.f()}var s={};s.ids=n,s.sourceCluster=this.form.sourceCluster.cluster,s.sourceNamespace=this.form.sourceCluster.namespace,s.targetCluster=this.form.targetCluster.cluster,s.targetNamespace=this.form.targetCluster.namespace,Object(i["b"])(s).then((function(e){t.$message.success("操作成功"),t.$alert('<pre style="overflow: auto;font-size: 10px;line-height:10px;">'+JSON.stringify(e.data,null,2)+"</pre>","操作成功，结果:",{dangerouslyUseHTMLString:!0})})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))}}},u=s,c=n("2877"),o=Object(c["a"])(u,r,l,!1,null,"b1f9966e",null);t["default"]=o.exports}}]);