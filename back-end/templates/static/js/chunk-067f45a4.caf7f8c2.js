(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-067f45a4"],{"1e42":function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},o=[],r=(a("a481"),a("25ca")),n=a("21a6"),i=a.n(n),c={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=r["a"].table_to_book(t,{raw:!0}),a=r["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var l=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";i.a.saveAs(new Blob([a],{type:"application/octet-stream"}),l)}catch(o){this.$message.error("导出失败, err: "+o.message),console.error(o)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},s=c,u=a("2877"),p=Object(u["a"])(s,l,o,!1,null,null,null);e["a"]=p.exports},5147:function(t,e,a){var l=a("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[l]=!1,!"/./"[t](e)}catch(o){}}return!0}},8916:function(t,e,a){"use strict";a.d(e,"a",(function(){return o}));var l=a("b775");function o(t){return Object(l["a"])({url:"/v1/log",method:"get",params:t})}},aae3:function(t,e,a){var l=a("d3f4"),o=a("2d95"),r=a("2b4c")("match");t.exports=function(t){var e;return l(t)&&(void 0!==(e=t[r])?!!e:"RegExp"==o(t))}},bbb3:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"操作类型"}},[a("el-autocomplete",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"使用全量匹配","fetch-suggestions":t.getOperateOptions},model:{value:t.searchForm.operate,callback:function(e){t.$set(t.searchForm,"operate",e)},expression:"searchForm.operate"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"操作人"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"使用模糊查询",clearable:""},model:{value:t.searchForm.author,callback:function(e){t.$set(t.searchForm,"author",e)},expression:"searchForm.author"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"操作对象"}},[a("el-input",{staticStyle:{width:"320px"},attrs:{placeholder:"使用模糊查询",clearable:""},model:{value:t.searchForm.target,callback:function(e){t.$set(t.searchForm,"target",e)},expression:"searchForm.target"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary"},on:{click:t.loadTableData},nativeOn:{submit:function(t){t.preventDefault()}}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,sizes,prev,pager,next","page-sizes":[10,20,50,100,200,400,1e3,2e3,3e3,5e3],total:t.tableData.count},on:{"size-change":t.pageSizeChange,"current-change":t.pageChange}}),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",prop:"operate"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作人",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作对象",prop:"target"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作时间",width:"200px;",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"内容"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.content.substring(0,100))+"\n        "),e.row.content.length>100?a("span",[a("span",{staticStyle:{"padding-left":"5px",color:"#777","font-weight":"bold"}},[t._v("...")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.logContent(e.row)}}},[t._v("查看所有\n        ")])],1):t._e(),t._v(" "),"tomcat-traffic-count"===e.row.operate?a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.tomcatTrafficAnalysis(e.row)}}},[t._v("分析Tomcat流量\n          ")])],1):t._e()]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:"Tomcat流量分析",visible:t.tomcatTraffic.visible,fullscreen:!0,width:"50%",top:"5vh"},on:{"update:visible":function(e){return t.$set(t.tomcatTraffic,"visible",e)}}},[a("el-alert",{attrs:{title:"提示",type:"info",description:"统计数据里过滤掉了k8s健康检测、logConf日志调整等内部组件的请求流量","show-icon":""}}),t._v(" "),a("el-table",{attrs:{data:t.tomcatTraffic.data}},[a("el-table-column",{attrs:{property:"app",sortable:"",label:"应用"}}),t._v(" "),a("el-table-column",{attrs:{property:"cluster",sortable:"",label:"集群"}}),t._v(" "),a("el-table-column",{attrs:{property:"namespace",sortable:"",label:"环境"}}),t._v(" "),a("el-table-column",{attrs:{label:"pod数"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.pods.length)+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{property:"time",label:"分析时间"}}),t._v(" "),a("el-table-column",{attrs:{property:"logFile",label:"分析日志"}}),t._v(" "),a("el-table-column",{attrs:{property:"tomcatTraffic",label:"业务访问量",sortable:""}})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"50%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticStyle:{"margin-top":"-50px",overflow:"auto"}},[a("div",{staticStyle:{"text-align":"center"}},[a("clipboard-icon",{attrs:{text:this.dialogContent,"button-text":"一键复制内容"}})],1),t._v(" "),a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(t.dialogContent))])])])],1)},o=[],r=(a("f559"),a("8916")),n=a("1e42"),i=a("da37"),c={components:{ClipboardIcon:i["a"],ExportButton:n["a"]},data:function(){return{tableData:{data:[],count:0},operateOptions:[{value:"应用-创建"},{value:"应用-删除"},{value:"应用-回滚"},{value:"应用-批量发布"},{value:"应用-批量重发"},{value:"应用-权限编辑"},{value:"应用-编辑"},{value:"应用-部署"},{value:"应用-部署取消"},{value:"应用-重发"},{value:"应用-资源临时调整"},{value:"发布流程-下线"},{value:"发布流程-创建"},{value:"发布流程-审核"},{value:"发布流程-批量同步配置"},{value:"发布流程-编辑"},{value:"发布流程-资源批量更新"},{value:"实例-删除"},{value:"实例-摘除"},{value:"实例-开启持续性能分析"},{value:"实例文件-上传"},{value:"实例文件-下载"},{value:"实例文件-归档"},{value:"日志-清理"},{value:"服务-使用重建升级"},{value:"服务-手动扩容"},{value:"服务-手动缩容"},{value:"服务重启"},{value:"服务-重启"},{value:"端口-备份"},{value:"系统配置-修改"},{value:"自动扩缩容-扩容"},{value:"自动扩缩容-新建"},{value:"语音电话-拨打"},{value:"部署模块-创建"},{value:"部署模块-删除"},{value:"部门-修改"},{value:"部门-创建"},{value:"app-version-snapshot"},{value:"Gitlab部署文件-下载"},{value:"GitTag-创建"},{value:"GitTag-批量删除"},{value:"Git分支-批量删除"},{value:"Harbor-GC"},{value:"jacoco-coverage-download"},{value:"jacoco-coverage-reset"},{value:"jacoco-dump-download"},{value:"jacoco-report"},{value:"jacoco-reset"},{value:"jar-scan"},{value:"tomcat-version-scan"},{value:"K8S-Ingress-Apply"},{value:"K8S-PodMonitor-Apply"},{value:"K8S-PrometheusMonitor-Apply"},{value:"K8S-Service-Apply"},{value:"K8S-ServiceMonitor-Apply"},{value:"oncall"},{value:"Pod-ThreadDump"},{value:"Pod事件-上报"},{value:"tomcat-traffic-count"},{value:"war-gc"},{value:"临时授权-添加"},{value:"创建Bugfix分支"},{value:"app-version-snapshot"},{value:"OnCall-服务扩容"},{value:"OnCall-服务缩容"},{value:"OnCall-线程Dump"},{value:"OnCall-节点驱逐"},{value:"OnCall-WebHook"},{value:"进入CMS批量替换页"}],tableLoading:!1,searchForm:{author:"",operate:"",target:"",page:1,limit:20},dialogVisible:!1,dialogTitle:"日志内容",dialogContent:"-",tomcatTraffic:{visible:!1,data:[]}}},computed:{},mounted:function(){this.searchForm.operate=this.$route.query.operate||"",this.searchForm.author=this.$route.query.author||"",this.searchForm.target=this.$route.query.target||"",this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(r["a"])(this.searchForm).then((function(e){t.tableData=e.data,t.tableLoading=!1})).catch((function(e){t.$message.error(e.message),t.tableLoading=!1}))},pageSizeChange:function(t){this.searchForm.limit=t,this.loadTableData()},pageChange:function(t){this.searchForm.page=t,this.loadTableData()},logContent:function(t){var e=t.content;if(e.startsWith("[")||e.startsWith("{"))try{e=JSON.stringify(JSON.parse(e),null,2)}catch(a){console.log("json parse fail")}this.dialogContent=e,this.dialogVisible=!0},tomcatTrafficAnalysis:function(t){var e=t.content;try{e=JSON.parse(e)}catch(a){this.$message.error("解析数据格式失败")}this.tomcatTraffic.data=e,this.tomcatTraffic.visible=!0},getOperateOptions:function(t,e){var a=this.operateOptions;t&&(a=a.filter((function(e){return e.value.toLowerCase().indexOf(t.toLowerCase())>-1}))),e(a)}}},s=c,u=a("2877"),p=Object(u["a"])(s,l,o,!1,null,null,null);e["default"]=p.exports},d2c8:function(t,e,a){var l=a("aae3"),o=a("be13");t.exports=function(t,e,a){if(l(e))throw TypeError("String#"+a+" doesn't accept regex!");return String(o(t))}},da37:function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?a("span",[t._v(t._s(this.buttonText))]):t._e()])},o=[],r={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",e),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},n=r,i=a("2877"),c=Object(i["a"])(n,l,o,!1,null,null,null);e["a"]=c.exports},f559:function(t,e,a){"use strict";var l=a("5ca1"),o=a("9def"),r=a("d2c8"),n="startsWith",i=""[n];l(l.P+l.F*a("5147")(n),"String",{startsWith:function(t){var e=r(this,t,n),a=o(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),l=String(t);return i?i.call(e,l,a):e.slice(a,a+l.length)===l}})}}]);