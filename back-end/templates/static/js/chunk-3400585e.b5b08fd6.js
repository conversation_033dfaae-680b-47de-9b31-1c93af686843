(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3400585e"],{"1e42":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},o=[],i=(a("a481"),a("25ca")),l=a("21a6"),r=a.n(l),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=i["a"].table_to_book(t,{raw:!0}),a=i["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var n=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";r.a.saveAs(new Blob([a],{type:"application/octet-stream"}),n)}catch(o){this.$message.error("导出失败, err: "+o.message),console.error(o)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),d=Object(u["a"])(c,n,o,!1,null,null,null);e["a"]=d.exports},"2ffc":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",staticStyle:{position:"relative"}},[a("div",{staticStyle:{position:"absolute",right:"10px",top:"20px","z-index":"999"}},[a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:"k8s0",namespace:"foneshare",app:"fs-k8s-app-manager"}},target:"_blank"}},[a("span",{staticStyle:{"font-size":"14px",color:"#409EFF","margin-right":"30px"}},[t._v("查看本系统实例")])]),t._v(" "),a("el-button",{staticStyle:{padding:"0",float:"right"},attrs:{type:"text"},on:{click:t.clear_cache}},[t._v("清除系统缓存\n    ")])],1),t._v(" "),a("div",{staticStyle:{position:"relative"}},[a("el-tabs",{attrs:{"tab-position":"top"},on:{"tab-click":t.handleTabClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"settings.json",name:"settings.json"}},[a("div",{staticStyle:{"font-size":"14px",position:"absolute",right:"10px",top:"0","z-index":"999"}},[a("el-button",{staticStyle:{color:"#aaa","margin-right":"30px"},attrs:{type:"text"},on:{click:t.getImagesDistinct}},[t._v("获取去重后的镜像")]),t._v(" "),a("clipboard-icon",{attrs:{text:JSON.stringify(this.settings,null,2),"button-text":"一键复制"}})],1),t._v(" "),a("vue-json-pretty",{attrs:{data:this.settings}})],1),t._v(" "),a("el-tab-pane",{attrs:{label:"oncall.json",name:"oncall.json"}},[a("div",{staticStyle:{"font-size":"14px",position:"absolute",right:"10px",top:"0","z-index":"999"}},[a("clipboard-icon",{attrs:{text:JSON.stringify(this.oncallConfig,null,2),"button-text":"一键复制"}})],1),t._v(" "),a("vue-json-pretty",{attrs:{data:this.oncallConfig}})],1),t._v(" "),a("el-tab-pane",{attrs:{label:"lb-pools.json",name:"lb-pools.json"}},[a("div",{staticStyle:{"font-size":"14px",position:"absolute",right:"10px",top:"0","z-index":"999"}},[a("clipboard-icon",{attrs:{text:JSON.stringify(this.lbPools,null,2),"button-text":"一键复制"}})],1),t._v(" "),a("vue-json-pretty",{attrs:{data:this.lbPools}})],1),t._v(" "),a("el-tab-pane",{attrs:{label:"parent-pom",name:"parent-pom"}},[a("parent-pom")],1)],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"内容",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("div",{staticStyle:{"text-align":"center"}},[a("clipboard-icon",{attrs:{text:this.dialogText,"button-text":"一键复制"}})],1),t._v(" "),a("pre",{staticStyle:{padding:"5px",border:"solid 1px #999"}},[t._v(t._s(this.dialogText))])])])],1)},o=[],i=a("2d63"),l=(a("7f7f"),a("d538")),r=a.n(l),s=a("6797"),c=a("da37"),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",staticStyle:{position:"relative"}},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.searchForm,size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"320px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword","string"===typeof e?e.trim():e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1),t._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.addParentPom}},[t._v("新建\n        ")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("el-table",{ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"name",label:"名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"showName",label:"显示名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"描述"}}),t._v(" "),a("el-table-column",{attrs:{prop:"isArchive",label:"是否归档"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{disabled:!0},model:{value:e.row.isArchive,callback:function(a){t.$set(e.row,"isArchive",a)},expression:"scope.row.isArchive"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"enable",label:"是否启用"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{disabled:!0},model:{value:e.row.enable,callback:function(a){t.$set(e.row,"enable",a)},expression:"scope.row.enable"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"namespaces",label:"命名空间"}},[a("template",{slot:"header"},[t._v("\n        命名空间\n        "),a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"表示允许被使用的环境，空则表示不做环境限制"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1)],2),t._v(" "),a("el-table-column",{attrs:{prop:"sortRank",label:"排序分数"}},[a("template",{slot:"header"},[t._v("\n        排序分数\n        "),a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"分数越高，排序越靠前"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1)],2),t._v(" "),a("el-table-column",{attrs:{prop:"CreatedAt",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.CreatedAt&&e.row.CreatedAt.length>19?e.row.CreatedAt.slice(0,19):e.row.CreatedAt)+"\n      ")]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"UpdatedAt",label:"更新时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.UpdatedAt&&e.row.UpdatedAt.length>19?e.row.UpdatedAt.slice(0,19):e.row.UpdatedAt)+"\n      ")]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.editParentPom(e.row)}}},[t._v("编辑")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.deleteParentPom(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("el-dialog",{attrs:{visible:t.dialogVisible,title:t.editForm.id?"编辑父pom":"新建父pom",width:"600px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{attrs:{model:t.editForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"名称"}},[a("el-input",{attrs:{disabled:0!==t.editForm.id},model:{value:t.editForm.name,callback:function(e){t.$set(t.editForm,"name","string"===typeof e?e.trim():e)},expression:"editForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"显示名称"}},[a("el-input",{model:{value:t.editForm.showName,callback:function(e){t.$set(t.editForm,"showName","string"===typeof e?e.trim():e)},expression:"editForm.showName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{model:{value:t.editForm.remark,callback:function(e){t.$set(t.editForm,"remark","string"===typeof e?e.trim():e)},expression:"editForm.remark"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"允许的命名空间"}},[a("el-input",{attrs:{placeholder:""},model:{value:t.editForm.namespaces,callback:function(e){t.$set(t.editForm,"namespaces","string"===typeof e?e.trim():e)},expression:"editForm.namespaces"}}),t._v(" "),a("div",{staticStyle:{color:"#666","margin-top":"-10px","font-size":"12px"}},[t._v("多个命名空间用逗号分隔，空白表示允许所有命名空间")])],1),t._v(" "),a("el-form-item",{attrs:{label:"排序分数"}},[a("el-input",{attrs:{type:"number",min:"0"},model:{value:t.editForm.sortRank,callback:function(e){t.$set(t.editForm,"sortRank",t._n(e))},expression:"editForm.sortRank"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否归档"}},[a("el-switch",{model:{value:t.editForm.isArchive,callback:function(e){t.$set(t.editForm,"isArchive",e)},expression:"editForm.isArchive"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否启用"}},[a("el-switch",{model:{value:t.editForm.enable,callback:function(e){t.$set(t.editForm,"enable",e)},expression:"editForm.enable"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.saveParentPom}},[t._v("保存")]),t._v(" "),a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")])],1)],1)],1)],1)},d=[],m=a("edaf"),p=a("1e42"),b={name:"ParentPom",components:{ExportButton:p["a"]},data:function(){return{loading:!1,searchForm:{keyword:""},tableData:[],dialogVisible:!1,editForm:{id:0,name:"",remark:"",isArchive:!1,sortRank:0,namespaces:""}}},mounted:function(){this.loadTableData()},methods:{saveParentPom:function(){var t=this;this.loading=!0,Object(m["b"])(this.editForm).then((function(e){t.loadTableData(),t.dialogVisible=!1})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},getMaxSortRank:function(){var t,e=1,a=Object(i["a"])(this.tableData);try{for(a.s();!(t=a.n()).done;){var n=t.value;n.sortRank>e&&(e=n.sortRank)}}catch(o){a.e(o)}finally{a.f()}return e},addParentPom:function(){this.dialogVisible=!0,this.editForm={id:0,name:"fxiaoke-parent-pom-",showName:"",remark:"",isArchive:!1,enable:!0,sortRank:this.getMaxSortRank()+2}},editParentPom:function(t){this.dialogVisible=!0,this.editForm={id:t.ID,name:t.name,showName:t.showName,remark:t.remark,isArchive:t.isArchive,enable:t.enable,sortRank:t.sortRank,namespaces:t.namespaces}},deleteParentPom:function(t){var e=this;this.$confirm("确定删除该父pom吗? "+t.name,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,Object(m["a"])({id:t.ID}).then((function(t){e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))}))},loadTableData:function(){var t=this;this.loading=!0,Object(m["c"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))}}},f=b,v=a("2877"),h=Object(v["a"])(f,u,d,!1,null,null,null),g=h.exports,y=(a("27aa"),{data:function(){return{loading:!1,dialogVisible:!1,dialogText:"",oncallConfig:{err:"数据未加载"},lbPools:{err:"数据未加载"},settings:{},activeTab:"settings.json"}},mounted:function(){var t=this.$route.query.tabName;t&&(this.activeTab=t),this.loadOncallConfig(),this.loadLBPools(),this.settings=this.$settings},methods:{loadOncallConfig:function(){var t=this;Object(s["c"])().then((function(e){t.oncallConfig=e.data})).catch((function(e){t.$message.error("oncall config load fail, err:"+e.message)}))},loadLBPools:function(){var t=this;Object(s["b"])().then((function(e){t.lbPools=e.data})).catch((function(e){t.$message.error("lb pools load fail, err:"+e.message)}))},clear_cache:function(){var t=this;this.loading=!0,Object(s["a"])().then((function(e){t.$message.success("操作成功")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},handleTabClick:function(t,e){this.$router.push({query:{tabName:t.name}})},getImagesDistinct:function(){var t,e=[],a=Object(i["a"])(this.settings.clusters);try{for(a.s();!(t=a.n()).done;){var n,o=t.value,l=Object(i["a"])(o.baseImages);try{for(l.s();!(n=l.n()).done;){var r=n.value;-1===e.indexOf(r)&&e.push(r)}}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}this.dialogText=e.join("\n"),this.dialogVisible=!0}},components:{ParentPom:g,ClipboardIcon:c["a"],VueJsonPretty:r.a}}),x=y,_=Object(v["a"])(x,n,o,!1,null,null,null);e["default"]=_.exports},6797:function(t,e,a){"use strict";a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return r}));var n=a("b775");function o(t){return Object(n["a"])({url:"/v1/sys/log",method:"get",params:{tailLines:t}})}function i(){return Object(n["a"])({url:"/v1/sys/oncall-config",method:"get"})}function l(){return Object(n["a"])({url:"/v1/sys/lb-pools",method:"get"})}function r(){return Object(n["a"])({url:"/v1/sys/setting/cache",method:"delete"})}},da37:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?a("span",[t._v(t._s(this.buttonText))]):t._e()])},o=[],i={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",e),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},l=i,r=a("2877"),s=Object(r["a"])(l,n,o,!1,null,null,null);e["a"]=s.exports},edaf:function(t,e,a){"use strict";a.d(e,"c",(function(){return o})),a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return l}));var n=a("b775");function o(t){return Object(n["a"])({url:"/v1/sys/parent-pom",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/v1/sys/parent-pom",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/v1/sys/parent-pom",method:"delete",params:t})}}}]);