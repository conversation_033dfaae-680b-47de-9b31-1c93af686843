(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-612ffcc5"],{"5b06":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[n("div",[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.form}},[n("el-form-item",{attrs:{label:"模式",prop:"sourceCluster"}},[n("el-select",{model:{value:t.form.op,callback:function(e){t.$set(t.form,"op",e)},expression:"form.op"}},[n("el-option",{attrs:{label:"NodePort转Service",value:"nodeport-to-service"}}),t._v(" "),n("el-option",{attrs:{label:"Service短名转长名",value:"serviceShort-to-serviceLong"}})],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"环境",prop:"sourceCluster"}},[n("el-select",{staticStyle:{width:"360px"},attrs:{"value-key":"id",filterable:""},model:{value:t.form.sourceCluster,callback:function(e){t.$set(t.form,"sourceCluster",e)},expression:"form.sourceCluster"}},t._l(t.clusterOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.cluster+"/"+t.namespace,value:t}})})),1),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[t._v("查询")])],1)],1)],1),t._v(" "),n("div",[n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini"}},[n("el-table-column",{attrs:{prop:"name",label:"文件"}}),t._v(" "),n("el-table-column",{attrs:{prop:"profile",label:"配置组"}}),t._v(" "),n("el-table-column",{attrs:{prop:"editor",label:"最后修改人"}}),t._v(" "),n("el-table-column",{attrs:{label:"内容",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(n){return t.cmsContentPreview(e.row)}}},[t._v("查看")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"addrs",label:"地址","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.repairAddrs,(function(o,r){return n("span",{staticStyle:{margin:"2px 5px",padding:"0 2px",border:"1px solid #e2e2e2",display:"inline-block"}},[t._v("\n            "+t._s(r)+"\n            "),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.editCMS(e.row.id)}}},[t._v("修改")]),t._v(" "),n("el-tooltip",{attrs:{content:r+" -> "+o,placement:"right"}},[n("el-button",{staticStyle:{"margin-left":"5px"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.batchEditCMS(e.row.profile,r,o)}}},[t._v("批量替换")])],1)],1)}))}}])})],1)],1),t._v(" "),n("div",[n("el-dialog",{attrs:{title:t.cmsPreview.name+" ("+t.cmsPreview.profile+")",visible:t.cmsPreview.visible,width:"50%"},on:{"update:visible":function(e){return t.$set(t.cmsPreview,"visible",e)}}},[n("div",{staticStyle:{"margin-top":"-40px",border:"1px solid #e2e2e2",padding:"5px","max-height":"600px",overflow:"auto"}},[n("pre",[t._v(t._s(t.cmsPreview.content))])])])],1)])},r=[],a=(n("7f7f"),n("2d63")),c=n("c1ab"),i={components:{},data:function(){return{form:{op:"nodeport-to-service",sourceCluster:{cluster:null,namespace:null}},data:[],loading:!1,cmsPreview:{visible:!1,name:"",profile:"",content:""}}},computed:{clusterOptions:function(){var t,e=[],n=0,o=Object(a["a"])(this.$settings.clusters);try{for(o.s();!(t=o.n()).done;){var r,c=t.value,i=Object(a["a"])(c.namespaces);try{for(i.s();!(r=i.n()).done;){var u=r.value,l={};l.cluster=c.name,l.namespace=u,l.id=n,e.push(l),n++}}catch(s){i.e(s)}finally{i.f()}}}catch(s){o.e(s)}finally{o.f()}return e}},methods:{loadData:function(){var t=this;this.form.sourceCluster.cluster&&this.form.sourceCluster.namespace?(this.loading=!0,Object(c["n"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.op).then((function(e){t.data=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.error("请选择环境")},editCMS:function(t){var e="/api/page/redirect?type=cmsEdit&cmsId=".concat(t,"&_t")+Date.now();window.open(e)},batchEditCMS:function(t,e,n){var o="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(n,"&_t")+Date.now();window.open(o)},cmsContentPreview:function(t){this.cmsPreview.name=t.name,this.cmsPreview.profile=t.profile,this.cmsPreview.content=t.content,this.cmsPreview.visible=!0}}},u=i,l=n("2877"),s=Object(l["a"])(u,o,r,!1,null,"05a6500e",null);e["default"]=s.exports},c1ab:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"j",(function(){return a})),n.d(e,"z",(function(){return c})),n.d(e,"A",(function(){return i})),n.d(e,"c",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"E",(function(){return s})),n.d(e,"G",(function(){return d})),n.d(e,"y",(function(){return p})),n.d(e,"a",(function(){return f})),n.d(e,"e",(function(){return m})),n.d(e,"D",(function(){return v})),n.d(e,"F",(function(){return b})),n.d(e,"x",(function(){return h})),n.d(e,"H",(function(){return g})),n.d(e,"k",(function(){return y})),n.d(e,"d",(function(){return j})),n.d(e,"B",(function(){return O})),n.d(e,"i",(function(){return w})),n.d(e,"g",(function(){return _})),n.d(e,"s",(function(){return C})),n.d(e,"v",(function(){return x})),n.d(e,"w",(function(){return k})),n.d(e,"o",(function(){return S})),n.d(e,"p",(function(){return P})),n.d(e,"t",(function(){return $})),n.d(e,"u",(function(){return E})),n.d(e,"b",(function(){return z})),n.d(e,"q",(function(){return D})),n.d(e,"r",(function(){return q})),n.d(e,"n",(function(){return M})),n.d(e,"C",(function(){return A})),n.d(e,"m",(function(){return N})),n.d(e,"l",(function(){return B}));var o=n("b775");function r(t){return Object(o["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function a(t){return Object(o["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(o["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function i(t){return Object(o["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(o["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function l(t){return Object(o["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(o["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function m(t,e,n,r,a,c,i,u){return Object(o["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(a,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(i),method:"post",data:u})}function v(t){return Object(o["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(o["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(o["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(o["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,n,r){return Object(o["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function j(t,e,n,r,a){return Object(o["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(a),method:"post"})}function O(){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function w(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function _(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function C(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function x(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function k(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function S(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function P(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function $(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function E(t,e){return Object(o["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function z(t){return Object(o["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function D(t){return Object(o["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function q(t){return Object(o["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function M(t,e,n){return Object(o["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}function A(t){return Object(o["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function N(t,e,n){return Object(o["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(n),method:"get"})}function B(){return Object(o["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}}}]);