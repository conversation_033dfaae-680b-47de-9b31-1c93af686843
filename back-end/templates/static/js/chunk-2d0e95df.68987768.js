(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e95df"],{"8cdb":function(t,n,a){"use strict";a.r(n);var e=function(){var t=this,n=t.$createElement,a=t._self._c||n;return a("div",{staticStyle:{padding:"20px",margin:"0 auto","max-width":"980px"}},[a("div",{staticClass:"layui-card"},[a("div",{staticClass:"layui-card-body"},[a("pre",{staticStyle:{display:"block",margin:"0 auto",width:"200px","padding-top":"20px","padding-left":"40px"}},[t._v(" .--,       .--,\n( (  \\.---./  ) )\n '.__/o   o\\__.'\n    {=  ^  =}\n     >  -  <\n    /       \\\n   //       \\\\\n  //|   .   |\\\\\n  \"'\\       /'\"_.-~^`'-.\n     \\  _  /--'         `\n   ___)( )(___\n  (((__) (__)))\n                 ")]),t._v(" "),a("div",{staticClass:"layui-card-header",staticStyle:{"font-weight":"bold","text-align":"center"}},[t._v("404 (页面找不到)")]),t._v(" "),a("div",{staticStyle:{"text-align":"center","padding-top":"20px"}},[a("el-link",{attrs:{type:"primary",href:"/"}},[t._v("返回首页")])],1)])])])},i=[],d={name:"Page404",computed:{message:function(){return"page not found"}}},l=d,c=a("2877"),s=Object(c["a"])(l,e,i,!1,null,null,null);n["default"]=s.exports}}]);