(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44c83c1f"],{"0604":function(t,e,n){},"8f3f":function(t,e,n){"use strict";n("0604")},b562:function(t,e,n){"use strict";n.d(e,"p",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return c})),n.d(e,"l",(function(){return o})),n.d(e,"j",(function(){return l})),n.d(e,"e",(function(){return u})),n.d(e,"i",(function(){return s})),n.d(e,"h",(function(){return p})),n.d(e,"m",(function(){return d})),n.d(e,"o",(function(){return m})),n.d(e,"g",(function(){return h})),n.d(e,"f",(function(){return f})),n.d(e,"c",(function(){return g})),n.d(e,"k",(function(){return b})),n.d(e,"r",(function(){return v})),n.d(e,"n",(function(){return _})),n.d(e,"q",(function(){return y})),n.d(e,"d",(function(){return j}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function i(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function c(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function o(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function l(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function u(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function p(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function h(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function v(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}function j(){return Object(a["a"])({url:"/v1/app/count-pipelines",method:"get"})}},cd5c:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",attrs:{"element-loading-text":"数据加载中"}},[n("div",{staticStyle:{width:"500px",margin:"0 auto"}},[n("el-input",{attrs:{placeholder:"输入关键字可过滤"},model:{value:t.searchName,callback:function(e){t.searchName=e},expression:"searchName"}},[n("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.loadData()}},slot:"append"},[t._v("查找")])],1)],1),t._v(" "),n("div",{staticClass:"app_info_panel",staticStyle:{"text-align":"center","padding-top":"20px"}},[n("b",[t._v("应用:")]),n("span",[t._v(t._s(this.$route.query.app))]),t._v(" "),n("b",[t._v("Git地址:")]),n("span",[t._v(t._s(this.$route.query.git_url))])]),t._v(" "),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-divider",{attrs:{"content-position":"left"}},[n("b",[t._v("分支列表")]),t._v(" "),n("small",{staticStyle:{"padding-left":"10px"}},[t._v("(最多显示 2000 条数据)")])]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.batchDeleteGitBranch()}}},[t._v("批量删除")]),t._v(" "),n("el-table",{key:"name",staticStyle:{width:"100%","max-height":"700px",overflow:"auto"},attrs:{data:t.tableData.branches},on:{"selection-change":t.branchSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{prop:"name",sortable:"",label:"分支名称"}}),t._v(" "),n("el-table-column",{attrs:{prop:"commitCreatedAt",label:"对应Commit时间",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{prop:"message",label:"描述"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("el-divider",{attrs:{"content-position":"left"}},[n("b",[t._v("Tag列表")]),t._v(" "),n("small",{staticStyle:{"padding-left":"10px"}},[t._v("(最多显示 2000 条数据)")])]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.batchDeleteGitTag()}}},[t._v("批量删除")]),t._v(" "),n("el-table",{key:"name",staticStyle:{width:"100%","max-height":"700px",overflow:"auto"},attrs:{data:t.tableData.tags},on:{"selection-change":t.tagSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{prop:"name",sortable:"",label:"Tag名称"}}),t._v(" "),n("el-table-column",{attrs:{prop:"commitCreatedAt",label:"对应Commit时间",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{prop:"message",label:"描述"}})],1)],1)],1)],1)},r=[],i=(n("7f7f"),n("b562")),c={name:"git-tag-batch-delete",data:function(){return{loading:!1,tableData:{},multipleSelectionBranches:[],multipleSelectionTags:[],searchName:""}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.loading=!0,Object(i["k"])(this.$route.query.git_url,this.searchName).then((function(e){e.data.branches=e.data.branches.filter((function(t){return"main"!==t.name&&"master"!==t.name})),t.tableData=e.data,t.loading=!1})).catch((function(e){t.$message.warning("查询失败！ "+e.message),t.loading=!1}))},tagSelectionChange:function(t){this.multipleSelectionTags=t},branchSelectionChange:function(t){this.multipleSelectionBranches=t},batchDeleteGitTag:function(){for(var t=this,e=[],n=0;n<this.multipleSelectionTags.length;n++)e.push(this.multipleSelectionTags[n].name);e.length<1?this.$message.info("请选择需要删除的数据"):this.$confirm("确认删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n={};n.tags=e,n.gitUrl=t.$route.query.git_url,t.loading=!0,Object(i["c"])(n).then((function(e){t.$message.success("删除成功"),t.loadData()})).catch((function(e){t.$message.warning("删除失败！ "+e.message)}))}))},batchDeleteGitBranch:function(){for(var t=this,e=[],n=0;n<this.multipleSelectionBranches.length;n++)e.push(this.multipleSelectionBranches[n].name);e.length<1?this.$message.info("请选择需要删除的数据"):this.$confirm("确认删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n={};n.branches=e,n.gitUrl=t.$route.query.git_url,t.loading=!0,Object(i["c"])(n).then((function(e){t.$message.success("删除成功"),t.loadData()})).catch((function(e){t.$message.warning("删除失败！ "+e.message)}))}))}}},o=c,l=(n("8f3f"),n("2877")),u=Object(l["a"])(o,a,r,!1,null,"7b797e8a",null);e["default"]=u.exports}}]);