(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f60660c6"],{"88fa":function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-alert",{attrs:{title:"应用批量重发",type:"info",description:"使用当前运行的版本重新发布应用。 操作步骤：创建任务 → 开始 → 每间隔1分钟发布一个服务","show-icon":""}}),t._v(" "),o("div",{staticStyle:{margin:"10px"}},[o("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){t.appRestartVisible=!0}}},[t._v("创建任务")]),t._v(" "),t.job&&t.job.items?o("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===t.job.status?o("el-button",{attrs:{type:"text",size:"small"},on:{click:t.start}},[t._v("开始")]):t._e(),t._v(" "),1===t.job.status?o("el-button",{attrs:{type:"text",size:"small"},on:{click:t.stop}},[t._v("暂停")]):t._e(),t._v(" "),o("el-button",{attrs:{type:"text",size:"small"},on:{click:t.remove}},[t._v("删除")])],1):t._e()],1),t._v(" "),t.job&&t.job.items?o("div",[o("div",{staticStyle:{margin:"20px"}},[t._v("任务状态： "+t._s(t.job.status))]),t._v(" "),o("el-timeline",t._l(t.job.items,(function(e,n){return o("el-timeline-item",{key:n,attrs:{color:e.output.endsWith("success")?"#67C23A":e.output.includes("fail,")?"#F56C6C":"#909399"}},[o("b",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(n+1))]),t._v(t._s(e.output)+"\n      ")])})),1)],1):o("div",{staticStyle:{margin:"20px"}},[t._v("\n    -无任何数据-\n  ")]),t._v(" "),o("el-dialog",{attrs:{title:"服务批量重发",visible:t.appRestartVisible},on:{"update:visible":function(e){t.appRestartVisible=e}}},[o("el-form",{attrs:{"label-width":"100px"}},[o("el-form-item",{attrs:{label:"服务列表"}},[t._t("label",[t._v("\n          内容为json格式，每行格式为 cluster/namespace/app，\n          "),o("el-popover",{attrs:{placement:"bottom-end",trigger:"click"}},[o("div",[t._v("\n              ["),o("br"),t._v('\n                "k8s0/fstest/fs-aaa-bbb",'),o("br"),t._v('\n                "k8s0/fstest/fs-k8s-tomcat-test",'),o("br"),t._v('\n                "k8s0/firstshare/fs-k8s-tomcat-test"'),o("br"),t._v("\n              ]\n            ")]),t._v(" "),o("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("点击查看示例")])],1)]),t._v(" "),o("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:t.appRestartForm,callback:function(e){t.appRestartForm=e},expression:"appRestartForm"}})],2),t._v(" "),o("el-form-item",{attrs:{label:"发版参数"}},[o("el-checkbox",{model:{value:t.forceCodeCompile,callback:function(e){t.forceCodeCompile=e},expression:"forceCodeCompile"}},[t._v("【强制代码编译】")]),t._v(" "),o("el-checkbox",{model:{value:t.dependencyCheck,callback:function(e){t.dependencyCheck=e},expression:"dependencyCheck"}},[t._v("【依赖包版本校验】")])],1),t._v(" "),o("el-form-item",{attrs:{label:"父POM "}},[o("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"如果不填写，则使用上一次发版的父POM"},model:{value:t.parentPom,callback:function(e){t.parentPom=e},expression:"parentPom"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"tag后缀"}},[o("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"tag后缀"},model:{value:t.suffixVersion,callback:function(e){t.suffixVersion=e},expression:"suffixVersion"}})],1),t._v(" "),o("el-form-item",{staticStyle:{"margin-top":"-20px"},attrs:{"label-width":"100"}},[o("div",{staticStyle:{color:"#666","font-size":"12px","padding-left":"60px"}},[t._v("备注：如果填写了tag后缀，使用当前运行tag+后缀名创建新tag发布；否则，使用当前运行tag重发")])]),t._v(" "),o("el-form-item",{attrs:{label:"备注"}},[o("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"输入备注"},model:{value:t.message,callback:function(e){t.message=e},expression:"message"}})],1)],1),t._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.appRestartVisible=!1}}},[t._v("取 消")]),t._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:t.create}},[t._v("确 定")])],1)],1)],1)},r=[],a=o("c1ab"),c={components:{},mounted:function(){this.loadOutput();var t=this.loadOutput;this.timer=setInterval((function(){t()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,forceCodeCompile:!0,dependencyCheck:!0,suffixVersion:"",message:"",appRestartForm:"",parentPom:""}},methods:{loadOutput:function(){var t=this;Object(a["a"])().then((function(e){t.job=e.data})).catch((function(e){t.$message.error(e.message)}))},create:function(){var t=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(a["e"])("redeploy","",t.suffixVersion,t.message,t.forceCodeCompile,t.dependencyCheck,t.parentPom,t.appRestartForm).then((function(e){t.appRestartVisible=!1,t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消")}))},start:function(){var t=this;Object(a["D"])(this.appRestartForm).then((function(e){t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))},stop:function(){var t=this;Object(a["F"])(this.appRestartForm).then((function(e){t.$message.success("Success"),t.loadOutput()})).catch((function(e){t.$message.error(e.message)}))},remove:function(){var t=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(a["x"])(t.appRestartForm).then((function(e){t.$message.success("Success")})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消")}))}}},s=c,u=o("2877"),i=Object(u["a"])(s,n,r,!1,null,null,null);e["default"]=i.exports},c1ab:function(t,e,o){"use strict";o.d(e,"h",(function(){return r})),o.d(e,"j",(function(){return a})),o.d(e,"z",(function(){return c})),o.d(e,"A",(function(){return s})),o.d(e,"c",(function(){return u})),o.d(e,"f",(function(){return i})),o.d(e,"E",(function(){return l})),o.d(e,"G",(function(){return p})),o.d(e,"y",(function(){return d})),o.d(e,"a",(function(){return f})),o.d(e,"e",(function(){return m})),o.d(e,"D",(function(){return v})),o.d(e,"F",(function(){return b})),o.d(e,"x",(function(){return h})),o.d(e,"H",(function(){return g})),o.d(e,"k",(function(){return j})),o.d(e,"d",(function(){return y})),o.d(e,"B",(function(){return O})),o.d(e,"i",(function(){return _})),o.d(e,"g",(function(){return x})),o.d(e,"s",(function(){return k})),o.d(e,"v",(function(){return C})),o.d(e,"w",(function(){return w})),o.d(e,"o",(function(){return R})),o.d(e,"p",(function(){return S})),o.d(e,"t",(function(){return V})),o.d(e,"u",(function(){return $})),o.d(e,"b",(function(){return F})),o.d(e,"q",(function(){return z})),o.d(e,"r",(function(){return P})),o.d(e,"n",(function(){return B})),o.d(e,"C",(function(){return q})),o.d(e,"m",(function(){return T})),o.d(e,"l",(function(){return A}));var n=o("b775");function r(t){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function a(t){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function i(t){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function m(t,e,o,r,a,c,s,u){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(a,"&fixVersion=").concat(e,"&suffixVersion=").concat(o,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(s),method:"post",data:u})}function v(t){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function j(t,e,o,r){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(o,"&overrideNamespace=").concat(r),method:"post"})}function y(t,e,o,r,a){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(o,"&remark=").concat(r,"&dryRun=").concat(a),method:"post"})}function O(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function k(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function C(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function w(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function R(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function S(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function V(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function $(t,e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function F(t){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function z(t){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function P(t){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function B(t,e,o){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(o),method:"get"})}function q(t){return Object(n["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function T(t,e,o){return Object(n["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(o),method:"get"})}function A(){return Object(n["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}}}]);