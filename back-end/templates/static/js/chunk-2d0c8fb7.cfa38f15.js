(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c8fb7"],{"56cc":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-progress",{attrs:{"text-inside":!0,"stroke-width":24,percentage:t.progress,status:"success"}}),t._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"app",label:"应用",width:"180"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"180"}})],1)],1)},n=[],o={name:"clone",data:function(){return{progress:0,tableData:[],timerId:null}},mounted:function(){var t=this;setTimeout((function(){t.loadData()}),10)},beforeDestroy:function(){clearTimeout(this.timerId),console.log("clear timer, id:"+this.timerId)},methods:{loadData:function(){this.$message.info("todo...")}}},r=o,i=a("2877"),l=Object(i["a"])(r,s,n,!1,null,"698b8c0d",null);e["default"]=l.exports}}]);