(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cb89a6f6"],{"0d8f":function(t,e,r){},"2f21":function(t,e,r){"use strict";var a=r("79e5");t.exports=function(t,e){return!!t&&a((function(){e?t.call(null,(function(){}),1):t.call(null)}))}},"55dd":function(t,e,r){"use strict";var a=r("5ca1"),n=r("d8e8"),s=r("4bf8"),c=r("79e5"),i=[].sort,o=[1,2,3];a(a.P+a.F*(c((function(){o.sort(void 0)}))||!c((function(){o.sort(null)}))||!r("2f21")(i)),"Array",{sort:function(t){return void 0===t?i.call(s(this)):i.call(s(this),n(t))}})},"6c5b":function(t,e,r){"use strict";r("0d8f")},8504:function(t,e,r){"use strict";r.d(e,"g",(function(){return n})),r.d(e,"a",(function(){return s})),r.d(e,"h",(function(){return c})),r.d(e,"c",(function(){return i})),r.d(e,"b",(function(){return o})),r.d(e,"i",(function(){return l})),r.d(e,"d",(function(){return u})),r.d(e,"f",(function(){return h})),r.d(e,"e",(function(){return p}));var a=r("b775");function n(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function s(t,e,r){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:r}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function i(t,e,r){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:r}})}function o(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,r){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:r}})}function h(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function p(t,e,r,n,s){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:r,revision:n,deployTag:s||""}})}},b689:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("el-form",{ref:"searchForm",attrs:{inline:!0,model:t.searchForm}},[r("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[r("el-select",{attrs:{placeholder:"选择k8s集群"},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(t.clusterOptions,(function(t){return r("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[r("el-select",{attrs:{placeholder:"选择Namespace"},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(t.namespaceOptions,(function(t){return r("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:t.onSearch}},[t._v("查询")])],1)],1),t._v(" "),r("div",{style:{width:t.chartSize.width,height:t.chartSize.height,margin:"10px"},attrs:{id:"my-chart"}})],1)},n=[],s=(r("55dd"),r("7f7f"),r("8504")),c=r("313e"),i={data:function(){return{searchForm:{cluster:"",namespace:"",app:""},chartSize:{width:"100%",height:"100%"},chart:null}},mounted:function(){this.initChart(),this.clusterOptions.length>0&&(this.searchForm.cluster=this.clusterOptions[0].name),this.namespaceOptions.length>0&&(this.searchForm.namespace=this.namespaceOptions[0]),this.searchForm.cluster&&this.searchForm.namespace},computed:{clusterOptions:function(){return this.$settings.clusters?this.$settings.clusters:[]},namespaceOptions:function(){if(this.searchForm.cluster)for(var t in this.$settings.clusters){var e=this.$settings.clusters[t];if(this.searchForm.cluster===e.name)return e.namespaces}return[]}},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart:function(){this.chart=c.init(document.getElementById("my-chart")),this.chart.setOption({title:{text:"应用内存分配(单位：MB)",subtext:"数据刷新于：--",right:"center"},color:["#0096f3","#34da62"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["最高内存","请求内存"],right:"10%"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",inverse:!0,data:[]},series:[{name:"最高内存",type:"bar",barGap:"0",label:{show:!0,position:"inside"},data:[]},{name:"请求内存",type:"bar",label:{show:!0,position:"inside"},data:[]}]})},loadData:function(t,e){var r=this;this.chart.showLoading(),Object(s["g"])(t,e).then((function(t){var e=t.data,a=[],n=[],s=[];e.sort((function(t,e){return e.limitMemory-t.limitMemory}));for(var c=0;c<e.length;c++){var i=e[c];i.replicas<1||(a.push(i.name),n.push(Math.floor(i.limitMemory/1024/1024)),s.push(Math.floor(i.requestMemory/1024/1024)))}r.chart.resize({height:30*a.length}),r.chart.setOption({yAxis:{data:a},series:[{name:"最高内存",data:n},{name:"请求内存",data:s}]})})).catch((function(){console.log("query app fail!")})).then((function(){r.chart.hideLoading()}))},onSearch:function(){this.fetchData()},fetchData:function(){var t=this;this.$refs["searchForm"].validate((function(e){e&&t.loadData(t.searchForm.cluster,t.searchForm.namespace)}))}}},o=i,l=(r("6c5b"),r("2877")),u=Object(l["a"])(o,a,n,!1,null,"6730354e",null);e["default"]=u.exports}}]);