(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-338b6501"],{"25fc":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"namespace-selector-wrapper"},[a("el-form-item",{attrs:{label:e.namespaceLabel,prop:"namespace"}},[a("el-select",{attrs:{placeholder:"",filterable:"",clearable:""},model:{value:e.namespace,callback:function(t){e.namespace=t},expression:"namespace"}},[e.includeAllOption?a("el-option",{attrs:{label:"所有",value:""}}):e._e(),e._v(" "),e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1)],1)},l=[],r=(a("6762"),a("2d63")),i={name:"NamespaceSelector",props:{namespaceLabel:{type:String,default:"运行环境"},includeAllOption:{type:Boolean,default:!1}},data:function(){return{namespace:""}},mounted:function(){},computed:{namespaceOptions:function(){var e,t=[],a=Object(r["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n,l=e.value,i=Object(r["a"])(l.namespaces);try{for(i.s();!(n=i.n()).done;){var s=n.value;t.includes(s)||t.push(s)}}catch(o){i.e(o)}finally{i.f()}}}catch(o){a.e(o)}finally{a.f()}return t}}},s=i,o=(a("95f5"),a("2877")),c=Object(o["a"])(s,n,l,!1,null,null,null);t["a"]=c.exports},6762:function(e,t,a){"use strict";var n=a("5ca1"),l=a("c366")(!0);n(n.P,"Array",{includes:function(e){return l(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"6d2d":function(e,t,a){},"84d4":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cicd-menu-tabs"},[a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.handleClick},model:{value:e.currTab,callback:function(t){e.currTab=t},expression:"currTab"}},[a("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),e._v("应用发布")],1)]),e._v(" "),a("el-tab-pane",{attrs:{name:"app-deploy-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),e._v("发布记录")],1)]),e._v(" "),a("el-tab-pane",{attrs:{name:"image-build"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),e._v("镜像构建")],1)]),e._v(" "),a("el-tab-pane",{attrs:{name:"image-build-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),e._v("构建记录")],1)]),e._v(" "),a("el-tab-pane",{attrs:{name:"image-list"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),e._v("镜像列表")],1)]),e._v(" "),a("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[a("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),e._v(" "),a("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[e._v("查看使用手册")])],1)])],1)],1)},l=[],r=(a("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(e,t){"app-deploy"===e.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===e.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===e.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===e.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===e.name?this.$router.push({name:"cicd-image-list"}):"doc"===e.name||this.$message.error("未知操作")}}}),i=r,s=(a("df01"),a("2877")),o=Object(s["a"])(i,n,l,!1,null,null,null);t["a"]=o.exports},"95f5":function(e,t,a){"use strict";a("e297")},b651:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container",staticStyle:{margin:"0",padding:"0"}},[a("div",{staticStyle:{padding:"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-form",{ref:"form1",staticClass:"demo-form-inline",attrs:{size:"small",inline:!0,model:e.searchForm,rules:e.rules},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"日期",prop:"recordDate"}},[a("el-date-picker",{attrs:{align:"right",type:"date",placeholder:"","value-format":"yyyy-MM-dd","default-value":e.defaultDate,"picker-options":e.pickerOptions},model:{value:e.searchForm.recordDate,callback:function(t){e.$set(e.searchForm,"recordDate",t)},expression:"searchForm.recordDate"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"",filterable:"",clearable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-input",{staticStyle:{width:"280px"},attrs:{clearable:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app","string"===typeof t?t.trim():t)},expression:"searchForm.app"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1)],1)],1),e._v(" "),a("div",{staticStyle:{float:"right"}},[a("el-pagination",{attrs:{"current-page":e.searchForm.page,"page-size":e.searchForm.limit,"page-sizes":[20,50,100,200,1e3,5e3],"pager-count":5,layout:"total,sizes,prev,pager,next",total:e.tableData.count},on:{"size-change":e.pageSizeChange,"current-change":e.pageChange}})],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData.data,"element-loading-text":"数据加载中...","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用",prop:"App"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"Namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"Cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"版本记录时间",prop:"RecordDate"}}),e._v(" "),a("el-table-column",{attrs:{label:"最后发布时间",prop:"DeployTime"}}),e._v(" "),a("el-table-column",{attrs:{label:"最后发布人",prop:"DeployAuthor"}}),e._v(" "),a("el-table-column",{attrs:{label:"部署模块数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.DeployModules.length)+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"版本号",prop:"Version"}}),e._v(" "),a("el-table-column",{attrs:{label:"版本详情",prop:"",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.showVersionDetail(t.row)}}},[e._v("查看所有\n          ")])]}}])})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:e.versionDetail.title,visible:e.versionDetail.visible,width:"50%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.versionDetail,"visible",t)}}},[a("div",[a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[e._v(e._s(e.versionDetail.content))])])])],1)},l=[],r=(a("6762"),a("2d63")),i=a("84d4"),s=a("25fc"),o=a("c356"),c={name:"deploy-history",components:{NamespaceSelector:s["a"],MenuTabs:i["a"]},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"今天",onClick:function(e){e.$emit("pick",new Date)}},{text:"昨天",onClick:function(e){var t=new Date;t.setTime(t.getTime()-864e5),e.$emit("pick",t)}},{text:"一周前",onClick:function(e){var t=new Date;t.setTime(t.getTime()-6048e5),e.$emit("pick",t)}},{text:"两周前",onClick:function(e){var t=new Date;t.setTime(t.getTime()-12096e5),e.$emit("pick",t)}},{text:"一月前",onClick:function(e){var t=new Date;t.setTime(t.getTime()-2592e6),e.$emit("pick",t)}}]},defaultDate:new Date,tableData:{data:[],count:0},artifacts:[],tableLoading:!1,searchForm:{app:"",namespace:"",recordDate:"",page:1,limit:20},versionDetail:{title:"",content:"",visible:!1},rules:{namespace:[{required:!0}],recordDate:[{required:!0}]}}},computed:{namespaceOptions:function(){var e,t=[],a=Object(r["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n,l=e.value,i=Object(r["a"])(l.namespaces);try{for(i.s();!(n=i.n()).done;){var s=n.value;t.includes(s)||t.push(s)}}catch(o){i.e(o)}finally{i.f()}}}catch(o){a.e(o)}finally{a.f()}return t}},mounted:function(){},methods:{loadTableData:function(){var e=this;this.$refs["form1"].validate((function(t){if(!t)return!1;e.tableLoading=!0,Object(o["a"])(e.searchForm).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))}))},pageChange:function(e){this.searchForm.page=e,this.loadTableData()},pageSizeChange:function(e){this.searchForm.limit=e,this.loadTableData()},showVersionDetail:function(e){try{this.versionDetail.title="".concat(e.RecordDate," / ").concat(e.Cluster," / ").concat(e.Namespace," / ").concat(e.App," "),this.versionDetail.content=JSON.stringify(e.DeployModules,null,2),this.versionDetail.visible=!0}catch(t){console.log("json parse fail")}}}},p=c,u=a("2877"),m=Object(u["a"])(p,n,l,!1,null,null,null);t["default"]=m.exports},c356:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i}));var n=a("b775");function l(e){return Object(n["a"])({url:"/operation/app-version-history",method:"get",params:e})}function r(){return Object(n["a"])({url:"/operation/cmdb-owner",method:"get"})}function i(){return Object(n["a"])({url:"/operation/cmdb-sync",method:"get"})}},df01:function(e,t,a){"use strict";a("6d2d")},e297:function(e,t,a){}}]);