(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-53d27e0d"],{b5a9:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-alert",{attrs:{title:"服务批量重启",type:"info",description:"批量重启：创建任务 → 开始；任务间隔时间1分钟","show-icon":""}}),t._v(" "),n("div",{staticStyle:{margin:"10px"}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){t.appRestartVisible=!0}}},[t._v("创建任务")]),t._v(" "),t.job&&t.job.items?n("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===t.job.status?n("el-button",{attrs:{type:"text",size:"small"},on:{click:t.start}},[t._v("开始")]):t._e(),t._v(" "),1===t.job.status?n("el-button",{attrs:{type:"text",size:"small"},on:{click:t.stop}},[t._v("暂停")]):t._e(),t._v(" "),n("el-button",{attrs:{type:"text",size:"small"},on:{click:t.remove}},[t._v("删除")])],1):t._e()],1),t._v(" "),t.job&&t.job.items?n("div",[n("div",{staticStyle:{margin:"20px"}},[t._v("任务状态： "+t._s(t.job.status))]),t._v(" "),n("el-timeline",{attrs:{reverse:!0}},t._l(t.job.items,(function(e,o){return n("el-timeline-item",{key:o,attrs:{color:e.output.endsWith("success")?"#67C23A":e.output.includes("fail,")?"#F56C6C":"#909399"}},[n("b",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(o+1))]),t._v(t._s(e.output)+"\n      ")])})),1)],1):n("div",{staticStyle:{margin:"20px"}},[t._v("\n    -无任何数据-\n  ")]),t._v(" "),n("el-dialog",{attrs:{title:"服务批量重启",visible:t.appRestartVisible},on:{"update:visible":function(e){t.appRestartVisible=e}}},[n("el-form",[n("el-form-item",{attrs:{label:"服务列表","label-width":"100"}},[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:t.appRestartForm,callback:function(e){t.appRestartForm=e},expression:"appRestartForm"}})],1),t._v(" "),n("el-form-item",{attrs:{label:""}},[n("div",{staticStyle:{width:"100%","line-height":"normal"}},[t._v("\n          内容为json格式，每行格式为 cluster/namespace/app，示例：\n          "),n("div",{staticStyle:{"background-color":"#eee",padding:"10px","margin-top":"10px"}},[t._v("\n            ["),n("br"),t._v('\n              "k8s1/fstest/fs-aaa-bbb",'),n("br"),t._v('\n              "k8s1/fstest/fs-k8s-tomcat-test",'),n("br"),t._v('\n              "k8s1/firstshare/fs-k8s-tomcat-test"'),n("br"),t._v("\n            ]\n          ")])])])],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.appRestartVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.createAppRestartJob}},[t._v("确 定")])],1)],1)],1)},r=[],a=n("c1ab"),c={components:{},mounted:function(){this.loadAppRestartOutput();var t=this.loadAppRestartOutput;this.timer=setInterval((function(){t()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,appRestartForm:""}},methods:{loadAppRestartOutput:function(){var t=this;Object(a["c"])().then((function(e){t.job=e.data})).catch((function(e){t.$message.error(e.message)}))},createAppRestartJob:function(){var t=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(a["f"])(t.appRestartForm).then((function(e){t.appRestartVisible=!1,t.$message.success("Success"),t.loadAppRestartOutput()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消批量重启")}))},start:function(){var t=this;Object(a["E"])(this.appRestartForm).then((function(e){t.$message.success("Success")})).catch((function(e){t.$message.error(e.message)}))},stop:function(){var t=this;Object(a["G"])(this.appRestartForm).then((function(e){t.$message.success("Success")})).catch((function(e){t.$message.error(e.message)}))},remove:function(){var t=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(a["y"])(t.appRestartForm).then((function(e){t.$message.success("Success")})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){console.log("取消")}))}}},s=c,u=n("2877"),i=Object(u["a"])(s,o,r,!1,null,null,null);e["default"]=i.exports},c1ab:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"j",(function(){return a})),n.d(e,"z",(function(){return c})),n.d(e,"A",(function(){return s})),n.d(e,"c",(function(){return u})),n.d(e,"f",(function(){return i})),n.d(e,"E",(function(){return l})),n.d(e,"G",(function(){return p})),n.d(e,"y",(function(){return d})),n.d(e,"a",(function(){return f})),n.d(e,"e",(function(){return m})),n.d(e,"D",(function(){return b})),n.d(e,"F",(function(){return v})),n.d(e,"x",(function(){return h})),n.d(e,"H",(function(){return g})),n.d(e,"k",(function(){return j})),n.d(e,"d",(function(){return O})),n.d(e,"B",(function(){return y})),n.d(e,"i",(function(){return _})),n.d(e,"g",(function(){return k})),n.d(e,"s",(function(){return R})),n.d(e,"v",(function(){return x})),n.d(e,"w",(function(){return w})),n.d(e,"o",(function(){return $})),n.d(e,"p",(function(){return S})),n.d(e,"t",(function(){return F})),n.d(e,"u",(function(){return A})),n.d(e,"b",(function(){return C})),n.d(e,"q",(function(){return V})),n.d(e,"r",(function(){return z})),n.d(e,"n",(function(){return B})),n.d(e,"C",(function(){return q})),n.d(e,"m",(function(){return J})),n.d(e,"l",(function(){return T}));var o=n("b775");function r(t){return Object(o["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function a(t){return Object(o["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(o["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function s(t){return Object(o["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function u(){return Object(o["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function i(t){return Object(o["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(o["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function m(t,e,n,r,a,c,s,u){return Object(o["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(a,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(s),method:"post",data:u})}function b(t){return Object(o["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function v(t){return Object(o["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(o["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(o["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function j(t,e,n,r){return Object(o["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function O(t,e,n,r,a){return Object(o["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(a),method:"post"})}function y(){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function k(t){return Object(o["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function R(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function x(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function w(t){return Object(o["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function $(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function S(t){return Object(o["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function F(t){return Object(o["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function A(t,e){return Object(o["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function C(t){return Object(o["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function V(t){return Object(o["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function z(t){return Object(o["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function B(t,e,n){return Object(o["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}function q(t){return Object(o["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function J(t,e,n){return Object(o["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(n),method:"get"})}function T(){return Object(o["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}}}]);