(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12a0dfa5"],{"1e42":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},n=[],l=(a("a481"),a("25ca")),s=a("21a6"),o=a.n(s),i={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=l["a"].table_to_book(t,{raw:!0}),a=l["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var r=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";o.a.saveAs(new Blob([a],{type:"application/octet-stream"}),r)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=i,u=a("2877"),p=Object(u["a"])(c,r,n,!1,null,null,null);e["a"]=p.exports},"25fc":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"namespace-selector-wrapper"},[a("el-form-item",{attrs:{label:t.namespaceLabel,prop:"namespace"}},[a("el-select",{attrs:{placeholder:"",filterable:"",clearable:""},model:{value:t.namespace,callback:function(e){t.namespace=e},expression:"namespace"}},[t.includeAllOption?a("el-option",{attrs:{label:"所有",value:""}}):t._e(),t._v(" "),t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}))],2)],1)],1)},n=[],l=(a("6762"),a("2d63")),s={name:"NamespaceSelector",props:{namespaceLabel:{type:String,default:"运行环境"},includeAllOption:{type:Boolean,default:!1}},data:function(){return{namespace:""}},mounted:function(){},computed:{namespaceOptions:function(){var t,e=[],a=Object(l["a"])(this.$settings.clusters);try{for(a.s();!(t=a.n()).done;){var r,n=t.value,s=Object(l["a"])(n.namespaces);try{for(s.s();!(r=s.n()).done;){var o=r.value;e.includes(o)||e.push(o)}}catch(i){s.e(i)}finally{s.f()}}}catch(i){a.e(i)}finally{a.f()}return e}}},o=s,i=(a("95f5"),a("2877")),c=Object(i["a"])(o,r,n,!1,null,null,null);e["a"]=c.exports},6762:function(t,e,a){"use strict";var r=a("5ca1"),n=a("c366")(!0);r(r.P,"Array",{includes:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"6d2d":function(t,e,a){},"76fe":function(t,e,a){"use strict";a.d(e,"k",(function(){return n})),a.d(e,"i",(function(){return l})),a.d(e,"a",(function(){return s})),a.d(e,"e",(function(){return o})),a.d(e,"b",(function(){return i})),a.d(e,"f",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return d})),a.d(e,"d",(function(){return m})),a.d(e,"l",(function(){return b})),a.d(e,"j",(function(){return f}));var r=a("b775");function n(t){return Object(r["a"])({url:"/v1/job/search",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function s(t){return Object(r["a"])({url:"/v1/job/build-image",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function c(t,e,a,n){return Object(r["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:n}})}function u(t,e,a,n){return Object(r["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:n}})}function p(t){return Object(r["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(r["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(r["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function b(t,e){return Object(r["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function f(t){return Object(r["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},"84d4":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cicd-menu-tabs"},[a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.currTab,callback:function(e){t.currTab=e},expression:"currTab"}},[a("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),t._v("应用发布")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"app-deploy-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("发布记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像构建")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("构建记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-list"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像列表")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[a("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v(" "),a("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[t._v("查看使用手册")])],1)])],1)],1)},n=[],l=(a("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(t,e){"app-deploy"===t.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===t.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===t.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===t.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===t.name?this.$router.push({name:"cicd-image-list"}):"doc"===t.name||this.$message.error("未知操作")}}}),s=l,o=(a("df01"),a("2877")),i=Object(o["a"])(s,r,n,!1,null,null,null);e["a"]=i.exports},"8a6c":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container",staticStyle:{margin:"0",padding:"0"}},[a("menu-tabs",{attrs:{"tab-name":"app-deploy-history"}}),t._v(" "),a("div",{staticStyle:{padding:"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{size:"small",inline:!0,model:t.searchForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"发布状态"}},[a("el-select",{staticStyle:{width:"190px"},attrs:{multiple:"",filterable:""},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[a("el-option",{attrs:{label:"运行中",value:"RUNNING"}}),t._v(" "),a("el-option",{attrs:{label:"已取消",value:"CANCEL"}}),t._v(" "),a("el-option",{attrs:{label:"等待中",value:"WAIT"}}),t._v(" "),a("el-option",{attrs:{label:"成功",value:"SUCCESS"}}),t._v(" "),a("el-option",{attrs:{label:"失败",value:"FAIL"}})],1)],1),t._v(" "),a("namespace-selector",{ref:"nsSelector",attrs:{"namespace-label":"环境"}}),t._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{staticStyle:{width:"280px"},attrs:{clearable:""},model:{value:t.searchForm.params.app,callback:function(e){t.$set(t.searchForm.params,"app","string"===typeof e?e.trim():e)},expression:"searchForm.params.app"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"操作人"}},[a("el-input",{staticStyle:{width:"140px"},attrs:{clearable:""},model:{value:t.searchForm.author,callback:function(e){t.$set(t.searchForm,"author","string"===typeof e?e.trim():e)},expression:"searchForm.author"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("div",{staticStyle:{float:"right"}},[a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,"page-sizes":[10,20,30,40,50,100,500,1e3,2e3],"pager-count":5,layout:"total,sizes,prev,pager,next",total:t.tableData.count},on:{"size-change":t.pageSizeChange,"current-change":t.pageChange}})],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData.data,"element-loading-text":"数据加载中...","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用",prop:"params.app"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{staticStyle:{color:"#409EFF"},attrs:{to:{name:"cicd-app-deploy-detail",query:{jobId:e.row.id}},target:"_blank"}},[t._v("\n            "+t._s(e.row.params.app)+"\n          ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"版本号",prop:"params.tag"}}),t._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"params.namespace"}}),t._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"params.cluster"}}),t._v(" "),a("el-table-column",{attrs:{label:"耗时（秒）",width:"100",prop:"timeCost"}}),t._v(" "),a("el-table-column",{attrs:{label:"备注",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作人",prop:"author",width:"130"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["SUCCESS"===e.row.status?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("成功")])],1):"FAIL"===e.row.status?a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("失败")])],1):"CANCEL"===e.row.status?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("已取消")])],1):"WAIT"===e.row.status?a("div",[a("el-tag",{attrs:{type:"info",effect:"plain"}},[t._v("等待中...")]),t._v(" "),a("div",{staticStyle:{"font-size":"12px",color:"#409EFF"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"前置任务完成后，才会启动当前任务",placement:"top"}},[a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.jobDetailPage(e.row.beforeJobId)}}},[t._v("\n                  查看前置任务\n                ")])],1)],1)],1):"RUNNING"===e.row.status?a("div",[a("el-tag",{attrs:{type:"info",effect:"plain"}},[t._v("运行中...")])],1):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createdAt",width:"100"}})],1)],1)],1)},n=[],l=a("76fe"),s=a("84d4"),o=a("25fc"),i=a("1e42"),c={name:"deploy-history",components:{ExportButton:i["a"],NamespaceSelector:o["a"],MenuTabs:s["a"]},data:function(){return{tableData:{data:[],count:0},artifacts:[],tableLoading:!1,searchForm:{type:"CD",author:null,params:{app:"",namespace:""},status:[],page:1,limit:20}}},computed:{},mounted:function(){null==this.searchForm.author&&this.$store.state.user&&this.$store.state.user.realName&&(this.searchForm.author=this.$store.state.user.realName),this.$route.query.app&&(this.searchForm.params.app=this.$route.query.app),this.$route.query.namespace&&(this.$refs.nsSelector.namespace=this.$route.query.namespace),this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,this.searchForm.params.namespace=this.$refs.nsSelector.namespace,Object(l["k"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pageChange:function(t){this.searchForm.page=t,this.loadTableData()},pageSizeChange:function(t){this.searchForm.limit=t,this.loadTableData()},jobDetailPage:function(t){var e=this;Object(l["g"])(t).then((function(a){var r=a.data,n="CD"===r.type?"cicd-app-deploy-detail":"cicd-image-build-detail",l=e.$router.resolve({name:n,query:{jobId:t}});window.open(l.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))}}},u=c,p=a("2877"),d=Object(p["a"])(u,r,n,!1,null,null,null);e["default"]=d.exports},"95f5":function(t,e,a){"use strict";a("e297")},df01:function(t,e,a){"use strict";a("6d2d")},e297:function(t,e,a){}}]);