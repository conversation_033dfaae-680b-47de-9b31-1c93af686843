(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-965839c4"],{"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return l})),a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return s})),a.d(t,"l",(function(){return u})),a.d(t,"m",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"f",(function(){return c})),a.d(t,"i",(function(){return p})),a.d(t,"j",(function(){return d})),a.d(t,"k",(function(){return f})),a.d(t,"n",(function(){return m})),a.d(t,"g",(function(){return h})),a.d(t,"b",(function(){return g})),a.d(t,"h",(function(){return b})),a.d(t,"o",(function(){return v}));var r=a("b775");function l(e){return Object(r["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function s(e,t,a){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function u(e){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function o(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function c(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function m(e){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function h(e,t,a,l){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:l}})}function g(e){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function b(e,t,a,l){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:l}})}function v(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},8964:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticStyle:{float:"right",right:"10px"}},[a("el-button",{attrs:{type:"text"},on:{click:e.statusPage}},[e._v("发布状态页")])],1),e._v(" "),a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"专属云环境",prop:"sourceCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"源集群"},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.dedicatedClusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"参考环境",prop:"targetCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"目标集群"},model:{value:e.form.targetCluster,callback:function(t){e.$set(e.form,"targetCluster",t)},expression:"form.targetCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("查询")])],1)],1),e._v(" "),a("el-row",{staticStyle:{margin:"5px"},attrs:{type:"flex",justify:"space-between"}},[a("el-col",{attrs:{span:12}},[a("el-select",{on:{change:e.tagChange},model:{value:e.tagValue,callback:function(t){e.tagValue=t},expression:"tagValue"}},e._l(e.tagOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:12}},[a("el-button",{attrs:{type:"primary"},on:{click:e.batchPublish}},[e._v("使用 ["+e._s(this.currentTargetCluster.cluster)+"/"+e._s(this.currentTargetCluster.namespace)+"] 版本重发选择的应用")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchPublish("master")}}},[e._v("使用 [Master分支] 重发选择的应用")])],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"数据加载中...",border:"",fit:"","cell-style":e.cellStyle,"highlight-current-row":""},on:{"selection-change":e.handleClonePipelineTableSelectionChange}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",width:"120px",sortable:"",prop:"sourcePipeline.app"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境描述",width:"120px",sortable:"",prop:"sourcePipeline.extraAttr.clusterSummary"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",sortable:"",prop:"sourcePipeline.namespace"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?a("div",[a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击发布"}},[a("span",{on:{click:function(a){return e.showDeployDialog([t.row])}}},[a("el-link",{attrs:{type:"primary",underline:!1}},[a("b",[e._v(e._s(t.row.sourcePipeline.namespace))])]),e._v(" ("+e._s(t.row.sourcePipeline.cluster)+")\n            ")],1)])],1):a("div",[e._v("\n          "+e._s(t.row.sourcePipeline.namespace)+" ("+e._s(t.row.sourcePipeline.cluster)+")\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行版本",sortable:"",prop:"sourcePipeline.extraAttr.deployTag"}}),e._v(" "),a("el-table-column",{attrs:{label:"实例",align:"center"}},[a("el-table-column",{attrs:{label:"配置数",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"发布流程里配置的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.sourcePipeline.replicas))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行数",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.sourcePipeline.extraAttr.runningPodNum))])])]}}])})],1),e._v(" "),a("el-table-column",{attrs:{label:this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"环境"}},[a("el-table-column",{attrs:{label:"运行版本",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的版本",placement:"top"}},[a("span",[e._v(e._s(t.row.targetPipeline.extraAttr.deployTag))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行数",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.targetPipeline.extraAttr.runningPodNum))])])]}}])})],1)],1)],1)},l=[],n=(a("7f7f"),a("2d63")),s=a("51a9"),u={name:"dedicated-cloud-publish-helper",components:{},data:function(){var e=function(e,t,a){null===t.cluster&&null===t.namespace?a(new Error("请选择集群")):a()};return{tableData:[],tableDataTmp:[],tableLoading:!1,form:{sourceCluster:{cluster:null,namespace:null},targetCluster:{cluster:null,namespace:null}},currentTargetCluster:{cluster:"",namespace:""},tagValue:"all",pipelineTableMultipleSelection:[],rules:{sourceCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}],targetCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}]}}},mounted:function(){var e,t=Object(n["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a,r=e.value,l=Object(n["a"])(r.namespaces);try{for(l.s();!(a=l.n()).done;){var s=a.value;return this.form.targetCluster.cluster=r.name,this.form.targetCluster.namespace=s,this.form.targetCluster.id=0,void this.switchCluster(this.form.targetCluster.cluster,this.form.targetCluster.namespace)}}catch(u){l.e(u)}finally{l.f()}}}catch(u){t.e(u)}finally{t.f()}},computed:{clusterOptions:function(){var e,t=[],a=0,r=Object(n["a"])(this.$settings.clusters);try{for(r.s();!(e=r.n()).done;){var l,s=e.value,u=Object(n["a"])(s.namespaces);try{for(u.s();!(l=u.n()).done;){var i=l.value,o={};o.cluster=s.name,o.namespace=i,o.id=a,t.push(o),a++}}catch(c){u.e(c)}finally{u.f()}}}catch(c){r.e(c)}finally{r.f()}return t},dedicatedClusterOptions:function(){var e,t=[],a=0,r=Object(n["a"])(this.$settings.clusters);try{for(r.s();!(e=r.n()).done;){var l=e.value;if("fxiaokeCloud"!==l.cloudCategory){var s,u=Object(n["a"])(l.namespaces);try{for(u.s();!(s=u.n()).done;){var i=s.value,o={};o.cluster=l.name,o.namespace=i,o.id=a,t.push(o),a++}}catch(p){u.e(p)}finally{u.f()}}}}catch(p){r.e(p)}finally{r.f()}var c={cluster:"k8s1",namespace:"foneshare"};return c.id=this.$settings.clusters.length+1,t.push(c),t},tagOptions:function(){var e=[];return e.push({value:"all",label:"所有"}),e.push({value:"inconsistent",label:"[运行版本] 与 ["+this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"] 版本不一致"}),e.push({value:"isEmpty",label:"[ "+this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"] 版本为空"}),e}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(s["h"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.targetCluster.cluster,this.form.targetCluster.namespace).then((function(t){e.tableData=t.data,e.tableDataTmp=e.tableData,e.switchCluster(e.form.targetCluster.cluster,e.form.targetCluster.namespace)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},switchCluster:function(e,t){this.currentTargetCluster.cluster=e,this.currentTargetCluster.namespace=t},tagChange:function(e){switch(e){case"all":this.loadTableData();break;case"inconsistent":var t=[];for(var a in this.tableDataTmp)this.tableDataTmp[a].sourcePipeline.extraAttr.deployTag!==this.tableDataTmp[a].targetPipeline.extraAttr.deployTag&&t.push(this.tableDataTmp[a]);this.tableData=t;break;case"isEmpty":var r=[];for(var l in this.tableDataTmp)""===this.tableDataTmp[l].targetPipeline.extraAttr.deployTag&&r.push(this.tableDataTmp[l]);this.tableData=r;break}},cellStyle:function(e){var t=e.row,a=e.column;e.rowIndex,e.columnIndex;if(1===a.level&&"运行版本"===a.label){if(t.sourcePipeline.extraAttr.deployTag===t.targetPipeline.extraAttr.deployTag)return"color:green";if(""===t.targetPipeline.extraAttr.deployTag)return"color:green";if(t.sourcePipeline.extraAttr.deployTag!==t.targetPipeline.extraAttr.deployTag)return"color:red"}},handleClonePipelineTableSelectionChange:function(e){this.pipelineTableMultipleSelection=e},batchPublish:function(e){this.$message.info("todo...")},statusPage:function(){this.$router.push({name:"dedicated-cloud-publish-status"})}}},i=u,o=a("2877"),c=Object(o["a"])(i,r,l,!1,null,"08e6c4aa",null);t["default"]=c.exports}}]);