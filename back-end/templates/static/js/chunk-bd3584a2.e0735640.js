(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bd3584a2"],{"51a9":function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"d",(function(){return u})),n.d(t,"l",(function(){return l})),n.d(t,"m",(function(){return i})),n.d(t,"a",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"i",(function(){return p})),n.d(t,"j",(function(){return d})),n.d(t,"k",(function(){return b})),n.d(t,"n",(function(){return f})),n.d(t,"g",(function(){return m})),n.d(t,"b",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"o",(function(){return g}));var r=n("b775");function a(e){return Object(r["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function u(e,t,n){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:n}})}function l(e){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function s(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function f(e){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function m(e,t,n,a){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:a}})}function v(e){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function h(e,t,n,a){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:a}})}function g(e){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},a2eb:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"数据加载中...","default-sort":{prop:"createdTime",order:"descending"},border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),e._v(" "),n("el-table-column",{attrs:{label:"集群",sortable:"",prop:"cluster"}}),e._v(" "),n("el-table-column",{attrs:{label:"命名空间",sortable:"",prop:"namespace"}}),e._v(" "),n("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),n("el-table-column",{attrs:{label:"模块数",sortable:"",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.appModules.length)+"\n      ")]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["enabled"===t.row.status?n("el-tag",{attrs:{type:"success"}},[e._v("\n          "+e._s(e.convertStatus(t.row.status))+"\n        ")]):n("el-tag",{attrs:{type:"warning"}},[e._v("\n          "+e._s(e.convertStatus(t.row.status))+"\n        ")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"实例数",align:"center",prop:"replicas",sortable:""}}),e._v(" "),n("el-table-column",{attrs:{label:"CPU",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.resources?[e._v("\n        "+e._s(t.row.resources.requestCPU.toFixed(1))+" - "+e._s(t.row.resources.limitCPU.toFixed(1))+"\n      ")]:void 0}}],null,!0)}),e._v(" "),n("el-table-column",{attrs:{label:"内存 (MB)",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.resources?[e._v("\n        "+e._s(t.row.resources.requestMemory)+" - "+e._s(t.row.resources.limitMemory)+"\n      ")]:void 0}}],null,!0)}),e._v(" "),n("el-table-column",{attrs:{label:"创建人",property:"author",sortable:""}}),e._v(" "),n("el-table-column",{attrs:{label:"创建时间",prop:"createdTime",sortable:""}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"260px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.auditPage(t.row)}}},[e._v("审核\n        ")]),e._v(" "),n("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.removePipe(t.row)}}},[e._v("删除\n        ")]),e._v(" "),n("el-button",{attrs:{type:"text",icon:"el-icon-position"},on:{click:function(n){return e.pipelinePage(t.row)}}},[e._v("发布流程页\n        ")])]}}])})],1)],1)},a=[],o=n("51a9"),u={data:function(){return{tableData:[],tableLoading:!1}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(o["f"])("audit").then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},auditPage:function(e){this.$router.push({name:"app-pipeline-edit",query:{pipelineId:e.id,operate:"audit"}})},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},removePipe:function(e){var t=this;this.$confirm("确认要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["k"])(e.id).then((function(e){t.$message.success("删除成功"),t.loadTableData(t.$route.query.app)})).catch((function(e){t.$message.error(e.message)}))}))},convertStatus:function(e){switch(e){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}}}},l=u,i=n("2877"),c=Object(i["a"])(l,r,a,!1,null,null,null);t["default"]=c.exports}}]);