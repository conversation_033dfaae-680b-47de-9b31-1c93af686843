(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-60cdb104","chunk-23e2cc90","chunk-2c04aaf0","chunk-5b55c780","chunk-1377be10","chunk-271072c5","chunk-f60660c6","chunk-53d27e0d","chunk-52428f3e","chunk-612ffcc5","chunk-965839c4","chunk-768b48b7","chunk-2d22bf82"],{"13fe":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("el-row",{staticStyle:{"max-width":"1480px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form",[a("el-form-item",{attrs:{label:"请输入参数"}},[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:12,maxRows:12}},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("提 交")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form",[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"参数格式模版（CPU单位：m，内存单位：Mi)"}}),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:""}},[a("div",{staticStyle:{"background-color":"#eee",overflow:"auto",height:"270px"}},[a("pre",{staticStyle:{"line-height":"normal"}},[e._v('[\n  {\n    "cluster": "k8s0",\n    "namespace": "fstest",\n    "app": "fs-k8s-tomcat-test",\n    "requestCPU": 200,\n    "limitCPU": 1000,\n    "requestMemory": -1,\n    "limitMemory": -1,\n    "replicas": -1\n  },\n  {\n    "cluster": "k8s0",\n    "namespace": "fstest",\n    "app": "fs-app-test",\n    "requestCPU": 100,\n    "limitCPU": 500,\n    "requestMemory": 128,\n    "limitMemory": 512,\n    "replicas": -1\n  },\n\n  }\n]\n            ')])])])],1)],1)],1),e._v(" "),e.result?a("div",{staticStyle:{"margin-top":"20px","max-width":"1480px"}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("操作结果")])]),e._v(" "),a("div",[a("pre",{staticStyle:{"font-size":"12px"}},[e._v(e._s(this.result))])])])],1):e._e()],1)},r=[],o=a("c1ab"),l={components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{form:"",result:"",pageLoading:!1}},methods:{submit:function(){var e=this;this.$confirm("确定要继续修改发布流程资源吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pageLoading=!0,Object(o["t"])(e.form).then((function(t){e.result=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))})).catch((function(){}))}}},s=l,i=a("2877"),c=Object(i["a"])(s,n,r,!1,null,null,null);t["default"]=c.exports},2861:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{ref:"searchForm",staticStyle:{height:"50px"},attrs:{inline:!0,model:e.searchForm}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群"},on:{change:e.loadApp},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择Namespace"},on:{change:e.loadApp},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-select",{staticStyle:{width:"340px"},attrs:{filterable:"",placeholder:"选择应用"},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},e._l(this.appOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.addApp}},[e._v("添加")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.addAllApp}},[e._v("添加所有")])],1)],1),e._v(" "),a("el-table",{attrs:{data:this.tableData,"row-key":"name"}},[a("el-table-column",{attrs:{label:"应用名",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace"}})],1),e._v(" "),a("div",{staticStyle:{"padding-top":"10px","text-align":"center"}},[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.yamlExportBtnLoading,expression:"yamlExportBtnLoading"}],attrs:{type:"primary",size:"small",icon:"el-icon-view"},on:{click:e.printYaml}},[e._v("查看表格服务Yaml")])],1),e._v(" "),a("el-dialog",{attrs:{title:"yaml内容",visible:e.yamlResultShow,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(t){e.yamlResultShow=t}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",size:"small",icon:"el-icon-document-copy"},on:{click:e.copyToClipboard}},[e._v("一键复制")]),e._v(" "),a("pre",{staticStyle:{"white-space":"pre-wrap",border:"1px solid #eee",padding:"5px","margin-top":"5px"}},[e._v(e._s(this.yamlResult))])],1)])],1)},r=[],o=(a("7f7f"),a("2d63")),l=a("8504"),s=a("c1ab"),i={components:{},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:""},appOptions:[],tableData:[],yamlResultShow:!1,yamlResult:"",yamlExportBtnLoading:!1}},methods:{loadApp:function(){var e=this.searchForm.cluster,t=this.searchForm.namespace;if(e&&t){var a=this;Object(l["g"])(e,t).then((function(e){a.appOptions=e.data})).catch((function(e){console.error(e)}))}else this.appOptions=[]},addApp:function(){var e=this.searchForm.cluster,t=this.searchForm.namespace,a=this.searchForm.app;if(a){var n,r="".concat(e,"@@").concat(t,"@@").concat(a),l=Object(o["a"])(this.tableData);try{for(l.s();!(n=l.n()).done;){var s=n.value;if(s.key===r)return void this.$message.warning("服务已存在")}}catch(c){l.e(c)}finally{l.f()}var i={key:r,cluster:e,namespace:t,app:a};this.tableData.push(i)}else this.$message.warning("请选择服务")},addAllApp:function(){this.$message.warning("todo")},printYaml:function(){var e=this;if(this.tableData.size<1)this.$message.warning("请先添加服务");else{this.yamlExportBtnLoading=!0,this.yamlResult="";var t=JSON.stringify(this.tableData);Object(s["H"])(t).then((function(t){e.yamlResult=t.data,e.yamlResultShow=!0})).catch((function(e){console.error(e)})).finally((function(){e.yamlExportBtnLoading=!1}))}},copyToClipboard:function(){var e=this,t=this.yamlResult;t?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",t),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),e.$message.success("复制成功")})):this.$message.warning("内容为空")}}},c=i,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);t["default"]=p.exports},"3edd":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[e._v("\n  todo\n")])},r=[],o=a("a68b"),l={components:{appManageTab:o["default"]},mounted:function(){},computed:{},data:function(){return{}},methods:{}},s=l,i=a("2877"),c=Object(i["a"])(s,n,r,!1,null,null,null);t["default"]=c.exports},"4f2b":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("el-alert",{attrs:{title:"应用批量构建",type:"info",description:"使用当前运行的版本重新构建镜像。","show-icon":""}}),e._v(" "),a("el-form",{staticStyle:{"max-width":"800px"},attrs:{"label-width":"120px"}},[a("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"查询条件"}},[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:e.searchForm,callback:function(t){e.searchForm=t},expression:"searchForm"}})],1),e._v(" "),a("el-form-item",{staticStyle:{margin:"3px"},attrs:{label:"构建备注"}},[a("el-input",{model:{value:e.buildRemark,callback:function(t){e.buildRemark=t},expression:"buildRemark"}})],1),e._v(" "),a("el-form-item",{staticStyle:{margin:"3px","text-align":"right"}},[a("el-button",{staticStyle:{margin:"3px"},attrs:{type:"primary",size:"small"},on:{click:e.loadTableData}},[e._v("查询")])],1)],1),e._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("el-table",{attrs:{data:e.tableData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"构建过？",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.extraAttr.reDeployed)+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.reBuild(t.row)}}},[e._v("使用当前版本构建\n            ")]),e._v(" "),a("br"),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-image-build-history",query:{}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n                历史\n              ")])],1),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:t.row.app}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n                发布流程\n              ")])],1)],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"app"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[e._v(e._s(t.row.app))]),e._v(" "),a("div",{staticStyle:{size:"10px",color:"#999"}},[e._v(e._s(t.row.namespace)+" ("+e._s(t.row.cluster)+")")])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"100px"}}),e._v(" "),a("el-table-column",{attrs:{label:"模块数",prop:"status",width:"80px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.appModules.length))]),e._v(" "),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.contentShow(t.row)}}},[e._v("查看\n            ")])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"模块镜像"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[e._v(e._s(t.row.extraAttr.deployModuleImages))])])]}}])})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:e.contentDialog.title,visible:e.contentDialog.visible,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.contentDialog,"visible",t)}}},[a("div",[a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[e._v(e._s(this.contentDialog.content))])])])],1)},r=[],o=a("2d63"),l=a("51a9"),s=a("8504"),i=a("76fe"),c={components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,searchForm:"",buildRemark:"",tableData:[],contentDialog:{visible:!1,title:"",content:""}}},methods:{loadTableData:function(){var e=this;this.pageLoading=!0,this.tableData=[],Object(l["m"])(this.searchForm).then((function(t){var a,n=Object(o["a"])(t.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.extraAttr.deployModuleImages="?",r.extraAttr.reDeployed="NO",r.extraAttr.deployModules="?",e.findDeployment(r)}}catch(l){n.e(l)}finally{n.f()}e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))},findDeployment:function(e){null!==e?Object(s["c"])(e.cluster,e.namespace,e.app).then((function(t){if(t.data.spec.template.spec.initContainers.length>0){var a,n=[],r=Object(o["a"])(t.data.spec.template.spec.initContainers);try{for(r.s();!(a=r.n()).done;){var l=a.value;n.push(l.image)}}catch(s){r.e(s)}finally{r.f()}e.extraAttr.deployModuleImages=n.join("<br/>"),e.extraAttr.deployModules=t.data.spec.template.spec.initContainers}})).catch((function(e){console.log(e.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reBuild:function(e){var t=this;Object(i["c"])(e.cluster,e.namespace,e.app,this.buildRemark).then((function(a){t.$message.success("操作成功"),e.extraAttr.reDeployed="YES"})).catch((function(e){t.$message.error(e.message)}))},contentShow:function(e){this.contentDialog.content=e.extraAttr.deployModules,this.contentDialog.title="应用："+e.app,this.contentDialog.visible=!0}}},u=c,p=a("2877"),d=Object(p["a"])(u,n,r,!1,null,null,null);t["default"]=d.exports},"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"e",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"l",(function(){return s})),a.d(t,"m",(function(){return i})),a.d(t,"a",(function(){return c})),a.d(t,"f",(function(){return u})),a.d(t,"i",(function(){return p})),a.d(t,"j",(function(){return d})),a.d(t,"k",(function(){return m})),a.d(t,"n",(function(){return f})),a.d(t,"g",(function(){return v})),a.d(t,"b",(function(){return b})),a.d(t,"h",(function(){return h})),a.d(t,"o",(function(){return g}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function o(e){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function l(e,t,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function s(e){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function u(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function p(e){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function f(e){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function v(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function b(e){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function h(e,t,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:r}})}function g(e){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"5b06":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"模式",prop:"sourceCluster"}},[a("el-select",{model:{value:e.form.op,callback:function(t){e.$set(e.form,"op",t)},expression:"form.op"}},[a("el-option",{attrs:{label:"NodePort转Service",value:"nodeport-to-service"}}),e._v(" "),a("el-option",{attrs:{label:"Service短名转长名",value:"serviceShort-to-serviceLong"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境",prop:"sourceCluster"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{"value-key":"id",filterable:""},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[e._v("查询")])],1)],1)],1),e._v(" "),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.data,size:"mini"}},[a("el-table-column",{attrs:{prop:"name",label:"文件"}}),e._v(" "),a("el-table-column",{attrs:{prop:"profile",label:"配置组"}}),e._v(" "),a("el-table-column",{attrs:{prop:"editor",label:"最后修改人"}}),e._v(" "),a("el-table-column",{attrs:{label:"内容",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.cmsContentPreview(t.row)}}},[e._v("查看")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"addrs",label:"地址","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.repairAddrs,(function(n,r){return a("span",{staticStyle:{margin:"2px 5px",padding:"0 2px",border:"1px solid #e2e2e2",display:"inline-block"}},[e._v("\n            "+e._s(r)+"\n            "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.editCMS(t.row.id)}}},[e._v("修改")]),e._v(" "),a("el-tooltip",{attrs:{content:r+" -> "+n,placement:"right"}},[a("el-button",{staticStyle:{"margin-left":"5px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.batchEditCMS(t.row.profile,r,n)}}},[e._v("批量替换")])],1)],1)}))}}])})],1)],1),e._v(" "),a("div",[a("el-dialog",{attrs:{title:e.cmsPreview.name+" ("+e.cmsPreview.profile+")",visible:e.cmsPreview.visible,width:"50%"},on:{"update:visible":function(t){return e.$set(e.cmsPreview,"visible",t)}}},[a("div",{staticStyle:{"margin-top":"-40px",border:"1px solid #e2e2e2",padding:"5px","max-height":"600px",overflow:"auto"}},[a("pre",[e._v(e._s(e.cmsPreview.content))])])])],1)])},r=[],o=(a("7f7f"),a("2d63")),l=a("c1ab"),s={components:{},data:function(){return{form:{op:"nodeport-to-service",sourceCluster:{cluster:null,namespace:null}},data:[],loading:!1,cmsPreview:{visible:!1,name:"",profile:"",content:""}}},computed:{clusterOptions:function(){var e,t=[],a=0,n=Object(o["a"])(this.$settings.clusters);try{for(n.s();!(e=n.n()).done;){var r,l=e.value,s=Object(o["a"])(l.namespaces);try{for(s.s();!(r=s.n()).done;){var i=r.value,c={};c.cluster=l.name,c.namespace=i,c.id=a,t.push(c),a++}}catch(u){s.e(u)}finally{s.f()}}}catch(u){n.e(u)}finally{n.f()}return t}},methods:{loadData:function(){var e=this;this.form.sourceCluster.cluster&&this.form.sourceCluster.namespace?(this.loading=!0,Object(l["n"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.op).then((function(t){e.data=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))):this.$message.error("请选择环境")},editCMS:function(e){var t="/api/page/redirect?type=cmsEdit&cmsId=".concat(e,"&_t")+Date.now();window.open(t)},batchEditCMS:function(e,t,a){var n="/api/page/redirect?type=cmsBatchEdit&profile=".concat(e,"&oldContent=").concat(t,"&newContent=").concat(a,"&_t")+Date.now();window.open(n)},cmsContentPreview:function(e){this.cmsPreview.name=e.name,this.cmsPreview.profile=e.profile,this.cmsPreview.content=e.content,this.cmsPreview.visible=!0}}},i=s,c=a("2877"),u=Object(c["a"])(i,n,r,!1,null,"05a6500e",null);t["default"]=u.exports},6762:function(e,t,a){"use strict";var n=a("5ca1"),r=a("c366")(!0);n(n.P,"Array",{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"6a0a":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticStyle:{width:"600px",margin:"0 auto"}},[a("el-steps",{attrs:{"finish-status":"success"}},[a("el-step",{attrs:{title:"选择集群参数","process-status":"error"}}),e._v(" "),a("el-step",{attrs:{title:"开始克隆"}})],1),e._v(" "),a("el-button",{staticStyle:{"margin-top":"12px"},attrs:{loading:e.loading},on:{click:e.next}},[e._v(e._s(e.nextBtnText))])],1),e._v(" "),a("el-divider"),e._v(" "),"query-pipelines"===e.active?a("div",[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"从",prop:"sourceCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"源集群",filterable:""},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"克隆到",prop:"targetCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"目标集群",filterable:""},model:{value:e.form.targetCluster,callback:function(t){e.$set(e.form,"targetCluster",t)},expression:"form.targetCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1)],1)],1):e._e(),e._v(" "),"clone-pipelines"===e.active?a("div",[a("el-alert",{attrs:{title:e.form.sourceCluster.cluster+"/"+e.form.sourceCluster.namespace+"--\x3e"+e.form.targetCluster.cluster+"/"+e.form.targetCluster.namespace,type:"warning",center:""}}),e._v(" "),a("el-table",{ref:"multipleTable",attrs:{data:e.clonePipelineTableData,size:"mini"},on:{"selection-change":e.handleClonePipelineTableSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{property:"app",label:"所属应用"}}),e._v(" "),a("el-table-column",{attrs:{property:"status",label:"发布流程状态"}})],1)],1):e._e()],1)},r=[],o=(a("7f7f"),a("2d63")),l=a("51a9"),s={name:"clone-pipeline",components:{},data:function(){var e=function(e,t,a){null===t.cluster&&null===t.namespace?a(new Error("请选择集群")):a()};return{active:"query-pipelines",form:{sourceCluster:{cluster:null,namespace:null},targetCluster:{cluster:null,namespace:null}},rules:{sourceCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}],targetCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}]},clonePipelineTableData:[],clonePipelineTableMultipleSelection:[],loading:!1}},computed:{nextBtnText:function(){return"query-pipelines"===this.active?"查询发布流程":"clone-pipelines"===this.active?"克隆发布流程":"未知操作"},clusterOptions:function(){var e,t=[],a=0,n=Object(o["a"])(this.$settings.clusters);try{for(n.s();!(e=n.n()).done;){var r,l=e.value,s=Object(o["a"])(l.namespaces);try{for(s.s();!(r=s.n()).done;){var i=r.value,c={};c.cluster=l.name,c.namespace=i,c.id=a,t.push(c),a++}}catch(u){s.e(u)}finally{s.f()}}}catch(u){n.e(u)}finally{n.f()}return t}},methods:{next:function(){var e=this;switch(this.active){case"query-pipelines":this.$refs["ruleForm"].validate((function(t){if(!t)return!1;e.loading=!0,e.loadClonePipeline()}));break;case"clone-pipelines":if(this.clonePipelineTableMultipleSelection.length<=0)return void this.$message({message:"未选择克隆的应用",type:"warning"});this.loading=!0,this.clonePipeline()}},loadClonePipeline:function(){var e=this;Object(l["g"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.targetCluster.cluster,this.form.targetCluster.namespace).then((function(t){e.active="clone-pipelines",e.clonePipelineTableData=t.data,e.$nextTick((function(){e.$refs.multipleTable.toggleAllSelection()}))})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},handleClonePipelineTableSelectionChange:function(e){this.clonePipelineTableMultipleSelection=e},clonePipeline:function(){var e,t=this,a=[],n=Object(o["a"])(this.clonePipelineTableMultipleSelection);try{for(n.s();!(e=n.n()).done;){var r=e.value;a.push(r.id)}}catch(i){n.e(i)}finally{n.f()}var s={};s.ids=a,s.sourceCluster=this.form.sourceCluster.cluster,s.sourceNamespace=this.form.sourceCluster.namespace,s.targetCluster=this.form.targetCluster.cluster,s.targetNamespace=this.form.targetCluster.namespace,Object(l["b"])(s).then((function(e){t.$message.success("操作成功"),t.$alert('<pre style="overflow: auto;font-size: 10px;line-height:10px;">'+JSON.stringify(e.data,null,2)+"</pre>","操作成功，结果:",{dangerouslyUseHTMLString:!0})})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))}}},i=s,c=a("2877"),u=Object(c["a"])(i,n,r,!1,null,"b1f9966e",null);t["default"]=u.exports},"6b5c":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("div",[a("el-form",{attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{filterable:""},model:{value:e.form.cluster,callback:function(t){e.$set(e.form,"cluster",t)},expression:"form.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster,value:e}})})),1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[e._v("查询")])],1)],1)],1),e._v(" "),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.data}},[a("el-table-column",{attrs:{prop:"metadata.name",label:"Pod"}}),e._v(" "),a("el-table-column",{attrs:{prop:"cluster",label:"集群"}}),e._v(" "),a("el-table-column",{attrs:{prop:"namespace",label:"命名空间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"app",label:"应用"}}),e._v(" "),a("el-table-column",{attrs:{label:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.podPage(t.row.cluster,t.row.namespace,t.row.app)}}},[e._v("实例管理页")])]}}])})],1)],1)])},r=[],o=(a("a481"),a("7f7f"),a("2d63")),l=a("a527"),s={components:{},data:function(){return{form:{cluster:null},data:[],loading:!1}},computed:{clusterOptions:function(){var e,t=[],a=Object(o["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n=e.value;t.push(n.name)}}catch(r){a.e(r)}finally{a.f()}return t}},methods:{loadData:function(){var e=this;this.form.cluster?(this.loading=!0,Object(l["m"])(this.form.cluster).then((function(t){e.data=t.data;var a,n=Object(o["a"])(e.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.cluster=e.form.cluster,r.namespace=r.metadata.namespace,r.app=r.metadata.labels.app.replace("-close","")}}catch(l){n.e(l)}finally{n.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))):this.$message.error("请选择环境")},podPage:function(e,t,a){var n=this.$router.resolve({name:"pod-index",query:{cluster:e,namespace:t,app:a}});window.open(n.href,"_blank")}}},i=s,c=a("2877"),u=Object(c["a"])(i,n,r,!1,null,"4e837af9",null);t["default"]=u.exports},"757c":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[a("el-alert",{attrs:{title:"应用批量重发V2(CI/CD分离模式）",type:"info",description:"使用当前运行的版本重新发布应用。","show-icon":""}}),e._v(" "),a("el-row",{staticStyle:{"max-width":"1080px","margin-top":"10px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:18}},[a("el-form",[a("el-form-item",[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:e.searchForm,callback:function(t){e.searchForm=t},expression:"searchForm"}})],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:6}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("br"),a("br"),e._v(" "),a("el-checkbox",{model:{value:e.openVersionCompare,callback:function(t){e.openVersionCompare=t},expression:"openVersionCompare"}},[e._v("开启版本对比")])],1)],1),e._v(" "),a("el-table",{attrs:{data:e.tableData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"发布过？",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.extraAttr.reDeployed)+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"!==t.row.cluster?e._e():a("div",[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.reDeploy(t.row)}}},[e._v("使用当前版本重发\n          ")]),e._v(" "),a("br"),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy-history",query:{}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n              发布历史\n            ")])],1),e._v(" "),a("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:t.row.app}},target:"_blank"}},[a("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[e._v("\n              发布流程\n            ")])],1)],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",width:"100px",prop:"status"}}),e._v(" "),a("el-table-column",{attrs:{label:"实例数(运行/配置)",align:"center",prop:"replicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"===t.row.cluster&&!e.replicasIsSame(t.row.app,t.row.namespace)?a("div",{staticStyle:{color:"#FB5151"}},[e._v("\n          "+e._s(t.row.extraAttr.runningPodNum)+" / "+e._s(t.row.replicas)+"\n        ")]):a("div",[e._v("\n          "+e._s(t.row.extraAttr.runningPodNum)+" / "+e._s(t.row.replicas)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"版本",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.openVersionCompare&&"k8s0"===t.row.cluster&&!e.versionIsSame(t.row.app,t.row.namespace)?a("div",{staticStyle:{color:"#FB5151"}},[e._v("\n          "+e._s(t.row.extraAttr.deployTag)+"\n        ")]):a("div",[e._v("\n          "+e._s(t.row.extraAttr.deployTag)+"\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"发版信息"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px"}},[a("div",[e._v("发布人:"+e._s(t.row.extraAttr.deployUser))]),e._v(" "),a("div",[e._v(e._s(t.row.extraAttr.deployRemark))])])]}}])})],1)],1)},r=[],o=a("2d63"),l=a("51a9"),s=a("8504"),i=a("76fe"),c={components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,openVersionCompare:!1,searchForm:"",tableData:[],deployRemark:""}},methods:{loadTableData:function(){var e=this;this.pageLoading=!0,this.tableData=[],Object(l["m"])(this.searchForm).then((function(t){var a,n=Object(o["a"])(t.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.extraAttr.deployTag="?",r.extraAttr.runningPodNum="?",r.extraAttr.reDeployed="NO",e.findDeployment(r)}}catch(l){n.e(l)}finally{n.f()}e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pageLoading=!1}))},versionIsSame:function(e,t){var a,n="",r="",l=Object(o["a"])(this.tableData);try{for(l.s();!(a=l.n()).done;){var s=a.value;"k8s0"===s.cluster&&s.namespace===t&&s.app===e&&(n=s.extraAttr.deployTag),"k8s1"===s.cluster&&s.namespace===t&&s.app===e&&(r=s.extraAttr.deployTag)}}catch(i){l.e(i)}finally{l.f()}return""!==n&&""!==r&&n===r},replicasIsSame:function(e,t){var a,n="",r="",l="",s="",i=Object(o["a"])(this.tableData);try{for(i.s();!(a=i.n()).done;){var c=a.value;"k8s0"===c.cluster&&c.namespace===t&&c.app===e&&(n=c.replicas,l=c.runningPodNum),"k8s1"===c.cluster&&c.namespace===t&&c.app===e&&(r=c.replicas,s=c.runningPodNum)}}catch(u){i.e(u)}finally{i.f()}return n===r&&l===s},findDeployment:function(e){null!==e?Object(s["a"])(e.cluster,e.namespace,e.app).then((function(t){e.extraAttr.deployTag=t.data.deployTag,e.extraAttr.runningPodNum=t.data.replicas,e.extraAttr.deployRemark=t.data.deployRemark,e.extraAttr.deployUser=t.data.deployUser})).catch((function(e){console.log(e.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reDeploy:function(e){var t=this;this.$prompt("请填写发版备注","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:this.deployRemark}).then((function(a){var n=a.value;t.deployRemark=n,Object(i["f"])(e.cluster,e.namespace,e.app,n).then((function(a){t.$message.success("操作成功"),e.extraAttr.reDeployed="YES"})).catch((function(e){t.$message.error(e.message)}))}))}}},u=c,p=a("2877"),d=Object(p["a"])(u,n,r,!1,null,null,null);t["default"]=d.exports},"76fe":function(e,t,a){"use strict";a.d(t,"k",(function(){return r})),a.d(t,"i",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"f",(function(){return c})),a.d(t,"c",(function(){return u})),a.d(t,"g",(function(){return p})),a.d(t,"h",(function(){return d})),a.d(t,"d",(function(){return m})),a.d(t,"l",(function(){return f})),a.d(t,"j",(function(){return v}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/job/search",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:e}})}function l(e){return Object(n["a"])({url:"/v1/job/build-image",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/v1/job/deploy-app",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/v1/job/build-and-deploy",method:"post",data:e})}function c(e,t,a,r){return Object(n["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:e,namespace:t,app:a,remark:r}})}function u(e,t,a,r){return Object(n["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:e,namespace:t,app:a,remark:r}})}function p(e){return Object(n["a"])({url:"/v1/job/detail",method:"get",params:{id:e}})}function d(e){return Object(n["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:e}})}function m(e){return Object(n["a"])({url:"/v1/job/cancel",method:"put",data:{id:e}})}function f(e,t){return Object(n["a"])({url:"/v1/job/update-status?id=".concat(e,"&status=").concat(t),method:"put"})}function v(e){return Object(n["a"])({url:"/v1/job/redo",method:"post",data:{id:e}})}},8504:function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"i",(function(){return c})),a.d(t,"d",(function(){return u})),a.d(t,"f",(function(){return p})),a.d(t,"e",(function(){return d}));var n=a("b775");function r(e,t){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:e,namespace:t}})}function o(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:e})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:e,namespace:t,app:a}})}function i(e){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:e})}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function p(e){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:e})}function d(e,t,a,r,o){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:e,namespace:t,app:a,revision:r,deployTag:o||""}})}},"88fa":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-alert",{attrs:{title:"应用批量重发",type:"info",description:"使用当前运行的版本重新发布应用。 操作步骤：创建任务 → 开始 → 每间隔1分钟发布一个服务","show-icon":""}}),e._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){e.appRestartVisible=!0}}},[e._v("创建任务")]),e._v(" "),e.job&&e.job.items?a("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.start}},[e._v("开始")]):e._e(),e._v(" "),1===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.stop}},[e._v("暂停")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.remove}},[e._v("删除")])],1):e._e()],1),e._v(" "),e.job&&e.job.items?a("div",[a("div",{staticStyle:{margin:"20px"}},[e._v("任务状态： "+e._s(e.job.status))]),e._v(" "),a("el-timeline",e._l(e.job.items,(function(t,n){return a("el-timeline-item",{key:n,attrs:{color:t.output.endsWith("success")?"#67C23A":t.output.includes("fail,")?"#F56C6C":"#909399"}},[a("b",{staticStyle:{"padding-right":"10px"}},[e._v(e._s(n+1))]),e._v(e._s(t.output)+"\n      ")])})),1)],1):a("div",{staticStyle:{margin:"20px"}},[e._v("\n    -无任何数据-\n  ")]),e._v(" "),a("el-dialog",{attrs:{title:"服务批量重发",visible:e.appRestartVisible},on:{"update:visible":function(t){e.appRestartVisible=t}}},[a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"服务列表"}},[e._t("label",[e._v("\n          内容为json格式，每行格式为 cluster/namespace/app，\n          "),a("el-popover",{attrs:{placement:"bottom-end",trigger:"click"}},[a("div",[e._v("\n              ["),a("br"),e._v('\n                "k8s0/fstest/fs-aaa-bbb",'),a("br"),e._v('\n                "k8s0/fstest/fs-k8s-tomcat-test",'),a("br"),e._v('\n                "k8s0/firstshare/fs-k8s-tomcat-test"'),a("br"),e._v("\n              ]\n            ")]),e._v(" "),a("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("点击查看示例")])],1)]),e._v(" "),a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:e.appRestartForm,callback:function(t){e.appRestartForm=t},expression:"appRestartForm"}})],2),e._v(" "),a("el-form-item",{attrs:{label:"发版参数"}},[a("el-checkbox",{model:{value:e.forceCodeCompile,callback:function(t){e.forceCodeCompile=t},expression:"forceCodeCompile"}},[e._v("【强制代码编译】")]),e._v(" "),a("el-checkbox",{model:{value:e.dependencyCheck,callback:function(t){e.dependencyCheck=t},expression:"dependencyCheck"}},[e._v("【依赖包版本校验】")])],1),e._v(" "),a("el-form-item",{attrs:{label:"父POM "}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"如果不填写，则使用上一次发版的父POM"},model:{value:e.parentPom,callback:function(t){e.parentPom=t},expression:"parentPom"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"tag后缀"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"tag后缀"},model:{value:e.suffixVersion,callback:function(t){e.suffixVersion=t},expression:"suffixVersion"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-top":"-20px"},attrs:{"label-width":"100"}},[a("div",{staticStyle:{color:"#666","font-size":"12px","padding-left":"60px"}},[e._v("备注：如果填写了tag后缀，使用当前运行tag+后缀名创建新tag发布；否则，使用当前运行tag重发")])]),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"输入备注"},model:{value:e.message,callback:function(t){e.message=t},expression:"message"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.appRestartVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.create}},[e._v("确 定")])],1)],1)],1)},r=[],o=a("c1ab"),l={components:{},mounted:function(){this.loadOutput();var e=this.loadOutput;this.timer=setInterval((function(){e()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,forceCodeCompile:!0,dependencyCheck:!0,suffixVersion:"",message:"",appRestartForm:"",parentPom:""}},methods:{loadOutput:function(){var e=this;Object(o["a"])().then((function(t){e.job=t.data})).catch((function(t){e.$message.error(t.message)}))},create:function(){var e=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["e"])("redeploy","",e.suffixVersion,e.message,e.forceCodeCompile,e.dependencyCheck,e.parentPom,e.appRestartForm).then((function(t){e.appRestartVisible=!1,e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消")}))},start:function(){var e=this;Object(o["D"])(this.appRestartForm).then((function(t){e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))},stop:function(){var e=this;Object(o["F"])(this.appRestartForm).then((function(t){e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))},remove:function(){var e=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["x"])(e.appRestartForm).then((function(t){e.$message.success("Success")})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消")}))}}},s=l,i=a("2877"),c=Object(i["a"])(s,n,r,!1,null,null,null);t["default"]=c.exports},8964:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticStyle:{float:"right",right:"10px"}},[a("el-button",{attrs:{type:"text"},on:{click:e.statusPage}},[e._v("发布状态页")])],1),e._v(" "),a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"专属云环境",prop:"sourceCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"源集群"},model:{value:e.form.sourceCluster,callback:function(t){e.$set(e.form,"sourceCluster",t)},expression:"form.sourceCluster"}},e._l(e.dedicatedClusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"参考环境",prop:"targetCluster"}},[a("el-select",{attrs:{"value-key":"id",placeholder:"目标集群"},model:{value:e.form.targetCluster,callback:function(t){e.$set(e.form,"targetCluster",t)},expression:"form.targetCluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.cluster+"/"+e.namespace,value:e}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("查询")])],1)],1),e._v(" "),a("el-row",{staticStyle:{margin:"5px"},attrs:{type:"flex",justify:"space-between"}},[a("el-col",{attrs:{span:12}},[a("el-select",{on:{change:e.tagChange},model:{value:e.tagValue,callback:function(t){e.tagValue=t},expression:"tagValue"}},e._l(e.tagOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:12}},[a("el-button",{attrs:{type:"primary"},on:{click:e.batchPublish}},[e._v("使用 ["+e._s(this.currentTargetCluster.cluster)+"/"+e._s(this.currentTargetCluster.namespace)+"] 版本重发选择的应用")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchPublish("master")}}},[e._v("使用 [Master分支] 重发选择的应用")])],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"数据加载中...",border:"",fit:"","cell-style":e.cellStyle,"highlight-current-row":""},on:{"selection-change":e.handleClonePipelineTableSelectionChange}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",width:"120px",sortable:"",prop:"sourcePipeline.app"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境描述",width:"120px",sortable:"",prop:"sourcePipeline.extraAttr.clusterSummary"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",sortable:"",prop:"sourcePipeline.namespace"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?a("div",[a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击发布"}},[a("span",{on:{click:function(a){return e.showDeployDialog([t.row])}}},[a("el-link",{attrs:{type:"primary",underline:!1}},[a("b",[e._v(e._s(t.row.sourcePipeline.namespace))])]),e._v(" ("+e._s(t.row.sourcePipeline.cluster)+")\n            ")],1)])],1):a("div",[e._v("\n          "+e._s(t.row.sourcePipeline.namespace)+" ("+e._s(t.row.sourcePipeline.cluster)+")\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行版本",sortable:"",prop:"sourcePipeline.extraAttr.deployTag"}}),e._v(" "),a("el-table-column",{attrs:{label:"实例",align:"center"}},[a("el-table-column",{attrs:{label:"配置数",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"发布流程里配置的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.sourcePipeline.replicas))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行数",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.sourcePipeline.extraAttr.runningPodNum))])])]}}])})],1),e._v(" "),a("el-table-column",{attrs:{label:this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"环境"}},[a("el-table-column",{attrs:{label:"运行版本",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的版本",placement:"top"}},[a("span",[e._v(e._s(t.row.targetPipeline.extraAttr.deployTag))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行数",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("span",[e._v(e._s(t.row.targetPipeline.extraAttr.runningPodNum))])])]}}])})],1)],1)],1)},r=[],o=(a("7f7f"),a("2d63")),l=a("51a9"),s={name:"dedicated-cloud-publish-helper",components:{},data:function(){var e=function(e,t,a){null===t.cluster&&null===t.namespace?a(new Error("请选择集群")):a()};return{tableData:[],tableDataTmp:[],tableLoading:!1,form:{sourceCluster:{cluster:null,namespace:null},targetCluster:{cluster:null,namespace:null}},currentTargetCluster:{cluster:"",namespace:""},tagValue:"all",pipelineTableMultipleSelection:[],rules:{sourceCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}],targetCluster:[{validator:e,trigger:"change"},{required:!0,trigger:"change"}]}}},mounted:function(){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a,n=e.value,r=Object(o["a"])(n.namespaces);try{for(r.s();!(a=r.n()).done;){var l=a.value;return this.form.targetCluster.cluster=n.name,this.form.targetCluster.namespace=l,this.form.targetCluster.id=0,void this.switchCluster(this.form.targetCluster.cluster,this.form.targetCluster.namespace)}}catch(s){r.e(s)}finally{r.f()}}}catch(s){t.e(s)}finally{t.f()}},computed:{clusterOptions:function(){var e,t=[],a=0,n=Object(o["a"])(this.$settings.clusters);try{for(n.s();!(e=n.n()).done;){var r,l=e.value,s=Object(o["a"])(l.namespaces);try{for(s.s();!(r=s.n()).done;){var i=r.value,c={};c.cluster=l.name,c.namespace=i,c.id=a,t.push(c),a++}}catch(u){s.e(u)}finally{s.f()}}}catch(u){n.e(u)}finally{n.f()}return t},dedicatedClusterOptions:function(){var e,t=[],a=0,n=Object(o["a"])(this.$settings.clusters);try{for(n.s();!(e=n.n()).done;){var r=e.value;if("fxiaokeCloud"!==r.cloudCategory){var l,s=Object(o["a"])(r.namespaces);try{for(s.s();!(l=s.n()).done;){var i=l.value,c={};c.cluster=r.name,c.namespace=i,c.id=a,t.push(c),a++}}catch(p){s.e(p)}finally{s.f()}}}}catch(p){n.e(p)}finally{n.f()}var u={cluster:"k8s1",namespace:"foneshare"};return u.id=this.$settings.clusters.length+1,t.push(u),t},tagOptions:function(){var e=[];return e.push({value:"all",label:"所有"}),e.push({value:"inconsistent",label:"[运行版本] 与 ["+this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"] 版本不一致"}),e.push({value:"isEmpty",label:"[ "+this.currentTargetCluster.cluster+" / "+this.currentTargetCluster.namespace+"] 版本为空"}),e}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,Object(l["h"])(this.form.sourceCluster.cluster,this.form.sourceCluster.namespace,this.form.targetCluster.cluster,this.form.targetCluster.namespace).then((function(t){e.tableData=t.data,e.tableDataTmp=e.tableData,e.switchCluster(e.form.targetCluster.cluster,e.form.targetCluster.namespace)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},switchCluster:function(e,t){this.currentTargetCluster.cluster=e,this.currentTargetCluster.namespace=t},tagChange:function(e){switch(e){case"all":this.loadTableData();break;case"inconsistent":var t=[];for(var a in this.tableDataTmp)this.tableDataTmp[a].sourcePipeline.extraAttr.deployTag!==this.tableDataTmp[a].targetPipeline.extraAttr.deployTag&&t.push(this.tableDataTmp[a]);this.tableData=t;break;case"isEmpty":var n=[];for(var r in this.tableDataTmp)""===this.tableDataTmp[r].targetPipeline.extraAttr.deployTag&&n.push(this.tableDataTmp[r]);this.tableData=n;break}},cellStyle:function(e){var t=e.row,a=e.column;e.rowIndex,e.columnIndex;if(1===a.level&&"运行版本"===a.label){if(t.sourcePipeline.extraAttr.deployTag===t.targetPipeline.extraAttr.deployTag)return"color:green";if(""===t.targetPipeline.extraAttr.deployTag)return"color:green";if(t.sourcePipeline.extraAttr.deployTag!==t.targetPipeline.extraAttr.deployTag)return"color:red"}},handleClonePipelineTableSelectionChange:function(e){this.pipelineTableMultipleSelection=e},batchPublish:function(e){this.$message.info("todo...")},statusPage:function(){this.$router.push({name:"dedicated-cloud-publish-status"})}}},i=s,c=a("2877"),u=Object(c["a"])(i,n,r,!1,null,"08e6c4aa",null);t["default"]=u.exports},"9ebb":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{ref:"searchForm",attrs:{model:e.searchForm}},[a("el-form-item",{attrs:{label:"部署到纯私有环境"}},[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:e.cloudTypeChange},model:{value:e.privateCloud,callback:function(t){e.privateCloud=t},expression:"privateCloud"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"选择集群",prop:"cluster"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择k8s集群",disabled:e.privateCloud},on:{change:e.loadApp},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"选择环境",prop:"namespace"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择Namespace",filterable:"",disabled:e.privateCloud},on:{change:e.loadApp},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"选择应用",prop:"app"}},[a("el-select",{staticStyle:{width:"600px"},attrs:{filterable:"",placeholder:"选择应用",multiple:""},model:{value:e.searchForm.app,callback:function(t){e.$set(e.searchForm,"app",t)},expression:"searchForm.app"}},[a("el-option",{attrs:{value:"",label:"全部"}}),e._v(" "),e._l(this.appOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}))],2)],1),e._v(" "),e.privateCloud?a("el-form-item",{attrs:{label:"覆盖环境"}},[a("el-select",{staticStyle:{width:"340px"},model:{value:e.searchForm.overrideNamespace,callback:function(t){e.$set(e.searchForm,"overrideNamespace",t)},expression:"searchForm.overrideNamespace"}},[a("el-option",{attrs:{value:"cmhk-crm-di-std",label:"招商局集团测试（cmhk-crm-di-std）"}})],1)],1):e._e(),e._v(" "),a("el-form-item",[a("div",{staticStyle:{"margin-top":"20px","padding-left":"160px"}},[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:e.logHistory}},[e._v("查看历史结果")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.createHelmChartBuildJob}},[e._v("开始构建")])],1)])],1)],1),e._v(" "),e.jobResult.length>0?a("div",{staticStyle:{"margin-top":"20px",border:"1px solid #ccc","background-color":"#eee",padding:"5px",width:"760px","font-size":"12px"}},e._l(e.jobResult,(function(t){return a("div",[e._v("\n      "+e._s(t)+"\n    ")])})),0):e._e()])},r=[],o=(a("7f7f"),a("2d63")),l=a("8504"),s=a("c1ab"),i={components:{},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:"",overrideNamespace:""},appOptions:[],jobResult:[],privateCloud:!1}},methods:{cloudTypeChange:function(){console.log(this.privateCloud),this.privateCloud?(this.searchForm.cluster="forceecrm-k8s1",this.searchForm.namespace="forceecrm-public-prod",this.loadApp()):this.searchForm.overrideNamespace=""},loadApp:function(){var e=this.searchForm.cluster,t=this.searchForm.namespace;if(e&&t){var a=this;Object(l["g"])(e,t).then((function(e){a.appOptions=e.data})).catch((function(e){console.error(e)}))}else this.appOptions=[]},createHelmChartBuildJob:function(){var e=this;!this.privateCloud||this.searchForm.overrideNamespace?Object(s["k"])(this.searchForm.cluster,this.searchForm.namespace,this.searchForm.app,this.searchForm.overrideNamespace).then((function(t){e.jobResult.push("- 操作成功，构建完成后结果会存储到审计日志。构建服务比较多时，耗时会比较长。"+t.data)})).catch((function(t){e.$message.error(t.message)})).finally((function(){})):this.$message.warning("私有部署环境，必须选择覆盖环境")},logHistory:function(){var e=this.$router.resolve({name:"log-list",query:{operate:"helm-chart-build-job"}});window.open(e.href,"_blank")}}},c=i,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);t["default"]=p.exports},a527:function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"g",(function(){return i})),a.d(t,"c",(function(){return c})),a.d(t,"k",(function(){return u})),a.d(t,"l",(function(){return p})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"r",(function(){return v})),a.d(t,"b",(function(){return b})),a.d(t,"a",(function(){return h})),a.d(t,"p",(function(){return g})),a.d(t,"q",(function(){return y})),a.d(t,"n",(function(){return _})),a.d(t,"j",(function(){return x}));a("96cf"),a("3b8d");var n=a("b775");function r(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e,t){return Object(n["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:e,namespace:t}})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:e,namespace:t,pod:a}})}function i(e,t,a,r,o,l){return Object(n["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:e,namespace:t,pod:a,container:r,tailLines:o,previous:l}})}function c(e,t,a,n,r){var o="/api/v1/k8s/pod/stdout/download?cluster=".concat(e,"&namespace=").concat(t,"&pod=").concat(a,"&container=").concat(n,"&tailLines=").concat(r,'&_time="')+(new Date).getTime();window.open(o)}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:e,namespace:t,pod:a}})}function p(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:e,namespace:t,pod:a}})}function d(e){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:e}})}function m(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:e,namespace:t,pod:a}})}function f(e){return Object(n["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:e})}function b(e,t){window.open("/api/v1/k8s/pod/file/download?fileId="+e+"&fileName="+t+"&_time="+(new Date).getTime())}function h(e){return Object(n["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:e})}function g(e){window.open("/api/v1/k8s/pod/file/preview?fileId="+e)}function y(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:e,namespace:t,pod:a}})}function _(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:e,namespace:t,pod:a}})}function x(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:e,namespace:t,pod:a}})}},a68b:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tool-app-manage-tab",staticStyle:{"margin-top":"-10px"}},[a("el-tabs",{staticStyle:{"margin-top":"10px"},attrs:{"tab-position":"left"},on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"应用清理",name:"app-gc",lazy:!0}},[a("app-gc")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"摘除Pod管理",name:"pod-deregister-manage",lazy:!0}},[a("pod-deregister-manage")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用定时重启",name:"app-cron-reboot",lazy:!0}},[a("app-cron-reboot")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重启",name:"app-restart",lazy:!0}},[a("app-restart")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发",name:"app-redeploy",lazy:!0}},[a("app-redeploy")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量重发V2",name:"app-redeploy-v2",lazy:!0}},[a("app-redeploy-v2")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量构建V2",name:"app-build-v2",lazy:!0}},[a("app-build-v2")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用批量发布",name:"app-deploy",lazy:!0}},[a("app-deploy")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"应用地址查询",name:"app-address-query",lazy:!0}},[a("app-address-query")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程资源修改",name:"pipeline-resource-update",lazy:!0}},[a("pipeline-resource-update")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"发布流程克隆",name:"pipeline-clone",lazy:!0}},[a("clone-pipeline")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"配置文件迁移",name:"cms-config-migrate",lazy:!0}},[a("cms-config-migrate")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"专属云发布助手",name:"dedicated-cloud-publish-helper",lazy:!0}},[a("dedicated-cloud-publish-helper")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Yaml导出",name:"yaml-export",lazy:!0}},[a("yaml-export")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"HelmChart导出",name:"helm-chart-build",lazy:!0}},[a("helm-chart-build")],1)],1)],1)},r=[],o=a("db72"),l=(a("7f7f"),a("6b5c")),s=a("3edd"),i=a("f0df"),c=a("b5a9"),u=a("9add"),p=a("13fe"),d=a("88fa"),m=a("757c"),f=a("4f2b"),v=a("fa56"),b=a("5b06"),h=a("9ebb"),g=a("2861"),y=a("6a0a"),_=a("8964"),x={components:{DedicatedCloudPublishHelper:_["default"],ClonePipeline:y["default"],YamlExport:g["default"],HelmChartBuild:h["default"],CmsConfigMigrate:b["default"],AppDeploy:v["default"],AppBuildV2:f["default"],AppRedeployV2:m["default"],AppRedeploy:d["default"],PipelineResourceUpdate:p["default"],AppAddressQuery:u["default"],AppRestart:c["default"],AppCronReboot:i["default"],AppGc:s["default"],PodDeregisterManage:l["default"]},props:{activeName:{type:String,default:""}},mounted:function(){var e=this.$route.query.tab;e||(e="app-gc"),this.activeTab=e},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(e,t){var a=e.name;this.$router.push({query:Object(o["a"])(Object(o["a"])({},this.$route.query),{},{tab:a})})}}},k=x,w=(a("d54f"),a("2877")),O=Object(w["a"])(k,n,r,!1,null,null,null);t["default"]=O.exports},b5a9:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-alert",{attrs:{title:"服务批量重启",type:"info",description:"批量重启：创建任务 → 开始；任务间隔时间1分钟","show-icon":""}}),e._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){e.appRestartVisible=!0}}},[e._v("创建任务")]),e._v(" "),e.job&&e.job.items?a("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.start}},[e._v("开始")]):e._e(),e._v(" "),1===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.stop}},[e._v("暂停")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.remove}},[e._v("删除")])],1):e._e()],1),e._v(" "),e.job&&e.job.items?a("div",[a("div",{staticStyle:{margin:"20px"}},[e._v("任务状态： "+e._s(e.job.status))]),e._v(" "),a("el-timeline",{attrs:{reverse:!0}},e._l(e.job.items,(function(t,n){return a("el-timeline-item",{key:n,attrs:{color:t.output.endsWith("success")?"#67C23A":t.output.includes("fail,")?"#F56C6C":"#909399"}},[a("b",{staticStyle:{"padding-right":"10px"}},[e._v(e._s(n+1))]),e._v(e._s(t.output)+"\n      ")])})),1)],1):a("div",{staticStyle:{margin:"20px"}},[e._v("\n    -无任何数据-\n  ")]),e._v(" "),a("el-dialog",{attrs:{title:"服务批量重启",visible:e.appRestartVisible},on:{"update:visible":function(t){e.appRestartVisible=t}}},[a("el-form",[a("el-form-item",{attrs:{label:"服务列表","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:e.appRestartForm,callback:function(t){e.appRestartForm=t},expression:"appRestartForm"}})],1),e._v(" "),a("el-form-item",{attrs:{label:""}},[a("div",{staticStyle:{width:"100%","line-height":"normal"}},[e._v("\n          内容为json格式，每行格式为 cluster/namespace/app，示例：\n          "),a("div",{staticStyle:{"background-color":"#eee",padding:"10px","margin-top":"10px"}},[e._v("\n            ["),a("br"),e._v('\n              "k8s1/fstest/fs-aaa-bbb",'),a("br"),e._v('\n              "k8s1/fstest/fs-k8s-tomcat-test",'),a("br"),e._v('\n              "k8s1/firstshare/fs-k8s-tomcat-test"'),a("br"),e._v("\n            ]\n          ")])])])],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.appRestartVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.createAppRestartJob}},[e._v("确 定")])],1)],1)],1)},r=[],o=a("c1ab"),l={components:{},mounted:function(){this.loadAppRestartOutput();var e=this.loadAppRestartOutput;this.timer=setInterval((function(){e()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,appRestartForm:""}},methods:{loadAppRestartOutput:function(){var e=this;Object(o["c"])().then((function(t){e.job=t.data})).catch((function(t){e.$message.error(t.message)}))},createAppRestartJob:function(){var e=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["f"])(e.appRestartForm).then((function(t){e.appRestartVisible=!1,e.$message.success("Success"),e.loadAppRestartOutput()})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消批量重启")}))},start:function(){var e=this;Object(o["E"])(this.appRestartForm).then((function(t){e.$message.success("Success")})).catch((function(t){e.$message.error(t.message)}))},stop:function(){var e=this;Object(o["G"])(this.appRestartForm).then((function(t){e.$message.success("Success")})).catch((function(t){e.$message.error(t.message)}))},remove:function(){var e=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["y"])(e.appRestartForm).then((function(t){e.$message.success("Success")})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消")}))}}},s=l,i=a("2877"),c=Object(i["a"])(s,n,r,!1,null,null,null);t["default"]=c.exports},c1ab:function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"j",(function(){return o})),a.d(t,"z",(function(){return l})),a.d(t,"A",(function(){return s})),a.d(t,"c",(function(){return i})),a.d(t,"f",(function(){return c})),a.d(t,"E",(function(){return u})),a.d(t,"G",(function(){return p})),a.d(t,"y",(function(){return d})),a.d(t,"a",(function(){return m})),a.d(t,"e",(function(){return f})),a.d(t,"D",(function(){return v})),a.d(t,"F",(function(){return b})),a.d(t,"x",(function(){return h})),a.d(t,"H",(function(){return g})),a.d(t,"k",(function(){return y})),a.d(t,"d",(function(){return _})),a.d(t,"B",(function(){return x})),a.d(t,"i",(function(){return k})),a.d(t,"g",(function(){return w})),a.d(t,"s",(function(){return O})),a.d(t,"v",(function(){return C})),a.d(t,"w",(function(){return j})),a.d(t,"o",(function(){return S})),a.d(t,"p",(function(){return $})),a.d(t,"t",(function(){return F})),a.d(t,"u",(function(){return T})),a.d(t,"b",(function(){return D})),a.d(t,"q",(function(){return R})),a.d(t,"r",(function(){return P})),a.d(t,"n",(function(){return A})),a.d(t,"C",(function(){return z})),a.d(t,"m",(function(){return V})),a.d(t,"l",(function(){return N}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:e}})}function o(e){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:e}})}function l(e){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:e})}function i(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function c(e){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:e})}function m(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(e,t,a,r,o,l,s,i){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(e,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(t,"&suffixVersion=").concat(a,"&message=").concat(r,"&dependencyCheck=").concat(l,"&parentPom=").concat(s),method:"post",data:i})}function v(e){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:e})}function y(e,t,a,r){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(e,"&namespace=").concat(t,"&app=").concat(a,"&overrideNamespace=").concat(r),method:"post"})}function _(e,t,a,r,o){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(e,"&namespace=").concat(t,"&version=").concat(a,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function x(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function k(e){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(e),method:"get"})}function w(e){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(e),method:"delete"})}function O(e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:e})}function C(e){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:e})}function j(e){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:e})}function S(e){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:e})}function $(e){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:e})}function F(e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:e})}function T(e,t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+e,method:"post",data:t})}function D(e){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:e})}function R(e){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:e})}function P(e){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:e})}function A(e,t,a){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(e,"&namespace=").concat(t,"&op=").concat(a),method:"get"})}function z(e){return Object(n["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(e),method:"get"})}function V(e,t,a){return Object(n["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(e,"&namespace=").concat(t,"&lbAddr=").concat(a),method:"get"})}function N(){return Object(n["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}},d54f:function(e,t,a){"use strict";a("eba1")},eba1:function(e,t,a){},f0df:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"260px"},model:{value:e.searchForm.keyword,callback:function(t){e.$set(e.searchForm,"keyword","string"===typeof t?t.trim():t)},expression:"searchForm.keyword"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.showEditDialog()}}},[e._v("新建\n      ")])],1)],1),e._v(" "),e._m(0),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"所在集群",prop:"cluster"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启时间点",align:"center",prop:"rebootHour",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        每天的 "),a("b",[e._v(e._s(t.row.rebootHour))]),e._v(" 点\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",align:"center",prop:"author"}}),e._v(" "),a("el-table-column",{attrs:{label:"备注",prop:"remark","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(a){return e.deleteReboot(t.row)}}},[a("el-button",{staticStyle:{"margin-right":"5px"},attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[e._v("删除\n          ")])],1)]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"创建服务重启任务",visible:e.dialogVisible,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:e.editForm,"label-width":"120px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"应用",prop:"app"}},[a("el-input",{model:{value:e.editForm.app,callback:function(t){e.$set(e.editForm,"app","string"===typeof t?t.trim():t)},expression:"editForm.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"k8s集群"}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.clusterChange},model:{value:e.editForm.cluster,callback:function(t){e.$set(e.editForm,"cluster",t)},expression:"editForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.namespace,callback:function(t){e.$set(e.editForm,"namespace",t)},expression:"editForm.namespace"}},e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"重启时间点"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.editForm.rebootHour,callback:function(t){e.$set(e.editForm,"rebootHour",t)},expression:"editForm.rebootHour"}},e._l([23,0,1,2,3,4,5,6],(function(e){return a("el-option",{key:e,attrs:{label:e+"点",value:e}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{model:{value:e.editForm.remark,callback:function(t){e.$set(e.editForm,"remark","string"===typeof t?t.trim():t)},expression:"editForm.remark"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.submitLoading,expression:"submitLoading"}],attrs:{type:"primary"},on:{click:function(t){return e.create()}}},[e._v("确 定")])],1)],1)],1)},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[a("tr",[a("th",{staticStyle:{width:"70px","text-align":"left","vertical-align":"top"}},[e._v("定时重启")]),e._v(" "),a("td",{staticStyle:{width:"1000px"}},[e._v("\n        服务会在配置的时间点上进行重启，按批重启所有实例，每批启动25%的实例\n      ")])])])}],o=(a("7f7f"),a("2d63")),l=a("b775");function s(e){return Object(l["a"])({url:"/v1/reboot?keyword="+e,method:"get"})}function i(e){return Object(l["a"])({url:"/v1/reboot",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/v1/reboot",method:"delete",params:{id:e}})}var u={components:{},mounted:function(){this.loadTableData()},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.editForm.cluster){var e,t=Object(o["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.editForm.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},data:function(){return{searchForm:{keyword:""},editForm:{},editFormRules:{app:[{required:!0,message:"值不能为空",trigger:"blur"}],cluster:[{required:!0,message:"值不能为空",trigger:"blur"}],namespace:[{required:!0,message:"值不能为空",trigger:"blur"}],rebootHour:[{required:!0,message:"值不能为空",trigger:"blur"}]},tableData:[],tableLoading:!1,dialogVisible:!1,submitLoading:!1,appOptions:[]}},methods:{loadTableData:function(){var e=this;this.tableLoading=!0,s(this.searchForm.keyword).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},editFormReset:function(){this.editForm={app:"",cluster:"",namespace:"",rebootHour:23,remark:""}},clusterChange:function(){this.editForm.namespace=""},showEditDialog:function(){this.editFormReset(),this.dialogVisible=!0},deleteReboot:function(e){var t=this;c(e.id).then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))},create:function(){var e=this;this.$refs["dialogEditForm"].validate((function(t){if(!t)return!1;e.submitLoading=!0,i(e.editForm).then((function(t){e.dialogVisible=!1,e.$message.success("操作成功"),e.loadTableData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))}))}}},p=u,d=a("2877"),m=Object(d["a"])(p,n,r,!1,null,null,null);t["default"]=m.exports},fa56:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-alert",{attrs:{title:"应用批量发布",type:"info",description:"使用指定的版本发布应用。 操作步骤：创建任务 → 开始 → 每间隔1分钟发布一个服务","show-icon":""}}),e._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){e.appRestartVisible=!0}}},[e._v("创建任务")]),e._v(" "),e.job&&e.job.items?a("div",{staticStyle:{display:"inline-block","margin-left":"30px"}},[0===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.start}},[e._v("开始")]):e._e(),e._v(" "),1===e.job.status?a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.stop}},[e._v("暂停")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.remove}},[e._v("删除")])],1):e._e()],1),e._v(" "),e.job&&e.job.items?a("div",[a("div",{staticStyle:{margin:"20px"}},[e._v("任务状态： "+e._s(e.job.status))]),e._v(" "),a("el-timeline",e._l(e.job.items,(function(t,n){return a("el-timeline-item",{key:n,attrs:{color:t.output.endsWith("success")?"#67C23A":t.output.includes("fail,")?"#F56C6C":"#909399"}},[a("b",{staticStyle:{"padding-right":"10px"}},[e._v(e._s(n+1))]),e._v(e._s(t.output)+"\n      ")])})),1)],1):a("div",{staticStyle:{margin:"20px"}},[e._v("\n    -无任何数据-\n  ")]),e._v(" "),a("el-dialog",{attrs:{title:"服务批量发布",visible:e.appRestartVisible},on:{"update:visible":function(t){e.appRestartVisible=t}}},[a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"服务列表"}},[e._t("label",[e._v("\n          内容为json格式，每行格式为 cluster/namespace/app，\n          "),a("el-popover",{attrs:{placement:"bottom-end",trigger:"click"}},[a("div",[e._v("\n              ["),a("br"),e._v('\n                "k8s0/fstest/fs-aaa-bbb",'),a("br"),e._v('\n                "k8s0/fstest/fs-k8s-tomcat-test",'),a("br"),e._v('\n                "k8s0/firstshare/fs-k8s-tomcat-test"'),a("br"),e._v("\n              ]\n            ")]),e._v(" "),a("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("点击查看示例")])],1)]),e._v(" "),a("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:20}},model:{value:e.appRestartForm,callback:function(t){e.appRestartForm=t},expression:"appRestartForm"}})],2),e._v(" "),a("el-form-item",{attrs:{label:"发版参数"}},[a("el-checkbox",{model:{value:e.forceCodeCompile,callback:function(t){e.forceCodeCompile=t},expression:"forceCodeCompile"}},[e._v("【强制代码编译】")]),e._v(" "),a("el-checkbox",{model:{value:e.dependencyCheck,callback:function(t){e.dependencyCheck=t},expression:"dependencyCheck"}},[e._v("【依赖包版本校验】")])],1),e._v(" "),a("el-form-item",{attrs:{label:"父POM "}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"如果不填写，则使用上一次发版的父POM"},model:{value:e.parentPom,callback:function(t){e.parentPom=t},expression:"parentPom"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"版本号"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"输入需要发布的版本号"},model:{value:e.fixVersion,callback:function(t){e.fixVersion=t},expression:"fixVersion"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"输入备注"},model:{value:e.message,callback:function(t){e.message=t},expression:"message"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.appRestartVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.create}},[e._v("确 定")])],1)],1)],1)},r=[],o=(a("6762"),a("2d63")),l=a("c1ab"),s={components:{},mounted:function(){this.loadOutput();var e=this.loadOutput;this.timer=setInterval((function(){e()}),3e3)},beforeDestroy:function(){this.timer&&(console.log("close timer"),clearInterval(this.timer))},computed:{namespaceOptions:function(){var e,t=[],a=Object(o["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n,r=e.value,l=Object(o["a"])(r.namespaces);try{for(l.s();!(n=l.n()).done;){var s=n.value;t.includes(s)||t.push(s)}}catch(i){l.e(i)}finally{l.f()}}}catch(i){a.e(i)}finally{a.f()}return t}},data:function(){return{timer:null,job:{items:[],status:0},appRestartVisible:!1,forceCodeCompile:!0,dependencyCheck:!0,fixVersion:"",appRestartForm:"",message:"",parentPom:""}},methods:{loadOutput:function(){var e=this;Object(l["a"])().then((function(t){e.job=t.data})).catch((function(t){e.$message.error(t.message)}))},create:function(){var e=this;this.$confirm("是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["e"])("deploy",e.fixVersion,"",e.message,e.forceCodeCompile,e.dependencyCheck,e.parentPom,e.appRestartForm).then((function(t){e.appRestartVisible=!1,e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消")}))},start:function(){var e=this;Object(l["D"])(this.appRestartForm).then((function(t){e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))},stop:function(){var e=this;Object(l["F"])(this.appRestartForm).then((function(t){e.$message.success("Success"),e.loadOutput()})).catch((function(t){e.$message.error(t.message)}))},remove:function(){var e=this;this.$confirm("确定要取消?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["x"])(e.appRestartForm).then((function(t){e.$message.success("Success")})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){console.log("取消")}))}}},i=s,c=a("2877"),u=Object(c["a"])(i,n,r,!1,null,null,null);t["default"]=u.exports}}]);