(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54798c82"],{"0736":function(t,e,s){},3336:function(t,e,s){},4331:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"app-container app-deploy-detail",staticStyle:{"min-height":"1200px"}},[s("div",{staticStyle:{width:"960px",margin:"0 auto"}},[s("el-steps",{attrs:{active:2,"align-center":""}},t._l(t.tasks,(function(e,a){return s("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.title,description:"",status:e.status,icon:e.icon},nativeOn:{click:function(e){return t.stepClick(a)}}},[s("template",{slot:"description"},[t._v("\n                "+t._s(e.statusDesc)+"\n                "),t.job.beforeJobId>0?s("div",{staticStyle:{display:"inline-block"}},[s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"前置任务成功，开始执行当前任务；前置任务失败，当前任务取消",placement:"top"}},[s("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(e){return t.jobDetailPage(t.job.beforeJobId)}}},[t._v("\n                      (查看前置任务)\n                    ")])],1)],1):t._e()])],2)})),1)],1),t._v(" "),s("div",{staticStyle:{position:"relative",height:"20px"}},[s("div",{staticStyle:{float:"right","padding-bottom":"5px"}},[["RUNNING","WAIT"].includes(this.job.status)?s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-switch-button"},on:{click:function(e){return t.cancel()}}},[t._v("取消发布\n      ")]):s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-refresh-left"},on:{click:function(e){return t.redo()}}},[t._v("重发\n      ")]),t._v(" "),s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-position"},on:{click:function(e){return t.pipelinePage()}}},[t._v("发布流程页\n      ")]),t._v(" "),s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-menu"},on:{click:function(e){return t.podPage()}}},[t._v("实例管理页\n      ")])],1),t._v(" "),s("div",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)",float:"left"}},[t._v("信息\n      "),s("div",{staticStyle:{"margin-left":"60px",display:"inline-block"}},[s("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobFailed()}}},[t._v("设置为失败\n        ")]),t._v(" "),s("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobSuccess()}}},[t._v("设置为成功\n        ")])],1)])]),t._v(" "),s("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.jobLoading,expression:"jobLoading"}],staticClass:"box-card",staticStyle:{clear:"both"}},[s("el-descriptions",{attrs:{column:4,border:""}},[s("el-descriptions-item",{attrs:{label:"应用"}},[t._v(t._s(this.job.params.app))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"集群"}},[t._v(t._s(this.job.params.cluster))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"环境"}},[t._v(t._s(this.job.params.namespace))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"发布批次"}},[t._v(t._s(this.job.params.maxSurge))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"发布版本"}},[t._v(t._s(this.job.params.tag))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(this.job.remark))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"操作人"}},[t._v(t._s(this.job.author))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"操作时间"}},[t._v(t._s(this.job.createdAt))]),t._v(" "),s("el-descriptions-item",{attrs:{label:"",span:"4"}},[s("template",{slot:"label"},[t._v("\n          部署模块\n        ")]),t._v(" "),s("div",{staticClass:"deploy-module-table",staticStyle:{"margin-top":"-10px","margin-left":"-10px"}},[s("el-table",{staticStyle:{width:"100%"},attrs:{size:"mini",data:t.job.params.deployModules}},[s("el-table-column",{attrs:{type:"index",width:"50"}}),t._v(" "),s("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),t._v(" "),s("el-table-column",{attrs:{prop:"module",label:"子模块"}}),t._v(" "),s("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}}),t._v(" "),s("el-table-column",{attrs:{prop:"tag",label:"Git 分支|标签"}}),t._v(" "),s("el-table-column",{attrs:{label:"镜像版本"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                "+t._s(e.row.artifactImage.split(":").pop())+"\n              ")]}}])})],1)],1)],2)],1)],1),t._v(" "),s("div",{staticStyle:{"margin-top":"20px","padding-bottom":"3px"}},[s("span",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)"}},[t._v("\n      实例（pod）列表\n    ")]),t._v(" "),s("span",{staticStyle:{display:"inline-block","margin-left":"40px",color:"coral","font-size":"13px"}},[t._v("\n      如果Pod无法正常启动\n      "),s("el-popover",{attrs:{placement:"top-start",width:"600",trigger:"click"}},[s("div",{staticStyle:{"line-height":"15px","font-size":"13px"}},[t._v('\n          1. 如果Pod一直处于"调度"状态，则说明集群资源不足。先确认Pod的CPU和内存资源配置是否合理。若配置合理，请联系集群管理员处理。 '),s("br"),s("br"),t._v('\n          2. 如果Pod一直处于"准备"状态，请查看Pod事件信息，确认是否存在具体的报错信息。'),s("br"),s("br"),t._v('\n          3. 如果Pod一直处于"启动"状态，请查看Pod启动日志，确认Tomcat启动状态，查看是否有业务报错信息。'),s("br"),s("br"),t._v('\n          4. 如果Pod发生了重启的话，请查看Pod重启前的日志（点击启动日志按钮，再勾选"重启前日志"复选框），查找可能得报错信息。'),s("br")]),t._v(" "),s("el-button",{staticStyle:{"font-size":"13px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("请查看排查手册")])],1)],1)]),t._v(" "),s("el-card",{staticClass:"box-card"},[t.job.id?s("deploy-pod-list",{attrs:{app:t.job.params.app,cluster:t.job.params.cluster,namespace:t.job.params.namespace,timestamp:new Date(t.job.createdAt).getTime()}}):t._e()],1),t._v(" "),s("div",{staticStyle:{"margin-top":"20px","font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)","padding-bottom":"3px"}},[t._v("日志")]),t._v(" "),s("el-card",{staticClass:"box-card"},[s("el-tabs",{attrs:{"tab-position":"top"}},t._l(t.tasks,(function(e,a){return s("el-tab-pane",{attrs:{label:e.title}},[s("div",[s("pre",{staticClass:"el-collapse-item__content",staticStyle:{"white-space":"pre-wrap","padding-left":"10px"}},[t._v(t._s(e.output))])])])})),1),t._v(" "),s("div",{staticStyle:{"padding-top":"10px"}})],1),t._v(" "),s("div",[s("el-backtop")],1)],1)},n=[],o=s("2d63"),i=(s("6762"),s("2fdb"),s("76fe")),c=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"pod-simple-table-wrapper"},[s("div",{staticStyle:{"text-align":"center"}},[t.podPendingAlert?s("div",{staticStyle:{display:"inline-block",width:"480px","text-align":"left"}},[s("el-alert",{attrs:{title:"有pod长时间未调度到宿主机上",type:"warning","show-icon":""}},[s("div",[s("div",[t._v("当资源池资源不足时，新的pod将无法正常调度到宿主机上")]),t._v(" "),s("div",[t._v("1. 如果必须要保障此次发布平滑性，可联系 @金哲玉 @吴志辉 协助处理")]),t._v(" "),s("div",[t._v("2. 如果允许此次发布造成业务抖动的话，可点击\n            "),s("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.redeployWithRecreate}},[t._v("使用重建升级")])],1),t._v(" "),s("div",[t._v("重建升级: 会先删除旧版本pod，释放资源后再创建新版本pod")])])])],1):t._e()]),t._v(" "),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.newPods.concat(t.oldPods),size:"small","expand-row-keys":t.expandRowKeys,"row-key":"name"}},[s("el-table-column",{attrs:{type:"expand",width:"40"},scopedSlots:t._u([{key:"default",fn:function(t){return[s("pod-event",{attrs:{cluster:t.row.cluster,namespace:t.row.namespace,pod:t.row.name}})]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return["new"===t.podVersionDesc(e.row.name)?s("el-tag",{attrs:{size:"mini",effect:"plain"}},[t._v(t._s(t.podVersionDesc(e.row.name)))]):s("el-tag",{attrs:{size:"mini",effect:"plain",type:"info"}},[t._v(t._s(t.podVersionDesc(e.row.name)))])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"实例名",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),s("el-table-column",{attrs:{width:"100"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n        状态\n        "),s("el-tooltip",{attrs:{effect:"light",placement:"top"}},[s("div",{attrs:{slot:"content"},slot:"content"},[s("el-image",{staticStyle:{"max-width":"800px"},attrs:{src:"images/pod-status.svg",alt:"pod状态"}})],1),t._v(" "),s("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return[s("span",{class:t.podStatusClass(e.row.statusDesc)}),t._v("\n        "+t._s(e.row.statusDesc)+"\n      ")]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"运行版本",prop:"deployTag","show-overflow-tooltip":""}}),t._v(" "),s("el-table-column",{attrs:{label:"重启数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.restartCount))]),t._v(" "),e.row.restartCount>0?s("div",{staticStyle:{"font-size":"10px",color:"#888"}},[t._v("重启原因: "+t._s(e.row.restartReason))]):t._e()]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"Pod IP",prop:"podIP","show-overflow-tooltip":""}}),t._v(" "),s("el-table-column",{attrs:{label:"Host IP",prop:"hostIP","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.hostIP))]),t._v(" "),e.row.resourcePool?s("div",{staticStyle:{"font-size":"10px",color:"#888"}},[t._v("资源池: "+t._s(e.row.resourcePool))]):t._e()]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"创建时间",prop:"createTime",width:"140"}}),t._v(" "),s("el-table-column",{attrs:{label:"",width:"160",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"事件列表",placement:"top"}},[s("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(s){return t.rowExpand(e.row.name)}}},[s("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"event"}})],1)],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"启动日志",placement:"top"}},[s("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(s){return t.podStdoutLog(e.row)}}},[s("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"log"}})],1)],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"进入容器",placement:"top"}},[s("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(s){return t.podShell(e.row)}}},[s("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"console"}})],1)],1)]}}])})],1),t._v(" "),s("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:t.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%","append-to-body":"",center:""},on:{"update:visible":function(e){t.podStdoutVisible=e},close:function(e){t.podStdout.pod=null}}},[s("div",{staticStyle:{"margin-top":"-30px"}},[s("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)])],1)},r=[],l=s("75fc"),d=(s("7f7f"),s("c5f6"),s("c1df")),u=s.n(d),p=s("cf89"),f=s("8504"),b=s("6e36"),m=s("1262"),j=s("a527"),h={name:"DeployPodList",components:{PodEvent:m["a"],PodExpand:b["a"],PodStdout:p["a"]},props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0},timestamp:{type:Number,required:!0}},data:function(){return{loading:!1,execution:{},oldPods:[],newPods:[],expandRowKeys:[],podPendingAlert:!1,podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]}}},watch:{},computed:{},mounted:function(){this.loadPods()},beforeDestroy:function(){},methods:{loadPods:function(){var t=this;this.loading=!0;var e=this;Object(j["h"])(this.cluster,this.namespace,this.app).then((function(e){t.oldPods=[],t.newPods=[];var s,a=Object(o["a"])(e.data);try{for(a.s();!(s=a.n()).done;){var n=s.value;new Date(n.createTime).getTime()<t.timestamp?t.oldPods.push(n):t.newPods.push(n)}}catch(i){a.e(i)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1,t.showAlert(),e.isReloadPods()&&setTimeout((function(){e.loadPods()}),2e4)}))},rowExpand:function(t){this.expandRowKeys&&this.expandRowKeys[0]===t?this.expandRowKeys=[]:this.expandRowKeys=[t]},isReloadPods:function(){if(this.oldPods.length>0)return!0;var t,e=!1,s=Object(o["a"])(this.newPods);try{for(s.s();!(t=s.n()).done;){var a=t.value;if(!a.ready){e=!0;break}}}catch(n){s.e(n)}finally{s.f()}return e},showAlert:function(){var t=!1;if(this.oldPods&&this.oldPods.length>0&&this.newPods&&this.newPods.length>0){var e,s=Object(o["a"])(this.newPods);try{for(s.s();!(e=s.n()).done;){var a=e.value;if("调度中"===a.statusDesc){var n=u()(),i=u()(a.createTime),c=n.diff(i,"seconds");if(c>120){console.log("podCreateTime pending time: "+c),t=!0;break}}}}catch(r){s.e(r)}finally{s.f()}}this.podPendingAlert=t},podVersionDesc:function(t){var e=this.oldPods.filter((function(e){return e.name===t}));return e&&e.length>0?"old":(e=this.newPods.filter((function(e){return e.name===t})),e&&e.length>0?"new":"--")},redeployWithRecreate:function(){var t=this;this.$confirm("此操作会先删除旧版本pod，释放资源后再创建新版本pod。在新版pod能正常提供服务前，当前服务将会处于不可用状态。确认是否继续？","重建升级",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(!t.oldPods||t.oldPods.length<1)t.$message.warning("没有旧版pod，无法执行当前操作");else{var e=t.oldPods[0];Object(f["i"])({cluster:e.cluster,namespace:e.namespace,app:e.labelApp}).then((function(e){t.$message.success("操作成功")})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()}))}})).catch((function(){}))},podStdoutLog:function(t){this.podStdoutVisible=!0,this.podStdout.cluster=t.cluster,this.podStdout.namespace=t.namespace,this.podStdout.pod=t.name,this.podStdout.containers=[t.container0Name].concat(Object(l["a"])(t.initContainersName))},podShell:function(t){var e="/api/page/redirect?type=webShell&cluster=".concat(t.cluster,"&namespace=").concat(t.namespace,"&app=").concat(t.labelApp,"&pods=").concat(t.name);window.open(e)},podStatusClass:function(t){if(t){if("运行中"===t)return"pod-status-green";if(["调度中","准备中","启动中"].includes(t))return"pod-status-orange"}return"pod-status-red"}}},v=h,g=(s("ffff8"),s("2877")),y=Object(g["a"])(v,c,r,!1,null,null,null),_=y.exports,w={data:function(){return{timerId:null,jobLoading:!0,job:{params:{}},tasks:[],activeNames:[],runningExecutions:[],jenkinsTable:"jenkinsTab2"}},components:{DeployPodList:_},computed:{},beforeDestroy:function(){this.timerId&&(clearTimeout(this.timerId),console.log("clear timer, id:"+this.timerId))},mounted:function(){var t=this.$route.query.jobId;t&&this.loadJob(t)},methods:{loadJob:function(t){var e=this;this.jobLoading=!0,Object(i["g"])(t).then((function(s){e.job=s.data,e.loadTasks(t);var a=e.job.status.toUpperCase(),n=e;"RUNNING"===a&&(e.timerId=setTimeout((function(){n.loadJob(t)}),5e3))})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.jobLoading=!1}))},loadTasks:function(t){var e=this;Object(i["h"])(t).then((function(t){e.tasks=t.data,e.modifyStages()})).catch((function(t){e.$message.error(t.message)}))},stepClick:function(t){this.activeNames.includes(t)?this.activeNames=[]:this.activeNames=[t]},modifyStages:function(){var t,e=Object(o["a"])(this.tasks);try{for(e.s();!(t=e.n()).done;){var s=t.value;s.status=this.toStepStatus(s.status),"process"===s.status?s.icon="el-icon-loading":s.icon=""}}catch(a){e.e(a)}finally{e.f()}},cancel:function(){var t=this;this.$confirm("取消后后端新旧实例还会继续进行替换。是否继续取消当前发布？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(i["d"])(t.job.id).then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},setJobFailed:function(){var t=this;Object(i["l"])(this.job.id,"fail").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},setJobSuccess:function(){var t=this;Object(i["l"])(this.job.id,"success").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},redo:function(){var t=this;this.$confirm("确认要使用当前参数重发吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(i["j"])(t.job.id).then((function(e){var s=e.data.id,a=t.$router.resolve({query:{jobId:s}});window.location.href=a.href,window.location.reload()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},podPage:function(){var t=this.$router.resolve({name:"pod-index",query:{cluster:this.job.params.cluster,namespace:this.job.params.namespace,app:this.job.params.app}});window.open(t.href,"_blank")},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.job.params.app}})},jobDetailPage:function(t){var e=this;Object(i["g"])(t).then((function(s){var a=s.data,n="CD"===a.type?"cicd-app-deploy-detail":"cicd-image-build-detail",o=e.$router.resolve({name:n,query:{jobId:t}});window.open(o.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))},toStepStatus:function(t){var e="";switch(t){case"WAIT":case"SKIP":case"CANCEL":e="wait";break;case"RUNNING":e="process";break;case"FAIL":e="error";break;case"SUCCESS":e="success";break;default:e="finish"}return e}}},k=w,S=(s("bb54"),Object(g["a"])(k,a,n,!1,null,null,null));e["default"]=S.exports},4678:function(t,e,s){var a={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"73332","./en-il.js":"73332","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function n(t){var e=o(t);return s(e)}function o(t){var e=a[t];if(!(e+1)){var s=new Error("Cannot find module '"+t+"'");throw s.code="MODULE_NOT_FOUND",s}return e}n.keys=function(){return Object.keys(a)},n.resolve=o,t.exports=n,n.id="4678"},"76fe":function(t,e,s){"use strict";s.d(e,"k",(function(){return n})),s.d(e,"i",(function(){return o})),s.d(e,"a",(function(){return i})),s.d(e,"e",(function(){return c})),s.d(e,"b",(function(){return r})),s.d(e,"f",(function(){return l})),s.d(e,"c",(function(){return d})),s.d(e,"g",(function(){return u})),s.d(e,"h",(function(){return p})),s.d(e,"d",(function(){return f})),s.d(e,"l",(function(){return b})),s.d(e,"j",(function(){return m}));var a=s("b775");function n(t){return Object(a["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function i(t){return Object(a["a"])({url:"/v1/job/build-image",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function l(t,e,s,n){return Object(a["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:s,remark:n}})}function d(t,e,s,n){return Object(a["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:s,remark:n}})}function u(t){return Object(a["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function p(t){return Object(a["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function f(t){return Object(a["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function b(t,e){return Object(a["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function m(t){return Object(a["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},bb54:function(t,e,s){"use strict";s("3336")},ffff8:function(t,e,s){"use strict";s("0736")}}]);