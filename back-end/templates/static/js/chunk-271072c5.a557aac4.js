(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-271072c5"],{2861:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-form",{ref:"searchForm",staticStyle:{height:"50px"},attrs:{inline:!0,model:t.searchForm}},[n("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[n("el-select",{attrs:{placeholder:"选择k8s集群"},on:{change:t.loadApp},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return n("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[n("el-select",{attrs:{placeholder:"选择Namespace"},on:{change:t.loadApp},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"应用",prop:"app"}},[n("el-select",{staticStyle:{width:"340px"},attrs:{filterable:"",placeholder:"选择应用"},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app",e)},expression:"searchForm.app"}},t._l(this.appOptions,(function(t){return n("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.addApp}},[t._v("添加")]),t._v(" "),n("el-button",{attrs:{type:"text"},on:{click:t.addAllApp}},[t._v("添加所有")])],1)],1),t._v(" "),n("el-table",{attrs:{data:this.tableData,"row-key":"name"}},[n("el-table-column",{attrs:{label:"应用名",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace"}})],1),t._v(" "),n("div",{staticStyle:{"padding-top":"10px","text-align":"center"}},[n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.yamlExportBtnLoading,expression:"yamlExportBtnLoading"}],attrs:{type:"primary",size:"small",icon:"el-icon-view"},on:{click:t.printYaml}},[t._v("查看表格服务Yaml")])],1),t._v(" "),n("el-dialog",{attrs:{title:"yaml内容",visible:t.yamlResultShow,width:"70%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.yamlResultShow=e}}},[n("div",{staticStyle:{"margin-top":"-30px"}},[n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",size:"small",icon:"el-icon-document-copy"},on:{click:t.copyToClipboard}},[t._v("一键复制")]),t._v(" "),n("pre",{staticStyle:{"white-space":"pre-wrap",border:"1px solid #eee",padding:"5px","margin-top":"5px"}},[t._v(t._s(this.yamlResult))])],1)])],1)},r=[],o=(n("7f7f"),n("2d63")),c=n("8504"),u=n("c1ab"),l={components:{},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(o["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(this.searchForm.cluster===n.name)return n.namespaces}}catch(a){e.e(a)}finally{e.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:""},appOptions:[],tableData:[],yamlResultShow:!1,yamlResult:"",yamlExportBtnLoading:!1}},methods:{loadApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace;if(t&&e){var n=this;Object(c["g"])(t,e).then((function(t){n.appOptions=t.data})).catch((function(t){console.error(t)}))}else this.appOptions=[]},addApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace,n=this.searchForm.app;if(n){var a,r="".concat(t,"@@").concat(e,"@@").concat(n),c=Object(o["a"])(this.tableData);try{for(c.s();!(a=c.n()).done;){var u=a.value;if(u.key===r)return void this.$message.warning("服务已存在")}}catch(s){c.e(s)}finally{c.f()}var l={key:r,cluster:t,namespace:e,app:n};this.tableData.push(l)}else this.$message.warning("请选择服务")},addAllApp:function(){this.$message.warning("todo")},printYaml:function(){var t=this;if(this.tableData.size<1)this.$message.warning("请先添加服务");else{this.yamlExportBtnLoading=!0,this.yamlResult="";var e=JSON.stringify(this.tableData);Object(u["H"])(e).then((function(e){t.yamlResult=e.data,t.yamlResultShow=!0})).catch((function(t){console.error(t)})).finally((function(){t.yamlExportBtnLoading=!1}))}},copyToClipboard:function(){var t=this,e=this.yamlResult;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},s=l,i=n("2877"),p=Object(i["a"])(s,a,r,!1,null,null,null);e["default"]=p.exports},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return c})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return l})),n.d(e,"i",(function(){return s})),n.d(e,"d",(function(){return i})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function s(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function i(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,o){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:o||""}})}},c1ab:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"j",(function(){return o})),n.d(e,"z",(function(){return c})),n.d(e,"A",(function(){return u})),n.d(e,"c",(function(){return l})),n.d(e,"f",(function(){return s})),n.d(e,"E",(function(){return i})),n.d(e,"G",(function(){return p})),n.d(e,"y",(function(){return d})),n.d(e,"a",(function(){return m})),n.d(e,"e",(function(){return f})),n.d(e,"D",(function(){return h})),n.d(e,"F",(function(){return v})),n.d(e,"x",(function(){return b})),n.d(e,"H",(function(){return y})),n.d(e,"k",(function(){return g})),n.d(e,"d",(function(){return O})),n.d(e,"B",(function(){return j})),n.d(e,"i",(function(){return k})),n.d(e,"g",(function(){return w})),n.d(e,"s",(function(){return x})),n.d(e,"v",(function(){return _})),n.d(e,"w",(function(){return F})),n.d(e,"o",(function(){return $})),n.d(e,"p",(function(){return A})),n.d(e,"t",(function(){return S})),n.d(e,"u",(function(){return C})),n.d(e,"b",(function(){return R})),n.d(e,"q",(function(){return D})),n.d(e,"r",(function(){return E})),n.d(e,"n",(function(){return B})),n.d(e,"C",(function(){return L})),n.d(e,"m",(function(){return q})),n.d(e,"l",(function(){return z}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(a["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(a["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function l(){return Object(a["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(a["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,n,r,o,c,u,l){return Object(a["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(u),method:"post",data:l})}function h(t){return Object(a["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function y(t){return Object(a["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function g(t,e,n,r){return Object(a["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function O(t,e,n,r,o){return Object(a["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function j(){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function k(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function w(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function x(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function _(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function F(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function $(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function A(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function S(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function C(t,e){return Object(a["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function R(t){return Object(a["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function D(t){return Object(a["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function E(t){return Object(a["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function B(t,e,n){return Object(a["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}function L(t){return Object(a["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function q(t,e,n){return Object(a["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(n),method:"get"})}function z(){return Object(a["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}}}]);