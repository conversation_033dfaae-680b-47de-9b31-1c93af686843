(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c04aaf0"],{"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return a})),n.d(e,"e",(function(){return o})),n.d(e,"d",(function(){return u})),n.d(e,"l",(function(){return c})),n.d(e,"m",(function(){return l})),n.d(e,"a",(function(){return i})),n.d(e,"f",(function(){return s})),n.d(e,"i",(function(){return p})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return m})),n.d(e,"n",(function(){return f})),n.d(e,"g",(function(){return b})),n.d(e,"b",(function(){return v})),n.d(e,"h",(function(){return h})),n.d(e,"o",(function(){return y}));var r=n("b775");function a(t){return Object(r["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(r["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function u(t,e,n){return Object(r["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(r["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function i(){return Object(r["a"])({url:"/v1/pipeline/all",method:"get"})}function s(t){return Object(r["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(r["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(r["a"])({url:"/v1/pipeline",method:"post",data:t})}function m(t){return Object(r["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function f(t){return Object(r["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function b(t,e,n,a){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function v(t){return Object(r["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function h(t,e,n,a){return Object(r["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:a}})}function y(t){return Object(r["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"757c":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}],staticClass:"app-container"},[n("el-alert",{attrs:{title:"应用批量重发V2(CI/CD分离模式）",type:"info",description:"使用当前运行的版本重新发布应用。","show-icon":""}}),t._v(" "),n("el-row",{staticStyle:{"max-width":"1080px","margin-top":"10px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:18}},[n("el-form",[n("el-form-item",[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"内容格式：集群/环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1)],1)],1),t._v(" "),n("el-col",{attrs:{span:6}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.loadTableData}},[t._v("查询")]),t._v(" "),n("br"),n("br"),t._v(" "),n("el-checkbox",{model:{value:t.openVersionCompare,callback:function(e){t.openVersionCompare=e},expression:"openVersionCompare"}},[t._v("开启版本对比")])],1)],1),t._v(" "),n("el-table",{attrs:{data:t.tableData,border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"发布过？",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.extraAttr.reDeployed)+"\n      ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.openVersionCompare&&"k8s0"!==e.row.cluster?t._e():n("div",[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.reDeploy(e.row)}}},[t._v("使用当前版本重发\n          ")]),t._v(" "),n("br"),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy-history",query:{}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n              发布历史\n            ")])],1),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n              发布流程\n            ")])],1)],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace"}}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",prop:"status"}}),t._v(" "),n("el-table-column",{attrs:{label:"实例数(运行/配置)",align:"center",prop:"replicas"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.openVersionCompare&&"k8s0"===e.row.cluster&&!t.replicasIsSame(e.row.app,e.row.namespace)?n("div",{staticStyle:{color:"#FB5151"}},[t._v("\n          "+t._s(e.row.extraAttr.runningPodNum)+" / "+t._s(e.row.replicas)+"\n        ")]):n("div",[t._v("\n          "+t._s(e.row.extraAttr.runningPodNum)+" / "+t._s(e.row.replicas)+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"版本",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.openVersionCompare&&"k8s0"===e.row.cluster&&!t.versionIsSame(e.row.app,e.row.namespace)?n("div",{staticStyle:{color:"#FB5151"}},[t._v("\n          "+t._s(e.row.extraAttr.deployTag)+"\n        ")]):n("div",[t._v("\n          "+t._s(e.row.extraAttr.deployTag)+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"发版信息"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticStyle:{"font-size":"10px"}},[n("div",[t._v("发布人:"+t._s(e.row.extraAttr.deployUser))]),t._v(" "),n("div",[t._v(t._s(e.row.extraAttr.deployRemark))])])]}}])})],1)],1)},a=[],o=n("2d63"),u=n("51a9"),c=n("8504"),l=n("76fe"),i={components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{pageLoading:!1,openVersionCompare:!1,searchForm:"",tableData:[],deployRemark:""}},methods:{loadTableData:function(){var t=this;this.pageLoading=!0,this.tableData=[],Object(u["m"])(this.searchForm).then((function(e){var n,r=Object(o["a"])(e.data);try{for(r.s();!(n=r.n()).done;){var a=n.value;a.extraAttr.deployTag="?",a.extraAttr.runningPodNum="?",a.extraAttr.reDeployed="NO",t.findDeployment(a)}}catch(u){r.e(u)}finally{r.f()}t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.pageLoading=!1}))},versionIsSame:function(t,e){var n,r="",a="",u=Object(o["a"])(this.tableData);try{for(u.s();!(n=u.n()).done;){var c=n.value;"k8s0"===c.cluster&&c.namespace===e&&c.app===t&&(r=c.extraAttr.deployTag),"k8s1"===c.cluster&&c.namespace===e&&c.app===t&&(a=c.extraAttr.deployTag)}}catch(l){u.e(l)}finally{u.f()}return""!==r&&""!==a&&r===a},replicasIsSame:function(t,e){var n,r="",a="",u="",c="",l=Object(o["a"])(this.tableData);try{for(l.s();!(n=l.n()).done;){var i=n.value;"k8s0"===i.cluster&&i.namespace===e&&i.app===t&&(r=i.replicas,u=i.runningPodNum),"k8s1"===i.cluster&&i.namespace===e&&i.app===t&&(a=i.replicas,c=i.runningPodNum)}}catch(s){l.e(s)}finally{l.f()}return r===a&&u===c},findDeployment:function(t){null!==t?Object(c["a"])(t.cluster,t.namespace,t.app).then((function(e){t.extraAttr.deployTag=e.data.deployTag,t.extraAttr.runningPodNum=e.data.replicas,t.extraAttr.deployRemark=e.data.deployRemark,t.extraAttr.deployUser=e.data.deployUser})).catch((function(t){console.log(t.message)})):this.$message.warning("找不到发布流程，应用：".concat(cluster," / ").concat(namespace," / ").concat(app))},reDeploy:function(t){var e=this;this.$prompt("请填写发版备注","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:this.deployRemark}).then((function(n){var r=n.value;e.deployRemark=r,Object(l["f"])(t.cluster,t.namespace,t.app,r).then((function(n){e.$message.success("操作成功"),t.extraAttr.reDeployed="YES"})).catch((function(t){e.$message.error(t.message)}))}))}}},s=i,p=n("2877"),d=Object(p["a"])(s,r,a,!1,null,null,null);e["default"]=d.exports},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return a})),n.d(e,"i",(function(){return o})),n.d(e,"a",(function(){return u})),n.d(e,"e",(function(){return c})),n.d(e,"b",(function(){return l})),n.d(e,"f",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"g",(function(){return p})),n.d(e,"h",(function(){return d})),n.d(e,"d",(function(){return m})),n.d(e,"l",(function(){return f})),n.d(e,"j",(function(){return b}));var r=n("b775");function a(t){return Object(r["a"])({url:"/v1/job/search",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function u(t){return Object(r["a"])({url:"/v1/job/build-image",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function i(t,e,n,a){return Object(r["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function s(t,e,n,a){return Object(r["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function p(t){return Object(r["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(r["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(r["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function f(t,e){return Object(r["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function b(t){return Object(r["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return u})),n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return l})),n.d(e,"i",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var r=n("b775");function a(t,e){return Object(r["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function u(t){return Object(r["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function c(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(r["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function i(t){return Object(r["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function s(t,e,n){return Object(r["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(r["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,a,o){return Object(r["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:a,deployTag:o||""}})}}}]);