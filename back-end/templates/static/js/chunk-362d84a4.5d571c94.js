(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-362d84a4"],{"1e42":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},i=[],l=(a("a481"),a("25ca")),n=a("21a6"),o=a.n(n),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=l["a"].table_to_book(t,{raw:!0}),a=l["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var r=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";o.a.saveAs(new Blob([a],{type:"application/octet-stream"}),r)}catch(i){this.$message.error("导出失败, err: "+i.message),console.error(i)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),d=Object(u["a"])(c,r,i,!1,null,null,null);e["a"]=d.exports},"28a5":function(t,e,a){"use strict";var r=a("aae3"),i=a("cb7c"),l=a("ebd6"),n=a("0390"),o=a("9def"),s=a("5f1b"),c=a("520a"),u=a("79e5"),d=Math.min,p=[].push,m="split",b="length",h="lastIndex",f=4294967295,v=!u((function(){RegExp(f,"y")}));a("214f")("split",2,(function(t,e,a,u){var g;return g="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[b]||2!="ab"[m](/(?:ab)*/)[b]||4!="."[m](/(.?)(.?)/)[b]||"."[m](/()()/)[b]>1||""[m](/.?/)[b]?function(t,e){var i=String(this);if(void 0===t&&0===e)return[];if(!r(t))return a.call(i,t,e);var l,n,o,s=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,m=void 0===e?f:e>>>0,v=new RegExp(t.source,u+"g");while(l=c.call(v,i)){if(n=v[h],n>d&&(s.push(i.slice(d,l.index)),l[b]>1&&l.index<i[b]&&p.apply(s,l.slice(1)),o=l[0][b],d=n,s[b]>=m))break;v[h]===l.index&&v[h]++}return d===i[b]?!o&&v.test("")||s.push(""):s.push(i.slice(d)),s[b]>m?s.slice(0,m):s}:"0"[m](void 0,0)[b]?function(t,e){return void 0===t&&0===e?[]:a.call(this,t,e)}:a,[function(a,r){var i=t(this),l=void 0==a?void 0:a[e];return void 0!==l?l.call(a,i,r):g.call(String(i),a,r)},function(t,e){var r=u(g,t,this,e,g!==a);if(r.done)return r.value;var c=i(t),p=String(this),m=l(c,RegExp),b=c.unicode,h=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(v?"y":"g"),y=new m(v?c:"^(?:"+c.source+")",h),_=void 0===e?f:e>>>0;if(0===_)return[];if(0===p.length)return null===s(y,p)?[p]:[];var x=0,k=0,S=[];while(k<p.length){y.lastIndex=v?k:0;var w,j=s(y,v?p:p.slice(k));if(null===j||(w=d(o(y.lastIndex+(v?0:k)),p.length))===x)k=n(p,k,b);else{if(S.push(p.slice(x,k)),S.length===_)return S;for(var F=1;F<=j.length-1;F++)if(S.push(j[F]),S.length===_)return S;k=x=w}}return S.push(p.slice(x)),S}]}))},"340b":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container build-history-container",staticStyle:{margin:"0",padding:"0"}},[a("menu-tabs",{attrs:{"tab-name":"image-build-history"}}),t._v(" "),a("div",{staticStyle:{padding:"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{size:"small",inline:!0,model:t.searchForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"任务状态"}},[a("el-select",{staticStyle:{width:"190px"},attrs:{multiple:"",filterable:"",clearable:""},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[a("el-option",{attrs:{label:"运行中",value:"RUNNING"}}),t._v(" "),a("el-option",{attrs:{label:"已取消",value:"CANCEL"}}),t._v(" "),a("el-option",{attrs:{label:"等待中",value:"WAIT"}}),t._v(" "),a("el-option",{attrs:{label:"成功",value:"SUCCESS"}}),t._v(" "),a("el-option",{attrs:{label:"失败",value:"FAIL"}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"来源应用"}},[a("el-input",{staticStyle:{width:"260px"},attrs:{clearable:""},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app","string"===typeof e?e.trim():e)},expression:"searchForm.app"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"操作人"}},[a("el-input",{staticStyle:{width:"140px"},attrs:{clearable:""},model:{value:t.searchForm.author,callback:function(e){t.$set(t.searchForm,"author","string"===typeof e?e.trim():e)},expression:"searchForm.author"}})],1),t._v(" "),a("br"),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"-10px"},attrs:{label:"Git模块"}},[a("el-select",{staticStyle:{"margin-bottom":"10px",width:"750px"},attrs:{filterable:"",clearable:""},model:{value:t.gitModuleFullName,callback:function(e){t.gitModuleFullName=e},expression:"gitModuleFullName"}},t._l(t.artifacts,(function(t){return a("el-option",{key:t.id,attrs:{label:t.gitUrl+(t.module?" --- "+t.module:""),value:t.gitUrl+"---"+t.module}})})),1)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"-10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("div",{staticStyle:{float:"right"}},[a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,"page-sizes":[10,20,50,100,500,1e3,2e3,5e3],"pager-count":5,layout:"total,sizes,prev,pager,next",total:t.tableData.count},on:{"size-change":t.pageSizeChange,"current-change":t.pageChange}})],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData.data,"element-loading-text":"数据加载中...","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"来源应用",prop:"app"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{color:"black"}},[t._v(t._s(e.row.app))]),t._v(" "),a("div",{staticStyle:{"font-weight":"bold"}},[a("router-link",{staticStyle:{color:"#409EFF",display:"inline-block"},attrs:{to:{name:"cicd-image-build-detail",query:{jobId:e.row.id}},target:"_blank"}},[a("div",{staticStyle:{"font-size":"12px"}},[t._v("构建详情")])]),t._v(" "),a("router-link",{staticStyle:{color:"#409EFF",display:"inline-block","margin-left":"5px"},attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[a("div",{staticStyle:{"font-size":"12px"}},[t._v("去发布")])])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"Git模块","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"12px","line-height":"18px"}},[a("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:e.row.params.gitUrl,target:"_blank"}},[t._v(t._s(e.row.params.gitUrl))]),t._v(" "),a("div",[t._v("[子模块]: "+t._s(e.row.params.gitModule))]),t._v(" "),a("div",[t._v("[分支|标签]: "+t._s(e.row.params.gitTag))]),t._v(" "),a("div",[t._v("[commitId]: "+t._s(e.row.params.commitId||"-"))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"容器镜像",prop:"params.gitTag","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"12px","line-height":"18px"}},[a("div",{staticStyle:{color:"black"}},[t._v("\n              版本："+t._s(e.row.params.artifactImage?e.row.params.artifactImage.split(":").pop():"-")+"\n            ")]),t._v(" "),a("div",{staticStyle:{color:"#888"}},[a("div",[t._v(" [父pom]: "+t._s(e.row.params.parentPom))])]),t._v(" "),a("div",{staticStyle:{color:"#888"}},[a("div",[t._v(" [构建环境]: "+t._s(e.row.buildEnv))])])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"参数"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-checkbox",{staticClass:"checkbox001",attrs:{label:"单元测试",checked:e.row.params.unitTest,disabled:""}}),t._v(" "),a("br"),t._v(" "),a("el-checkbox",{staticClass:"checkbox001",attrs:{label:"强制编译",checked:e.row.params.forceCodeCompile,disabled:""}}),t._v(" "),a("br"),t._v(" "),a("el-checkbox",{staticClass:"checkbox001",attrs:{label:"依赖包校验",checked:e.row.params.dependencyCheck,disabled:""}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"耗时（秒）",width:"100",prop:"timeCost",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"备注",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return["SUCCESS"===e.row.status?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("成功")])],1):"FAIL"===e.row.status?a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("失败")])],1):"CANCEL"===e.row.status?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("已取消")])],1):"WAIT"===e.row.status?a("div",[a("el-tag",{attrs:{type:"info",effect:"plain"}},[t._v("等待中...")]),t._v(" "),a("div",{staticStyle:{"font-size":"12px",color:"#409EFF"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"前置任务完成后，才会启动当前任务",placement:"top"}},[a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.jobDetailPage(e.row.beforeJobId)}}},[t._v("\n                  查看前置任务\n                ")])],1)],1)],1):"RUNNING"===e.row.status?a("div",[a("el-tag",{attrs:{type:"info",effect:"plain"}},[t._v("运行中...")])],1):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作人",width:"130",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"100",prop:"createdAt"}})],1)],1)],1)},i=[],l=a("2d63"),n=(a("28a5"),a("76fe")),o=a("530d"),s=a("84d4"),c=a("1e42"),u={name:"image-build-history",components:{ExportButton:c["a"],MenuTabs:s["a"]},data:function(){return{tableData:{data:[],count:0},artifacts:[],tableLoading:!1,gitModuleFullName:"",searchForm:{type:"CI",author:null,app:"",params:{gitUrl:"",gitModule:""},status:"",page:1,limit:20}}},computed:{},mounted:function(){this.$route.query.author&&(this.searchForm.author=this.$route.query.author),null==this.searchForm.author&&this.$store.state.user&&this.$store.state.user.realName&&(this.searchForm.author=this.$store.state.user.realName),this.$route.query.gitUrl&&(this.gitModuleFullName=this.$route.query.gitUrl+"---"+(this.$route.query.gitModule?this.$route.query.gitModule:"")),this.loadAllArtifact(),this.loadTableData()},methods:{loadTableData:function(){var t=this;if(this.tableLoading=!0,this.gitModuleFullName){var e=this.gitModuleFullName.split("---");this.searchForm.params.gitUrl=e[0],this.searchForm.params.gitModule=e[1]}else this.searchForm.params.gitUrl="",this.searchForm.params.gitModule="";Object(n["k"])(this.searchForm).then((function(e){var a,r=[],i=Object(l["a"])(e.data.data);try{for(i.s();!(a=i.n()).done;){var n=a.value,o=n.params.mavenImage;if(o){var s=o.split("/");s.length>0&&(o=s[s.length-1])}n.buildEnv=o,r.push(n)}}catch(c){i.e(c)}finally{i.f()}t.tableData={data:r,count:e.data.count}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},loadAllArtifact:function(){var t=this;Object(o["d"])().then((function(e){t.artifacts=e.data})).catch((function(e){t.$message.error(e.message)}))},pageChange:function(t){this.searchForm.page=t,this.loadTableData()},pageSizeChange:function(t){this.searchForm.limit=t,this.loadTableData()},jobDetailPage:function(t){var e=this;Object(n["g"])(t).then((function(a){var r=a.data,i="CD"===r.type?"cicd-app-deploy-detail":"cicd-image-build-detail",l=e.$router.resolve({name:i,query:{jobId:t}});window.open(l.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))}}},d=u,p=(a("3b3e"),a("2877")),m=Object(p["a"])(d,r,i,!1,null,null,null);e["default"]=m.exports},"3b3e":function(t,e,a){"use strict";a("3b7e")},"3b7e":function(t,e,a){},"530d":function(t,e,a){"use strict";a.d(e,"d",(function(){return i})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s}));var r=a("b775");function i(){return Object(r["a"])({url:"/v1/artifact/all",method:"get"})}function l(t){return Object(r["a"])({url:"/v1/artifact/search",method:"get",params:t})}function n(t){return Object(r["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/v1/artifact",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},"6d2d":function(t,e,a){},"76fe":function(t,e,a){"use strict";a.d(e,"k",(function(){return i})),a.d(e,"i",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"e",(function(){return o})),a.d(e,"b",(function(){return s})),a.d(e,"f",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"g",(function(){return d})),a.d(e,"h",(function(){return p})),a.d(e,"d",(function(){return m})),a.d(e,"l",(function(){return b})),a.d(e,"j",(function(){return h}));var r=a("b775");function i(t){return Object(r["a"])({url:"/v1/job/search",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function n(t){return Object(r["a"])({url:"/v1/job/build-image",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function c(t,e,a,i){return Object(r["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:i}})}function u(t,e,a,i){return Object(r["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:a,remark:i}})}function d(t){return Object(r["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function p(t){return Object(r["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function m(t){return Object(r["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function b(t,e){return Object(r["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function h(t){return Object(r["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},"84d4":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cicd-menu-tabs"},[a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.currTab,callback:function(e){t.currTab=e},expression:"currTab"}},[a("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),t._v("应用发布")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"app-deploy-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("发布记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像构建")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-build-history"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("构建记录")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"image-list"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像列表")],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[a("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v(" "),a("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[t._v("查看使用手册")])],1)])],1)],1)},i=[],l=(a("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(t,e){"app-deploy"===t.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===t.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===t.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===t.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===t.name?this.$router.push({name:"cicd-image-list"}):"doc"===t.name||this.$message.error("未知操作")}}}),n=l,o=(a("df01"),a("2877")),s=Object(o["a"])(n,r,i,!1,null,null,null);e["a"]=s.exports},aae3:function(t,e,a){var r=a("d3f4"),i=a("2d95"),l=a("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[l])?!!e:"RegExp"==i(t))}},df01:function(t,e,a){"use strict";a("6d2d")}}]);