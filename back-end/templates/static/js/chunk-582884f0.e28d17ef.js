(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-582884f0"],{"11e9":function(t,e,n){var o=n("52a7"),a=n("4630"),r=n("6821"),i=n("6a99"),s=n("69a8"),c=n("c69a"),u=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?u:function(t,e){if(t=r(t),e=i(e,!0),c)try{return u(t,e)}catch(n){}if(s(t,e))return a(!o.f.call(t,e),t[e])}},1262:function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.eventsLoading,expression:"eventsLoading"}],staticClass:"pod-event",attrs:{"element-loading-text":"事件加载中..."}},[t.events&&t.events.length>0?n("div",[n("el-timeline",t._l(t.events,(function(e,o){return n("el-timeline-item",{key:o,attrs:{placement:"top","hide-timestamp":!0,timestamp:e.lastTime?e.lastTime:e.createTime}},[n("div",[n("span",[t._v(t._s(e.lastTime?e.lastTime:e.createTime))]),t._v(" "),n("el-tag",{staticStyle:{margin:"0 10px"},attrs:{size:"small",type:"Warning"===e.type?"warning":"info"}},[t._v(t._s(e.type))]),t._v(" "),n("span",{staticStyle:{padding:"0 10px"}},[t._v(" ( x"+t._s(e.count)+" )")]),t._v(" "),n("span",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(e.reason)+":")]),t._v(" "+t._s(e.message)+"\n        ")],1)])})),1)],1):t.events&&0===t.events.length?n("div",[n("el-empty",{attrs:{description:"暂无事件"}})],1):t._e()])},a=[],r=(n("6762"),n("2fdb"),n("a527")),i={props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!0},hiddenStartUpEvent:{type:Boolean,default:!0}},created:function(){var t=this;this.$parent.$el&&this.$parent.$el.offsetParent&&this.$parent.$el.offsetParent.className&&this.$parent.$el.offsetParent.className.includes("fixed")||this.$nextTick((function(){t.loadEvents()}))},computed:{},data:function(){return{events:null,eventsLoading:!1}},methods:{loadEvents:function(){var t=this;this.eventsLoading=!0,Object(r["n"])(this.cluster,this.namespace,this.pod).then((function(e){var n=e.data;t.hiddenStartUpEvent&&(n=n.filter((function(t){return!t.message.includes("Startup probe failed")}))),t.events=n})).catch((function(e){t.$message.error("pod事件加载失败: "+e.message)})).finally((function(){t.eventsLoading=!1}))}}},s=i,c=n("2877"),u=Object(c["a"])(s,o,a,!1,null,null,null);e["a"]=u.exports},"2fdb":function(t,e,n){"use strict";var o=n("5ca1"),a=n("d2c8"),r="includes";o(o.P+o.F*n("5147")(r),"String",{includes:function(t){return!!~a(this,t,r).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},5147:function(t,e,n){var o=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[o]=!1,!"/./"[t](e)}catch(a){}}return!0}},"5dbc":function(t,e,n){var o=n("d3f4"),a=n("8b97").set;t.exports=function(t,e,n){var r,i=e.constructor;return i!==n&&"function"==typeof i&&(r=i.prototype)!==n.prototype&&o(r)&&a&&a(t,r),t}},6762:function(t,e,n){"use strict";var o=n("5ca1"),a=n("c366")(!0);o(o.P,"Array",{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")("includes")},"6e36":function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pod-expand"},[t.pod?n("div",{staticClass:"expand-wrapper"},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form",{attrs:{"label-position":"left","label-width":"80px",size:"mini"}},[n("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"实例名"}},[n("span",[t._v(t._s(t.pod.name))])]),t._v(" "),n("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"集群/环境"}},[n("span",[t._v(t._s(t.pod.cluster)+"/"+t._s(t.pod.namespace))])]),t._v(" "),n("el-form-item",{attrs:{label:"运行版本"}},[n("span",[t._v(t._s(t.pod.deployTag))])]),t._v(" "),n("el-form-item",{attrs:{label:"基础镜像"}},[n("span",[t._v(t._s(t.pod.container0Image))])]),t._v(" "),n("el-form-item",{attrs:{label:"业务镜像"}},t._l(t.pod.initContainersImage,(function(e){return n("div",{key:e.module},[t._v("\n              • "),n("span",[t._v(t._s(e))])])})),0),t._v(" "),n("el-form-item",{attrs:{label:"状态"}},[t._v("\n            "+t._s(t.pod.statusDesc)+"\n            "),n("span",{staticStyle:{"padding-left":"10px"}},[t._v(" |\n               "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("PodPhase: "+t._s(t.pod.phase))]),t._v(" "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("podReady: "+t._s(t.pod.ready))]),t._v(" "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("Container0Status: "+t._s(t.pod.container0Status))])],1)]),t._v(" "),n("el-form-item",{attrs:{label:"所在机器"}},[n("div",[n("span",[t._v(t._s(t.pod.hostIP))]),t._v(" "),n("span",[t._v("\n                （资源池: "+t._s(t.pod.resourcePool?t.pod.resourcePool:"Common")+" ）\n              ")])])]),t._v(" "),n("el-form-item",{attrs:{label:"资源配置"}},[n("span",[n("b",{staticStyle:{"padding-right":"5px"}},[t._v("CPU: ")]),t._v(t._s((t.pod.requestCpu/1e3).toFixed(2))+" - "+t._s((t.pod.limitCpu/1e3).toFixed(2))+" ")]),t._v(" "),n("span",[n("b",{staticStyle:{"padding-right":"5px","padding-left":"30px"}},[t._v("内存 (MB): ")]),t._v("\n              "+t._s(Math.floor(t.pod.requestMemory/1024/1024))+" - "+t._s(Math.floor(t.pod.limitMemory/1024/1024))+"\n            ")])]),t._v(" "),n("el-form-item",{attrs:{label:"重启次数"}},[n("span",[t._v(t._s(t.pod.restartCount))])]),t._v(" "),t.pod.restartCount>0?n("el-form-item",{attrs:{label:"最近重启时间"}},[n("span",[t._v(t._s(t.pod.restartTime))])]):t._e(),t._v(" "),t.pod.restartCount>0?n("el-form-item",{attrs:{label:"最近重启说明"}},[n("span",[t._v("Reason: "+t._s(t.pod.restartReason||"--"))]),t._v(" "),n("span",{staticStyle:{"padding-left":"20px"}},[t._v("ExitCode: "+t._s(t.pod.restartCode))])]):t._e()],1)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{color:"#606266","font-weight":"bold","padding-left":"15px","line-height":"40px"}},[t._v("事件列表")]),t._v(" "),n("pod-event",{attrs:{cluster:t.pod.cluster,namespace:t.pod.namespace,pod:t.pod.name}})],1)],1)],1):t._e()])},a=[],r=n("1262"),i={components:{PodEvent:r["a"]},props:{pod:{type:Object,default:function(){return{}},required:!0}},created:function(){},computed:{},data:function(){return{}},methods:{}},s=i,c=(n("8f56"),n("2877")),u=Object(c["a"])(s,o,a,!1,null,null,null);e["a"]=u.exports},"75fc":function(t,e,n){"use strict";var o=n("a745"),a=n.n(o),r=n("db2a");function i(t){if(a()(t))return Object(r["a"])(t)}var s=n("67bb"),c=n.n(s),u=n("5d58"),l=n.n(u),d=n("774e"),p=n.n(d);function f(t){if("undefined"!==typeof c.a&&null!=t[l.a]||null!=t["@@iterator"])return p()(t)}var m=n("e630");function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t){return i(t)||f(t)||Object(m["a"])(t)||v()}n.d(e,"a",(function(){return h}))},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"a",(function(){return r})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return u})),n.d(e,"d",(function(){return l})),n.d(e,"f",(function(){return d})),n.d(e,"e",(function(){return p}));var o=n("b775");function a(t,e){return Object(o["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function r(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(o["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function s(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(o["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function u(t){return Object(o["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function l(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function d(t){return Object(o["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function p(t,e,n,a,r){return Object(o["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:a,deployTag:r||""}})}},"8b97":function(t,e,n){var o=n("d3f4"),a=n("cb7c"),r=function(t,e){if(a(t),!o(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,o){try{o=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),o(t,[]),e=!(t instanceof Array)}catch(a){e=!0}return function(t,n){return r(t,n),e?t.__proto__=n:o(t,n),t}}({},!1):void 0),check:r}},"8f56":function(t,e,n){"use strict";n("e0c2")},9093:function(t,e,n){var o=n("ce10"),a=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return o(t,a)}},a527:function(t,e,n){"use strict";n.d(e,"h",(function(){return a})),n.d(e,"d",(function(){return r})),n.d(e,"i",(function(){return i})),n.d(e,"e",(function(){return s})),n.d(e,"g",(function(){return c})),n.d(e,"c",(function(){return u})),n.d(e,"k",(function(){return l})),n.d(e,"l",(function(){return d})),n.d(e,"m",(function(){return p})),n.d(e,"o",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"r",(function(){return v})),n.d(e,"b",(function(){return h})),n.d(e,"a",(function(){return g})),n.d(e,"p",(function(){return _})),n.d(e,"q",(function(){return b})),n.d(e,"n",(function(){return y})),n.d(e,"j",(function(){return S}));n("96cf"),n("3b8d");var o=n("b775");function a(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function r(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t,e){return Object(o["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:t,namespace:e}})}function s(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:t,namespace:e,pod:n}})}function c(t,e,n,a,r,i){return Object(o["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:t,namespace:e,pod:n,container:a,tailLines:r,previous:i}})}function u(t,e,n,o,a){var r="/api/v1/k8s/pod/stdout/download?cluster=".concat(t,"&namespace=").concat(e,"&pod=").concat(n,"&container=").concat(o,"&tailLines=").concat(a,'&_time="')+(new Date).getTime();window.open(r)}function l(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:t,namespace:e,pod:n}})}function d(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:t,namespace:e,pod:n}})}function p(t){return Object(o["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:t}})}function f(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:t,namespace:e,pod:n}})}function m(t){return Object(o["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:t})}function v(t){return Object(o["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:t})}function h(t,e){window.open("/api/v1/k8s/pod/file/download?fileId="+t+"&fileName="+e+"&_time="+(new Date).getTime())}function g(t){return Object(o["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:t})}function _(t){window.open("/api/v1/k8s/pod/file/preview?fileId="+t)}function b(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:t,namespace:e,pod:n}})}function y(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:t,namespace:e,pod:n}})}function S(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:t,namespace:e,pod:n}})}},aa77:function(t,e,n){var o=n("5ca1"),a=n("be13"),r=n("79e5"),i=n("fdef"),s="["+i+"]",c="​",u=RegExp("^"+s+s+"*"),l=RegExp(s+s+"*$"),d=function(t,e,n){var a={},s=r((function(){return!!i[t]()||c[t]()!=c})),u=a[t]=s?e(p):i[t];n&&(a[n]=u),o(o.P+o.F*s,"String",a)},p=d.trim=function(t,e){return t=String(a(t)),1&e&&(t=t.replace(u,"")),2&e&&(t=t.replace(l,"")),t};t.exports=d},aae3:function(t,e,n){var o=n("d3f4"),a=n("2d95"),r=n("2b4c")("match");t.exports=function(t){var e;return o(t)&&(void 0!==(e=t[r])?!!e:"RegExp"==a(t))}},c5f6:function(t,e,n){"use strict";var o=n("7726"),a=n("69a8"),r=n("2d95"),i=n("5dbc"),s=n("6a99"),c=n("79e5"),u=n("9093").f,l=n("11e9").f,d=n("86cc").f,p=n("aa77").trim,f="Number",m=o[f],v=m,h=m.prototype,g=r(n("2aeb")(h))==f,_="trim"in String.prototype,b=function(t){var e=s(t,!1);if("string"==typeof e&&e.length>2){e=_?e.trim():p(e,3);var n,o,a,r=e.charCodeAt(0);if(43===r||45===r){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===r){switch(e.charCodeAt(1)){case 66:case 98:o=2,a=49;break;case 79:case 111:o=8,a=55;break;default:return+e}for(var i,c=e.slice(2),u=0,l=c.length;u<l;u++)if(i=c.charCodeAt(u),i<48||i>a)return NaN;return parseInt(c,o)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(g?c((function(){h.valueOf.call(n)})):r(n)!=f)?i(new v(b(e)),n,m):b(e)};for(var y,S=n("9e1e")?u(v):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),x=0;S.length>x;x++)a(v,y=S[x])&&!a(m,y)&&d(m,y,l(v,y));m.prototype=h,h.constructor=m,n("2aba")(o,f,m)}},cf89:function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[n("div",{staticStyle:{position:"relative"}},[n("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[t._v(t._s(this.pod)+" /\n      "),n("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:t.loadStdoutLog},model:{value:t.container,callback:function(e){t.container=t._n(e)},expression:"container"}},t._l(t.containers,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),n("div",{staticStyle:{float:"right","margin-right":"20px"}},[n("span",[n("el-checkbox",{on:{change:t.loadStdoutLog},model:{value:t.stdout.previous,callback:function(e){t.$set(t.stdout,"previous",e)},expression:"stdout.previous"}},[t._v("重启前日志")])],1),t._v(" "),n("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n            行数:\n            "),n("el-select",{staticStyle:{width:"120px"},on:{change:t.loadStdoutLog},model:{value:t.stdout.tailLines,callback:function(e){t.$set(t.stdout,"tailLines",t._n(e))},expression:"stdout.tailLines"}},[n("el-option",{attrs:{label:"2000",value:"2000"}}),t._v(" "),n("el-option",{attrs:{label:"5000",value:"5000"}}),t._v(" "),n("el-option",{attrs:{label:"10000",value:"10000"}}),t._v(" "),n("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),t._v(" "),n("span",{staticStyle:{display:"none"}},[t._v("\n        自动刷新("+t._s(t.stdout.reloadPeriod)+"秒):\n      "),n("el-switch",{on:{change:t.autoReloadSwitch},model:{value:t.stdout.autoReload,callback:function(e){t.$set(t.stdout,"autoReload",e)},expression:"stdout.autoReload"}})],1),t._v(" "),n("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.loadStdoutLog()}}},[t._v("刷新\n      ")]),t._v(" "),n("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.podStdoutLogDownload()}}},[t._v("下载\n      ")])],1),t._v(" "),n("div",{staticStyle:{clear:"both"}})]),t._v(" "),n("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[t._v("加载时间: "+t._s(t.stdout.lastReloadTime))]),t._v(" "),n("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[t._v(t._s(t.stdout.content))])])},a=[],r=n("a527"),i={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(t,e){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var t=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(r["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(e){t.stdout.content=e.data;var n=t;setTimeout((function(){n.scrollStdoutLogView()}),200),setTimeout((function(){n.scrollStdoutLogView()}),500),setTimeout((function(){n.scrollStdoutLogView()}),700),t.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()})).finally((function(){t.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(r["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var t=document.getElementById("stdout-log-content");t.scrollTop=t.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var t=this;this.stdout.reloadTimer=setInterval((function(){t.loadStdoutLog()}),1e3*t.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(t){this.stdout.autoReload=t,t?this.startReloadTimer():this.stopReloadTimer()}}},s=i,c=(n("fbec"),n("2877")),u=Object(c["a"])(s,o,a,!1,null,"6ff71a9f",null);e["a"]=u.exports},d2c8:function(t,e,n){var o=n("aae3"),a=n("be13");t.exports=function(t,e,n){if(o(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},e0c2:function(t,e,n){},e18c:function(t,e,n){},fbec:function(t,e,n){"use strict";n("e18c")},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);