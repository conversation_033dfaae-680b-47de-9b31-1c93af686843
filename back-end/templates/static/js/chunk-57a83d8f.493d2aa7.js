(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-57a83d8f"],{"157a":function(e,t,a){"use strict";a("3858")},"1e42":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},r=[],l=(a("a481"),a("25ca")),o=a("21a6"),s=a.n(o),i={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=l["a"].table_to_book(e,{raw:!0}),a=l["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var n=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";s.a.saveAs(new Blob([a],{type:"application/octet-stream"}),n)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=i,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);t["a"]=p.exports},"2fdb":function(e,t,a){"use strict";var n=a("5ca1"),r=a("d2c8"),l="includes";n(n.P+n.F*a("5147")(l),"String",{includes:function(e){return!!~r(this,e,l).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},3520:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-tabs",{on:{"tab-click":e.tabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"集群列表",name:"k8s-cluster",lazy:!0}},[a("k8s-cluster")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"宿主机资源使用",name:"cluster-resource",lazy:!0}},[a("cluster-resource")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"服务资源分配",name:"app-resource",lazy:!0}},[a("app-resource")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Pod状态",name:"pod-status",lazy:!0}},[a("pod-status")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"事件列表",name:"cluster-events",lazy:!0}},[a("k8s-event")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"收集预热镜像",name:"image-preheat-manager",lazy:!0}},[a("image-preheat-manager")],1),e._v(" "),a("el-tab-pane",{attrs:{label:"镜像预热任务",name:"image-preheat-job-list",lazy:!0}},[a("image-preheat-job-list")],1)],1)],1)},r=[],l=(a("7f7f"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"K8S集群"}},[a("el-select",{attrs:{placeholder:"请选择一个集群",filterable:""},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"节点类型"}},[a("el-select",{attrs:{placeholder:"请选择节点类型"},model:{value:e.searchForm.nodeType,callback:function(t){e.$set(e.searchForm,"nodeType",t)},expression:"searchForm.nodeType"}},e._l(this.nodeOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),e._v(" "),a("el-table",{ref:"table001",attrs:{data:e.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","row-class-name":e.tableRowClassName,"default-sort":{prop:"cpu.requests",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"宿主机",sortable:"",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"专属类型",align:"center",prop:"dedicatedName",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(t.row.dedicatedName||"--"))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"CPU",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"cpu.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requests,a.cpu.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"cpu.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requestsPercent,a.cpu.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"cpu.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limits,a.cpu.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"cpu.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limitsPercent,a.cpu.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.utilizationPercent,a.cpu.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"memory.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requests,a.memory.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"memory.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requestsPercent,a.memory.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"memory.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limits,a.memory.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"memory.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limitsPercent,a.memory.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"memory.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.utilizationPercent,a.memory.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return["所有"!==t.row.name?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.showPodCapacity(t.row.name)}}},[e._v("查看Pod\n        ")]):e._e()]}}])})],1),e._v(" "),a("el-drawer",{attrs:{withHeader:!1,visible:e.podCapacity.show,direction:"btt",size:"500"},on:{"update:visible":function(t){return e.$set(e.podCapacity,"show",t)}}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("宿主机："+e._s(e.podCapacity.node))])]),e._v(" "),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.podCapacity.loading,expression:"podCapacity.loading"}],attrs:{data:e.podCapacity.data,"element-loading-text":"Loading",border:"",height:"500",fit:"","highlight-current-row":"","default-sort":{prop:"cpu.requestsPercent",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"Pod",sortable:"",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"环境",sortable:"",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"CPU",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"cpu.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requests,a.cpu.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"cpu.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.requestsPercent,a.cpu.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"cpu.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limits,a.cpu.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"cpu.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.limitsPercent,a.cpu.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.cpu.utilizationPercent,a.cpu.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存",align:"center"}},[a("el-table-column",{attrs:{label:"请求",align:"center",prop:"memory.requests",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requests,a.memory.requests)}}}),e._v(" "),a("el-table-column",{attrs:{label:"请求百分比",align:"center",prop:"memory.requestsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.requestsPercent,a.memory.requestsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大",align:"center",prop:"memory.limits",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limits,a.memory.limits)}}}),e._v(" "),a("el-table-column",{attrs:{label:"最大百分比",align:"center",prop:"memory.limitsPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.limitsPercent,a.memory.limitsPercent)}}}),e._v(" "),a("el-table-column",{attrs:{label:"当前使用率",align:"center",prop:"cpu.utilizationPercent",sortable:"","sort-method":function(t,a){return e.strValueSort(t.memory.utilizationPercent,a.memory.utilizationPercent)}}})],1),e._v(" "),a("el-table-column",{attrs:{width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.podShell(t.row)}}},[e._v("\n                进入容器\n              ")])]}}])})],1)],1)])],1)],1)}),o=[],s=(a("aef6"),a("2d63")),i=a("b775");function c(e){return Object(i["a"])({url:"/v1/k8s/cluster/capacity",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/v1/k8s/cluster/pod/capacity",method:"get",params:e})}var p=a("1e42"),m={name:"clusterResource",data:function(){return{loading:!1,tableData:[],searchForm:{cluster:"",nodeType:""},podCapacity:{show:!1,loading:!1,node:"",data:[]}}},components:{ExportButton:p["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},nodeOptions:function(){if(this.searchForm.cluster)for(var e in this.$settings.clusters){var t=this.$settings.clusters[e];if(this.searchForm.cluster===t.name){var a={name:"全部",value:""},n=JSON.parse(JSON.stringify(t.nodes));return n.splice(0,0,a),console.log(n),n}}return[]}},mounted:function(){},methods:{loadTableData:function(){var e=this;this.searchForm.cluster&&(this.loading=!0,c(this.searchForm).then((function(t){e.tableData=t.data.nodes||[];var a=t.data.clusterTotals;a&&(a["name"]="所有",e.tableData.unshift(a));var n,r=Object(s["a"])(e.tableData);try{for(r.s();!(n=r.n()).done;){var l=n.value;e.rowValueHandler(l)}}catch(o){r.e(o)}finally{r.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1})))},tableRowClassName:function(e){var t=e.row;e.rowIndex;return"所有"===t.name?"bold-row":""},pipelinePage:function(e){this.$router.push({name:"cicd-app-deploy",query:{app:e.app}})},rowValueHandler:function(e){function t(e){return/^\d+$/.test(e)?1e3*parseInt(e)+"m":e}function a(e){return/^\d+$/.test(e)?Math.floor(parseInt(e)/1024/1024)+"Mi":e.endsWith("Ki")||e.endsWith("ki")?Math.floor(parseInt(e)/1024)+"Mi":e}e["cpu"]["requests"]=t(e["cpu"]["requests"]),e["cpu"]["limits"]=t(e["cpu"]["limits"]),e["memory"]["requests"]=a(e["memory"]["requests"]),e["memory"]["limits"]=a(e["memory"]["limits"])},showPodCapacity:function(e){var t=this;if(e){this.podCapacity.node=e,this.podCapacity.loading=!0,this.podCapacity.show=!0;var a={cluster:this.searchForm.cluster,node:e};u(a).then((function(e){t.podCapacity.data=e.data.nodes[0].pods})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.podCapacity.loading=!1}))}else this.$message.error("请选择一个宿主机")},strValueSort:function(e,t){return parseInt(e)-parseInt(t)},podShell:function(e){var t="/api/page/redirect?type=webShell&cluster=".concat(this.searchForm.cluster,"&namespace=").concat(e.namespace,"&app=").concat(e.app,"&pods=").concat(e.name,"&_t")+Date.now();window.open(t)}}},d=m,f=(a("157a"),a("2877")),b=Object(f["a"])(d,l,o,!1,null,null,null),h=b.exports,v=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.loadTableData(t)},submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.searchForm.cluster,callback:function(t){e.$set(e.searchForm,"cluster",t)},expression:"searchForm.cluster"}},e._l(e.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",filterable:""},model:{value:e.searchForm.namespace,callback:function(t){e.$set(e.searchForm,"namespace",t)},expression:"searchForm.namespace"}},[e._l(e.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),e._v(" "),a("el-option",{key:"*",attrs:{label:"所有",value:"*"}})],2)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTableData}},[e._v("查询")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1),e._v(" "),a("div",{staticStyle:{"font-size":"12px"}},[e._v("\n    显示：\n    "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appRemark,callback:function(t){e.$set(e.showFields,"appRemark",t)},expression:"showFields.appRemark"}},[e._v("服务描述")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appOwner,callback:function(t){e.$set(e.showFields,"appOwner",t)},expression:"showFields.appOwner"}},[e._v("服务负责人")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.appLevel,callback:function(t){e.$set(e.showFields,"appLevel",t)},expression:"showFields.appLevel"}},[e._v("服务等级")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.baseImage,callback:function(t){e.$set(e.showFields,"baseImage",t)},expression:"showFields.baseImage"}},[e._v("基础镜像")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.baseImageFullPath,callback:function(t){e.$set(e.showFields,"baseImageFullPath",t)},expression:"showFields.baseImageFullPath"}},[e._v("基础镜像(全地址)")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.cluster,callback:function(t){e.$set(e.showFields,"cluster",t)},expression:"showFields.cluster"}},[e._v("集群")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},model:{value:e.showFields.deployModules,callback:function(t){e.$set(e.showFields,"deployModules",t)},expression:"showFields.deployModules"}},[e._v("部署模块")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},on:{change:e.onShowDiffReplicasChange},model:{value:e.showFields.onlyShowDiffReplicas,callback:function(t){e.$set(e.showFields,"onlyShowDiffReplicas",t)},expression:"showFields.onlyShowDiffReplicas"}},[e._v("只显示副本差异数据")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},on:{change:e.onShowHasReplicasChange},model:{value:e.showFields.onlyShowHasReplicas,callback:function(t){e.$set(e.showFields,"onlyShowHasReplicas",t)},expression:"showFields.onlyShowHasReplicas"}},[e._v("副本数>0")])],1),e._v(" "),a("el-table",{ref:"table001",attrs:{data:e.tableDataFilters,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","show-summary":"","default-sort":{prop:"requestMemoryTotal",order:"descending"}}},[a("el-table-column",{attrs:{type:"index"}}),e._v(" "),a("el-table-column",{attrs:{label:"应用",sortable:"",prop:"name"}}),e._v(" "),e.showFields.appLevel?a("el-table-column",{attrs:{label:"等级",prop:"appLevel",width:"80px"}}):e._e(),e._v(" "),e.showFields.deployModules?a("el-table-column",{attrs:{label:"部署模块(url | module)",prop:"deployModules"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.deployModules,(function(t){return a("div",{key:t,attrs:{type:"success"}},[e._v("\n          "+e._s(t.gitUrl)+" | "+e._s(t.module))])}))}}],null,!1,3397380528)}):e._e(),e._v(" "),e.showFields.appRemark?a("el-table-column",{attrs:{label:"应用描述",prop:"appRemark"}}):e._e(),e._v(" "),e.showFields.appOwner?a("el-table-column",{attrs:{label:"应用负责人",prop:"appOwner"}}):e._e(),e._v(" "),e.showFields.baseImage?a("el-table-column",{attrs:{label:"基础镜像",prop:"container0Image"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.container0Image?t.row.container0Image.substring(t.row.container0Image.lastIndexOf("/")+1):"--")+"\n      ")]}}],null,!1,1085994705)}):e._e(),e._v(" "),e.showFields.baseImageFullPath?a("el-table-column",{attrs:{label:"基础镜像(全地址)",prop:"container0Image"}}):e._e(),e._v(" "),e.showFields.cluster?a("el-table-column",{attrs:{label:"集群",prop:"cluster"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"环境",prop:"namespace",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"变动时间",sortable:"",prop:"updateTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("span",[e._v("C: "+e._s(t.row.createTime))]),a("br"),e._v(" "),a("span",[e._v("U: "+e._s(t.row.updateTime))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行副本",sortable:"",align:"center",width:"110px",prop:"replicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",[e._v(e._s(t.row.replicas))]),a("br"),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",type:"text",size:"mini"},on:{click:function(a){return e.replicasEditDialog(t.row.cluster,t.row.namespace,t.row.name,t.row.replicas)}},slot:"reference"},[e._v("修改\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"发布流程副本",align:"center",width:"110px",prop:"pipelineReplicas"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.pipelineReplicas>=0?a("b",[e._v(e._s(t.row.pipelineReplicas))]):a("span",[e._v("--")]),e._v(" "),a("br"),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{slot:"reference",size:"mini",type:"text"},on:{click:function(a){return e.pipelinePage(t.row.name)}},slot:"reference"},[e._v("流程页\n        ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"CPU （单位：core）",align:"center"}},[a("el-table-column",{attrs:{label:"分配总计",width:"160",align:"center",prop:"requestCpuTotal",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",{staticStyle:{color:"#01AAED"}},[e._v(e._s(t.row.requestCpuTotal))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"分配",width:"100",align:"center",prop:"requestCpu",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"最高",width:"100",align:"center",prop:"limitCpu",sortable:""}})],1),e._v(" "),a("el-table-column",{attrs:{label:"内存（单位：MiB）",align:"center"}},[a("el-table-column",{attrs:{label:"分配总计",width:"160",align:"center",prop:"requestMemoryTotal",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("b",{staticStyle:{color:"#01AAED"}},[e._v(e._s(t.row.requestMemoryTotal))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"分配",width:"100",align:"center",prop:"requestMemory",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"最高",width:"100",align:"center",prop:"limitMemory",sortable:""}})],1),e._v(" "),a("el-table-column",{attrs:{width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:t.row.cluster,namespace:t.row.namespace,app:t.row.name}},target:"_blank"}},[a("i",{staticClass:"el-icon-menu",staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("实例管理")])])]}}])})],1),e._v(" "),a("div",[a("el-backtop")],1),e._v(" "),a("el-dialog",{attrs:{title:"修改实例数",visible:e.replicasEditVisible,width:"500px"},on:{"update:visible":function(t){e.replicasEditVisible=t}}},[a("el-form",{attrs:{"label-position":"right","label-width":"80px"}},[a("el-form-item",{attrs:{label:"集群"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.cluster,callback:function(t){e.$set(e.replicasEdit,"cluster",t)},expression:"replicasEdit.cluster"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.namespace,callback:function(t){e.$set(e.replicasEdit,"namespace",t)},expression:"replicasEdit.namespace"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{disabled:""},model:{value:e.replicasEdit.app,callback:function(t){e.$set(e.replicasEdit,"app",t)},expression:"replicasEdit.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"实例数"}},[a("el-input-number",{attrs:{min:0,max:50},model:{value:e.replicasEdit.replicas,callback:function(t){e.$set(e.replicasEdit,"replicas",t)},expression:"replicasEdit.replicas"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.replicasEditVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",loading:e.replicasEditLoading},on:{click:e.replicasEditSubmit}},[e._v("确 定")])],1)],1)],1)},g=[],y=a("8504"),_=a("b562"),w={name:"appResource",mounted:function(){this.loadTableData()},components:{ExportButton:p["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.searchForm.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},data:function(){return{loading:!1,searchEnv:"",showFields:{appRemark:!1,appOwner:!1,appLevel:!1,baseImage:!1,baseImageFullPath:!1,cluster:!1,deployModules:!1,onlyShowDiffReplicas:!1,onlyShowHasReplicas:!1},searchForm:{cluster:"",namespace:"",keyword:""},tableData:[],tableDataFilters:[],replicasEditLoading:!1,replicasEditVisible:!1,replicasEdit:{cluster:"",namespace:"",app:"",replicas:1}}},methods:{loadTableData:function(){var e=this;this.searchForm.cluster&&this.searchForm.namespace&&(this.loading=!0,Object(y["g"])(this.searchForm.cluster,this.searchForm.namespace).then((function(t){var a,n=t.data,r=Object(s["a"])(n);try{for(r.s();!(a=r.n()).done;){var l=a.value;l.cluster=e.searchForm.cluster,l.limitCpu=(l.limitCpu/1e3).toFixed(1),l.requestCpu=(l.requestCpu/1e3).toFixed(1),l.limitMemory=Math.floor(l.limitMemory/1024/1024),l.requestMemory=Math.floor(l.requestMemory/1024/1024),l.requestCpuTotal=(l.replicas*l.requestCpu).toFixed(1),l.requestMemoryTotal=Math.floor(l.replicas*l.requestMemory),l.appOwner="-",l.appRemark="-",l.appLevel="-"}}catch(o){r.e(o)}finally{r.f()}e.tableData=n,e.tableDataFilters=e.tableData,e.appendAppInfo()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1})))},clusterChange:function(){this.searchForm.namespace=""},onShowDiffReplicasChange:function(e){this.tableDataFilters=!0!==e?this.tableData:this.tableData.filter((function(e){return e.replicas!==e.pipelineReplicas}))},onShowHasReplicasChange:function(e){this.tableDataFilters=!0!==e?this.tableData:this.tableData.filter((function(e){return e.replicas>0}))},replicasEditDialog:function(e,t,a,n){this.replicasEdit={cluster:e,namespace:t,app:a,replicas:n},this.replicasEditVisible=!0},appendAppInfo:function(){var e=this;Object(_["a"])().then((function(t){var a,n={},r=Object(s["a"])(t.data);try{for(r.s();!(a=r.n()).done;){var l=a.value;n[l["name"]]=l}}catch(p){r.e(p)}finally{r.f()}var o,i=Object(s["a"])(e.tableData);try{for(i.s();!(o=i.n()).done;){var c=o.value,u=n[c.name];u&&(c.appRemark=u.remark,c.appOwner=u.mainOwner,c.appLevel=u.level,c.createTime=u.createTime)}}catch(p){i.e(p)}finally{i.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){}))},replicasEditSubmit:function(){var e=this;this.replicasEditLoading=!0,Object(y["f"])(this.replicasEdit).then((function(t){e.$message.success("操作成功");var a,n=Object(s["a"])(e.tableData);try{for(n.s();!(a=n.n()).done;){var r=a.value;r.cluster===e.replicasEdit.cluster&&r.namespace===e.replicasEdit.namespace&&r.name===e.replicasEdit.app&&(r.replicas=e.replicasEdit.replicas)}}catch(l){n.e(l)}finally{n.f()}e.replicasEditVisible=!1})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.replicasEditLoading=!1}))},grafanaPage:function(e){var t="/api/page/redirect?type=grafana&app=".concat(e.name,"&namespace=").concat(e.namespace,"&pod=");window.open(t)},pipelinePage:function(e){var t=this.$router.resolve({name:"cicd-app-deploy",query:{app:e}});window.open(t.href,"_blank")}}},x=w,k=Object(f["a"])(x,v,g,!1,null,null,null),S=k.exports,O=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("env-selector-form",{attrs:{"show-all-namespaces":!1,display:"inline"},on:{submitHandler:e.loadTableData}}),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{attrs:{data:this.tableData,"row-key":"name","default-sort":{prop:"statusDesc",order:"ascending"}}},[a("el-table-column",{attrs:{label:"名称",prop:"name",width:"360",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",sortable:"","sort-by":"statusDesc",prop:"statusDesc",filters:e.statusFilterData,"filter-method":e.filterStatus},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e.podStatusClass(t.row.statusDesc)}),e._v("\n          "+e._s(t.row.statusDesc)+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace"}}),e._v(" "),a("el-table-column",{attrs:{label:"运行版本",prop:"deployTag","show-overflow-tooltip":"","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启数/最近重启",prop:"restartCount",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.restartCount))]),e._v(" "),t.row.restartCount>0?a("div",{staticStyle:{"font-size":"12px",color:"#888"}},[e._v(e._s(t.row.restartTime))]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",prop:"createTime","min-width":"110"}}),e._v(" "),a("el-table-column",{attrs:{width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:function(a){return e.podPage(t.row)}}},[e._v("实例管理\n          ")])]}}])})],1)],1)],1)},j=[],F=(a("6762"),a("2fdb"),a("a527")),D=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"env-selector-form",style:{display:e.display}},[a("el-form",{staticClass:"demo-form-inline",style:{display:e.display},attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"k8s集群",prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:e.clusterChange},model:{value:e.cluster,callback:function(t){e.cluster=t},expression:"cluster"}},e._l(this.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"运行环境",prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择namespace",filterable:""},model:{value:e.namespace,callback:function(t){e.namespace=t},expression:"namespace"}},[e.showAllNamespaces?a("el-option",{key:"*",attrs:{label:"所有",value:""}}):e._e(),e._v(" "),e._l(this.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确认")])],1)],1)},$=[],C={name:"EnvSelectorForm",props:{showAllNamespaces:{type:Boolean,default:!1},display:{type:String,default:"block"}},data:function(){return{cluster:"",namespace:""}},mounted:function(){!this.cluster&&this.clusterOptions&&this.clusterOptions.length&&(this.cluster=this.clusterOptions[0].name),this.cluster&&!this.namespace&&this.namespaceOptions&&this.namespaceOptions.length&&(this.namespace=this.namespaceOptions[0])},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.cluster){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(this.cluster===a.name)return a.namespaces}}catch(n){t.e(n)}finally{t.f()}}return[]}},methods:{clusterChange:function(){this.namespace=""},submit:function(){this.$emit("submitHandler",this.cluster,this.namespace)}}},P=C,T=Object(f["a"])(P,D,$,!1,null,null,null),E=T.exports,I={name:"podStatus",data:function(){return{loading:!1,statusFilterData:[{text:"调度中",value:"调度中"},{text:"准备中",value:"准备中"},{text:"启动中",value:"启动中"},{text:"运行中",value:"运行中"},{text:"关闭中",value:"关闭中"},{text:"容器关闭中",value:"容器关闭中"},{text:"不健康",value:"不健康"},{text:"已关闭",value:"已关闭"},{text:"未知状态",value:"未知状态"}],tableData:[]}},components:{ExportButton:p["a"],EnvSelectorForm:E},mounted:function(){},computed:{},methods:{loadTableData:function(e,t){var a=this;this.loading=!0,Object(F["i"])(e,t).then((function(e){var t,n=[],r=Object(s["a"])(e.data);try{for(r.s();!(t=r.n()).done;){var l=t.value;"已关闭"!==l.statusDesc&&n.push(l)}}catch(o){r.e(o)}finally{r.f()}a.tableData=n})).catch((function(e){a.$message.error(e.message)})).finally((function(){a.loading=!1}))},podPage:function(e){var t=this.$router.resolve({name:"pod-index",query:{cluster:e.cluster,namespace:e.namespace,app:e.labelApp}});window.open(t.href,"_blank")},filterStatus:function(e,t){return t.statusDesc===e},podStatusClass:function(e){if(e){if("运行中"===e)return"pod-status-green";if(["调度中","准备中","启动中"].includes(e))return"pod-status-orange"}return"pod-status-red"}}},V=I,q=(a("8d4f"),Object(f["a"])(V,O,j,!1,null,null,null)),R=q.exports,N=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"k8s-cluster-container"},[a("div",{staticClass:"show-field-wrapper"},[e._v("\n    显示：\n    "),a("el-checkbox",{model:{value:e.showFields.autoScaleV2,callback:function(t){e.$set(e.showFields,"autoScaleV2",t)},expression:"showFields.autoScaleV2"}},[e._v("自动扩缩容")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.cronScale,callback:function(t){e.$set(e.showFields,"cronScale",t)},expression:"showFields.cronScale"}},[e._v("定时扩缩容")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.imageRegistryProxy,callback:function(t){e.$set(e.showFields,"imageRegistryProxy",t)},expression:"showFields.imageRegistryProxy"}},[e._v("镜像代理库")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.cloudCategory,callback:function(t){e.$set(e.showFields,"cloudCategory",t)},expression:"showFields.cloudCategory"}},[e._v("云类型")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.namespaces,callback:function(t){e.$set(e.showFields,"namespaces",t)},expression:"showFields.namespaces"}},[e._v("命名空间")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.prometheusDS,callback:function(t){e.$set(e.showFields,"prometheusDS",t)},expression:"showFields.prometheusDS"}},[e._v("PrometheusDS")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.clickHouseDS,callback:function(t){e.$set(e.showFields,"clickHouseDS",t)},expression:"showFields.clickHouseDS"}},[e._v("ClickHouseDS")]),e._v(" "),a("el-checkbox",{model:{value:e.showFields.pyroscopeDS,callback:function(t){e.$set(e.showFields,"pyroscopeDS",t)},expression:"showFields.pyroscopeDS"}},[e._v("PyroscopeDS")]),e._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{ref:"table001",attrs:{data:this.clusters,"row-key":"name",size:"small"}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群名",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"集群描述",prop:"description"}}),e._v(" "),a("el-table-column",{attrs:{label:"版本号",prop:"version"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"disable",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):a("div",{staticStyle:{color:"red"}},[e._v("关闭")])]}}])}),e._v(" "),e.showFields.autoScaleV2?a("el-table-column",{attrs:{label:"自动扩缩",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["true"===t.row.autoScaleV2?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):"false"===t.row.autoScaleV2?a("div",{staticStyle:{color:"red"}},[e._v("关闭")]):a("div",[e._v(e._s(t.row.autoScaleV2))])]}}],null,!1,1078303857)}):e._e(),e._v(" "),e.showFields.cronScale?a("el-table-column",{attrs:{label:"定时扩缩",prop:"cronScale",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.cronScale?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("开启")]):a("div",{staticStyle:{color:"red"}},[e._v("关闭")])]}}],null,!1,1534277068)}):e._e(),e._v(" "),e.showFields.imageRegistryProxy?a("el-table-column",{attrs:{label:"大版本预热?",prop:"cronScale",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.needImagePreheat?a("div",{staticStyle:{color:"#14b614","font-weight":"bold"}},[e._v("需要")]):a("div",{staticStyle:{color:"red"}},[e._v("不需要")])]}}],null,!1,1181275562)}):e._e(),e._v(" "),e.showFields.imageRegistryProxy?a("el-table-column",{attrs:{label:"镜像库代理",prop:"imageRegistryProxy"}}):e._e(),e._v(" "),e.showFields.cloudCategory?a("el-table-column",{attrs:{label:"云类型",prop:"cloudCategory"}}):e._e(),e._v(" "),e.showFields.prometheusDS?a("el-table-column",{attrs:{label:"PrometheusDS",prop:"thirdServices.grafana.prometheusDS"}}):e._e(),e._v(" "),e.showFields.clickHouseDS?a("el-table-column",{attrs:{label:"ClickHouseDS",prop:"thirdServices.grafana.clickHouseDS"}}):e._e(),e._v(" "),e.showFields.pyroscopeDS?a("el-table-column",{attrs:{label:"PyroscopeDS",prop:"thirdServices.grafana.pyroscopeDS"}}):e._e(),e._v(" "),e.showFields.namespaces?a("el-table-column",{attrs:{label:"命名空间",prop:"namespaces"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.namespaces)+"\n        ")]}}],null,!1,263394605)}):e._e()],1)],1)],1)},z=[],L=a("b144"),M=a("bb0b"),A={name:"k8sCluster",data:function(){return{clusters:[],showFields:{autoScaleV2:!0,cronScale:!0,cloudCategory:!1,namespaces:!1,imageRegistryProxy:!0,prometheusDS:!1,clickHouseDS:!1,pyroscopeDS:!1}}},components:{ExportButton:p["a"]},mounted:function(){var e,t=this,a=Object(s["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n=e.value,r=Object(L["a"])(n);r["autoScaleV2"]="--",this.clusters.push(r)}}catch(l){a.e(l)}finally{a.f()}Object(M["f"])().then((function(e){var a,n=Object(s["a"])(t.clusters);try{for(n.s();!(a=n.n()).done;){var r=a.value;e.data&&(e.data[r.name]?r["autoScaleV2"]="true":r["autoScaleV2"]="false")}}catch(l){n.e(l)}finally{n.f()}})).catch((function(e){console.error(e.message)})).finally((function(){}))},computed:{},methods:{}},H=A,J=(a("f0e1"),Object(f["a"])(H,N,z,!1,null,null,null)),B=J.exports,U=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",[a("env-selector-form",{attrs:{"show-all-namespaces":!0},on:{submitHandler:e.loadEvents}})],1),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-timeline",e._l(e.events,(function(t,n){return a("el-timeline-item",{key:n,attrs:{placement:"top","hide-timestamp":!0,timestamp:t.lastTime?t.lastTime:t.createTime}},[a("div",[a("span",[e._v(e._s(t.lastTime?t.lastTime:t.createTime))]),e._v(" "),a("el-tag",{staticStyle:{margin:"0 10px"},attrs:{size:"small",type:"Warning"===t.type?"warning":"info"}},[e._v(e._s(t.type))]),e._v(" "),e.showNamespace?a("span",{staticStyle:{padding:"0 10px"}},[e._v(e._s(t.namespace))]):e._e(),e._v(" "),a("span",{staticStyle:{padding:"0 10px"}},[e._v(" ( x"+e._s(t.count)+" )")]),e._v(" "),a("span",{staticStyle:{"padding-right":"10px"}},[e._v(e._s(t.reason)+":")]),e._v(" "+e._s(t.message)+"\n        ")],1)])})),1)],1)],1)},W=[],K=a("655a"),Y={name:"k8s-event",data:function(){return{loading:!1,showNamespace:!1,events:[]}},components:{EnvSelectorForm:E},mounted:function(){},computed:{},methods:{loadEvents:function(e,t){var a=this;this.loading=!0,this.showNamespace=null===t||""===t,Object(K["a"])(e,t).then((function(e){a.events=e.data})).catch((function(e){a.$message.error(e.message)})).finally((function(){a.loading=!1}))}}},G=Y,Q=Object(f["a"])(G,U,W,!1,null,null,null),X=Q.exports,Z=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:function(t){return e.image_preheat_dialog()}}},[e._v("收集预热镜像")]),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{ref:"table001",attrs:{data:this.tableData,"row-key":"name",size:"small",stripe:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"5px 10px"},attrs:{size:"mini"},on:{click:function(a){return e.image_preheat_detail(t.row)}}},[e._v("详情")]),e._v(" "),a("el-button",{staticStyle:{padding:"5px 10px"},attrs:{size:"mini",type:"primary"},on:{click:function(a){return e.image_preheat_execute_dialog(t.row)}}},[e._v("预热")]),e._v(" "),a("el-button",{staticStyle:{padding:"5px 10px"},attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.image_preheat_delete(t.row)}}},[e._v("删除")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"名称",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{label:"收集时间",prop:"CreatedAt"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.CreatedAt.split(".")[0])+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),e._v(" "),a("el-table-column",{attrs:{label:"创建人",prop:"creator"}}),e._v(" "),a("el-table-column",{attrs:{label:"收集条件",prop:"query","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(JSON.stringify(t.row.query,null,2))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"镜像个数",prop:"images","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.images.length)+"\n        ")]}}])})],1)],1),e._v(" "),a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.imagePreheatLoading,expression:"imagePreheatLoading"}],attrs:{title:"收集预热镜像",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"form001",attrs:{"label-width":"120px",model:e.form}},[a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"名称"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"描述"}},[a("el-input",{attrs:{placeholder:"请输入描述"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"最近构建的镜像"}},[a("div",{staticStyle:{color:"#888"}},[e._v("\n          对最近\n          "),a("el-input-number",{staticStyle:{width:"120px"},attrs:{"controls-position":"right",min:0,max:480,step:12},model:{value:e.form.imageRecentHours,callback:function(t){e.$set(e.form,"imageRecentHours",t)},expression:"form.imageRecentHours"}}),e._v("\n          小时内构建的应用镜像进行预热\n        ")],1)]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"镜像所属环境"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择环境"},model:{value:e.form.imageInEnvs,callback:function(t){e.$set(e.form,"imageInEnvs",t)},expression:"form.imageInEnvs"}},e._l(e.evnOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"5px"},attrs:{label:"指定的镜像"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"","allow-create":"",clearable:"",placeholder:"请选择镜像"},model:{value:e.form.specifiedImages,callback:function(t){e.$set(e.form,"specifiedImages",t)},expression:"form.specifiedImages"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.create_image_preheat()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"查看详情",visible:e.detailDialogVisible,width:"50%",top:"5vh"},on:{"update:visible":function(t){e.detailDialogVisible=t}}},[a("div",{staticStyle:{"margin-top":"-50px",overflow:"auto"}},[a("div",{staticStyle:{"text-align":"center"}},[a("clipboard-icon",{attrs:{text:this.detail,"button-text":"一键复制内容"}})],1),e._v(" "),a("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[e._v(e._s(e.detail))])])]),e._v(" "),a("el-dialog",{attrs:{title:"执行镜像预热",visible:e.executeDialogVisible,width:"80%",top:"5vh"},on:{"update:visible":function(t){e.executeDialogVisible=t}}},[a("el-form",{ref:"executeForm001",staticStyle:{"margin-top":"-20px"},attrs:{"label-width":"120px",model:e.executeForm}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"预热名称",prop:"imagePreheatName"}},[e._v("\n        "+e._s(e.executeForm.imagePreheatName)+"\n      ")]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"预热创建时间",prop:"imagePreheatCreatedAt"}},[e._v("\n        "+e._s(e.executeForm.imagePreheatCreatedAt)+"\n      ")]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"预热任务个数",prop:"jobNumber"}},[a("el-input-number",{attrs:{"controls-position":"right",min:1,max:100,step:1},model:{value:e.executeForm.jobNumber,callback:function(t){e.$set(e.executeForm,"jobNumber",t)},expression:"executeForm.jobNumber"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"请选择集群",prop:"cluster"}},[a("el-button",{attrs:{size:"mini"},on:{click:e.clusterWithImageProxy}},[e._v("配置了镜像代理库的所有集群")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:e.clusterWithNeedImagePreheat}},[e._v("大版本需要镜像预热的所有集群")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:e.clusterAll}},[e._v("全选")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:e.clusterNone}},[e._v("清空")]),e._v(" "),a("el-checkbox-group",{model:{value:e.executeForm.clusters,callback:function(t){e.$set(e.executeForm,"clusters",t)},expression:"executeForm.clusters"}},[a("div",{staticStyle:{padding:"10px",border:"solid 1px #eee","background-color":"#eee"}},[a("el-row",e._l(e.clusterOptions,(function(t){return a("el-col",{key:t.name,staticStyle:{"line-height":"14px"},attrs:{span:8}},[a("el-checkbox",{attrs:{label:t.name}},[e._v(e._s(t.name)+" | "+e._s(t.description))])],1)})),1)],1)])],1),e._v(" "),a("el-form-item",[a("div",{staticStyle:{"text-align":"left","padding-top":"20px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.image_preheat_execute}},[e._v("执行预热")]),e._v(" "),a("el-button",{on:{click:function(t){e.executeDialogVisible=!1}}},[e._v("取消")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.downloadJobYaml}},[e._v("下载预热YAML文件")])],1)])],1)],1)],1)},ee=[],te=a("768b");a("ac6a"),a("ffc1");function ae(e){return Object(i["a"])({url:"/v1/image-preheat",method:"post",data:e})}function ne(e){return Object(i["a"])({url:"/v1/image-preheat",method:"get",params:e})}function re(e){return Object(i["a"])({url:"/v1/image-preheat?id=".concat(e),method:"delete"})}function le(e){return Object(i["a"])({url:"/v1/image-preheat/execute",method:"post",data:e})}function oe(e,t){return Object(i["a"])({url:"/v1/image-preheat/jobs?cluster=".concat(e,"&namespace=").concat(t),method:"get"})}function se(e){return Object(i["a"])({url:"/v1/image-preheat/jobs",method:"delete",data:e})}function ie(e,t){return Object(i["a"])({url:"/v1/image-preheat/preheat-with-curr-cluster-image?cluster=".concat(e,"&jobNumber=").concat(t),method:"post"})}function ce(e){var t=e?"?"+Object.entries(e).map((function(e){var t=Object(te["a"])(e,2),a=t[0],n=t[1];return Array.isArray(n)?n.map((function(e){return"".concat(encodeURIComponent(a),"=").concat(encodeURIComponent(e))})).join("&"):"".concat(encodeURIComponent(a),"=").concat(encodeURIComponent(n))})).join("&"):"";window.open("/api/v1/image-preheat/download-job-yaml".concat(t))}var ue=a("da37"),pe={name:"image-preheat-manager",data:function(){return{loading:!1,imagePreheatLoading:!1,tableData:[],evnOptions:[],dialogVisible:!1,detailDialogVisible:!1,executeDialogVisible:!1,executeForm:{imagePreheatId:"",imagePreheatName:"",imagePreheatCreatedAt:"",jobNumber:6,clusters:[]},detail:{},form:{name:"",remark:"",imageRecentHours:24,imageInEnvs:["k8s0/foneshare-gray","k8s0/foneshare-stage","k8s1/foneshare-gray","k8s1/foneshare-stage"],specifiedImages:["reg.foneshare.cn/base/fs-tomcat8:openjdk8","reg.foneshare.cn/base/fs-tomcat8:openjdk11","reg.foneshare.cn/base/fs-tomcat8:openjdk17","reg.foneshare.cn/base/fs-tomcat8:openjdk21","reg.foneshare.cn/base/fs-tomcat8:openjdk24","reg.foneshare.cn/base/fs-tomcat9:openjdk8","reg.foneshare.cn/base/fs-tomcat9:openjdk11","reg.foneshare.cn/base/fs-tomcat9:openjdk17","reg.foneshare.cn/base/fs-tomcat9:openjdk21","reg.foneshare.cn/base/fs-tomcat9:openjdk24","reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"]}}},components:{ClipboardIcon:ue["a"]},mounted:function(){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a,n=e.value,r=Object(s["a"])(n.namespaces);try{for(r.s();!(a=r.n()).done;){var l=a.value;this.evnOptions.push("".concat(n.name,"/").concat(l))}}catch(o){r.e(o)}finally{r.f()}}}catch(o){t.e(o)}finally{t.f()}this.loadData()},computed:{clusterOptions:function(){return this.$settings.clusters}},methods:{loadData:function(){var e=this;this.loading=!0,ne({keywords:""}).then((function(t){e.tableData=t.data})).catch((function(e){console.error(e.message)})).finally((function(){e.loading=!1}))},image_preheat_dialog:function(){this.dialogVisible=!0},create_image_preheat:function(){var e=this;""!==this.form.name?this.$confirm("确定执行操作吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.imagePreheatLoading=!0,ae(e.form).then((function(t){e.$message.success("创建成功"),e.dialogVisible=!1,e.loadData()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.imagePreheatLoading=!1}))})).catch((function(){})):this.$message.error("请输入名称")},image_preheat_detail:function(e){this.detail=JSON.stringify(e,null,2),this.detailDialogVisible=!0},image_preheat_delete:function(e){var t=this;this.$confirm("确定删除预热Job吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){re(e.ID).then((function(e){t.$message.success("删除成功"),t.loadData()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(){}))},image_preheat_execute_dialog:function(e){this.executeForm.imagePreheatId=e.ID,this.executeForm.imagePreheatName=e.name,this.executeForm.imagePreheatCreatedAt=e.CreatedAt,this.executeDialogVisible=!0},image_preheat_execute:function(){var e=this;this.executeForm.clusters.length<1?this.$message.error("请选择需要预热的集群"):this.$confirm("确定预热镜像吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){le(e.executeForm).then((function(t){e.$message.success(t.data),e.loadData()})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){}))},clusterNone:function(){this.executeForm.clusters=[]},clusterAll:function(){this.executeForm.clusters=this.$settings.clusters.map((function(e){return e.name}))},clusterWithImageProxy:function(){var e,t=[],a=Object(s["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n=e.value;n.imageRegistryProxy&&t.push(n.name)}}catch(r){a.e(r)}finally{a.f()}this.executeForm.clusters=t},clusterWithNeedImagePreheat:function(){var e,t=[],a=Object(s["a"])(this.$settings.clusters);try{for(a.s();!(e=a.n()).done;){var n=e.value;n.needImagePreheat&&t.push(n.name)}}catch(r){a.e(r)}finally{a.f()}this.executeForm.clusters=t},downloadJobYaml:function(){ce({imagePreheatId:this.executeForm.imagePreheatId,jobNumber:this.executeForm.jobNumber,clusters:this.executeForm.clusters,operate:this.executeForm.operate})}}},me=pe,de=Object(f["a"])(me,Z,ee,!1,null,null,null),fe=de.exports,be=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",e._l(e.clusterOptions,(function(e){return a("el-col",{key:e.name,attrs:{span:12}},[a("image-preheat-job",{attrs:{cluster:e.name}})],1)})),1)],1)},he=[],ve=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"image-preheat"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(this.clusterObj.name)+" | ")]),e._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"10px"}},[this.clusterObj.needImagePreheat?a("span",{staticStyle:{color:"green","font-size":"12px"}},[e._v("需要预热")]):a("span",{staticStyle:{color:"red","font-size":"12px"}},[e._v("无需预热")])]),e._v(" "),a("el-tooltip",{attrs:{placement:"top",effect:"light"}},[a("div",{staticStyle:{"line-height":"20px"},attrs:{slot:"content"},slot:"content"},[a("div",[e._v("需要预热: 表示有自己独立的镜像缓存库,大版本发布需要做预热镜像")]),e._v(" "),a("div",[e._v("无需预热: 表示使用公共镜像缓存库,大版本发布不需要做预热镜像")])]),e._v(" "),a("i",{staticClass:"el-icon-warning-outline"})]),e._v(" "),a("span",{staticStyle:{float:"right"}},[a("el-button",{attrs:{icon:"el-icon-refresh",type:"text",size:"small"},on:{click:e.loadTable}},[e._v("刷新")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-delete",type:"text",size:"small"},on:{click:e.removeAllJobs}},[e._v("删除所有")]),e._v(" "),a("el-tooltip",{attrs:{placement:"top",effect:"light",content:"把本环境在用的镜像预热到镜像缓存库"}},[a("el-button",{attrs:{type:"text",size:"small"},on:{click:e.imagePreheatInCurrCluster}},[e._v("使用本环境镜像预热"),a("i",{staticClass:"el-icon-warning-outline"})])],1)],1)],1),e._v(" "),a("div",[a("el-table",{attrs:{data:this.tableData,"row-key":"name",size:"small"}},[a("el-table-column",{attrs:{label:"name",prop:"name","min-width":"220"}}),e._v(" "),a("el-table-column",{attrs:{label:"status",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticStyle:{"font-weight":"bold",color:"black"}},[e._v(e._s(t.row.status))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"restarts",prop:"restarts"}}),e._v(" "),a("el-table-column",{attrs:{label:"age",prop:"age"}}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.podDetailDialog(t.row.name)}}},[e._v("详情")]),e._v(" "),a("el-button",{staticStyle:{color:"#E6A23C","margin-left":"0"},attrs:{type:"text"},on:{click:function(a){return e.jobRemove(t.row.jobName)}}},[e._v("删除")])]}}])})],1)],1)]),e._v(" "),a("el-dialog",{attrs:{title:e.podDetail.name,visible:e.podDetailVisible,top:"5vh"},on:{"update:visible":function(t){e.podDetailVisible=t}}},[a("div",{staticStyle:{margin:"5px auto","text-align":"center"}},[a("clipboard-icon",{attrs:{text:e.podDetail.json}},[e._v("一键复制")])],1),e._v(" "),a("vue-json-pretty",{directives:[{name:"loading",rawName:"v-loading",value:e.podDetailLoading,expression:"podDetailLoading"}],staticStyle:{"max-height":"600px","overflow-y":"auto"},attrs:{data:e.podDetail.data}})],1)],1)},ge=[],ye=a("d538"),_e=a.n(ye),we={name:"image-preheat-job",props:{cluster:{type:String,required:!0},namespace:{type:String,default:"image-preheat"}},data:function(){return{loading:!1,tableData:[],podDetailVisible:!1,podDetailLoading:!1,podDetail:{name:"",data:{},json:""}}},computed:{clusterObj:function(){var e,t=Object(s["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var a=e.value;if(a.name===this.cluster)return a}}catch(n){t.e(n)}finally{t.f()}return{}}},components:{ClipboardIcon:ue["a"],VueJsonPretty:_e.a},mounted:function(){this.loadTable()},methods:{loadTable:function(){var e=this;this.loading=!0,oe(this.cluster,this.namespace).then((function(t){e.tableData=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))},podDetailDialog:function(e){var t=this;this.podDetailVisible=!0,this.podDetail.name===e&&this.podDetail.data||(this.podDetailLoading=!0,Object(F["e"])(this.cluster,this.namespace,e).then((function(a){t.podDetailLoading=!1,t.podDetail.name=e,t.podDetail.data=a.data,t.podDetail.json=JSON.stringify(t.podDetail.data,null,2)})).catch((function(e){t.$message.error(e.message),t.podDetailLoading=!1})))},jobRemove:function(e){var t=this;this.$confirm("确认删除? job：".concat(e),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0;var a={cluster:t.cluster,namespace:t.namespace,names:[e]};se(a).then((function(e){t.$message.success("操作成功,将刷新数据"),t.loadTable()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))})).catch((function(e){}))},imagePreheatInCurrCluster:function(){var e=this;this.$confirm("是否确认使用本环境镜像预热？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,ie(e.cluster,6).then((function(t){e.$message.success("操作成功,将刷新数据"),e.loadTable()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))})).catch((function(t){e.$message.error(t.message)}))},removeAllJobs:function(){var e=this;this.$confirm("确认删除所有Job?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,se({cluster:e.cluster,namespace:e.namespace,names:e.tableData.map((function(e){return e.jobName}))}).then((function(t){e.$message.success("操作成功,将刷新数据"),e.loadTable()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))})).catch((function(e){}))}}},xe=we,ke=(a("c83e"),Object(f["a"])(xe,ve,ge,!1,null,null,null)),Se=ke.exports,Oe={name:"image-preheat-job-list",data:function(){return{}},components:{ImagePreheatJob:Se},mounted:function(){},computed:{clusterOptions:function(){return this.$settings.clusters}},methods:{}},je=Oe,Fe=Object(f["a"])(je,be,he,!1,null,null,null),De=Fe.exports,$e={components:{ImagePreheatJobList:De,ImagePreheatManager:fe,K8sEvent:X,K8sCluster:B,PodStatus:R,ClusterResource:h,AppResource:S},mounted:function(){var e=this.$route.query.activeTab;e&&(this.activeTab=e)},computed:{},data:function(){return{activeTab:"k8s-cluster"}},methods:{tabClick:function(e){var t=Object(L["a"])(this.$route.query);t["activeTab"]=e.name,this.$router.push({query:t})}}},Ce=$e,Pe=Object(f["a"])(Ce,n,r,!1,null,null,null);t["default"]=Pe.exports},3858:function(e,t,a){},"504c":function(e,t,a){var n=a("9e1e"),r=a("0d58"),l=a("6821"),o=a("52a7").f;e.exports=function(e){return function(t){var a,s=l(t),i=r(s),c=i.length,u=0,p=[];while(c>u)a=i[u++],n&&!o.call(s,a)||p.push(e?[a,s[a]]:s[a]);return p}}},5147:function(e,t,a){var n=a("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,!"/./"[e](t)}catch(r){}}return!0}},"655a":function(e,t,a){"use strict";a.d(t,"a",(function(){return r}));var n=a("b775");function r(e,t,a,r){return Object(n["a"])({url:"/v1/k8s/event/search",method:"get",params:{cluster:e,namespace:t,type:a||"",reason:r||""}})}},6762:function(e,t,a){"use strict";var n=a("5ca1"),r=a("c366")(!0);n(n.P,"Array",{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"768b":function(e,t,a){"use strict";var n=a("a745"),r=a.n(n);function l(e){if(r()(e))return e}var o=a("67bb"),s=a.n(o),i=a("5d58"),c=a.n(i);function u(e,t){var a=null==e?null:"undefined"!==typeof s.a&&e[c.a]||e["@@iterator"];if(null!=a){var n,r,l=[],o=!0,i=!1;try{for(a=a.call(e);!(o=(n=a.next()).done);o=!0)if(l.push(n.value),t&&l.length===t)break}catch(u){i=!0,r=u}finally{try{o||null==a["return"]||a["return"]()}finally{if(i)throw r}}return l}}var p=a("e630");function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){return l(e)||u(e,t)||Object(p["a"])(e,t)||m()}a.d(t,"a",(function(){return d}))},8504:function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"h",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"i",(function(){return c})),a.d(t,"d",(function(){return u})),a.d(t,"f",(function(){return p})),a.d(t,"e",(function(){return m}));var n=a("b775");function r(e,t){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:e,namespace:t}})}function l(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:e})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:e,namespace:t,app:a}})}function i(e){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:e})}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function p(e){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:e})}function m(e,t,a,r,l){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:e,namespace:t,app:a,revision:r,deployTag:l||""}})}},"8d4f":function(e,t,a){"use strict";a("a4d0")},"9b24":function(e,t,a){},a4d0:function(e,t,a){},a527:function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"d",(function(){return l})),a.d(t,"i",(function(){return o})),a.d(t,"e",(function(){return s})),a.d(t,"g",(function(){return i})),a.d(t,"c",(function(){return c})),a.d(t,"k",(function(){return u})),a.d(t,"l",(function(){return p})),a.d(t,"m",(function(){return m})),a.d(t,"o",(function(){return d})),a.d(t,"f",(function(){return f})),a.d(t,"r",(function(){return b})),a.d(t,"b",(function(){return h})),a.d(t,"a",(function(){return v})),a.d(t,"p",(function(){return g})),a.d(t,"q",(function(){return y})),a.d(t,"n",(function(){return _})),a.d(t,"j",(function(){return w}));a("96cf"),a("3b8d");var n=a("b775");function r(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e,t){return Object(n["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:e,namespace:t}})}function s(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:e,namespace:t,pod:a}})}function i(e,t,a,r,l,o){return Object(n["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:e,namespace:t,pod:a,container:r,tailLines:l,previous:o}})}function c(e,t,a,n,r){var l="/api/v1/k8s/pod/stdout/download?cluster=".concat(e,"&namespace=").concat(t,"&pod=").concat(a,"&container=").concat(n,"&tailLines=").concat(r,'&_time="')+(new Date).getTime();window.open(l)}function u(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:e,namespace:t,pod:a}})}function p(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:e,namespace:t,pod:a}})}function m(e){return Object(n["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:e}})}function d(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:e,namespace:t,pod:a}})}function f(e){return Object(n["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:e})}function h(e,t){window.open("/api/v1/k8s/pod/file/download?fileId="+e+"&fileName="+t+"&_time="+(new Date).getTime())}function v(e){return Object(n["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:e})}function g(e){window.open("/api/v1/k8s/pod/file/preview?fileId="+e)}function y(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:e,namespace:t,pod:a}})}function _(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:e,namespace:t,pod:a}})}function w(e,t,a){return Object(n["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:e,namespace:t,pod:a}})}},aae3:function(e,t,a){var n=a("d3f4"),r=a("2d95"),l=a("2b4c")("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[l])?!!t:"RegExp"==r(e))}},aef6:function(e,t,a){"use strict";var n=a("5ca1"),r=a("9def"),l=a("d2c8"),o="endsWith",s=""[o];n(n.P+n.F*a("5147")(o),"String",{endsWith:function(e){var t=l(this,e,o),a=arguments.length>1?arguments[1]:void 0,n=r(t.length),i=void 0===a?n:Math.min(r(a),n),c=String(e);return s?s.call(t,c,i):t.slice(i-c.length,i)===c}})},b144:function(e,t,a){"use strict";function n(e){return JSON.parse(JSON.stringify(e))}function r(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function l(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"b",(function(){return l}))},b562:function(e,t,a){"use strict";a.d(t,"p",(function(){return r})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"l",(function(){return s})),a.d(t,"j",(function(){return i})),a.d(t,"e",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"m",(function(){return m})),a.d(t,"o",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"f",(function(){return b})),a.d(t,"c",(function(){return h})),a.d(t,"k",(function(){return v})),a.d(t,"r",(function(){return g})),a.d(t,"n",(function(){return y})),a.d(t,"q",(function(){return _})),a.d(t,"d",(function(){return w}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/app/search",method:"get",params:e})}function l(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function s(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function i(e){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(n["a"])({url:"/v1/app",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/app",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function m(e,t,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:a}})}function d(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function f(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function v(e,t){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function g(e,t){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function y(e,t){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function _(){return Object(n["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}function w(){return Object(n["a"])({url:"/v1/app/count-pipelines",method:"get"})}},bb0b:function(e,t,a){"use strict";a.d(t,"i",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return o})),a.d(t,"k",(function(){return s})),a.d(t,"j",(function(){return i})),a.d(t,"b",(function(){return c})),a.d(t,"c",(function(){return u})),a.d(t,"e",(function(){return p})),a.d(t,"g",(function(){return m})),a.d(t,"f",(function(){return d})),a.d(t,"h",(function(){return f}));var n=a("b775");function r(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function s(e){return Object(n["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function p(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function m(e){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function d(){return Object(n["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function f(e,t,a){return Object(n["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}},c83e:function(e,t,a){"use strict";a("9b24")},d2c8:function(e,t,a){var n=a("aae3"),r=a("be13");e.exports=function(e,t,a){if(n(t))throw TypeError("String#"+a+" doesn't accept regex!");return String(r(e))}},d89d:function(e,t,a){},da37:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(t){return e.copyToClipboard()}}},[a("i",{staticClass:"el-icon-document-copy"}),e._v(" "),this.buttonText?a("span",[e._v(e._s(this.buttonText))]):e._e()])},r=[],l={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var e=this,t=this.text;t?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){var a=document.createElement("input");document.body.appendChild(a),a.setAttribute("value",t),a.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(a),e.$message.success("复制成功")})):this.$message.warning("内容为空")}}},o=l,s=a("2877"),i=Object(s["a"])(o,n,r,!1,null,null,null);t["a"]=i.exports},f0e1:function(e,t,a){"use strict";a("d89d")},ffc1:function(e,t,a){var n=a("5ca1"),r=a("504c")(!0);n(n.S,"Object",{entries:function(e){return r(e)}})}}]);