(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8fecc9e2"],{"02f4":function(e,t,r){var n=r("4588"),a=r("be13");e.exports=function(e){return function(t,r){var i,s,o=String(a(t)),l=n(r),f=o.length;return l<0||l>=f?e?"":void 0:(i=o.charCodeAt(l),i<55296||i>56319||l+1===f||(s=o.charCodeAt(l+1))<56320||s>57343?e?o.charAt(l):i:e?o.slice(l,l+2):s-56320+(i-55296<<10)+65536)}}},"0390":function(e,t,r){"use strict";var n=r("02f4")(!0);e.exports=function(e,t,r){return t+(r?n(e,t).length:1)}},"0bfb":function(e,t,r){"use strict";var n=r("cb7c");e.exports=function(){var e=n(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},"214f":function(e,t,r){"use strict";r("b0c5");var n=r("2aba"),a=r("32e9"),i=r("79e5"),s=r("be13"),o=r("2b4c"),l=r("520a"),f=o("species"),c=!i((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),h=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return 2===r.length&&"a"===r[0]&&"b"===r[1]}();e.exports=function(e,t,r){var u=o(e),d=!i((function(){var t={};return t[u]=function(){return 7},7!=""[e](t)})),p=d?!i((function(){var t=!1,r=/a/;return r.exec=function(){return t=!0,null},"split"===e&&(r.constructor={},r.constructor[f]=function(){return r}),r[u](""),!t})):void 0;if(!d||!p||"replace"===e&&!c||"split"===e&&!h){var m=/./[u],g=r(s,u,""[e],(function(e,t,r,n,a){return t.exec===l?d&&!a?{done:!0,value:m.call(t,r,n)}:{done:!0,value:e.call(r,t,n)}:{done:!1}})),v=g[0],T=g[1];n(String.prototype,e,v),a(RegExp.prototype,u,2==t?function(e,t){return T.call(e,this,t)}:function(e){return T.call(e,this)})}}},"21a6":function(e,t,r){(function(r){var n,a,i;(function(r,s){a=[],n=s,i="function"===typeof n?n.apply(t,a):n,void 0===i||(e.exports=i)})(0,(function(){"use strict";function t(e,t){return"undefined"==typeof t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function n(e,t,r){var n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=function(){l(n.response,t,r)},n.onerror=function(){console.error("could not download file")},n.send()}function a(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return 200<=t.status&&299>=t.status}function i(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var s="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof r&&r.global===r?r:void 0,o=s.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),l=s.saveAs||("object"!=typeof window||window!==s?function(){}:"download"in HTMLAnchorElement.prototype&&!o?function(e,t,r){var o=s.URL||s.webkitURL,l=document.createElement("a");t=t||e.name||"download",l.download=t,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?i(l):a(l.href)?n(e,t,r):i(l,l.target="_blank")):(l.href=o.createObjectURL(e),setTimeout((function(){o.revokeObjectURL(l.href)}),4e4),setTimeout((function(){i(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,r,s){if(r=r||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(t(e,s),r);else if(a(e))n(e,r,s);else{var o=document.createElement("a");o.href=e,o.target="_blank",setTimeout((function(){i(o)}))}}:function(e,t,r,a){if(a=a||open("","_blank"),a&&(a.document.title=a.document.body.innerText="downloading..."),"string"==typeof e)return n(e,t,r);var i="application/octet-stream"===e.type,l=/constructor/i.test(s.HTMLElement)||s.safari,f=/CriOS\/[\d]+/.test(navigator.userAgent);if((f||i&&l||o)&&"undefined"!=typeof FileReader){var c=new FileReader;c.onloadend=function(){var e=c.result;e=f?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=e:location=e,a=null},c.readAsDataURL(e)}else{var h=s.URL||s.webkitURL,u=h.createObjectURL(e);a?a.location=u:location.href=u,a=null,setTimeout((function(){h.revokeObjectURL(u)}),4e4)}});s.saveAs=l.saveAs=l,e.exports=l}))}).call(this,r("c8ba"))},"25ca":function(e,t,r){"use strict";r.d(t,"b",(function(){return mu})),r.d(t,"a",(function(){return Lu}));
/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var n={version:"0.18.5"},a=1200,i=1252,s=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],o={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},l=function(e){-1!=s.indexOf(e)&&(i=o[0]=e)};function f(){l(1252)}var c=function(e){a=e,l(e)};function h(){c(1200),f()}function u(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var d,p=function(e){return String.fromCharCode(e)},m=function(e){return String.fromCharCode(e)};var g=null,v=!0,T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function w(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0,l=0,f=0;f<e.length;)r=e.charCodeAt(f++),i=r>>2,n=e.charCodeAt(f++),s=(3&r)<<4|n>>4,a=e.charCodeAt(f++),o=(15&n)<<2|a>>6,l=63&a,isNaN(n)?o=l=64:isNaN(a)&&(l=64),t+=T.charAt(i)+T.charAt(s)+T.charAt(o)+T.charAt(l);return t}function b(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var f=0;f<e.length;)i=T.indexOf(e.charAt(f++)),s=T.indexOf(e.charAt(f++)),r=i<<2|s>>4,t+=String.fromCharCode(r),o=T.indexOf(e.charAt(f++)),n=(15&s)<<4|o>>2,64!==o&&(t+=String.fromCharCode(n)),l=T.indexOf(e.charAt(f++)),a=(3&o)<<6|l,64!==l&&(t+=String.fromCharCode(a));return t}var E=function(){return"undefined"!==typeof Buffer&&"undefined"!==typeof process&&"undefined"!==typeof process.versions&&!!process.versions.node}(),S=function(){if("undefined"!==typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function A(e){return E?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function y(e){return E?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var _=function(e){return E?S(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function O(e){if("undefined"===typeof ArrayBuffer)return _(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function x(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function C(e){if("undefined"===typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}var R=E?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:S(e)})))}:function(e){if("undefined"!==typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};function k(e){for(var t=[],r=0,n=e.length+250,a=A(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|63&s;else if(s>=55296&&s<57344){s=64+(1023&s);var o=1023&e.charCodeAt(++i);a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|o>>6&15|(3&s)<<4,a[r++]=128|63&o}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|63&s;r>n&&(t.push(a.slice(0,r)),r=0,a=A(65535),n=65530)}return t.push(a.slice(0,r)),R(t)}var N=/\u0000/g,I=/[\u0001-\u0006]/g;function D(e){var t="",r=e.length-1;while(r>=0)t+=e.charAt(r--);return t}function P(e,t){var r=""+e;return r.length>=t?r:dt("0",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:dt(" ",t-r.length)+r}function M(e,t){var r=""+e;return r.length>=t?r:r+dt(" ",t-r.length)}function F(e,t){var r=""+Math.round(e);return r.length>=t?r:dt("0",t-r.length)+r}function U(e,t){var r=""+e;return r.length>=t?r:dt("0",t-r.length)+r}var B=Math.pow(2,32);function W(e,t){if(e>B||e<-B)return F(e,t);var r=Math.round(e);return U(r,t)}function H(e,t){return t=t||0,e.length>=7+t&&103===(32|e.charCodeAt(t))&&101===(32|e.charCodeAt(t+1))&&110===(32|e.charCodeAt(t+2))&&101===(32|e.charCodeAt(t+3))&&114===(32|e.charCodeAt(t+4))&&97===(32|e.charCodeAt(t+5))&&108===(32|e.charCodeAt(t+6))}var G=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function j(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var z={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},X={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},K={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Y(e,t,r){var n=e<0?-1:1,a=e*n,i=0,s=1,o=0,l=1,f=0,c=0,h=Math.floor(a);while(f<t){if(h=Math.floor(a),o=h*s+i,c=h*f+l,a-h<5e-8)break;a=1/(a-h),i=s,s=o,l=f,f=c}if(c>t&&(f>t?(c=l,o=i):(c=f,o=s)),!r)return[0,n*o,c];var u=Math.floor(n*o/c);return[u,n*o-u*c,c]}function J(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),i=0,s=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)s=r?[1317,10,29]:[1900,2,29],i=3;else if(0===n)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var l=new Date(1900,0,1);l.setDate(l.getDate()+n-1),s=[l.getFullYear(),l.getMonth()+1,l.getDate()],i=l.getDay(),n<60&&(i=(i+6)%7),r&&(i=oe(l,s))}return o.y=s[0],o.m=s[1],o.d=s[2],o.S=a%60,a=Math.floor(a/60),o.M=a%60,a=Math.floor(a/60),o.H=a,o.q=i,o}var Z=new Date(1899,11,31,0,0,0),q=Z.getTime(),Q=new Date(1900,2,1,0,0,0);function ee(e,t){var r=e.getTime();return t?r-=1262304e5:e>=Q&&(r+=864e5),(r-(q+6e4*(e.getTimezoneOffset()-Z.getTimezoneOffset())))/864e5}function te(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function re(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function ne(e){var t=e<0?12:11,r=te(e.toFixed(12));return r.length<=t?r:(r=e.toPrecision(10),r.length<=t?r:e.toExponential(5))}function ae(e){var t=te(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}function ie(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?ne(e):10===r?e.toFixed(10).substr(0,12):ae(e),te(re(t.toUpperCase()))}function se(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):ie(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Ue(14,ee(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function oe(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function le(e,t,r,n){var a,i="",s=0,o=0,l=r.y,f=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:a=l%100,f=2;break;default:a=l%1e4,f=4;break}break;case 109:switch(t.length){case 1:case 2:a=r.m,f=t.length;break;case 3:return V[r.m-1][1];case 5:return V[r.m-1][0];default:return V[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,f=t.length;break;case 3:return G[r.q][0];default:return G[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,f=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,f=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,f=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=n>=2?3===n?1e3:100:1===n?10:1,s=Math.round(o*(r.S+r.u)),s>=60*o&&(s=0),"s"===t?0===s?"0":""+s/o:(i=P(s,2+n),"ss"===t?i.substr(0,2):"."+i.substr(2,t.length-1))):P(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":a=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}f=3===t.length?1:2;break;case 101:a=l,f=1;break}var c=f>0?P(a,f):"";return c}function fe(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var ce=/%/g;function he(e,t,r){var n=t.replace(ce,""),a=t.length-n.length;return ke(e,n,r*Math.pow(10,2*a))+dt("%",a)}function ue(e,t,r){var n=t.length-1;while(44===t.charCodeAt(n-1))--n;return ke(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function de(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+de(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),-1===r.indexOf("e")){var s=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);while("0."===r.substr(0,2))r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var pe=/# (\?+)( ?)\/( ?)(\d+)/;function me(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,o=n;return r+(0===i?"":""+i)+" "+(0===s?dt(" ",e[1].length+1+e[4].length):L(s,e[1].length)+e[2]+"/"+e[3]+P(o,e[4].length))}function ge(e,t,r){return r+(0===t?"":""+t)+dt(" ",e[1].length+2+e[4].length)}var ve=/^#*0*\.([0#]+)/,Te=/\).*[0#]/,we=/\(###\) ###\\?-####/;function be(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function Ee(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function Se(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Ae(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function ye(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}function _e(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Te)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?_e("n",n,r):"("+_e("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return ue(e,t,r);if(-1!==t.indexOf("%"))return he(e,t,r);if(-1!==t.indexOf("E"))return de(t,r);if(36===t.charCodeAt(0))return"$"+_e(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+W(l,t.length);if(t.match(/^[#?]+$/))return a=W(r,0),"0"===a&&(a=""),a.length>t.length?a:be(t.substr(0,t.length-a.length))+a;if(i=t.match(pe))return me(i,l,f);if(t.match(/^#+0+$/))return f+W(l,t.length-t.indexOf("0"));if(i=t.match(ve))return a=Ee(r,i[1].length).replace(/^([^\.]+)$/,"$1."+be(i[1])).replace(/\.$/,"."+be(i[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+dt("0",be(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return f+Ee(l,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return f+fe(W(l,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+_e(e,t,-r):fe(""+(Math.floor(r)+Ae(r,i[1].length)))+"."+P(Se(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return _e(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=D(_e(e,t.replace(/[\\-]/g,""),r)),s=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(we))return a=_e(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=Y(l,Math.pow(10,s)-1,!1),a=""+f,c=ke("n",i[1],o[1])," "==c.charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=M(o[2],s),c.length<i[4].length&&(c=be(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),o=Y(l,Math.pow(10,s)-1,!0),f+(o[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],s)+i[2]+"/"+i[3]+M(o[2],s):dt(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=W(r,0),t.length<=a.length?a:be(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return be(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=Se(r,i[1].length),r<0?"-"+_e(e,t,-r):fe(ye(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e}))+"."+P(s,i[1].length);switch(t){case"###,##0.00":return _e(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=fe(W(l,0));return"0"!==d?f+d:"";case"###,###.00":return _e(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return _e(e,"#,##0.00",r).replace(/^0\./,".");default:}throw new Error("unsupported format |"+t+"|")}function Oe(e,t,r){var n=t.length-1;while(44===t.charCodeAt(n-1))--n;return ke(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function xe(e,t,r){var n=t.replace(ce,""),a=t.length-n.length;return ke(e,n,r*Math.pow(10,2*a))+dt("%",a)}function Ce(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+Ce(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Re(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Te)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Re("n",n,r):"("+Re("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return Oe(e,t,r);if(-1!==t.indexOf("%"))return xe(e,t,r);if(-1!==t.indexOf("E"))return Ce(t,r);if(36===t.charCodeAt(0))return"$"+Re(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+P(l,t.length);if(t.match(/^[#?]+$/))return a=""+r,0===r&&(a=""),a.length>t.length?a:be(t.substr(0,t.length-a.length))+a;if(i=t.match(pe))return ge(i,l,f);if(t.match(/^#+0+$/))return f+P(l,t.length-t.indexOf("0"));if(i=t.match(ve))return a=(""+r).replace(/^([^\.]+)$/,"$1."+be(i[1])).replace(/\.$/,"."+be(i[1])),a=a.replace(/\.(\d*)$/,(function(e,t){return"."+t+dt("0",be(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return f+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return f+fe(""+l);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Re(e,t,-r):fe(""+r)+"."+dt("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Re(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=D(Re(e,t.replace(/[\\-]/g,""),r)),s=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(we))return a=Re(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=Y(l,Math.pow(10,s)-1,!1),a=""+f,c=ke("n",i[1],o[1])," "==c.charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=M(o[2],s),c.length<i[4].length&&(c=be(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),o=Y(l,Math.pow(10,s)-1,!0),f+(o[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],s)+i[2]+"/"+i[3]+M(o[2],s):dt(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:be(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return be(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Re(e,t,-r):fe(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e}))+"."+P(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=fe(""+l);return"0"!==d?f+d:"";default:if(t.match(/\.[0#?]*$/))return Re(e,t.slice(0,t.lastIndexOf(".")),r)+be(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function ke(e,t,r){return(0|r)===r?Re(e,t,r):_e(e,t,r)}function Ne(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}var Ie=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function De(e){var t=0,r="",n="";while(t<e.length)switch(r=e.charAt(t)){case"G":H(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":n=r;while("]"!==e.charAt(t++)&&t<e.length)n+=e.charAt(t);if(n.match(Ie))return!0;break;case".":case"0":case"#":while(t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1));break;case"?":while(e.charAt(++t)===r);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":while(t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1);break;case" ":++t;break;default:++t;break}return!1}function Pe(e,t,r,n){var a,i,s,o=[],l="",f=0,c="",h="t",u="H";while(f<e.length)switch(c=e.charAt(f)){case"G":if(!H(e,f))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"G",v:"General"},f+=7;break;case'"':for(l="";34!==(s=e.charCodeAt(++f))&&f<e.length;)l+=String.fromCharCode(s);o[o.length]={t:"t",v:l},++f;break;case"\\":var d=e.charAt(++f),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++f;break;case"_":o[o.length]={t:"t",v:" "},f+=2;break;case"@":o[o.length]={t:"T",v:t},++f;break;case"B":case"b":if("1"===e.charAt(f+1)||"2"===e.charAt(f+1)){if(null==a&&(a=J(t,r,"2"===e.charAt(f+1)),null==a))return"";o[o.length]={t:"X",v:e.substr(f,2)},h=c,f+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==a&&(a=J(t,r),null==a))return"";l=c;while(++f<e.length&&e.charAt(f).toLowerCase()===c)l+=c;"m"===c&&"h"===h.toLowerCase()&&(c="M"),"h"===c&&(c=u),o[o.length]={t:c,v:l},h=c;break;case"A":case"a":case"上":var m={t:c,v:c};if(null==a&&(a=J(t,r)),"A/P"===e.substr(f,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",u="h",f+=3):"AM/PM"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",f+=5,u="h"):"上午/下午"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"下午":"上午"),m.t="T",f+=5,u="h"):(m.t="t",++f),null==a&&"T"===m.t)return"";o[o.length]=m,h=c;break;case"[":l=c;while("]"!==e.charAt(f++)&&f<e.length)l+=e.charAt(f);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(Ie)){if(null==a&&(a=J(t,r),null==a))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",De(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=a){l=c;while(++f<e.length&&"0"===(c=e.charAt(f)))l+=c;o[o.length]={t:"s",v:l};break}case"0":case"#":l=c;while(++f<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(f))>-1)l+=c;o[o.length]={t:"n",v:l};break;case"?":l=c;while(e.charAt(++f)===c)l+=c;o[o.length]={t:c,v:l},h=c;break;case"*":++f," "!=e.charAt(f)&&"*"!=e.charAt(f)||++f;break;case"(":case")":o[o.length]={t:1===n?"t":c,v:c},++f;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":l=c;while(f<e.length&&"0123456789".indexOf(e.charAt(++f))>-1)l+=e.charAt(f);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:c,v:c},++f;break;case"$":o[o.length]={t:"t",v:"$"},++f;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"t",v:c},++f;break}var g,v=0,T=0;for(f=o.length-1,h="t";f>=0;--f)switch(o[f].t){case"h":case"H":o[f].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[f].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[f].t;break;case"m":"s"===h&&(o[f].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[f].v.match(/[Hh]/)&&(v=1),v<2&&o[f].v.match(/[Mm]/)&&(v=2),v<3&&o[f].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M);break}var w,b="";for(f=0;f<o.length;++f)switch(o[f].t){case"t":case"T":case" ":case"D":break;case"X":o[f].v="",o[f].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[f].v=le(o[f].t.charCodeAt(0),o[f].v,a,T),o[f].t="t";break;case"n":case"?":w=f+1;while(null!=o[w]&&("?"===(c=o[w].t)||"D"===c||(" "===c||"t"===c)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[f].t&&(" "===c||"n"===c||")"===c)||"t"===c&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t)))o[f].v+=o[w].v,o[w]={v:"",t:";"},++w;b+=o[f].v,f=w-1;break;case"G":o[f].t="t",o[f].v=se(t,r);break}var E,S,A="";if(b.length>0){40==b.charCodeAt(0)?(E=t<0&&45===b.charCodeAt(0)?-t:t,S=ke("n",b,E)):(E=t<0&&n>1?-t:t,S=ke("n",b,E),E<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),w=S.length-1;var y=o.length;for(f=0;f<o.length;++f)if(null!=o[f]&&"t"!=o[f].t&&o[f].v.indexOf(".")>-1){y=f;break}var _=o.length;if(y===o.length&&-1===S.indexOf("E")){for(f=o.length-1;f>=0;--f)null!=o[f]&&-1!=="n?".indexOf(o[f].t)&&(w>=o[f].v.length-1?(w-=o[f].v.length,o[f].v=S.substr(w+1,o[f].v.length)):w<0?o[f].v="":(o[f].v=S.substr(0,w+1),w=-1),o[f].t="t",_=f);w>=0&&_<o.length&&(o[_].v=S.substr(0,w+1)+o[_].v)}else if(y!==o.length&&-1===S.indexOf("E")){for(w=S.indexOf(".")-1,f=y;f>=0;--f)if(null!=o[f]&&-1!=="n?".indexOf(o[f].t)){for(i=o[f].v.indexOf(".")>-1&&f===y?o[f].v.indexOf(".")-1:o[f].v.length-1,A=o[f].v.substr(i+1);i>=0;--i)w>=0&&("0"===o[f].v.charAt(i)||"#"===o[f].v.charAt(i))&&(A=S.charAt(w--)+A);o[f].v=A,o[f].t="t",_=f}for(w>=0&&_<o.length&&(o[_].v=S.substr(0,w+1)+o[_].v),w=S.indexOf(".")+1,f=y;f<o.length;++f)if(null!=o[f]&&(-1!=="n?(".indexOf(o[f].t)||f===y)){for(i=o[f].v.indexOf(".")>-1&&f===y?o[f].v.indexOf(".")+1:0,A=o[f].v.substr(0,i);i<o[f].v.length;++i)w<S.length&&(A+=S.charAt(w++));o[f].v=A,o[f].t="t",_=f}}}for(f=0;f<o.length;++f)null!=o[f]&&"n?".indexOf(o[f].t)>-1&&(E=n>1&&t<0&&f>0&&"-"===o[f-1].v?-t:t,o[f].v=ke(o[f].t,o[f].v,E),o[f].t="t");var O="";for(f=0;f!==o.length;++f)null!=o[f]&&(O+=o[f].v);return O}var Le=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Me(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Fe(e,t){var r=Ne(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!==typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break;case 4:break}var i=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,i];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var s=r[0].match(Le),o=r[1].match(Le);return Me(t,s)?[n,r[0]]:Me(t,o)?[n,r[1]]:[n,r[null!=s&&null!=o?2:1]]}return[n,i]}function Ue(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:z)[e],null==n&&(n=r.table&&r.table[X[e]]||z[X[e]]),null==n&&(n=K[e]||"General");break}if(H(n,0))return se(t,r);t instanceof Date&&(t=ee(t,r.date1904));var a=Fe(n,t);if(H(a[1]))return se(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return Pe(a[1],t,r,a[0])}function Be(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(void 0!=z[r]){if(z[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return z[t]=e,t}function We(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Be(e[t],t)}function He(){z=j()}var Ge=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ve(e){var t="number"==typeof e?z[e]:e;return t=t.replace(Ge,"(\\d+)"),new RegExp("^"+t+"$")}function je(e,t,r){var n=-1,a=-1,i=-1,s=-1,o=-1,l=-1;(t.match(Ge)||[]).forEach((function(e,t){var f=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":n=f;break;case"d":i=f;break;case"h":s=f;break;case"s":l=f;break;case"m":s>=0?o=f:a=f;break}})),l>=0&&-1==o&&a>=0&&(o=a,a=-1);var f=(""+(n>=0?n:(new Date).getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);7==f.length&&(f="0"+f),8==f.length&&(f="20"+f);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);return-1==s&&-1==o&&-1==l?f:-1==n&&-1==a&&-1==i?c:f+"T"+c}var ze=function(){var e={};function t(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=r,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!==typeof Int32Array?new Int32Array(t):t}e.version="1.2.0";var r=t();function n(e){var t=0,r=0,n=0,a="undefined"!==typeof Int32Array?new Int32Array(4096):new Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var i=[];for(n=1;16!=n;++n)i[n-1]="undefined"!==typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return i}var a=n(r),i=a[0],s=a[1],o=a[2],l=a[3],f=a[4],c=a[5],h=a[6],u=a[7],d=a[8],p=a[9],m=a[10],g=a[11],v=a[12],T=a[13],w=a[14];function b(e,t){for(var n=-1^t,a=0,i=e.length;a<i;)n=n>>>8^r[255&(n^e.charCodeAt(a++))];return~n}function E(e,t){for(var n=-1^t,a=e.length-15,b=0;b<a;)n=w[e[b++]^255&n]^T[e[b++]^n>>8&255]^v[e[b++]^n>>16&255]^g[e[b++]^n>>>24]^m[e[b++]]^p[e[b++]]^d[e[b++]]^u[e[b++]]^h[e[b++]]^c[e[b++]]^f[e[b++]]^l[e[b++]]^o[e[b++]]^s[e[b++]]^i[e[b++]]^r[e[b++]];a+=15;while(b<a)n=n>>>8^r[255&(n^e[b++])];return~n}function S(e,t){for(var n=-1^t,a=0,i=e.length,s=0,o=0;a<i;)s=e.charCodeAt(a++),s<128?n=n>>>8^r[255&(n^s)]:s<2048?(n=n>>>8^r[255&(n^(192|s>>6&31))],n=n>>>8^r[255&(n^(128|63&s))]):s>=55296&&s<57344?(s=64+(1023&s),o=1023&e.charCodeAt(a++),n=n>>>8^r[255&(n^(240|s>>8&7))],n=n>>>8^r[255&(n^(128|s>>2&63))],n=n>>>8^r[255&(n^(128|o>>6&15|(3&s)<<4))],n=n>>>8^r[255&(n^(128|63&o))]):(n=n>>>8^r[255&(n^(224|s>>12&15))],n=n>>>8^r[255&(n^(128|s>>6&63))],n=n>>>8^r[255&(n^(128|63&s))]);return~n}return e.table=r,e.bstr=b,e.buf=E,e.str=S,e}(),$e=function(){var e,t={};function r(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,i=0,s=Math.min(r.length,n.length);a<s;++a){if(i=r[a].length-n[a].length)return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}function n(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:n(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function i(e,t){"string"===typeof t&&(t=new Date(t));var r=t.getHours();r=r<<6|t.getMinutes(),r=r<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=n<<4|t.getMonth()+1,n=n<<5|t.getDate(),e.write_shift(2,n)}function s(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,a=31&r;r>>>=5;var i=15&r;r>>>=4,n.setMilliseconds(0),n.setFullYear(r+1980),n.setMonth(i-1),n.setDate(a);var s=31&t;t>>>=5;var o=63&t;return t>>>=6,n.setHours(t),n.setMinutes(o),n.setSeconds(s<<1),n}function o(e){Rr(e,0);var t={},r=0;while(e.l<=e.length-4){var n=e.read_shift(2),a=e.read_shift(2),i=e.l+a,s={};switch(n){case 21589:r=e.read_shift(1),1&r&&(s.mtime=e.read_shift(4)),a>5&&(2&r&&(s.atime=e.read_shift(4)),4&r&&(s.ctime=e.read_shift(4))),s.mtime&&(s.mt=new Date(1e3*s.mtime));break}e.l=i,t[n]=s}return t}function l(){return e||(e={})}function f(e,t){if(80==e[0]&&75==e[1])return Ne(e,t);if(109==(32|e[0])&&105==(32|e[1]))return We(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r=3,n=512,a=0,i=0,s=0,o=0,l=0,f=[],p=e.slice(0,512);Rr(p,0);var g=c(p);switch(r=g[0],r){case 3:n=512;break;case 4:n=4096;break;case 0:if(0==g[1])return Ne(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==n&&(p=e.slice(0,n),Rr(p,28));var w=e.slice(0,n);h(p,r);var b=p.read_shift(4,"i");if(3===r&&0!==b)throw new Error("# Directory Sectors: Expected 0 saw "+b);p.l+=4,s=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),o=p.read_shift(4,"i"),a=p.read_shift(4,"i"),l=p.read_shift(4,"i"),i=p.read_shift(4,"i");for(var E=-1,S=0;S<109;++S){if(E=p.read_shift(4,"i"),E<0)break;f[S]=E}var A=u(e,n);m(l,i,A,n,f);var y=v(A,s,f,n);y[s].name="!Directory",a>0&&o!==B&&(y[o].name="!MiniFAT"),y[f[0]].name="!FAT",y.fat_addrs=f,y.ssz=n;var _={},O=[],x=[],C=[];T(s,y,A,O,a,_,x,o),d(x,C,O),O.shift();var R={FileIndex:x,FullPaths:C};return t&&t.raw&&(R.raw={header:w,sectors:A}),R}function c(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(W,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}function h(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}function u(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}function d(e,t,r){for(var n=0,a=0,i=0,s=0,o=0,l=r.length,f=[],c=[];n<l;++n)f[n]=c[n]=n,t[n]=r[n];for(;o<c.length;++o)n=c[o],a=e[n].L,i=e[n].R,s=e[n].C,f[n]===n&&(-1!==a&&f[a]!==a&&(f[n]=f[a]),-1!==i&&f[i]!==i&&(f[n]=f[i])),-1!==s&&(f[s]=n),-1!==a&&n!=f[n]&&(f[a]=f[n],c.lastIndexOf(a)<o&&c.push(a)),-1!==i&&n!=f[n]&&(f[i]=f[n],c.lastIndexOf(i)<o&&c.push(i));for(n=1;n<l;++n)f[n]===n&&(-1!==i&&f[i]!==i?f[n]=f[i]:-1!==a&&f[a]!==a&&(f[n]=f[a]));for(n=1;n<l;++n)if(0!==e[n].type){if(o=n,o!=f[o])do{o=f[o],t[n]=t[o]+"/"+t[n]}while(0!==o&&-1!==f[o]&&o!=f[o]);f[n]=-1}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}function p(e,t,r){var n=e.start,a=e.size,i=[],s=n;while(r&&a>0&&s>=0)i.push(t.slice(s*U,s*U+U)),a-=U,s=Er(r,4*s);return 0===i.length?Nr(0):R(i).slice(0,e.size)}function m(e,t,r,n,a){var i=B;if(e===B){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var s=r[e],o=(n>>>2)-1;if(!s)return;for(var l=0;l<o;++l){if((i=Er(s,4*l))===B)break;a.push(i)}m(Er(s,n-4),t-1,r,n,a)}}function g(e,t,r,n,a){var i=[],s=[];a||(a=[]);var o=n-1,l=0,f=0;for(l=t;l>=0;){a[l]=!0,i[i.length]=l,s.push(e[l]);var c=r[Math.floor(4*l/n)];if(f=4*l&o,n<4+f)throw new Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[c])break;l=Er(e[c],f)}return{nodes:i,data:Jt([s])}}function v(e,t,r,n){var a=e.length,i=[],s=[],o=[],l=[],f=n-1,c=0,h=0,u=0,d=0;for(c=0;c<a;++c)if(o=[],u=c+t,u>=a&&(u-=a),!s[u]){l=[];var p=[];for(h=u;h>=0;){p[h]=!0,s[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/n)];if(d=4*h&f,n<4+d)throw new Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[m])break;if(h=Er(e[m],d),p[h])break}i[u]={nodes:o,data:Jt([l])}}return i}function T(e,t,r,n,a,i,s,o){for(var l,f=0,c=n.length?2:0,h=t[e].data,u=0,d=0;u<h.length;u+=128){var m=h.slice(u,u+128);Rr(m,64),d=m.read_shift(2),l=qt(m,0,d-c),n.push(l);var v={name:l,type:m.read_shift(1),color:m.read_shift(1),L:m.read_shift(4,"i"),R:m.read_shift(4,"i"),C:m.read_shift(4,"i"),clsid:m.read_shift(16),state:m.read_shift(4,"i"),start:0,size:0},T=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==T&&(v.ct=O(m,m.l-8));var w=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==w&&(v.mt=O(m,m.l-8)),v.start=m.read_shift(4,"i"),v.size=m.read_shift(4,"i"),v.size<0&&v.start<0&&(v.size=v.type=0,v.start=B,v.name=""),5===v.type?(f=v.start,a>0&&f!==B&&(t[f].name="!StreamData")):v.size>=4096?(v.storage="fat",void 0===t[v.start]&&(t[v.start]=g(r,v.start,t.fat_addrs,t.ssz)),t[v.start].name=v.name,v.content=t[v.start].data.slice(0,v.size)):(v.storage="minifat",v.size<0?v.size=0:f!==B&&v.start!==B&&t[f]&&(v.content=p(v,t[f].data,(t[o]||{}).data))),v.content&&Rr(v.content,0),i[l]=v,s.push(v)}}function O(e,t){return new Date(1e3*(br(e,t+4)/1e7*Math.pow(2,32)+br(e,t)/1e7-11644473600))}function x(t,r){return l(),f(e.readFileSync(t),r)}function C(e,t){var r=t&&t.type;switch(r||E&&Buffer.isBuffer(e)&&(r="buffer"),r||"base64"){case"file":return x(e,t);case"base64":return f(_(b(e)),t);case"binary":return f(_(e),t)}return f(e,t)}function k(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),D(e)}function D(e){var t="Sh33tJ5";if(!$e.find(e,"/"+t)){var r=Nr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),P(e)}}function P(e,t){k(e);for(var i=!1,s=!1,o=e.FullPaths.length-1;o>=0;--o){var l=e.FileIndex[o];switch(l.type){case 0:s?i=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(l.R*l.L*l.C)&&(i=!0),l.R>-1&&l.L>-1&&l.R==l.L&&(i=!0);break;default:i=!0;break}}if(i||t){var f=new Date(1987,1,19),c=0,h=Object.create?Object.create(null):{},u=[];for(o=0;o<e.FullPaths.length;++o)h[e.FullPaths[o]]=!0,0!==e.FileIndex[o].type&&u.push([e.FullPaths[o],e.FileIndex[o]]);for(o=0;o<u.length;++o){var d=n(u[o][0]);s=h[d],s||(u.push([d,{name:a(d).replace("/",""),type:1,clsid:G,ct:f,mt:f,content:null}]),h[d]=!0)}for(u.sort((function(e,t){return r(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],o=0;o<u.length;++o)e.FullPaths[o]=u[o][0],e.FileIndex[o]=u[o][1];for(o=0;o<u.length;++o){var p=e.FileIndex[o],m=e.FullPaths[o];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||G,0===o)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=o+1;c<u.length;++c)if(n(e.FullPaths[c])==m)break;for(p.C=c>=u.length?-1:c,c=o+1;c<u.length;++c)if(n(e.FullPaths[c])==n(m))break;p.R=c>=u.length?-1:c,p.type=1}else n(e.FullPaths[o+1]||"")==n(m)&&(p.R=o+1),p.type=2}}}function L(e,t){var r=t||{};if("mad"==r.fileType)return He(e,r);switch(P(e),r.fileType){case"zip":return De(e,r)}var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var i=a.content.length;i>0&&(i<4096?t+=i+63>>6:r+=i+511>>9)}}var s=e.FullPaths.length+3>>2,o=t+7>>3,l=t+127>>7,f=o+r+s+l,c=f+127>>7,h=c<=109?0:Math.ceil((c-109)/127);while(f+c+h+127>>7>c)h=++c<=109?0:Math.ceil((c-109)/127);var u=[1,h,c,l,s,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),a=Nr(n[7]<<9),i=0,s=0;for(i=0;i<8;++i)a.write_shift(1,H[i]);for(i=0;i<8;++i)a.write_shift(2,0);for(a.write_shift(2,62),a.write_shift(2,3),a.write_shift(2,65534),a.write_shift(2,9),a.write_shift(2,6),i=0;i<3;++i)a.write_shift(2,0);for(a.write_shift(4,0),a.write_shift(4,n[2]),a.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),a.write_shift(4,0),a.write_shift(4,4096),a.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:B),a.write_shift(4,n[3]),a.write_shift(-4,n[1]?n[0]-1:B),a.write_shift(4,n[1]),i=0;i<109;++i)a.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(s=0;s<n[1];++s){for(;i<236+127*s;++i)a.write_shift(-4,i<n[2]?n[1]+i:-1);a.write_shift(-4,s===n[1]-1?B:s+1)}var o=function(e){for(s+=e;i<s-1;++i)a.write_shift(-4,i+1);e&&(++i,a.write_shift(-4,B))};for(s=i=0,s+=n[1];i<s;++i)a.write_shift(-4,V.DIFSECT);for(s+=n[2];i<s;++i)a.write_shift(-4,V.FATSECT);o(n[3]),o(n[4]);for(var l=0,f=0,c=e.FileIndex[0];l<e.FileIndex.length;++l)c=e.FileIndex[l],c.content&&(f=c.content.length,f<4096||(c.start=s,o(f+511>>9)));o(n[6]+7>>3);while(511&a.l)a.write_shift(-4,V.ENDOFCHAIN);for(s=i=0,l=0;l<e.FileIndex.length;++l)c=e.FileIndex[l],c.content&&(f=c.content.length,!f||f>=4096||(c.start=s,o(f+63>>6)));while(511&a.l)a.write_shift(-4,V.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var h=e.FullPaths[i];if(h&&0!==h.length){c=e.FileIndex[i],0===i&&(c.start=c.size?c.start-1:B);var u=0===i&&r.root||c.name;if(f=2*(u.length+1),a.write_shift(64,u,"utf16le"),a.write_shift(2,f),a.write_shift(1,c.type),a.write_shift(1,c.color),a.write_shift(-4,c.L),a.write_shift(-4,c.R),a.write_shift(-4,c.C),c.clsid)a.write_shift(16,c.clsid,"hex");else for(l=0;l<4;++l)a.write_shift(4,0);a.write_shift(4,c.state||0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,c.start),a.write_shift(4,c.size),a.write_shift(4,0)}else{for(l=0;l<17;++l)a.write_shift(4,0);for(l=0;l<3;++l)a.write_shift(4,-1);for(l=0;l<12;++l)a.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if(c=e.FileIndex[i],c.size>=4096)if(a.l=c.start+1<<9,E&&Buffer.isBuffer(c.content))c.content.copy(a,a.l,0,c.size),a.l+=c.size+511&-512;else{for(l=0;l<c.size;++l)a.write_shift(1,c.content[l]);for(;511&l;++l)a.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if(c=e.FileIndex[i],c.size>0&&c.size<4096)if(E&&Buffer.isBuffer(c.content))c.content.copy(a,a.l,0,c.size),a.l+=c.size+63&-64;else{for(l=0;l<c.size;++l)a.write_shift(1,c.content[l]);for(;63&l;++l)a.write_shift(1,0)}if(E)a.l=a.length;else while(a.l<a.length)a.write_shift(1,0);return a}function M(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),n=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var i=t.toUpperCase(),s=!0===a?r.indexOf(i):n.indexOf(i);if(-1!==s)return e.FileIndex[s];var o=!i.match(I);for(i=i.replace(N,""),o&&(i=i.replace(I,"!")),s=0;s<r.length;++s){if((o?r[s].replace(I,"!"):r[s]).replace(N,"")==i)return e.FileIndex[s];if((o?n[s].replace(I,"!"):n[s]).replace(N,"")==i)return e.FileIndex[s]}return null}t.version="1.2.1";var F,U=64,B=-2,W="d0cf11e0a1b11ae1",H=[208,207,17,224,161,177,26,225],G="00000000000000000000000000000000",V={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:B,FREESECT:-1,HEADER_SIGNATURE:W,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:G,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function j(t,r,n){l();var a=L(t,n);e.writeFileSync(r,a)}function z(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function $(t,r){var n=L(t,r);switch(r&&r.type||"buffer"){case"file":return l(),e.writeFileSync(r.filename,n),n;case"binary":return"string"==typeof n?n:z(n);case"base64":return w("string"==typeof n?n:z(n));case"buffer":if(E)return Buffer.isBuffer(n)?n:S(n);case"array":return"string"==typeof n?_(n):n}return n}function X(e){try{var t=e.InflateRaw,r=new t;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),!r.bytesRead)throw new Error("zlib does not expose bytesRead");F=e}catch(n){console.error("cannot use native zlib: "+(n.message||n))}}function K(e,t){if(!F)return Re(e,t);var r=F.InflateRaw,n=new r,a=n._processChunk(e.slice(e.l),n._finishFlushFlag);return e.l+=n.bytesRead,a}function Y(e){return F?F.deflateRawSync(e):Ee(e)}var J=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Z=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Q(e){var t=139536&(e<<1|e<<11)|558144&(e<<5|e<<15);return 255&(t>>16|t>>8|t)}for(var ee="undefined"!==typeof Uint8Array,te=ee?new Uint8Array(256):[],re=0;re<256;++re)te[re]=Q(re);function ne(e,t){var r=te[255&e];return t<=8?r>>>8-t:(r=r<<8|te[e>>8&255],t<=16?r>>>16-t:(r=r<<8|te[e>>16&255],r>>>24-t))}function ae(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}function ie(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function se(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=4?0:e[n+1]<<8))>>>r&15}function oe(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function le(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function fe(e,t,r){var n=7&t,a=t>>>3,i=(1<<r)-1,s=e[a]>>>n;return r<8-n?s&i:(s|=e[a+1]<<8-n,r<16-n?s&i:(s|=e[a+2]<<16-n,r<24-n||(s|=e[a+3]<<24-n),s&i))}function ce(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function he(e,t,r){var n=7&t,a=t>>>3;return r=(1&r)<<n,e[a]|=r,t+1}function ue(e,t,r){var n=7&t,a=t>>>3;return r<<=n,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function de(e,t,r){var n=7&t,a=t>>>3;return r<<=n,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function pe(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(E){var i=y(n);if(e.copy)e.copy(i);else for(;a<e.length;++a)i[a]=e[a];return i}if(ee){var s=new Uint8Array(n);if(s.set)s.set(e);else for(;a<r;++a)s[a]=e[a];return s}return e.length=n,e}function me(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function ge(e,t,r){var n=1,a=0,i=0,s=0,o=0,l=e.length,f=ee?new Uint16Array(32):me(32);for(i=0;i<32;++i)f[i]=0;for(i=l;i<r;++i)e[i]=0;l=e.length;var c=ee?new Uint16Array(l):me(l);for(i=0;i<l;++i)f[a=e[i]]++,n<a&&(n=a),c[i]=0;for(f[0]=0,i=1;i<=n;++i)f[i+16]=o=o+f[i-1]<<1;for(i=0;i<l;++i)o=e[i],0!=o&&(c[i]=f[o+16]++);var h=0;for(i=0;i<l;++i)if(h=e[i],0!=h)for(o=ne(c[i],n)>>n-h,s=(1<<n+4-h)-1;s>=0;--s)t[o|s<<h]=15&h|i<<4;return n}var ve=ee?new Uint16Array(512):me(512),Te=ee?new Uint16Array(32):me(32);if(!ee){for(var we=0;we<512;++we)ve[we]=0;for(we=0;we<32;++we)Te[we]=0}(function(){for(var e=[],t=0;t<32;t++)e.push(5);ge(e,Te,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);ge(r,ve,288)})();var be=function(){for(var e=ee?new Uint8Array(32768):[],t=0,r=0;t<q.length-1;++t)for(;r<q[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=ee?new Uint8Array(259):[];for(t=0,r=0;t<Z.length-1;++t)for(;r<Z[t+1];++r)n[r]=t;function a(e,t){var r=0;while(r<e.length){var n=Math.min(65535,e.length-r),a=r+n==e.length;t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);while(n-- >0)t[t.l++]=e[r++]}return t.l}function i(t,r){var a=0,i=0,s=ee?new Uint16Array(32768):[];while(i<t.length){var o=Math.min(65535,t.length-i);if(o<10){a=ce(r,a,+!(i+o!=t.length)),7&a&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);while(o-- >0)r[r.l++]=t[i++];a=8*r.l}else{a=ce(r,a,+!(i+o!=t.length)+2);var l=0;while(o-- >0){var f=t[i];l=32767&(l<<5^f);var c=-1,h=0;if((c=s[l])&&(c|=-32768&i,c>i&&(c-=32768),c<i))while(t[c+h]==t[i+h]&&h<250)++h;if(h>2){f=n[h],f<=22?a=ue(r,a,te[f+1]>>1)-1:(ue(r,a,3),a+=5,ue(r,a,te[f-23]>>5),a+=3);var u=f<8?0:f-4>>2;u>0&&(de(r,a,h-Z[f]),a+=u),f=e[i-c],a=ue(r,a,te[f]>>3),a-=3;var d=f<4?0:f-2>>1;d>0&&(de(r,a,i-c-q[f]),a+=d);for(var p=0;p<h;++p)s[l]=32767&i,l=32767&(l<<5^t[i]),++i;o-=h-1}else f<=143?f+=48:a=he(r,a,1),a=ue(r,a,te[f]),s[l]=32767&i,++i}a=ue(r,a,0)-1}}return r.l=(a+7)/8|0,r.l}return function(e,t){return e.length<8?a(e,t):i(e,t)}}();function Ee(e){var t=Nr(50+Math.floor(1.1*e.length)),r=be(e,t);return t.slice(0,r)}var Se=ee?new Uint16Array(32768):me(32768),Ae=ee?new Uint16Array(32768):me(32768),ye=ee?new Uint16Array(128):me(128),_e=1,Oe=1;function xe(e,t){var r=oe(e,t)+257;t+=5;var n=oe(e,t)+1;t+=5;var a=se(e,t)+4;t+=4;for(var i=0,s=ee?new Uint8Array(19):me(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=1,f=ee?new Uint8Array(8):me(8),c=ee?new Uint8Array(8):me(8),h=s.length,u=0;u<a;++u)s[J[u]]=i=ie(e,t),l<i&&(l=i),f[i]++,t+=3;var d=0;for(f[0]=0,u=1;u<=l;++u)c[u]=d=d+f[u-1]<<1;for(u=0;u<h;++u)0!=(d=s[u])&&(o[u]=c[d]++);var p=0;for(u=0;u<h;++u)if(p=s[u],0!=p){d=te[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)ye[d|m<<p]=7&p|u<<3}var g=[];for(l=1;g.length<r+n;)switch(d=ye[le(e,t)],t+=7&d,d>>>=3){case 16:i=3+ae(e,t),t+=2,d=g[g.length-1];while(i-- >0)g.push(d);break;case 17:i=3+ie(e,t),t+=3;while(i-- >0)g.push(0);break;case 18:i=11+le(e,t),t+=7;while(i-- >0)g.push(0);break;default:g.push(d),l<d&&(l=d);break}var v=g.slice(0,r),T=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=n;u<30;++u)T[u]=0;return _e=ge(v,Se,286),Oe=ge(T,Ae,30),t}function Ce(e,t){if(3==e[0]&&!(3&e[1]))return[A(t),2];var r=0,n=0,a=y(t||1<<18),i=0,s=a.length>>>0,o=0,l=0;while(0==(1&n))if(n=ie(e,r),r+=3,n>>>1!=0)for(n>>1==1?(o=9,l=5):(r=xe(e,r),o=_e,l=Oe);;){!t&&s<i+32767&&(a=pe(a,i+32767),s=a.length);var f=fe(e,r,o),c=n>>>1==1?ve[f]:Se[f];if(r+=15&c,c>>>=4,0===(c>>>8&255))a[i++]=c;else{if(256==c)break;c-=257;var h=c<8?0:c-4>>2;h>5&&(h=0);var u=i+Z[c];h>0&&(u+=fe(e,r,h),r+=h),f=fe(e,r,l),c=n>>>1==1?Te[f]:Ae[f],r+=15&c,c>>>=4;var d=c<4?0:c-2>>1,p=q[c];d>0&&(p+=fe(e,r,d),r+=d),!t&&s<u&&(a=pe(a,u+100),s=a.length);while(i<u)a[i]=a[i-p],++i}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0){!t&&s<i+m&&(a=pe(a,i+m),s=a.length);while(m-- >0)a[i++]=e[r>>>3],r+=8}}return t?[a,r+7>>>3]:[a.slice(0,i),r+7>>>3]}function Re(e,t){var r=e.slice(e.l||0),n=Ce(r,t);return e.l+=n[1],n[0]}function ke(e,t){if(!e)throw new Error(t);"undefined"!==typeof console&&console.error(t)}function Ne(e,t){var r=e;Rr(r,0);var n=[],a=[],i={FileIndex:n,FullPaths:a};k(i,{root:t.root});var s=r.length-4;while((80!=r[s]||75!=r[s+1]||5!=r[s+2]||6!=r[s+3])&&s>=0)--s;r.l=s+4,r.l+=4;var l=r.read_shift(2);r.l+=6;var f=r.read_shift(4);for(r.l=f,s=0;s<l;++s){r.l+=20;var c=r.read_shift(4),h=r.read_shift(4),u=r.read_shift(2),d=r.read_shift(2),p=r.read_shift(2);r.l+=8;var m=r.read_shift(4),g=o(r.slice(r.l+u,r.l+u+d));r.l+=u+d+p;var v=r.l;r.l=m+4,Ie(r,c,h,i,g),r.l=v}return i}function Ie(e,t,r,n,a){e.l+=2;var i=e.read_shift(2),l=e.read_shift(2),f=s(e);if(8257&i)throw new Error("Unsupported ZIP encryption");for(var c=e.read_shift(4),h=e.read_shift(4),u=e.read_shift(4),d=e.read_shift(2),p=e.read_shift(2),m="",g=0;g<d;++g)m+=String.fromCharCode(e[e.l++]);if(p){var v=o(e.slice(e.l,e.l+p));(v[21589]||{}).mt&&(f=v[21589].mt),((a||{})[21589]||{}).mt&&(f=a[21589].mt)}e.l+=p;var T=e.slice(e.l,e.l+h);switch(l){case 8:T=K(e,u);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+l)}var w=!1;8&i&&(c=e.read_shift(4),134695760==c&&(c=e.read_shift(4),w=!0),h=e.read_shift(4),u=e.read_shift(4)),h!=t&&ke(w,"Bad compressed size: "+t+" != "+h),u!=r&&ke(w,"Bad uncompressed size: "+r+" != "+u),Ve(n,m,T,{unsafe:!0,mt:f})}function De(e,t){var r=t||{},n=[],a=[],s=Nr(1),o=r.compression?8:0,l=0,f=!1;f&&(l|=8);var c=0,h=0,u=0,d=0,p=e.FullPaths[0],m=p,g=e.FileIndex[0],v=[],T=0;for(c=1;c<e.FullPaths.length;++c)if(m=e.FullPaths[c].slice(p.length),g=e.FileIndex[c],g.size&&g.content&&"Sh33tJ5"!=m){var w=u,b=Nr(m.length);for(h=0;h<m.length;++h)b.write_shift(1,127&m.charCodeAt(h));b=b.slice(0,b.l),v[d]=ze.buf(g.content,0);var E=g.content;8==o&&(E=Y(E)),s=Nr(30),s.write_shift(4,67324752),s.write_shift(2,20),s.write_shift(2,l),s.write_shift(2,o),g.mt?i(s,g.mt):s.write_shift(4,0),s.write_shift(-4,8&l?0:v[d]),s.write_shift(4,8&l?0:E.length),s.write_shift(4,8&l?0:g.content.length),s.write_shift(2,b.length),s.write_shift(2,0),u+=s.length,n.push(s),u+=b.length,n.push(b),u+=E.length,n.push(E),8&l&&(s=Nr(12),s.write_shift(-4,v[d]),s.write_shift(4,E.length),s.write_shift(4,g.content.length),u+=s.l,n.push(s)),s=Nr(46),s.write_shift(4,33639248),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,l),s.write_shift(2,o),s.write_shift(4,0),s.write_shift(-4,v[d]),s.write_shift(4,E.length),s.write_shift(4,g.content.length),s.write_shift(2,b.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,w),T+=s.l,a.push(s),T+=b.length,a.push(b),++d}return s=Nr(22),s.write_shift(4,101010256),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,d),s.write_shift(2,d),s.write_shift(4,T),s.write_shift(4,u),s.write_shift(2,0),R([R(n),R(a),s])}var Pe={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Le(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&Pe[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/),n&&Pe[n[1]])?Pe[n[1]]:"application/octet-stream"}function Me(e){for(var t=w(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}function Fe(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09"),"\n"==t.charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A");for(var r=[],n=t.split("\r\n"),a=0;a<n.length;++a){var i=n[a];if(0!=i.length)for(var s=0;s<i.length;){var o=76,l=i.slice(s,s+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=i.slice(s,s+o),s+=o,s<i.length&&(l+="="),r.push(l)}else r.push("")}return r.join("\r\n")}function Ue(e){for(var t=[],r=0;r<e.length;++r){var n=e[r];while(r<=e.length&&"="==n.charAt(n.length-1))n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return _(t.join("\r\n"))}function Be(e,t,r){for(var n,a="",i="",s="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var f=l.match(/^(.*?):\s*([^\s].*)$/);if(f)switch(f[1].toLowerCase()){case"content-location":a=f[2].trim();break;case"content-type":s=f[2].trim();break;case"content-transfer-encoding":i=f[2].trim();break}}switch(++o,i.toLowerCase()){case"base64":n=_(b(t.slice(o).join("")));break;case"quoted-printable":n=Ue(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+i)}var c=Ve(e,a.slice(r.length),n,{unsafe:!0});s&&(c.ctype=s)}function We(e,t){if("mime-version:"!=z(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",n=(E&&Buffer.isBuffer(e)?e.toString("binary"):z(e)).split("\r\n"),a=0,i="";for(a=0;a<n.length;++a)if(i=n[a],/^Content-Location:/i.test(i)&&(i=i.slice(i.indexOf("file")),r||(r=i.slice(0,i.lastIndexOf("/")+1)),i.slice(0,r.length)!=r))while(r.length>0)if(r=r.slice(0,r.length-1),r=r.slice(0,r.lastIndexOf("/")+1),i.slice(0,r.length)==r)break;var s=(n[1]||"").match(/boundary="(.*?)"/);if(!s)throw new Error("MAD cannot find boundary");var o="--"+(s[1]||""),l=[],f=[],c={FileIndex:l,FullPaths:f};k(c);var h,u=0;for(a=0;a<n.length;++a){var d=n[a];d!==o&&d!==o+"--"||(u++&&Be(c,n.slice(h,a),r),h=a)}return c}function He(e,t){var r=t||{},n=r.boundary||"SheetJS";n="------="+n;for(var a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+n.slice(2)+'"',"","",""],i=e.FullPaths[0],s=i,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(s=e.FullPaths[l].slice(i.length),o=e.FileIndex[l],o.size&&o.content&&"Sh33tJ5"!=s){s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var f=o.content,c=E&&Buffer.isBuffer(f)?f.toString("binary"):z(f),h=0,u=Math.min(1024,c.length),d=0,p=0;p<=u;++p)(d=c.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+s),a.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),a.push("Content-Type: "+Le(o,s)),a.push(""),a.push(m?Fe(c):Me(c))}return a.push(n+"--\r\n"),a.join("\r\n")}function Ge(e){var t={};return k(t,e),t}function Ve(e,t,r,n){var i=n&&n.unsafe;i||k(e);var s=!i&&$e.find(e,t);if(!s){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),s={name:a(t),type:2},e.FileIndex.push(s),e.FullPaths.push(o),i||$e.utils.cfb_gc(e)}return s.content=r,s.size=r?r.length:0,n&&(n.CLSID&&(s.clsid=n.CLSID),n.mt&&(s.mt=n.mt),n.ct&&(s.ct=n.ct)),s}function je(e,t){k(e);var r=$e.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1}function Xe(e,t,r){k(e);var n=$e.find(e,t);if(n)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n)return e.FileIndex[i].name=a(r),e.FullPaths[i]=r,!0;return!1}function Ke(e){P(e,!0)}return t.find=M,t.read=C,t.parse=f,t.write=$,t.writeFile=j,t.utils={cfb_new:Ge,cfb_add:Ve,cfb_del:je,cfb_mov:Xe,cfb_gc:Ke,ReadShift:Ar,CheckField:Cr,prep_blob:Rr,bconcat:R,use_zlib:X,_deflateRaw:Ee,_inflateRaw:Re,consts:V},t}();let Xe=void 0;function Ke(e){return"string"===typeof e?O(e):Array.isArray(e)?C(e):e}function Ye(e,t,r){if("undefined"!==typeof Xe&&Xe.writeFileSync)return r?Xe.writeFileSync(e,t,r):Xe.writeFileSync(e,t);if("undefined"!==typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=O(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?Mt(t):t;if("undefined"!==typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!==typeof Blob){var a=new Blob([Ke(n)],{type:"application/octet-stream"});if("undefined"!==typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if("undefined"!==typeof saveAs)return saveAs(a,e);if("undefined"!==typeof URL&&"undefined"!==typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if("object"===typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(null!=s.download)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),i}}if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=x(t)),o.write(t),o.close(),t}catch(l){if(!l.message||!l.message.match(/onstruct/))throw l}throw new Error("cannot save file "+e)}function Je(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function Ze(e,t){for(var r=[],n=Je(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function qe(e){for(var t=[],r=Je(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Qe(e){for(var t=[],r=Je(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function et(e){for(var t=[],r=Je(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var tt=new Date(1899,11,30,0,0,0);function rt(e,t){var r=e.getTime();t&&(r-=1263168e5);var n=tt.getTime()+6e4*(e.getTimezoneOffset()-tt.getTimezoneOffset());return(r-n)/864e5}var nt=new Date,at=tt.getTime()+6e4*(nt.getTimezoneOffset()-tt.getTimezoneOffset()),it=nt.getTimezoneOffset();function st(e){var t=new Date;return t.setTime(24*e*60*60*1e3+at),t.getTimezoneOffset()!==it&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-it)),t}var ot=new Date("2017-02-19T19:06:09.000Z"),lt=isNaN(ot.getFullYear())?new Date("2/19/17"):ot,ft=2017==lt.getFullYear();function ct(e,t){var r=new Date(e);if(ft)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==lt.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-60*i.getTimezoneOffset()*1e3)),i}function ht(e,t){if(E&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Mt(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Mt(u(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!==typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Mt(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Mt(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(i){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function ut(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ut(e[r]));return t}function dt(e,t){var r="";while(r.length<t)r+=e;return r}function pt(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(n))?(n=n.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(n))?t:t/r):t/r}var mt=["january","february","march","april","may","june","july","august","september","october","november","december"];function gt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&-1==mt.indexOf(s))return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function vt(e,t,r){if(e.FullPaths){var n;if("string"==typeof r)return n=E?S(r):k(r),$e.utils.cfb_add(e,t,n);$e.utils.cfb_add(e,t,r)}else e.file(t,r)}function Tt(){return $e.utils.cfb_new()}var wt='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var bt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Et=qe(bt),St=/[&<>'"]/g,At=/[\u0000-\u0008\u000b-\u001f]/g;function yt(e){var t=e+"";return t.replace(St,(function(e){return Et[e]})).replace(At,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function _t(e){return yt(e).replace(/ /g,"_x0020_")}var Ot=/[\u0000-\u001f]/g;function xt(e){var t=e+"";return t.replace(St,(function(e){return Et[e]})).replace(/\n/g,"<br/>").replace(Ot,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}function Ct(e){var t=e+"";return t.replace(St,(function(e){return Et[e]})).replace(Ot,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}function Rt(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function kt(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Nt(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0;while(r<e.length)n=e.charCodeAt(r++),n<128?t+=String.fromCharCode(n):(a=e.charCodeAt(r++),n>191&&n<224?(s=(31&n)<<6,s|=63&a,t+=String.fromCharCode(s)):(i=e.charCodeAt(r++),n<240?t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&i):(s=e.charCodeAt(r++),o=((7&n)<<18|(63&a)<<12|(63&i)<<6|63&s)-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function It(e){var t,r,n,a=A(2*e.length),i=1,s=0,o=0;for(r=0;r<e.length;r+=i)i=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=64*(31&n)+(63&e.charCodeAt(r+1)),i=2):n<240?(t=4096*(15&n)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),i=3):(i=4,t=262144*(7&n)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),t-=65536,o=55296+(t>>>10&1023),t=56320+(1023&t)),0!==o&&(a[s++]=255&o,a[s++]=o>>>8,o=0),a[s++]=t%256,a[s++]=t>>>8;return a.slice(0,s).toString("ucs2")}function Dt(e){return S(e,"binary").toString("utf8")}var Pt="foo bar bazâð£",Lt=E&&(Dt(Pt)==Nt(Pt)&&Dt||It(Pt)==Nt(Pt)&&It)||Nt,Mt=E?function(e){return S(e,"utf8").toString("binary")}:function(e){var t=[],r=0,n=0,a=0;while(r<e.length)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},Ft=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}();var Ut=/(^\s|\s$|\n)/;function Bt(e,t){return"<"+e+(t.match(Ut)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Wt(e){return Je(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function Ht(e,t,r){return"<"+e+(null!=r?Wt(r):"")+(null!=t?(t.match(Ut)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Gt(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Vt(e,t){switch(typeof e){case"string":var r=Ht("vt:lpwstr",yt(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Ht((0|e)==e?"vt:i4":"vt:r8",yt(String(e)));case"boolean":return Ht("vt:bool",e?"true":"false")}if(e instanceof Date)return Ht("vt:filetime",Gt(e));throw new Error("Unable to serialize "+e)}var jt={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},zt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],$t={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Xt(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],i=5;i>=0;--i)a=256*a+e[t+i];return 2047==n?0==a?r*(1/0):NaN:(0==n?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function Kt(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?0==s?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var o=0;o<=5;++o,i/=256)e[r+o]=255&i;e[r+6]=(15&a)<<4|15&i,e[r+7]=a>>4|n}var Yt=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},Jt=E?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:S(e)}))):Yt(e)}:Yt,Zt=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Tr(e,a)));return n.join("").replace(N,"")},qt=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(N,""):Zt(e,t,r)}:Zt,Qt=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},er=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Qt(e,t,r)}:Qt,tr=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(vr(e,a)));return n.join("")},rr=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):tr(e,t,r)}:tr,nr=function(e,t){var r=br(e,t);return r>0?rr(e,t+4,t+4+r-1):""},ar=nr,ir=function(e,t){var r=br(e,t);return r>0?rr(e,t+4,t+4+r-1):""},sr=ir,or=function(e,t){var r=2*br(e,t);return r>0?rr(e,t+4,t+4+r-1):""},lr=or,fr=function(e,t){var r=br(e,t);return r>0?qt(e,t+4,t+4+r):""},cr=fr,hr=function(e,t){var r=br(e,t);return r>0?rr(e,t+4,t+4+r):""},ur=hr,dr=function(e,t){return Xt(e,t)},pr=dr,mr=function(e){return Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array};function gr(){qt=function(e,t,r){return d.utils.decode(1200,e.slice(t,r)).replace(N,"")},rr=function(e,t,r){return d.utils.decode(65001,e.slice(t,r))},ar=function(e,t){var r=br(e,t);return r>0?d.utils.decode(i,e.slice(t+4,t+4+r-1)):""},sr=function(e,t){var r=br(e,t);return r>0?d.utils.decode(a,e.slice(t+4,t+4+r-1)):""},lr=function(e,t){var r=2*br(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},cr=function(e,t){var r=br(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r)):""},ur=function(e,t){var r=br(e,t);return r>0?d.utils.decode(65001,e.slice(t+4,t+4+r)):""}}E&&(ar=function(e,t){if(!Buffer.isBuffer(e))return nr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},sr=function(e,t){if(!Buffer.isBuffer(e))return ir(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},lr=function(e,t){if(!Buffer.isBuffer(e))return or(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},cr=function(e,t){if(!Buffer.isBuffer(e))return fr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},ur=function(e,t){if(!Buffer.isBuffer(e))return hr(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},pr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):dr(e,t)},mr=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array}),"undefined"!==typeof d&&gr();var vr=function(e,t){return e[t]},Tr=function(e,t){return 256*e[t+1]+e[t]},wr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},br=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Er=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Sr=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Ar(e,t){var r,n,i,s,o,l,f="",c=[];switch(t){case"dbcs":if(l=this.l,E&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(Tr(this,l)),l+=2;e*=2;break;case"utf8":f=rr(this,this.l,this.l+e);break;case"utf16le":e*=2,f=qt(this,this.l,this.l+e);break;case"wstr":if("undefined"===typeof d)return Ar.call(this,e,"dbcs");f=d.utils.decode(a,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=ar(this,this.l),e=4+br(this,this.l);break;case"lpstr-cp":f=sr(this,this.l),e=4+br(this,this.l);break;case"lpwstr":f=lr(this,this.l),e=4+2*br(this,this.l);break;case"lpp4":e=4+br(this,this.l),f=cr(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+br(this,this.l),f=ur(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":e=0,f="";while(0!==(i=vr(this,this.l+e++)))c.push(p(i));f=c.join("");break;case"_wstr":e=0,f="";while(0!==(i=Tr(this,this.l+e)))c.push(p(i)),e+=2;e+=2,f=c.join("");break;case"dbcs-cont":for(f="",l=this.l,o=0;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return i=vr(this,l),this.l=l+1,s=Ar.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(p(Tr(this,l))),l+=2}f=c.join(""),e*=2;break;case"cpstr":if("undefined"!==typeof d){f=d.utils.decode(a,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(f="",l=this.l,o=0;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return i=vr(this,l),this.l=l+1,s=Ar.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(p(vr(this,l))),l+=1}f=c.join("");break;default:switch(e){case 1:return r=vr(this,this.l),this.l++,r;case 2:return r=("i"===t?wr:Tr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0===(128&this[this.l+3])?(r=(e>0?Er:Sr)(this,this.l),this.l+=4,r):(n=br(this,this.l),this.l+=4,n);case 8:case-8:if("f"===t)return n=8==e?pr(this,this.l):pr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:f=er(this,this.l,e);break}}return this.l+=e,f}var yr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},_r=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Or=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function xr(e,t,r){var n=0,a=0;if("dbcs"===r){for(a=0;a!=t.length;++a)Or(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if("sbcs"===r){if("undefined"!==typeof d&&874==i)for(a=0;a!=t.length;++a){var s=d.utils.encode(i,t.charAt(a));this[this.l+a]=s[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=255&t.charCodeAt(a);n=t.length}else{if("hex"===r){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var l=t.charCodeAt(a);this[this.l++]=255&l,this[this.l++]=l>>8}while(this.l<o)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,yr(this,t,this.l);break;case 8:if(n=8,"f"===r){Kt(this,t,this.l);break}case 16:break;case-4:n=4,_r(this,t,this.l);break}}return this.l+=n,this}function Cr(e,t){var r=er(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Rr(e,t){e.l=t,e.read_shift=Ar,e.chk=Cr,e.write_shift=xr}function kr(e,t){e.l+=t}function Nr(e){var t=A(e);return Rr(t,0),t}function Ir(){var e=[],t=E?256:2048,r=function(e){var t=Nr(e);return Rr(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))},s=function(){return a(),R(e)},o=function(e){a(),n=e,null==n.l&&(n.l=n.length),i(t)};return{next:i,push:o,end:s,_bufs:e}}function Dr(e,t,r,n){var a,i=+t;if(!isNaN(i)){n||(n=nh[i].p||(r||[]).length||0),a=1+(i>=128?1:0)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var s=e.next(a);i<=127?s.write_shift(1,i):(s.write_shift(1,128+(127&i)),s.write_shift(1,i>>7));for(var o=0;4!=o;++o){if(!(n>=128)){s.write_shift(1,n);break}s.write_shift(1,128+(127&n)),n>>=7}n>0&&mr(r)&&e.push(r)}}function Pr(e,t,r){var n=ut(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){while(n.c>=256)n.c-=256;while(n.r>=65536)n.r-=65536}return n}function Lr(e,t,r){var n=ut(e);return n.s=Pr(n.s,t.s,r),n.e=Pr(n.e,t.s,r),n}function Mr(e,t){if(e.cRel&&e.c<0){e=ut(e);while(e.c<0)e.c+=t>8?16384:256}if(e.rRel&&e.r<0){e=ut(e);while(e.r<0)e.r+=t>8?1048576:t>5?65536:16384}var r=Kr(e);return e.cRel||null==e.cRel||(r=jr(r)),e.rRel||null==e.rRel||(r=Wr(r)),r}function Fr(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?Mr(e.s,t.biff)+":"+Mr(e.e,t.biff):(e.s.rRel?"":"$")+Br(e.s.r)+":"+(e.e.rRel?"":"$")+Br(e.e.r):(e.s.cRel?"":"$")+Vr(e.s.c)+":"+(e.e.cRel?"":"$")+Vr(e.e.c)}function Ur(e){return parseInt(Hr(e),10)-1}function Br(e){return""+(e+1)}function Wr(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Hr(e){return e.replace(/\$(\d+)$/,"$1")}function Gr(e){for(var t=zr(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function Vr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function jr(e){return e.replace(/^([A-Z])/,"$$$1")}function zr(e){return e.replace(/^\$([A-Z])/,"$1")}function $r(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Xr(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Kr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Yr(e){var t=e.indexOf(":");return-1==t?{s:Xr(e),e:Xr(e)}:{s:Xr(e.slice(0,t)),e:Xr(e.slice(t+1))}}function Jr(e,t){return"undefined"===typeof t||"number"===typeof t?Jr(e.s,e.e):("string"!==typeof e&&(e=Kr(e)),"string"!==typeof t&&(t=Kr(t)),e==t?e:e+":"+t)}function Zr(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i;++n){if((a=e.charCodeAt(n)-64)<1||a>26)break;r=26*r+a}for(t.s.c=--r,r=0;n<i;++n){if((a=e.charCodeAt(n)-48)<0||a>9)break;r=10*r+a}if(t.s.r=--r,n===i||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i;++n){if((a=e.charCodeAt(n)-64)<1||a>26)break;r=26*r+a}for(t.e.c=--r,r=0;n!=i;++n){if((a=e.charCodeAt(n)-48)<0||a>9)break;r=10*r+a}return t.e.r=--r,t}function qr(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=Ue(e.z,r?rt(t):t)}catch(n){}try{return e.w=Ue((e.XF||{}).numFmtId||(r?14:0),r?rt(t):t)}catch(n){return""+t}}function Qr(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?qn[e.v]||e.v:qr(e,void 0==t?e.v:t))}function en(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function tn(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense;null!=g&&null==a&&(a=g);var i=e||(a?[]:{}),s=0,o=0;if(i&&null!=n.origin){if("number"==typeof n.origin)s=n.origin;else{var l="string"==typeof n.origin?Xr(n.origin):n.origin;s=l.r,o=l.c}i["!ref"]||(i["!ref"]="A1:A1")}var f={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=Zr(i["!ref"]);f.s.c=c.s.c,f.s.r=c.s.r,f.e.c=Math.max(f.e.c,c.e.c),f.e.r=Math.max(f.e.r,c.e.r),-1==s&&(f.e.r=s=c.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if("undefined"!==typeof t[h][u]){var d={v:t[h][u]},p=s+h,m=o+u;if(f.s.r>p&&(f.s.r=p),f.s.c>m&&(f.s.c=m),f.e.r<p&&(f.e.r=p),f.e.c<m&&(f.e.c=m),!t[h][u]||"object"!==typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}else"number"===typeof d.v?d.t="n":"boolean"===typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||z[14],n.cellDates?(d.t="d",d.w=Ue(d.z,rt(d.v))):(d.t="n",d.v=rt(d.v),d.w=Ue(d.z,d.v))):d.t="s";else d=t[h][u];if(a)i[p]||(i[p]=[]),i[p][m]&&i[p][m].z&&(d.z=i[p][m].z),i[p][m]=d;else{var v=Kr({c:m,r:p});i[v]&&i[v].z&&(d.z=i[v].z),i[v]=d}}}return f.s.c<1e7&&(i["!ref"]=Jr(f)),i}function rn(e,t){return tn(null,e,t)}function nn(e){return e.read_shift(4,"i")}function an(e,t){return t||(t=Nr(4)),t.write_shift(4,e),t}function sn(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function on(e,t){var r=!1;return null==t&&(r=!0,t=Nr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function ln(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function fn(e,t){return t||(t=Nr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function cn(e,t){var r=e.l,n=e.read_shift(1),a=sn(e),i=[],s={t:a,h:a};if(0!==(1&n)){for(var o=e.read_shift(4),l=0;l!=o;++l)i.push(ln(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function hn(e,t){var r=!1;return null==t&&(r=!0,t=Nr(15+4*e.t.length)),t.write_shift(1,0),on(e.t,t),r?t.slice(0,t.l):t}var un=cn;function dn(e,t){var r=!1;return null==t&&(r=!0,t=Nr(23+4*e.t.length)),t.write_shift(1,1),on(e.t,t),t.write_shift(4,1),fn({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function pn(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function mn(e,t){return null==t&&(t=Nr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function gn(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function vn(e,t){return null==t&&(t=Nr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Tn=sn,wn=on;function bn(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function En(e,t){var r=!1;return null==t&&(r=!0,t=Nr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Sn=sn,An=bn,yn=En;function _n(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?pr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Er(t,0)>>2;return r?a/100:a}function On(e,t){null==t&&(t=Nr(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?n=1:a==(0|a)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?a:e)<<2)+(r+2))}function xn(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Cn(e,t){return t||(t=Nr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var Rn=xn,kn=Cn;function Nn(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function In(e,t){return(t||Nr(8)).write_shift(8,e,"f")}function Dn(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),o=e.read_shift(1),l=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var f=Zn[a];f&&(t.rgb=Di(f));break;case 2:t.rgb=Di([s,o,l]);break;case 3:t.theme=a;break}return 0!=i&&(t.tint=i>0?i/32767:i/32768),t}function Pn(e,t){if(t||(t=Nr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function Ln(e){var t=e.read_shift(1);e.l++;var r={fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t};return r}function Mn(e,t){t||(t=Nr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Fn=2,Un=3,Bn=11,Wn=19,Hn=64,Gn=65,Vn=71,jn=4108,zn=4126,$n=80,Xn={1:{n:"CodePage",t:Fn},2:{n:"Category",t:$n},3:{n:"PresentationFormat",t:$n},4:{n:"ByteCount",t:Un},5:{n:"LineCount",t:Un},6:{n:"ParagraphCount",t:Un},7:{n:"SlideCount",t:Un},8:{n:"NoteCount",t:Un},9:{n:"HiddenCount",t:Un},10:{n:"MultimediaClipCount",t:Un},11:{n:"ScaleCrop",t:Bn},12:{n:"HeadingPairs",t:jn},13:{n:"TitlesOfParts",t:zn},14:{n:"Manager",t:$n},15:{n:"Company",t:$n},16:{n:"LinksUpToDate",t:Bn},17:{n:"CharacterCount",t:Un},19:{n:"SharedDoc",t:Bn},22:{n:"HyperlinksChanged",t:Bn},23:{n:"AppVersion",t:Un,p:"version"},24:{n:"DigSig",t:Gn},26:{n:"ContentType",t:$n},27:{n:"ContentStatus",t:$n},28:{n:"Language",t:$n},29:{n:"Version",t:$n},255:{},2147483648:{n:"Locale",t:Wn},2147483651:{n:"Behavior",t:Wn},1919054434:{}},Kn={1:{n:"CodePage",t:Fn},2:{n:"Title",t:$n},3:{n:"Subject",t:$n},4:{n:"Author",t:$n},5:{n:"Keywords",t:$n},6:{n:"Comments",t:$n},7:{n:"Template",t:$n},8:{n:"LastAuthor",t:$n},9:{n:"RevNumber",t:$n},10:{n:"EditTime",t:Hn},11:{n:"LastPrinted",t:Hn},12:{n:"CreatedDate",t:Hn},13:{n:"ModifiedDate",t:Hn},14:{n:"PageCount",t:Un},15:{n:"WordCount",t:Un},16:{n:"CharCount",t:Un},17:{n:"Thumbnail",t:Vn},18:{n:"Application",t:$n},19:{n:"DocSecurity",t:Un},255:{},2147483648:{n:"Locale",t:Wn},2147483651:{n:"Behavior",t:Wn},1919054434:{}};function Yn(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var Jn=Yn([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Zn=ut(Jn),qn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Qn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},ea={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function ta(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function ra(e,t){var r,n=et(Qn),a=[];a[a.length]=wt,a[a.length]=Ht("Types",null,{xmlns:jt.CT,"xmlns:xsd":jt.xsd,"xmlns:xsi":jt.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return Ht("Default",null,{Extension:e[0],ContentType:e[1]})})));var i=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=Ht("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:ea[n][t.bookType]||ea[n]["xlsx"]}))},s=function(r){(e[r]||[]).forEach((function(e){a[a.length]=Ht("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:ea[r][t.bookType]||ea[r]["xlsx"]})}))},o=function(t){(e[t]||[]).forEach((function(e){a[a.length]=Ht("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return i("workbooks"),s("sheets"),s("charts"),o("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),s("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var na={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function aa(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ia(e){var t=[wt,Ht("Relationships",null,{xmlns:jt.RELS})];return Je(e["!id"]).forEach((function(r){t[t.length]=Ht("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function sa(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[na.HLINK,na.XPATH,na.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function oa(e){var t=[wt];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}function la(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function fa(e,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+'"/>\n',"  </rdf:Description>\n"].join("")}function ca(e){var t=[wt];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(la(e[r][0],e[r][1])),t.push(fa("",e[r][0]));return t.push(la("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ha(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+n.version+"</meta:generator></office:meta></office:document-meta>"}var ua=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function da(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=yt(t),n[n.length]=r?Ht(e,t,r):Bt(e,t))}function pa(e,t){var r=t||{},n=[wt,Ht("cp:coreProperties",null,{"xmlns:cp":jt.CORE_PROPS,"xmlns:dc":jt.dc,"xmlns:dcterms":jt.dcterms,"xmlns:dcmitype":jt.dcmitype,"xmlns:xsi":jt.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&da("dcterms:created","string"===typeof e.CreatedDate?e.CreatedDate:Gt(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&da("dcterms:modified","string"===typeof e.ModifiedDate?e.ModifiedDate:Gt(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=ua.length;++i){var s=ua[i],o=r.Props&&null!=r.Props[s[1]]?r.Props[s[1]]:e?e[s[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&da(s[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var ma=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ga=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function va(e){var t=[],r=Ht;return e||(e={}),e.Application="SheetJS",t[t.length]=wt,t[t.length]=Ht("Properties",null,{xmlns:jt.EXT_PROPS,"xmlns:vt":jt.vt}),ma.forEach((function(n){if(void 0!==e[n[1]]){var a;switch(n[2]){case"string":a=yt(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}void 0!==a&&(t[t.length]=r(n[0],a))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+yt(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function Ta(e){var t=[wt,Ht("Properties",null,{xmlns:jt.CUST_PROPS,"xmlns:vt":jt.vt})];if(!e)return t.join("");var r=1;return Je(e).forEach((function(n){++r,t[t.length]=Ht("property",Vt(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:yt(n)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var wa={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function ba(e,t){var r=[];return Je(wa).map((function(e){for(var t=0;t<ua.length;++t)if(ua[t][1]==e)return ua[t];for(t=0;t<ma.length;++t)if(ma[t][1]==e)return ma[t];throw e})).forEach((function(n){if(null!=e[n[1]]){var a=t&&t.Props&&null!=t.Props[n[1]]?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}"number"==typeof a?a=String(a):!0===a||!1===a?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Bt(wa[n[1]]||n[1],a))}})),Ht("DocumentProperties",r.join(""),{xmlns:$t.o})}function Ea(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&Je(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var n=0;n<ua.length;++n)if(t==ua[n][1])return;for(n=0;n<ma.length;++n)if(t==ma[n][1])return;for(n=0;n<r.length;++n)if(t==r[n])return;var i=e[t],s="string";"number"==typeof i?(s="float",i=String(i)):!0===i||!1===i?(s="boolean",i=i?"1":"0"):i=String(i),a.push(Ht(_t(t),i,{"dt:dt":s}))}})),t&&Je(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var n=t[r],i="string";"number"==typeof n?(i="float",n=String(n)):!0===n||!1===n?(i="boolean",n=n?"1":"0"):n instanceof Date?(i="dateTime.tz",n=n.toISOString()):n=String(n),a.push(Ht(_t(r),n,{"dt:dt":i}))}})),"<"+n+' xmlns="'+$t.o+'">'+a.join("")+"</"+n+">"}function Sa(e){var t="string"==typeof e?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n%=Math.pow(2,32),a+=i);var s=Nr(8);return s.write_shift(4,n),s.write_shift(4,a),s}function Aa(e,t){var r=Nr(4),n=Nr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=Nr(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=Sa(t);break;case 31:case 80:n=Nr(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");while(n.l!=n.length)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return R([r,n])}var ya=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function _a(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Oa(e,t,r){var n=Nr(8),a=[],i=[],s=8,o=0,l=Nr(8),f=Nr(8);if(l.write_shift(4,2),l.write_shift(4,1200),f.write_shift(4,1),i.push(l),a.push(f),s+=8+l.length,!t){f=Nr(8),f.write_shift(4,0),a.unshift(f);var c=[Nr(4)];for(c[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];l=Nr(8+2*(h.length+1)+(h.length%2?0:2)),l.write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");while(l.l!=l.length)l.write_shift(1,0);c.push(l)}l=R(c),i.unshift(l),s+=8+l.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(ya.indexOf(e[o][0])>-1||ga.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){d=+t[e[o][0]];var p=r[d];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}l=Aa(p.t,u)}else{var g=_a(u);-1==g&&(g=31,u=String(u)),l=Aa(g,u)}i.push(l),f=Nr(8),f.write_shift(4,t?d:2+o),a.push(f),s+=8+l.length}var v=8*(i.length+1);for(o=0;o<i.length;++o)a[o].write_shift(4,v),v+=i[o].length;return n.write_shift(4,s),n.write_shift(4,i.length),R([n].concat(a).concat(i))}function xa(e,t,r,n,a,i){var s=Nr(a?68:48),o=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,$e.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var l=Oa(e,r,n);if(o.push(l),a){var f=Oa(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+l.length),o.push(f)}return R(o)}function Ca(e,t){t||(t=Nr(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Ra(e,t){return 1===e.read_shift(t)}function ka(e,t){return t||(t=Nr(2)),t.write_shift(2,+!!e),t}function Na(e){return e.read_shift(2,"u")}function Ia(e,t){return t||(t=Nr(2)),t.write_shift(2,e),t}function Da(e,t,r){return r||(r=Nr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function Pa(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),i="sbcs-cont",s=a;if(r&&r.biff>=8&&(a=1200),r&&8!=r.biff)12==r.biff&&(i="wstr");else{var o=e.read_shift(1);o&&(i="dbcs-cont")}r.biff>=2&&r.biff<=5&&(i="cpstr");var l=n?e.read_shift(n,i):"";return a=s,l}function La(e){var t=e.t||"",r=1,n=Nr(3+(r>1?2:0));n.write_shift(2,t.length),n.write_shift(1,1|(r>1?8:0)),r>1&&n.write_shift(2,r);var a=Nr(2*t.length);a.write_shift(2*t.length,t,"utf16le");var i=[n,a];return R(i)}function Ma(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return n=0===a?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont"),n}function Fa(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):Ma(e,n,r)}function Ua(e,t,r){if(r.biff>5)return Fa(e,t,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Ba(e,t,r){return r||(r=Nr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Wa(e,t){t||(t=Nr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Ha(e){var t=Nr(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(28==i)n=n.slice(1),Wa(n,t);else if(2&i){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&i&&Wa(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var l=0;while("../"==n.slice(3*l,3*l+3)||"..\\"==n.slice(3*l,3*l+3))++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,255&n.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Ga(e,t,r,n){return n||(n=Nr(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Va(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function ja(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r:r}}}function za(e,t){return t||(t=Nr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function $a(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=Nr(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function Xa(e,t){var r=!t||8==t.biff,n=Nr(r?112:54);n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));while(n.l<n.length)n.write_shift(1,r?0:32);return n}function Ka(e,t){var r=!t||t.biff>=8?2:1,n=Nr(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function Ya(e,t){var r=Nr(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=La(e[a],t);var i=R([r].concat(n));return i.parts=[r.length].concat(n.map((function(e){return e.length}))),i}function Ja(){var e=Nr(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function Za(e){var t=Nr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function qa(e,t){var r=e.name||"Arial",n=t&&5==t.biff,a=n?15+r.length:16+2*r.length,i=Nr(a);return i.write_shift(2,20*(e.sz||12)),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function Qa(e,t,r,n){var a=Nr(10);return Ga(e,t,n,a),a.write_shift(4,r),a}function ei(e,t,r,n,a){var i=!a||8==a.biff,s=Nr(+i+8+(1+i)*r.length);return Ga(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function ti(e,t,r,n){var a=r&&5==r.biff;n||(n=Nr(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return null==i.l&&(i.l=i.length),i}function ri(e,t){var r=8!=t.biff&&t.biff?2:4,n=Nr(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function ni(e,t,r,n){var a=r&&5==r.biff;n||(n=Nr(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function ai(e){var t=Nr(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function ii(e,t,r,n,a,i){var s=Nr(8);return Ga(e,t,n,s),Da(r,i,s),s}function si(e,t,r,n){var a=Nr(14);return Ga(e,t,n,a),In(r,a),a}function oi(e,t,r){if(r.biff<8)return li(e,t,r);var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);while(0!==i--)n.push(Va(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function li(e,t,r){3==e[e.l+1]&&e[e.l]++;var n=Pa(e,t,r);return 3==n.charCodeAt(0)?n.slice(1):n}function fi(e){var t=Nr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)za(e[r],t);return t}function ci(e){var t=Nr(24),r=Xr(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return R([t,Ha(e[1])])}function hi(e){var t=e[1].Tooltip,r=Nr(10+2*(t.length+1));r.write_shift(2,2048);var n=Xr(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function ui(e){return e||(e=Nr(4)),e.write_shift(2,1),e.write_shift(2,1),e}function di(e,t,r){if(!r.cellStyles)return kr(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),o=e.read_shift(n),l=e.read_shift(2);2==n&&(e.l+=2);var f={s:a,e:i,w:s,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(f.level=l>>8&7),f}function pi(e,t){var r=Nr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function mi(e){for(var t=Nr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function gi(e,t,r){var n=Nr(15);return sh(n,e,t),n.write_shift(8,r,"f"),n}function vi(e,t,r){var n=Nr(9);return sh(n,e,t),n.write_shift(2,r),n}var Ti=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=qe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=[],a=A(1);switch(r.type){case"base64":a=_(b(t));break;case"binary":a=_(t);break;case"buffer":case"array":a=t;break}Rr(a,0);var i=a.read_shift(1),s=!!(136&i),o=!1,l=!1;switch(i){case 2:break;case 3:break;case 48:o=!0,s=!0;break;case 49:o=!0,s=!0;break;case 131:break;case 139:break;case 140:l=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var f=0,c=521;2==i&&(f=a.read_shift(2)),a.l+=3,2!=i&&(f=a.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(c=a.read_shift(2));var h=a.read_shift(2),u=r.codepage||1252;2!=i&&(a.l+=16,a.read_shift(1),0!==a[a.l]&&(u=e[a[a.l]]),a.l+=1,a.l+=2),l&&(a.l+=36);var p=[],m={},g=Math.min(a.length,2==i?521:c-10-(o?264:0)),v=l?32:11;while(a.l<g&&13!=a[a.l])switch(m={},m.name=d.utils.decode(u,a.slice(a.l,a.l+v)).replace(/[\u0000\r\n].*$/g,""),a.l+=v,m.type=String.fromCharCode(a.read_shift(1)),2==i||l||(m.offset=a.read_shift(4)),m.len=a.read_shift(1),2==i&&(m.offset=a.read_shift(2)),m.dec=a.read_shift(1),m.name.length&&p.push(m),2!=i&&(a.l+=l?13:14),m.type){case"B":o&&8==m.len||!r.WTF||console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+m.type)}if(13!==a[a.l]&&(a.l=c-1),13!==a.read_shift(1))throw new Error("DBF Terminator not found "+a.l+" "+a[a.l]);a.l=c;var T=0,w=0;for(n[0]=[],w=0;w!=p.length;++w)n[0][w]=p[w].name;while(f-- >0)if(42!==a[a.l])for(++a.l,n[++T]=[],w=0,w=0;w!=p.length;++w){var E=a.slice(a.l,a.l+p[w].len);a.l+=p[w].len,Rr(E,0);var S=d.utils.decode(u,E);switch(p[w].type){case"C":S.trim().length&&(n[T][w]=S.replace(/\s+$/,""));break;case"D":8===S.length?n[T][w]=new Date(+S.slice(0,4),+S.slice(4,6)-1,+S.slice(6,8)):n[T][w]=S;break;case"F":n[T][w]=parseFloat(S.trim());break;case"+":case"I":n[T][w]=l?2147483648^E.read_shift(-4,"i"):E.read_shift(4,"i");break;case"L":switch(S.trim().toUpperCase()){case"Y":case"T":n[T][w]=!0;break;case"N":case"F":n[T][w]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+S+"|")}break;case"M":if(!s)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));n[T][w]="##MEMO##"+(l?parseInt(S.trim(),10):E.read_shift(4));break;case"N":S=S.replace(/\u0000/g,"").trim(),S&&"."!=S&&(n[T][w]=+S||0);break;case"@":n[T][w]=new Date(E.read_shift(-8,"f")-621356832e5);break;case"T":n[T][w]=new Date(864e5*(E.read_shift(4)-2440588)+E.read_shift(4));break;case"Y":n[T][w]=E.read_shift(4,"i")/1e4+E.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[T][w]=-E.read_shift(-8,"f");break;case"B":if(o&&8==p[w].len){n[T][w]=E.read_shift(8,"f");break}case"G":case"P":E.l+=p[w].len;break;case"0":if("_NullFlags"===p[w].name)break;default:throw new Error("DBF Unsupported data type "+p[w].type)}}else a.l+=h;if(2!=i&&a.l<a.length&&26!=a[a.l++])throw new Error("DBF EOF Marker missing "+(a.l-1)+" of "+a.length+" "+a[a.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=p,n}function n(e,t){var n=t||{};n.dateNF||(n.dateNF="yyyymmdd");var a=rn(r(e,n),n);return a["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,a}function a(e,t){try{return en(n(e,t),t)}catch(r){if(t&&t.WTF)throw r}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function o(e,r){var n=r||{};if(+n.codepage>=0&&c(+n.codepage),"string"==n.type)throw new Error("Cannot write DBF to JS string");var a=Ir(),o=vu(e,{header:1,raw:!0,cellDates:!0}),l=o[0],f=o.slice(1),h=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<l.length;++u)if(((h[u]||{}).DBF||{}).name)l[u]=h[u].DBF.name,++p;else if(null!=l[u]){if(++p,"number"===typeof l[u]&&(l[u]=l[u].toString(10)),"string"!==typeof l[u])throw new Error("DBF Invalid column name "+l[u]+" |"+typeof l[u]+"|");if(l.indexOf(l[u])!==u)for(d=0;d<1024;++d)if(-1==l.indexOf(l[u]+"_"+d)){l[u]+="_"+d;break}}var g=Zr(e["!ref"]),v=[],T=[],w=[];for(u=0;u<=g.e.c-g.s.c;++u){var b="",E="",S=0,A=[];for(d=0;d<f.length;++d)null!=f[d][u]&&A.push(f[d][u]);if(0!=A.length&&null!=l[u]){for(d=0;d<A.length;++d){switch(typeof A[d]){case"number":E="B";break;case"string":E="C";break;case"boolean":E="L";break;case"object":E=A[d]instanceof Date?"D":"C";break;default:E="C"}S=Math.max(S,String(A[d]).length),b=b&&b!=E?"C":E}S>250&&(S=250),E=((h[u]||{}).DBF||{}).type,"C"==E&&h[u].DBF.len>S&&(S=h[u].DBF.len),"B"==b&&"N"==E&&(b="N",w[u]=h[u].DBF.dec,S=h[u].DBF.len),T[u]="C"==b||"N"==E?S:s[b]||0,m+=T[u],v[u]=b}else v[u]="?"}var y=a.next(32);for(y.write_shift(4,318902576),y.write_shift(4,f.length),y.write_shift(2,296+32*p),y.write_shift(2,m),u=0;u<4;++u)y.write_shift(4,0);for(y.write_shift(4,0|(+t[i]||3)<<8),u=0,d=0;u<l.length;++u)if(null!=l[u]){var _=a.next(32),O=(l[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_.write_shift(1,O,"sbcs"),_.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),_.write_shift(4,d),_.write_shift(1,T[u]||s[v[u]]||0),_.write_shift(1,w[u]||0),_.write_shift(1,2),_.write_shift(4,0),_.write_shift(1,0),_.write_shift(4,0),_.write_shift(4,0),d+=T[u]||s[v[u]]||0}var x=a.next(264);for(x.write_shift(4,13),u=0;u<65;++u)x.write_shift(4,0);for(u=0;u<f.length;++u){var C=a.next(m);for(C.write_shift(1,0),d=0;d<l.length;++d)if(null!=l[d])switch(v[d]){case"L":C.write_shift(1,null==f[u][d]?63:f[u][d]?84:70);break;case"B":C.write_shift(8,f[u][d]||0,"f");break;case"N":var R="0";for("number"==typeof f[u][d]&&(R=f[u][d].toFixed(w[d]||0)),p=0;p<T[d]-R.length;++p)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":f[u][d]?(C.write_shift(4,("0000"+f[u][d].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(f[u][d].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+f[u][d].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var k=String(null!=f[u][d]?f[u][d]:"").slice(0,T[d]);for(C.write_shift(1,k,"sbcs"),p=0;p<T[d]-k.length;++p)C.write_shift(1,32);break}}return a.next(1).write_shift(1,26),a.end()}return{to_workbook:a,to_sheet:n,from_sheet:o}}(),wi=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+Je(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?m(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:m(n)};function a(e,t){switch(t.type){case"base64":return i(b(e),t);case"binary":return i(e,t);case"buffer":return i(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),t);case"array":return i(ht(e),t)}throw new Error("Unrecognized type "+t.type)}function i(e,a){var i,s=e.split(/[\n\r]+/),o=-1,l=-1,f=0,h=0,u=[],p=[],m=null,g={},v=[],T=[],w=[],b=0;for(+a.codepage>=0&&c(+a.codepage);f!==s.length;++f){b=0;var E,S=s[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),A=S.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),y=A[0];if(S.length>0)switch(y){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":"P"==A[1].charAt(0)&&p.push(S.slice(3).replace(/;;/g,";"));break;case"C":var _=!1,O=!1,x=!1,C=!1,R=-1,k=-1;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"A":break;case"X":l=parseInt(A[h].slice(1))-1,O=!0;break;case"Y":for(o=parseInt(A[h].slice(1))-1,O||(l=0),i=u.length;i<=o;++i)u[i]=[];break;case"K":E=A[h].slice(1),'"'===E.charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(pt(E))?isNaN(gt(E).getDate())||(E=ct(E)):(E=pt(E),null!==m&&De(m)&&(E=st(E))),"undefined"!==typeof d&&"string"==typeof E&&"string"!=(a||{}).type&&(a||{}).codepage&&(E=d.utils.decode(a.codepage,E)),_=!0;break;case"E":C=!0;var N=Gs(A[h].slice(1),{r:o,c:l});u[o][l]=[u[o][l],N];break;case"S":x=!0,u[o][l]=[u[o][l],"S5S"];break;case"G":break;case"R":R=parseInt(A[h].slice(1))-1;break;case"C":k=parseInt(A[h].slice(1))-1;break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+S)}if(_&&(u[o][l]&&2==u[o][l].length?u[o][l][0]=E:u[o][l]=E,m=null),x){if(C)throw new Error("SYLK shared formula cannot have own formula");var I=R>-1&&u[R][k];if(!I||!I[1])throw new Error("SYLK shared formula cannot find base");u[o][l][1]=zs(I[1],{r:o-R,c:l-k})}break;case"F":var D=0;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"X":l=parseInt(A[h].slice(1))-1,++D;break;case"Y":for(o=parseInt(A[h].slice(1))-1,i=u.length;i<=o;++i)u[i]=[];break;case"M":b=parseInt(A[h].slice(1))/20;break;case"F":break;case"G":break;case"P":m=p[parseInt(A[h].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(w=A[h].slice(1).split(" "),i=parseInt(w[0],10);i<=parseInt(w[1],10);++i)b=parseInt(w[2],10),T[i-1]=0===b?{hidden:!0}:{wch:b},Bi(T[i-1]);break;case"C":l=parseInt(A[h].slice(1))-1,T[l]||(T[l]={});break;case"R":o=parseInt(A[h].slice(1))-1,v[o]||(v[o]={}),b>0?(v[o].hpt=b,v[o].hpx=Vi(b)):0===b&&(v[o].hidden=!0);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+S)}D<1&&(m=null);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+S)}}return v.length>0&&(g["!rows"]=v),T.length>0&&(g["!cols"]=T),a&&a.sheetRows&&(u=u.slice(0,a.sheetRows)),[u,g]}function s(e,t){var r=a(e,t),n=r[0],i=r[1],s=rn(n,t);return Je(i).forEach((function(e){s[e]=i[e]})),s}function o(e,t){return en(s(e,t),t)}function l(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+js(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return a}function f(e,t){t.forEach((function(t,r){var n="F;W"+(r+1)+" "+(r+1)+" ";t.hidden?n+="0":("number"!=typeof t.width||t.wpx||(t.wpx=Mi(t.width)),"number"!=typeof t.wpx||t.wch||(t.wch=Fi(t.wpx)),"number"==typeof t.wch&&(n+=Math.round(t.wch)))," "!=n.charAt(n.length-1)&&e.push(n)}))}function h(e,t){t.forEach((function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*Gi(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))}))}function u(e,t){var r,n=["ID;PWXL;N;E"],a=[],i=Zr(e["!ref"]),s=Array.isArray(e),o="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&f(n,e["!cols"]),e["!rows"]&&h(n,e["!rows"]),n.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));for(var c=i.s.r;c<=i.e.r;++c)for(var u=i.s.c;u<=i.e.c;++u){var d=Kr({r:c,c:u});r=s?(e[c]||[])[u]:e[d],r&&(null!=r.v||r.f&&!r.F)&&a.push(l(r,e,c,u,t))}return n.join(o)+o+a.join(o)+o+"E"+o}return e["|"]=254,{to_workbook:o,to_sheet:s,from_sheet:u}}(),bi=function(){function e(e,r){switch(r.type){case"base64":return t(b(e),r);case"binary":return t(e,r);case"buffer":return t(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),r);case"array":return t(ht(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i)if("BOT"!==r[i].trim()){if(!(n<0)){var o=r[i].trim().split(","),l=o[0],f=o[1];++i;var c=r[i]||"";while(1&(c.match(/["]/g)||[]).length&&i<r.length-1)c+="\n"+r[++i];switch(c=c.trim(),+l){case-1:if("BOT"===c){s[++n]=[],a=0;continue}if("EOD"!==c)throw new Error("Unrecognized DIF special command "+c);break;case 0:"TRUE"===c?s[n][a]=!0:"FALSE"===c?s[n][a]=!1:isNaN(pt(f))?isNaN(gt(f).getDate())?s[n][a]=f:s[n][a]=ct(f):s[n][a]=pt(f),++a;break;case 1:c=c.slice(1,c.length-1),c=c.replace(/""/g,'"'),v&&c&&c.match(/^=".*"$/)&&(c=c.slice(2,-1)),s[n][a++]=""!==c?c:null;break}if("EOD"===c)break}}else s[++n]=[],a=0;return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}function r(t,r){return rn(e(t,r),r)}function n(e,t){return en(r(e,t),t)}var a=function(){var e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)};return function(r){var n,a=[],i=Zr(r["!ref"]),s=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,i.e.r-i.s.r+1,""),e(a,"TUPLES",0,i.e.c-i.s.c+1,""),e(a,"DATA",0,0,"");for(var o=i.s.r;o<=i.e.r;++o){t(a,-1,0,"BOT");for(var l=i.s.c;l<=i.e.c;++l){var f=Kr({r:o,c:l});if(n=s?(r[o]||[])[l]:r[f],n)switch(n.t){case"n":var c=v?n.w:n.v;c||null==n.v||(c=n.v),null==c?v&&n.f&&!n.F?t(a,1,0,"="+n.f):t(a,1,0,""):t(a,0,c,"V");break;case"b":t(a,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,!v||isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=Ue(n.z||z[14],rt(ct(n.v)))),v?t(a,0,n.w,"V"):t(a,1,0,n.w);break;default:t(a,1,0,"")}else t(a,1,0,"")}}t(a,-1,0,"EOD");var h="\r\n",u=a.join(h);return u}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),Ei=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function t(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(t,r){for(var n=t.split("\n"),a=-1,i=-1,s=0,o=[];s!==n.length;++s){var l=n[s].trim().split(":");if("cell"===l[0]){var f=Xr(l[1]);if(o.length<=f.r)for(a=o.length;a<=f.r;++a)o[a]||(o[a]=[]);switch(a=f.r,i=f.c,l[2]){case"t":o[a][i]=e(l[3]);break;case"v":o[a][i]=+l[3];break;case"vtf":var c=l[l.length-1];case"vtc":switch(l[3]){case"nl":o[a][i]=!!+l[4];break;default:o[a][i]=+l[4];break}"vtf"==l[2]&&(o[a][i]=[o[a][i],c])}}}return r&&r.sheetRows&&(o=o.slice(0,r.sheetRows)),o}function n(e,t){return rn(r(e,t),t)}function a(e,t){return en(n(e,t),t)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",o=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),l="--SocialCalcSpreadsheetControlSave--";function f(e){if(!e||!e["!ref"])return"";for(var r,n=[],a=[],i="",s=Yr(e["!ref"]),o=Array.isArray(e),l=s.s.r;l<=s.e.r;++l)for(var f=s.s.c;f<=s.e.c;++f)if(i=Kr({r:l,c:f}),r=o?(e[l]||[])[f]:e[i],r&&null!=r.v&&"z"!==r.t){switch(a=["cell",i,"t"],r.t){case"s":case"str":a.push(t(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=t(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=t(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var c=rt(ct(r.v));a[2]="vtc",a[3]="nd",a[4]=""+c,a[5]=r.w||Ue(r.z||z[14],c);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}function c(e){return[i,s,o,s,f(e),l].join("\n")}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),Si=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(pt(e))?isNaN(gt(e).getDate())?t[r][n]=e:t[r][n]=ct(e):t[r][n]=pt(e))}function t(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;var i=t.split(/[\r\n]/),s=i.length-1;while(s>=0&&0===i[s].length)--s;for(var o=10,l=0,f=0;f<=s;++f)l=i[f].indexOf(" "),-1==l?l=i[f].length:l++,o=Math.max(o,l);for(f=0;f<=s;++f){a[f]=[];var c=0;for(e(i[f].slice(0,o).trim(),a,f,c,n),c=1;c<=(i[f].length-o)/10+1;++c)e(i[f].slice(o+10*(c-1),o+10*c).trim(),a,f,c,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}var r={44:",",9:"\t",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(e){for(var t={},a=!1,i=0,s=0;i<e.length;++i)34==(s=e.charCodeAt(i))?a=!a:!a&&s in r&&(t[s]=(t[s]||0)+1);for(i in s=[],t)Object.prototype.hasOwnProperty.call(t,i)&&s.push([t[i],i]);if(!s.length)for(i in t=n,t)Object.prototype.hasOwnProperty.call(t,i)&&s.push([t[i],i]);return s.sort((function(e,t){return e[0]-t[0]||n[e[1]]-n[t[1]]})),r[s.pop()[1]]||44}function i(e,t){var r=t||{},n="";null!=g&&null==r.dense&&(r.dense=g);var i=r.dense?[]:{},s={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=r&&r.FS?r.FS:a(e.slice(0,1024));var o=0,l=0,f=0,c=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var v=null!=r.dateNF?Ve(r.dateNF):null;function T(){var t=e.slice(c,h),n={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)n.t="z";else if(r.raw)n.t="s",n.v=t;else if(0===t.trim().length)n.t="s",n.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(n.t="s",n.v=t.slice(2,-1).replace(/""/g,'"')):$s(t)?(n.t="n",n.f=t.slice(1)):(n.t="s",n.v=t);else if("TRUE"==t)n.t="b",n.v=!0;else if("FALSE"==t)n.t="b",n.v=!1;else if(isNaN(f=pt(t)))if(!isNaN(gt(t).getDate())||v&&t.match(v)){n.z=r.dateNF||z[14];var a=0;v&&t.match(v)&&(t=je(t,r.dateNF,t.match(v)||[]),a=1),r.cellDates?(n.t="d",n.v=ct(t,a)):(n.t="n",n.v=rt(ct(t,a))),!1!==r.cellText&&(n.w=Ue(n.z,n.v instanceof Date?rt(n.v):n.v)),r.cellNF||delete n.z}else n.t="s",n.v=t;else n.t="n",!1!==r.cellText&&(n.w=t),n.v=f;if("z"==n.t||(r.dense?(i[o]||(i[o]=[]),i[o][l]=n):i[Kr({c:l,r:o})]=n),c=h+1,m=e.charCodeAt(c),s.e.c<l&&(s.e.c=l),s.e.r<o&&(s.e.r=o),p==u)++l;else if(l=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&T())break e;break;default:break}return h-c>0&&T(),i["!ref"]=Jr(s),i}function s(e,r){return r&&r.PRN?r.FS||"sep="==e.slice(0,4)||e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0?i(e,r):rn(t(e,r),r):i(e,r)}function o(e,t){var r="",n="string"==t.type?[0,0,0,0]:ou(e,t);switch(t.type){case"base64":r=b(e);break;case"binary":r=e;break;case"buffer":r=65001==t.codepage?e.toString("utf8"):t.codepage&&"undefined"!==typeof d?d.utils.decode(t.codepage,e):E&&Buffer.isBuffer(e)?e.toString("binary"):x(e);break;case"array":r=ht(e);break;case"string":r=e;break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=Lt(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=Lt(r):"binary"==t.type&&"undefined"!==typeof d&&t.codepage&&(r=d.utils.decode(t.codepage,d.utils.encode(28591,r))),"socialcalc:version:"==r.slice(0,19)?Ei.to_sheet("string"==t.type?r:Lt(r),t):s(r,t)}function l(e,t){return en(o(e,t),t)}function f(e){for(var t,r=[],n=Zr(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){for(var s=[],o=n.s.c;o<=n.e.c;++o){var l=Kr({r:i,c:o});if(t=a?(e[i]||[])[o]:e[l],t&&null!=t.v){var f=(t.w||(Qr(t),t.w)||"").slice(0,10);while(f.length<10)f+=" ";s.push(f+(0===o?" ":""))}else s.push("          ")}r.push(s.join(""))}return r.join("\n")}return{to_workbook:l,to_sheet:o,from_sheet:f}}();var Ai=function(){function e(e,t,r){if(e){Rr(e,e.l||0);var n=r.Enum||W;while(e.l<e.length){var a=e.read_shift(2),i=n[a]||n[65535],s=e.read_shift(2),o=e.l+s,l=i.f&&i.f(e,s,r);if(e.l=o,t(l,i,a))return}}}function t(e,t){switch(t.type){case"base64":return r(_(b(e)),t);case"binary":return r(_(e),t);case"buffer":case"array":return r(e,t)}throw"Unsupported type "+t.type}function r(t,r){if(!t)return t;var n=r||{};null!=g&&null==n.dense&&(n.dense=g);var a=n.dense?[]:{},i="Sheet1",s="",o=0,l={},f=[],c=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=W,e(t,(function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:h=e;break;case 204:e&&(s=e);break;case 222:s=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||z[14],n.cellDates&&(e[1].t="d",e[1].v=st(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=Jr(h),l[i]=a,f.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i=s||"Sheet"+(o+1),s="");var c=n.dense?(a[e[0].r]||[])[e[0].c]:a[Kr(e[0])];if(c){c.t=e[1].t,c.v=e[1].v,null!=e[1].z&&(c.z=e[1].z),null!=e[1].f&&(c.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[Kr(e[0])]=e[1];break;default:}}),n);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);n.Enum=H,14==t[2]&&(n.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=Jr(h),l[i]=a,f.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i="Sheet"+(o+1)),u>0&&e[0].r>=u)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[Kr(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(c[e[14e3][0]]=e[14e3][1]);break;case 1537:c[e[0]]=e[1],e[0]==o&&(i=e[1]);break;default:break}}),n)}if(a["!ref"]=Jr(h),l[s||i]=a,f.push(s||i),!c.length)return{SheetNames:f,Sheets:l};for(var d={},p=[],m=0;m<c.length;++m)l[f[m]]?(p.push(c[m]||f[m]),d[c[m]]=l[c[m]]||l[f[m]]):(p.push(c[m]),d[c[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function n(e,t){var r=t||{};if(+r.codepage>=0&&c(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var n=Ir(),a=Zr(e["!ref"]),s=Array.isArray(e),o=[];ah(n,0,i(1030)),ah(n,6,l(a));for(var f=Math.min(a.e.r,8191),h=a.s.r;h<=f;++h)for(var d=Br(h),m=a.s.c;m<=a.e.c;++m){h===a.s.r&&(o[m]=Vr(m));var g=o[m]+d,T=s?(e[h]||[])[m]:e[g];if(T&&"z"!=T.t)if("n"==T.t)(0|T.v)==T.v&&T.v>=-32768&&T.v<=32767?ah(n,13,p(h,m,T.v)):ah(n,14,v(h,m,T.v));else{var w=Qr(T);ah(n,15,u(h,m,w.slice(0,239)))}}return ah(n,1),n.end()}function a(e,t){var r=t||{};if(+r.codepage>=0&&c(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var n=Ir();ah(n,0,s(e));for(var a=0,i=0;a<e.SheetNames.length;++a)(e.Sheets[e.SheetNames[a]]||{})["!ref"]&&ah(n,27,B(e.SheetNames[a],i++));var o=0;for(a=0;a<e.SheetNames.length;++a){var l=e.Sheets[e.SheetNames[a]];if(l&&l["!ref"]){for(var f=Zr(l["!ref"]),h=Array.isArray(l),u=[],d=Math.min(f.e.r,8191),p=f.s.r;p<=d;++p)for(var m=Br(p),g=f.s.c;g<=f.e.c;++g){p===f.s.r&&(u[g]=Vr(g));var v=u[g]+m,T=h?(l[p]||[])[g]:l[v];if(T&&"z"!=T.t)if("n"==T.t)ah(n,23,k(p,g,o,T.v));else{var w=Qr(T);ah(n,22,x(p,g,o,w.slice(0,239)))}}++o}}return ah(n,1),n.end()}function i(e){var t=Nr(2);return t.write_shift(2,e),t}function s(e){var t=Nr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,i=0;i<e.SheetNames.length;++i){var s=e.SheetNames[i],o=e.Sheets[s];if(o&&o["!ref"]){++a;var l=Yr(o["!ref"]);r<l.e.r&&(r=l.e.r),n<l.e.c&&(n=l.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}function o(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2),n):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0),n)}function l(e){var t=Nr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}function f(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function h(e,t,r){var n=e.l+t,a=f(e,t,r);if(a[1].t="s",20768==r.vers){e.l++;var i=e.read_shift(1);return a[1].v=e.read_shift(i,"utf8"),a}return r.qpro&&e.l++,a[1].v=e.read_shift(n-e.l,"cstr"),a}function u(e,t,r){var n=Nr(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var i=r.charCodeAt(a);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function d(e,t,r){var n=f(e,t,r);return n[1].v=e.read_shift(2,"i"),n}function p(e,t,r){var n=Nr(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}function m(e,t,r){var n=f(e,t,r);return n[1].v=e.read_shift(8,"f"),n}function v(e,t,r){var n=Nr(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}function T(e,t,r){var n=e.l+t,a=f(e,t,r);if(a[1].v=e.read_shift(8,"f"),r.qpro)e.l=n;else{var i=e.read_shift(2);A(e.slice(e.l,e.l+i),a),e.l+=i}return a}function w(e,t,r){var n=32768&t;return t&=-32769,t=(n?e:0)+(t>=8192?t-16384:t),(n?"":"$")+(r?Vr(t):Br(t))}var E={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},S=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function A(e,t){Rr(e,0);var r=[],n=0,a="",i="",s="",o="";while(e.l<e.length){var l=e[e.l++];switch(l){case 0:r.push(e.read_shift(8,"f"));break;case 1:i=w(t[0].c,e.read_shift(2),!0),a=w(t[0].r,e.read_shift(2),!1),r.push(i+a);break;case 2:var f=w(t[0].c,e.read_shift(2),!0),c=w(t[0].r,e.read_shift(2),!1);i=w(t[0].c,e.read_shift(2),!0),a=w(t[0].r,e.read_shift(2),!1),r.push(f+c+":"+i+a);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:var h="";while(l=e[e.l++])h+=String.fromCharCode(l);r.push('"'+h.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:o=r.pop(),s=r.pop(),r.push(["AND","OR"][l-20]+"("+s+","+o+")");break;default:if(l<32&&S[l])o=r.pop(),s=r.pop(),r.push(s+S[l]+o);else{if(!E[l])return l<=7?console.error("WK1 invalid opcode "+l.toString(16)):l<=24?console.error("WK1 unsupported op "+l.toString(16)):l<=30?console.error("WK1 invalid opcode "+l.toString(16)):l<=115?console.error("WK1 unsupported function opcode "+l.toString(16)):console.error("WK1 unrecognized opcode "+l.toString(16));if(n=E[l][1],69==n&&(n=e[e.l++]),n>r.length)return void console.error("WK1 bad formula parse 0x"+l.toString(16)+":|"+r.join("|")+"|");var u=r.slice(-n);r.length-=n,r.push(E[l][0]+"("+u.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}function y(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function O(e,t){var r=y(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}function x(e,t,r,n){var a=Nr(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var i=0;i<n.length;++i){var s=n.charCodeAt(i);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function C(e,t){var r=y(e,t);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=5e3*(n>>3);break;case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64;break}return r[1].v=n,r}function R(e,t){var r=y(e,t),n=e.read_shift(4),a=e.read_shift(4),i=e.read_shift(2);if(65535==i)return 0===n&&3221225472===a?(r[1].t="e",r[1].v=15):0===n&&3489660928===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var s=32768&i;return i=(32767&i)-16446,r[1].v=(1-2*s)*(a*Math.pow(2,i+32)+n*Math.pow(2,i)),r}function k(e,t,r,n){var a=Nr(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var i=0,s=0,o=0,l=0;return n<0&&(i=1,n=-n),s=0|Math.log2(n),n/=Math.pow(2,s-31),l=n>>>0,0==(2147483648&l)&&(n/=2,++s,l=n>>>0),n-=l,l|=2147483648,l>>>=0,n*=Math.pow(2,32),o=n>>>0,a.write_shift(4,o),a.write_shift(4,l),s+=16383+(i?32768:0),a.write_shift(2,s),a}function N(e,t){var r=R(e,14);return e.l+=t-14,r}function I(e,t){var r=y(e,t),n=e.read_shift(4);return r[1].v=n>>6,r}function D(e,t){var r=y(e,t),n=e.read_shift(8,"f");return r[1].v=n,r}function P(e,t){var r=D(e,14);return e.l+=t-10,r}function L(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function M(e,t){var r=e[e.l++];r>t-1&&(r=t-1);var n="";while(n.length<r)n+=String.fromCharCode(e[e.l++]);return n}function F(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);e.l+=17,e.l+=1,e.l+=2;var a=e.read_shift(t-21,"cstr");return[n,a]}}function U(e,t){var r={},n=e.l+t;while(e.l<n){var a=e.read_shift(2);if(14e3==a){r[a]=[0,""],r[a][0]=e.read_shift(2);while(e[e.l])r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}function B(e,t){var r=Nr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}var W={0:{n:"BOF",f:Na},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:o},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:m},15:{n:"LABEL",f:h},16:{n:"FORMULA",f:T},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:h},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:L},222:{n:"SHEETNAMELP",f:M},65535:{n:""}},H={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:O},23:{n:"NUMBER17",f:R},24:{n:"NUMBER18",f:C},25:{n:"FORMULA19",f:N},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:U},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:I},38:{n:"??"},39:{n:"NUMBER27",f:D},40:{n:"FORMULA28",f:P},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:L},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:F},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}();var yi=/^\s|\s$|[\t\n\r]/;function _i(e,t){if(!t.bookSST)return"";var r=[wt];r[r.length]=Ht("sst",null,{xmlns:zt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(yi)&&(i+=' xml:space="preserve"'),i+=">"+yt(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Oi(e){return[e.read_shift(4),e.read_shift(4)]}function xi(e,t){return t||(t=Nr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Ci=hn;function Ri(e){var t=Ir();Dr(t,159,xi(e));for(var r=0;r<e.length;++r)Dr(t,19,Ci(e[r]));return Dr(t,160),t.end()}function ki(e){if("undefined"!==typeof d)return d.utils.encode(i,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function Ni(e){var t,r,n,a,i,s,o=0,l=ki(e),f=l.length+1;for(t=A(f),t[0]=l.length,r=1;r!=f;++r)t[r]=l[r-1];for(r=f-1;r>=0;--r)n=t[r],a=0===(16384&o)?0:1,i=o<<1&32767,s=a|i,o=s^n;return 52811^o}var Ii=function(){function e(e,r){switch(r.type){case"base64":return t(b(e),r);case"binary":return t(e,r);case"buffer":return t(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),r);case"array":return t(ht(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=t||{},n=r.dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var i={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,t){Array.isArray(n)&&(n[t]=[]);var r,a=/\\\w+\b/g,s=0,o=-1;while(r=a.exec(e)){switch(r[0]){case"\\cell":var l=e.slice(s,a.lastIndex-r[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var f={v:l,t:"s"};Array.isArray(n)?n[t][o]=f:n[Kr({r:t,c:o})]=f}break}s=a.lastIndex}o>i.e.c&&(i.e.c=o)})),n["!ref"]=Jr(i),n}function r(t,r){return en(e(t,r),r)}function n(e){for(var t,r=["{\\rtf1\\ansi"],n=Zr(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){r.push("\\trowd\\trautofit1");for(var s=n.s.c;s<=n.e.c;++s)r.push("\\cellx"+(s+1));for(r.push("\\pard\\intbl"),s=n.s.c;s<=n.e.c;++s){var o=Kr({r:i,c:s});t=a?(e[i]||[])[s]:e[o],t&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(Qr(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Di(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Pi=6,Li=Pi;function Mi(e){return Math.floor((e+Math.round(128/Li)/256)*Li)}function Fi(e){return Math.floor((e-5)/Li*100+.5)/100}function Ui(e){return Math.round((e*Li+5)/Li*256)/256}function Bi(e){e.width?(e.wpx=Mi(e.width),e.wch=Fi(e.wpx),e.MDW=Li):e.wpx?(e.wch=Fi(e.wpx),e.width=Ui(e.wch),e.MDW=Li):"number"==typeof e.wch&&(e.width=Ui(e.wch),e.wpx=Mi(e.width),e.MDW=Li),e.customWidth&&delete e.customWidth}var Wi=96,Hi=Wi;function Gi(e){return 96*e/Hi}function Vi(e){return e*Hi/96}function ji(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=e[n]&&(t[t.length]=Ht("numFmt",null,{numFmtId:n,formatCode:yt(e[n])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=Ht("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function zi(e){var t=[];return t[t.length]=Ht("cellXfs",null),e.forEach((function(e){t[t.length]=Ht("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=Ht("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function $i(e,t){var r,n=[wt,Ht("styleSheet",null,{xmlns:zt[0],"xmlns:vt":jt.vt})];return e.SSF&&null!=(r=ji(e.SSF))&&(n[n.length]=r),n[n.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',n[n.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',n[n.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',n[n.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=zi(t.cellXfs))&&(n[n.length]=r),n[n.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',n[n.length]='<dxfs count="0"/>',n[n.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',n.length>2&&(n[n.length]="</styleSheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Xi(e,t){var r=e.read_shift(2),n=sn(e,t-2);return[r,n]}function Ki(e,t,r){r||(r=Nr(6+4*t.length)),r.write_shift(2,e),on(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}function Yi(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Ln(e,2,r);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(700===i&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);0!=s&&(n.underline=s);var o=e.read_shift(1);o>0&&(n.family=o);var l=e.read_shift(1);switch(l>0&&(n.charset=l),e.l++,n.color=Dn(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=sn(e,t-21),n}function Ji(e,t){t||(t=Nr(153)),t.write_shift(2,20*e.sz),Mn(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Pn(e.color,t);var n=0;return"major"==e.scheme&&(n=1),"minor"==e.scheme&&(n=2),t.write_shift(1,n),on(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Zi,qi=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Qi=kr;function es(e,t){t||(t=Nr(84)),Zi||(Zi=qe(qi));var r=Zi[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(Pn({auto:1},t),Pn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ts(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function rs(e,t,r){r||(r=Nr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function ns(e,t){return t||(t=Nr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var as=kr;function is(e,t){return t||(t=Nr(51)),t.write_shift(1,0),ns(null,t),ns(null,t),ns(null,t),ns(null,t),ns(null,t),t.length>t.l?t.slice(0,t.l):t}function ss(e,t){return t||(t=Nr(52)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),En(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function os(e,t,r){var n=Nr(2052);return n.write_shift(4,e),En(t,n),En(r,n),n.length>n.l?n.slice(0,n.l):n}function ls(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r})),0!=r&&(Dr(e,615,an(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&Dr(e,44,Ki(n,t[n]))})),Dr(e,616))}}function fs(e){var t=1;0!=t&&(Dr(e,611,an(t)),Dr(e,43,Ji({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Dr(e,612))}function cs(e){var t=2;0!=t&&(Dr(e,603,an(t)),Dr(e,45,es({patternType:"none"})),Dr(e,45,es({patternType:"gray125"})),Dr(e,604))}function hs(e){var t=1;0!=t&&(Dr(e,613,an(t)),Dr(e,46,is({})),Dr(e,614))}function us(e){var t=1;Dr(e,626,an(t)),Dr(e,47,rs({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Dr(e,627)}function ds(e,t){Dr(e,617,an(t.length)),t.forEach((function(t){Dr(e,47,rs(t,0))})),Dr(e,618)}function ps(e){var t=1;Dr(e,619,an(t)),Dr(e,48,ss({xfId:0,builtinId:0,name:"Normal"})),Dr(e,620)}function ms(e){var t=0;Dr(e,505,an(t)),Dr(e,506)}function gs(e){var t=0;Dr(e,508,os(t,"TableStyleMedium9","PivotStyleMedium4")),Dr(e,509)}function vs(){}function Ts(e,t){var r=Ir();return Dr(r,278),ls(r,e.SSF),fs(r,e),cs(r,e),hs(r,e),us(r,e),ds(r,t.cellXfs),ps(r,e),ms(r,e),gs(r,e),vs(r,e),Dr(r,279),r.end()}function ws(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[wt];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function bs(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:sn(e,t-8)}}function Es(e){var t=Nr(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),on(e.name,t),t.slice(0,t.l)}function Ss(e){var t=[],r=e.read_shift(4);while(r-- >0)t.push([e.read_shift(4),e.read_shift(4)]);return t}function As(e){var t=Nr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function ys(e,t){var r=Nr(8+2*t.length);return r.write_shift(4,e),on(t,r),r.slice(0,r.l)}function _s(e){return e.l+=4,0!=e.read_shift(4)}function Os(e,t){var r=Nr(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function xs(){var e=Ir();return Dr(e,332),Dr(e,334,an(1)),Dr(e,335,Es({name:"XLDAPR",version:12e4,flags:3496657072})),Dr(e,336),Dr(e,339,ys(1,"XLDAPR")),Dr(e,52),Dr(e,35,an(514)),Dr(e,4096,an(0)),Dr(e,4097,Ia(1)),Dr(e,36),Dr(e,53),Dr(e,340),Dr(e,337,Os(1,!0)),Dr(e,51,As([[1,0]])),Dr(e,338),Dr(e,333),e.end()}function Cs(){var e=[wt];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}function Rs(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Kr(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}var ks=1024;function Ns(e,t){var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Ht("xml",null,{"xmlns:v":$t.v,"xmlns:o":$t.o,"xmlns:x":$t.x,"xmlns:mv":$t.mv}).replace(/\/>/,">"),Ht("o:shapelayout",Ht("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Ht("v:shapetype",[Ht("v:stroke",null,{joinstyle:"miter"}),Ht("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];while(ks<1e3*e)ks+=1e3;return t.forEach((function(e){var t=Xr(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?Ht("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=Ht("v:fill",n,r),s={on:"t",obscured:"t"};++ks,a=a.concat(["<v:shape"+Wt({id:"_x0000_s"+ks,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,Ht("v:shadow",null,s),Ht("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Bt("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),Bt("x:AutoFill","False"),Bt("x:Row",String(t.r)),Bt("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),a.push("</xml>"),a.join("")}function Is(e){var t=[wt,Ht("comments",null,{xmlns:zt[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var n=yt(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(n=r.indexOf(yt(e.a))),a.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(Bt("t",yt(a[0]||"")));else{for(var i="Comment:\n    "+a[0]+"\n",s=1;s<a.length;++s)i+="Reply:\n    "+a[s]+"\n";t.push(Bt("t",yt(i)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Ds(e,t,r){var n=[wt,Ht("ThreadedComments",null,{xmlns:jt.TCMNT}).replace(/[\/]>/,">")];return e.forEach((function(e){var a="";(e[1]||[]).forEach((function(i,s){if(i.T){i.a&&-1==t.indexOf(i.a)&&t.push(i.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==s?a=o.id:o.parentId=a,i.ID=o.id,i.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),n.push(Ht("threadedComment",Bt("text",i.t||""),o))}else delete i.ID}))})),n.push("</ThreadedComments>"),n.join("")}function Ps(e){var t=[wt,Ht("personList",null,{xmlns:jt.TCMNT,"xmlns:x":zt[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(Ht("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}function Ls(e){var t={};t.iauthor=e.read_shift(4);var r=Rn(e,16);return t.rfx=r.s,t.ref=Kr(r.s),e.l+=16,t}function Ms(e,t){return null==t&&(t=Nr(36)),t.write_shift(4,e[1].iauthor),kn(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Fs=sn;function Us(e){return on(e.slice(0,54))}function Bs(e){var t=Ir(),r=[];return Dr(t,628),Dr(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),Dr(t,632,Us(e.a)))}))})),Dr(t,631),Dr(t,633),e.forEach((function(e){e[1].forEach((function(n){n.iauthor=r.indexOf(n.a);var a={s:Xr(e[0]),e:Xr(e[0])};Dr(t,635,Ms([a,n])),n.t&&n.t.length>0&&Dr(t,637,dn(n)),Dr(t,636),delete n.iauthor}))})),Dr(t,634),Dr(t,629),t.end()}function Ws(e,t){t.FullPaths.forEach((function(r,n){if(0!=n){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&$e.utils.cfb_add(e,a,t.FileIndex[n].content)}}))}var Hs=["xlsb","xlsm","xlam","biff8","xla"];var Gs=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var i=!1,s=!1;0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1)),0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,l=a.length>0?0|parseInt(a,10):0;return i?l+=t.c:--l,s?o+=t.r:--o,r+(i?"":"$")+Vr(l)+(s?"":"$")+Br(o)}return function(n,a){return t=a,n.replace(e,r)}}(),Vs=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,js=function(){return function(e,t){return e.replace(Vs,(function(e,r,n,a,i,s){var o=Gr(a)-(n?0:t.c),l=Ur(s)-(i?0:t.r),f=0==l?"":i?l+1:"["+l+"]",c=0==o?"":n?o+1:"["+o+"]";return r+"R"+f+"C"+c}))}}();function zs(e,t){return e.replace(Vs,(function(e,r,n,a,i,s){return r+("$"==n?n+a:Vr(Gr(a)+t.c))+("$"==i?i+s:Br(Ur(s)+t.r))}))}function $s(e){return 1!=e.length}function Xs(e){e.l+=1}function Ks(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Ys(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Js(e,t,r);12==r.biff&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=Ks(e,2),o=Ks(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:o[0],cRel:o[1],rRel:o[2]}}}function Js(e){var t=Ks(e,2),r=Ks(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Zs(e,t,r){if(r.biff<8)return Js(e,t,r);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),i=Ks(e,2),s=Ks(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function qs(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Qs(e,t,r);var n=e.read_shift(r&&12==r.biff?4:2),a=Ks(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Qs(e){var t=Ks(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function eo(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function to(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return ro(e,t,r);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(16384&i)>>14,o=(32768&i)>>15;if(i&=16383,1==o)while(a>524287)a-=1048576;if(1==s)while(i>8191)i-=16384;return{r:a,c:i,cRel:s,rRel:o}}function ro(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,a=(16384&t)>>14;return t&=16383,1==n&&t>=8192&&(t-=16384),1==a&&r>=128&&(r-=256),{r:t,c:r,cRel:a,rRel:n}}function no(e,t,r){var n=(96&e[e.l++])>>5,a=Ys(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function ao(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Ys(e,i,r);return[n,a,s]}function io(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function so(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function oo(e,t,r){var n=(96&e[e.l++])>>5,a=Zs(e,t-1,r);return[n,a]}function lo(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}function fo(e){var t=1&e[e.l+1],r=1;return e.l+=4,[t,r]}function co(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&2==r.biff?1:2));return a}function ho(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}function uo(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}function po(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}function mo(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}function go(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function vo(e){return e.read_shift(2),go(e,2)}function To(e){return e.read_shift(2),go(e,2)}function wo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=qs(e,0,r);return[n,a]}function bo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=to(e,0,r);return[n,a]}function Eo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);r&&5==r.biff&&(e.l+=12);var i=qs(e,0,r);return[n,a,i]}function So(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Il[a],Nl[a],n]}function Ao(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:yo(e);return[a,(0===i[0]?Nl:kl)[i[1]]]}function yo(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}function _o(e,t,r){e.l+=r&&2==r.biff?3:4}function Oo(e,t,r){if(e.l++,r&&12==r.biff)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function xo(e){return e.l++,qn[e.read_shift(1)]}function Co(e){return e.l++,e.read_shift(2)}function Ro(e){return e.l++,0!==e.read_shift(1)}function ko(e){return e.l++,Nn(e,8)}function No(e,t,r){return e.l++,Pa(e,t-1,r)}function Io(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Ra(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=qn[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Nn(e,8);break;case 2:r[1]=Ua(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Do(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],i=0;i!=n;++i)a.push((12==r.biff?Rn:ja)(e,8));return a}function Po(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var o=0;o!=a;++o)s[i][o]=Io(e,r.biff);return s}function Lo(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function Mo(e,t,r){if(5==r.biff)return Fo(e,t,r);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function Fo(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function Uo(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&2==r.biff?3:4;var a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function Bo(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function Wo(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}function Ho(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var Go=kr,Vo=kr,jo=kr;function zo(e,t,r){return e.l+=2,[eo(e,4,r)]}function $o(e){return e.l+=6,[]}var Xo=zo,Ko=$o,Yo=$o,Jo=zo;function Zo(e){return e.l+=2,[Na(e),1&e.read_shift(2)]}var qo=zo,Qo=Zo,el=$o,tl=zo,rl=zo,nl=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function al(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=nl[r>>2&31];return{ixti:t,coltype:3&r,rt:s,idx:n,c:a,C:i}}function il(e){return e.l+=2,[e.read_shift(4)]}function sl(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}function ol(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}function ll(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function fl(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function cl(e){return e.l+=4,[0,0]}var hl={1:{n:"PtgExp",f:Oo},2:{n:"PtgTbl",f:jo},3:{n:"PtgAdd",f:Xs},4:{n:"PtgSub",f:Xs},5:{n:"PtgMul",f:Xs},6:{n:"PtgDiv",f:Xs},7:{n:"PtgPower",f:Xs},8:{n:"PtgConcat",f:Xs},9:{n:"PtgLt",f:Xs},10:{n:"PtgLe",f:Xs},11:{n:"PtgEq",f:Xs},12:{n:"PtgGe",f:Xs},13:{n:"PtgGt",f:Xs},14:{n:"PtgNe",f:Xs},15:{n:"PtgIsect",f:Xs},16:{n:"PtgUnion",f:Xs},17:{n:"PtgRange",f:Xs},18:{n:"PtgUplus",f:Xs},19:{n:"PtgUminus",f:Xs},20:{n:"PtgPercent",f:Xs},21:{n:"PtgParen",f:Xs},22:{n:"PtgMissArg",f:Xs},23:{n:"PtgStr",f:No},26:{n:"PtgSheet",f:sl},27:{n:"PtgEndSheet",f:ol},28:{n:"PtgErr",f:xo},29:{n:"PtgBool",f:Ro},30:{n:"PtgInt",f:Co},31:{n:"PtgNum",f:ko},32:{n:"PtgArray",f:lo},33:{n:"PtgFunc",f:So},34:{n:"PtgFuncVar",f:Ao},35:{n:"PtgName",f:Lo},36:{n:"PtgRef",f:wo},37:{n:"PtgArea",f:no},38:{n:"PtgMemArea",f:Uo},39:{n:"PtgMemErr",f:Go},40:{n:"PtgMemNoMem",f:Vo},41:{n:"PtgMemFunc",f:Bo},42:{n:"PtgRefErr",f:Wo},43:{n:"PtgAreaErr",f:io},44:{n:"PtgRefN",f:bo},45:{n:"PtgAreaN",f:oo},46:{n:"PtgMemAreaN",f:ll},47:{n:"PtgMemNoMemN",f:fl},57:{n:"PtgNameX",f:Mo},58:{n:"PtgRef3d",f:Eo},59:{n:"PtgArea3d",f:ao},60:{n:"PtgRefErr3d",f:Ho},61:{n:"PtgAreaErr3d",f:so},255:{}},ul={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},dl={1:{n:"PtgElfLel",f:Zo},2:{n:"PtgElfRw",f:tl},3:{n:"PtgElfCol",f:Xo},6:{n:"PtgElfRwV",f:rl},7:{n:"PtgElfColV",f:Jo},10:{n:"PtgElfRadical",f:qo},11:{n:"PtgElfRadicalS",f:el},13:{n:"PtgElfColS",f:Ko},15:{n:"PtgElfColSV",f:Yo},16:{n:"PtgElfRadicalLel",f:Qo},25:{n:"PtgList",f:al},29:{n:"PtgSxName",f:il},255:{}},pl={0:{n:"PtgAttrNoop",f:cl},1:{n:"PtgAttrSemi",f:mo},2:{n:"PtgAttrIf",f:uo},4:{n:"PtgAttrChoose",f:co},8:{n:"PtgAttrGoto",f:ho},16:{n:"PtgAttrSum",f:_o},32:{n:"PtgAttrBaxcel",f:fo},33:{n:"PtgAttrBaxcel",f:fo},64:{n:"PtgAttrSpace",f:vo},65:{n:"PtgAttrSpaceSemi",f:To},128:{n:"PtgAttrIfError",f:po},255:{}};function ml(e,t,r,n){if(n.biff<8)return kr(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Po(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Do(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&12==n.biff&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0];default:break}return t=a-e.l,0!==t&&i.push(kr(e,t)),i}function gl(e,t,r){var n,a,i=e.l+t,s=[];while(i!=e.l)t=i-e.l,a=e[e.l],n=hl[a]||hl[ul[a]],24!==a&&25!==a||(n=(24===a?dl:pl)[e[e.l+1]]),n&&n.f?s.push([n.n,n.f(e,t,r)]):kr(e,t);return s}function vl(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var Tl={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function wl(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function bl(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[n[0]][0][3]?(a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function El(e,t,r){var n=bl(e,t,r);return"#REF"==n?n:wl(n,r)}function Sl(e,t,r,n,a){var i,s,o,l,f=a&&a.biff||8,c={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(i=h.pop(),s=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=dt(" ",e[0][m][1][1]);break;case 1:g=dt("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}s+=g,m=-1}h.push(s+Tl[w[0]]+i);break;case"PtgIsect":i=h.pop(),s=h.pop(),h.push(s+" "+i);break;case"PtgUnion":i=h.pop(),s=h.pop(),h.push(s+","+i);break;case"PtgRange":i=h.pop(),s=h.pop(),h.push(s+":"+i);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":o=Pr(w[1][1],c,a),h.push(Mr(o,f));break;case"PtgRefN":o=r?Pr(w[1][1],r,a):w[1][1],h.push(Mr(o,f));break;case"PtgRef3d":u=w[1][1],o=Pr(w[1][2],c,a),p=El(n,u,a);h.push(p+"!"+Mr(o,f));break;case"PtgFunc":case"PtgFuncVar":var b=w[1][0],E=w[1][1];b||(b=0),b&=127;var S=0==b?[]:h.slice(-b);h.length-=b,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgErr":h.push(w[1]);break;case"PtgAreaN":l=Lr(w[1][1],r?{s:r}:c,a),h.push(Fr(l,a));break;case"PtgArea":l=Lr(w[1][1],c,a),h.push(Fr(l,a));break;case"PtgArea3d":u=w[1][1],l=w[1][2],p=El(n,u,a),h.push(p+"!"+Fr(l,a));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=w[1][2];var A=(n.names||[])[d-1]||(n[0]||[])[d],y=A?A.Name:"SH33TJSNAME"+String(d);y&&"_xlfn."==y.slice(0,6)&&!a.xlfn&&(y=y.slice(6)),h.push(y);break;case"PtgNameX":var _,O=w[1][1];if(d=w[1][2],!(a.biff<=5)){var x="";if(14849==((n[O]||[])[0]||[])[0]||(1025==((n[O]||[])[0]||[])[0]?n[O][d]&&n[O][d].itab>0&&(x=n.SheetNames[n[O][d].itab-1]+"!"):x=n.SheetNames[d-1]+"!"),n[O]&&n[O][d])x+=n[O][d].Name;else if(n[0]&&n[0][d])x+=n[0][d].Name;else{var C=(bl(n,O,a)||"").split(";;");C[d-1]?x=C[d-1]:x+="SH33TJSERRX"}h.push(x);break}O<0&&(O=-O),n[O]&&(_=n[O][d]),_||(_={Name:"SH33TJSERRY"}),h.push(_.Name);break;case"PtgParen":var R="(",k=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:R=dt(" ",e[0][m][1][1])+R;break;case 3:R=dt("\r",e[0][m][1][1])+R;break;case 4:k=dt(" ",e[0][m][1][1])+k;break;case 5:k=dt("\r",e[0][m][1][1])+k;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(R+h.pop()+k);break;case"PtgRefErr":h.push("#REF!");break;case"PtgRefErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var N={c:r.c,r:r.r};if(n.sharedf[Kr(o)]){var I=n.sharedf[Kr(o)];h.push(Sl(I,c,N,n,a))}else{var D=!1;for(i=0;i!=n.arrayf.length;++i)if(s=n.arrayf[i],!(o.c<s[0].s.c||o.c>s[0].e.c)&&!(o.r<s[0].s.r||o.r>s[0].e.r)){h.push(Sl(s[1],c,N,n,a)),D=!0;break}D||h.push(w[1])}break;case"PtgArray":h.push("{"+vl(w[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":h.push("");break;case"PtgAreaErr":h.push("#REF!");break;case"PtgAreaErr3d":h.push("#REF!");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(w));default:throw new Error("Unrecognized Formula Token: "+String(w))}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=a.biff&&m>=0&&-1==P.indexOf(e[0][v][0])){w=e[0][m];var L=!0;switch(w[1][0]){case 4:L=!1;case 0:g=dt(" ",w[1][1]);break;case 5:L=!1;case 1:g=dt("\r",w[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((L?g:"")+h.pop()+(L?"":g)),m=-1}}if(h.length>1&&a.WTF)throw new Error("bad formula stack");return h[0]}function Al(e){if(null==e){var t=Nr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return In("number"==typeof e?e:0)}function yl(e,t,r,n,a){var i=Ga(t,r,a),s=Al(e.v),o=Nr(6),l=33;o.write_shift(2,l),o.write_shift(4,0);for(var f=Nr(e.bf.length),c=0;c<e.bf.length;++c)f[c]=e.bf[c];var h=R([i,s,o,f]);return h}function _l(e,t,r){var n=e.read_shift(4),a=gl(e,n,r),i=e.read_shift(4),s=i>0?ml(e,i,a,r):null;return[a,s]}var Ol=_l,xl=_l,Cl=_l,Rl=_l,kl={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Nl={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Il={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Dl(e){var t="of:="+e.replace(Vs,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function Pl(e){return e.replace(/\./,"!")}var Ll="undefined"!==typeof Map;function Ml(e,t,r){var n=0,a=e.length;if(r){if(Ll?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var i=Ll?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(Ll?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Fl(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Li=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=Fi(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=Ui(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function Ul(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function Bl(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,i=e.length;if(null==n&&r.ssf)for(;a<392;++a)if(null==r.ssf[a]){Be(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function Wl(e,t,r){if(e&&e["!ref"]){var n=Zr(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function Hl(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Jr(e[r])+'"/>';return t+"</mergeCells>"}function Gl(e,t,r,n,a){var i=!1,s={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(c){}i=!0,s.codeName=Mt(yt(l))}if(e&&e["!outline"]){var f={summaryBelow:1,summaryRight:1};e["!outline"].above&&(f.summaryBelow=0),e["!outline"].left&&(f.summaryRight=0),o=(o||"")+Ht("outlinePr",null,f)}(i||o)&&(a[a.length]=Ht("sheetPr",o,s))}var Vl=["objects","scenarios","selectLockedCells","selectUnlockedCells"],jl=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function zl(e){var t={sheet:1};return Vl.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),jl.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=Ni(e.password).toString(16).toUpperCase()),Ht("sheetProtection",null,t)}function $l(e){return Ul(e),Ht("pageMargins",null,e)}function Xl(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=Ht("col",null,Fl(a,r)));return n[n.length]="</cols>",n.join("")}function Kl(e,t,r,n){var a="string"==typeof e.ref?e.ref:Jr(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Yr(a);s.s.r==s.e.r&&(s.e.r=Yr(t["!ref"]).e.r,a=Jr(s));for(var o=0;o<i.length;++o){var l=i[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Ht("autoFilter",null,{ref:a})}function Yl(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Ht("sheetViews",Ht("sheetView",null,a),{})}function Jl(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!==typeof e.f||"z"===e.t&&!e.f)return"";var a="",i=e.t,s=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=qn[e.v];break;case"d":n&&n.cellDates?a=ct(e.v,-1).toISOString():(e=ut(e),e.t="n",a=""+(e.v=rt(ct(e.v)))),"undefined"===typeof e.z&&(e.z=z[14]);break;default:a=e.v;break}var o=Bt("v",yt(a)),l={r:t},f=Bl(n.cellXfs,e,n);switch(0!==f&&(l.s=f),e.t){case"n":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;case"z":break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=Bt("v",""+Ml(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),"string"==typeof e.f&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=Ht("f",yt(e.f),c)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),Ht("c",o,l)}function Zl(e,t,r,n){var a,i,s=[],o=[],l=Zr(e["!ref"]),f="",c="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:c},v=-1;for(d=l.s.c;d<=l.e.c;++d)h[d]=Vr(d);for(u=l.s.r;u<=l.e.r;++u){for(o=[],c=Br(u),d=l.s.c;d<=l.e.c;++d){a=h[d]+c;var T=m?(e[u]||[])[d]:e[a];void 0!==T&&(null!=(f=Jl(T,a,e,t,r,n))&&o.push(f))}(o.length>0||p&&p[u])&&(g={r:c},p&&p[u]&&(i=p[u],i.hidden&&(g.hidden=1),v=-1,i.hpx?v=Gi(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level)),s[s.length]=Ht("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},i=p[u],i.hidden&&(g.hidden=1),v=-1,i.hpx?v=Gi(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level),s[s.length]=Ht("row","",g));return s.join("")}function ql(e,t,r,n){var a=[wt,Ht("worksheet",null,{xmlns:zt[0],"xmlns:r":jt.r})],i=r.SheetNames[e],s=0,o="",l=r.Sheets[i];null==l&&(l={});var f=l["!ref"]||"A1",c=Zr(f);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+f+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),f=Jr(c)}n||(n={}),l["!comments"]=[];var h=[];Gl(l,r,e,t,a),a[a.length]=Ht("dimension",null,{ref:f}),a[a.length]=Yl(l,t,e,r),t.sheetFormat&&(a[a.length]=Ht("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=l["!cols"]&&l["!cols"].length>0&&(a[a.length]=Xl(l,l["!cols"])),a[s=a.length]="<sheetData/>",l["!links"]=[],null!=l["!ref"]&&(o=Zl(l,t,e,r,n),o.length>0&&(a[a.length]=o)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),l["!protect"]&&(a[a.length]=zl(l["!protect"])),null!=l["!autofilter"]&&(a[a.length]=Kl(l["!autofilter"],l,r,e)),null!=l["!merges"]&&l["!merges"].length>0&&(a[a.length]=Hl(l["!merges"]));var u,d=-1,p=-1;return l["!links"].length>0&&(a[a.length]="<hyperlinks>",l["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(p=sa(n,-1,yt(e[1].Target).replace(/#.*$/,""),na.HLINK),u["r:id"]="rId"+p),(d=e[1].Target.indexOf("#"))>-1&&(u.location=yt(e[1].Target.slice(d+1))),e[1].Tooltip&&(u.tooltip=yt(e[1].Tooltip)),a[a.length]=Ht("hyperlink",null,u))})),a[a.length]="</hyperlinks>"),delete l["!links"],null!=l["!margins"]&&(a[a.length]=$l(l["!margins"])),t&&!t.ignoreEC&&void 0!=t.ignoreEC||(a[a.length]=Bt("ignoredErrors",Ht("ignoredError",null,{numberStoredAsText:1,sqref:f}))),h.length>0&&(p=sa(n,-1,"../drawings/drawing"+(e+1)+".xml",na.DRAW),a[a.length]=Ht("drawing",null,{"r:id":"rId"+p}),l["!drawing"]=h),l["!comments"].length>0&&(p=sa(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",na.VML),a[a.length]=Ht("legacyDrawing",null,{"r:id":"rId"+p}),l["!legacy"]=p),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Ql(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,7&i&&(r.level=7&i),16&i&&(r.hidden=!0),32&i&&(r.hpt=a/20),r}function ef(e,t,r){var n=Nr(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=20*Gi(a.hpx):a.hpt&&(i=20*a.hpt),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var o=0,l=n.l;n.l+=4;for(var f={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var h=-1,u=-1,d=c<<10;d<c+1<<10;++d){f.c=d;var p=Array.isArray(r)?(r[f.r]||[])[f.c]:r[Kr(f)];p&&(h<0&&(h=d),u=d)}h<0||(++o,n.write_shift(4,h),n.write_shift(4,u))}var m=n.l;return n.l=l,n.write_shift(4,o),n.l=m,n.length>n.l?n.slice(0,n.l):n}function tf(e,t,r,n){var a=ef(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&Dr(e,0,a)}var rf=Rn,nf=kn;function af(){}function sf(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=Tn(e,t-19),r}function of(e,t,r){null==r&&(r=Nr(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Pn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),wn(e,r),r.slice(0,r.l)}function lf(e){var t=pn(e);return[t]}function ff(e,t,r){return null==r&&(r=Nr(8)),mn(t,r)}function cf(e){var t=gn(e);return[t]}function hf(e,t,r){return null==r&&(r=Nr(4)),vn(t,r)}function uf(e){var t=pn(e),r=e.read_shift(1);return[t,r,"b"]}function df(e,t,r){return null==r&&(r=Nr(9)),mn(t,r),r.write_shift(1,e.v?1:0),r}function pf(e){var t=gn(e),r=e.read_shift(1);return[t,r,"b"]}function mf(e,t,r){return null==r&&(r=Nr(5)),vn(t,r),r.write_shift(1,e.v?1:0),r}function gf(e){var t=pn(e),r=e.read_shift(1);return[t,r,"e"]}function vf(e,t,r){return null==r&&(r=Nr(9)),mn(t,r),r.write_shift(1,e.v),r}function Tf(e){var t=gn(e),r=e.read_shift(1);return[t,r,"e"]}function wf(e,t,r){return null==r&&(r=Nr(8)),vn(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function bf(e){var t=pn(e),r=e.read_shift(4);return[t,r,"s"]}function Ef(e,t,r){return null==r&&(r=Nr(12)),mn(t,r),r.write_shift(4,t.v),r}function Sf(e){var t=gn(e),r=e.read_shift(4);return[t,r,"s"]}function Af(e,t,r){return null==r&&(r=Nr(8)),vn(t,r),r.write_shift(4,t.v),r}function yf(e){var t=pn(e),r=Nn(e);return[t,r,"n"]}function _f(e,t,r){return null==r&&(r=Nr(16)),mn(t,r),In(e.v,r),r}function Of(e){var t=gn(e),r=Nn(e);return[t,r,"n"]}function xf(e,t,r){return null==r&&(r=Nr(12)),vn(t,r),In(e.v,r),r}function Cf(e){var t=pn(e),r=_n(e);return[t,r,"n"]}function Rf(e,t,r){return null==r&&(r=Nr(12)),mn(t,r),On(e.v,r),r}function kf(e){var t=gn(e),r=_n(e);return[t,r,"n"]}function Nf(e,t,r){return null==r&&(r=Nr(8)),vn(t,r),On(e.v,r),r}function If(e){var t=pn(e),r=cn(e);return[t,r,"is"]}function Df(e){var t=pn(e),r=sn(e);return[t,r,"str"]}function Pf(e,t,r){return null==r&&(r=Nr(12+4*e.v.length)),mn(t,r),on(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Lf(e){var t=gn(e),r=sn(e);return[t,r,"str"]}function Mf(e,t,r){return null==r&&(r=Nr(8+4*e.v.length)),vn(t,r),on(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Ff(e,t,r){var n=e.l+t,a=pn(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var o=xl(e,n-e.l,r);s[3]=Sl(o,null,a,r.supbooks,r)}else e.l=n;return s}function Uf(e,t,r){var n=e.l+t,a=pn(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var o=xl(e,n-e.l,r);s[3]=Sl(o,null,a,r.supbooks,r)}else e.l=n;return s}function Bf(e,t,r){var n=e.l+t,a=pn(e);a.r=r["!row"];var i=Nn(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var o=xl(e,n-e.l,r);s[3]=Sl(o,null,a,r.supbooks,r)}else e.l=n;return s}function Wf(e,t,r){var n=e.l+t,a=pn(e);a.r=r["!row"];var i=sn(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var o=xl(e,n-e.l,r);s[3]=Sl(o,null,a,r.supbooks,r)}else e.l=n;return s}var Hf=Rn,Gf=kn;function Vf(e,t){return null==t&&(t=Nr(4)),t.write_shift(4,e),t}function jf(e,t){var r=e.l+t,n=Rn(e,16),a=bn(e),i=sn(e),s=sn(e),o=sn(e);e.l=r;var l={rfx:n,relId:a,loc:i,display:o};return s&&(l.Tooltip=s),l}function zf(e,t){var r=Nr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));kn({s:Xr(e[0]),e:Xr(e[0])},r),yn("rId"+t,r);var n=e[1].Target.indexOf("#"),a=-1==n?"":e[1].Target.slice(n+1);return on(a||"",r),on(e[1].Tooltip||"",r),on("",r),r.slice(0,r.l)}function $f(){}function Xf(e,t,r){var n=e.l+t,a=xn(e,16),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var o=Ol(e,n-e.l,r);s[1]=o}else e.l=n;return s}function Kf(e,t,r){var n=e.l+t,a=Rn(e,16),i=[a];if(r.cellFormula){var s=Rl(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function Yf(e,t,r){null==r&&(r=Nr(18));var n=Fl(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var Jf=["left","right","top","bottom","header","footer"];function Zf(e){var t={};return Jf.forEach((function(r){t[r]=Nn(e,8)})),t}function qf(e,t){return null==t&&(t=Nr(48)),Ul(e),Jf.forEach((function(r){In(e[r],t)})),t}function Qf(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}function ec(e,t,r){null==r&&(r=Nr(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function tc(e){var t=Nr(24);return t.write_shift(4,4),t.write_shift(4,1),kn(e,t),t}function rc(e,t){return null==t&&(t=Nr(66)),t.write_shift(2,e.password?Ni(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(r){r[1]?t.write_shift(4,null==e[r[0]]||e[r[0]]?0:1):t.write_shift(4,null!=e[r[0]]&&e[r[0]]?0:1)})),t}function nc(){}function ac(){}function ic(e,t,r,n,a,i,s){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":t=ut(t),t.z=t.z||z[14],t.v=rt(ct(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v;break}var l={r:r,c:n};switch(l.s=Bl(a.cellXfs,t,a),t.l&&i["!links"].push([Kr(l),t.l]),t.c&&i["!comments"].push([Kr(l),t.c]),t.t){case"s":case"str":return a.bookSST?(o=Ml(a.Strings,t.v,a.revStrings),l.t="s",l.v=o,s?Dr(e,18,Af(t,l)):Dr(e,7,Ef(t,l))):(l.t="str",s?Dr(e,17,Mf(t,l)):Dr(e,6,Pf(t,l))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?s?Dr(e,13,Nf(t,l)):Dr(e,2,Rf(t,l)):s?Dr(e,16,xf(t,l)):Dr(e,5,_f(t,l)),!0;case"b":return l.t="b",s?Dr(e,15,mf(t,l)):Dr(e,4,df(t,l)),!0;case"e":return l.t="e",s?Dr(e,14,wf(t,l)):Dr(e,3,vf(t,l)),!0}return s?Dr(e,12,hf(t,l)):Dr(e,1,ff(t,l)),!0}function sc(e,t,r,n){var a,i=Zr(t["!ref"]||"A1"),s="",o=[];Dr(e,145);var l=Array.isArray(t),f=i.e.r;t["!rows"]&&(f=Math.max(i.e.r,t["!rows"].length-1));for(var c=i.s.r;c<=f;++c){s=Br(c),tf(e,t,i,c);var h=!1;if(c<=i.e.r)for(var u=i.s.c;u<=i.e.c;++u){c===i.s.r&&(o[u]=Vr(u)),a=o[u]+s;var d=l?(t[c]||[])[u]:t[a];d?h=ic(e,d,c,u,n,t,h):h=!1}}Dr(e,146)}function oc(e,t){t&&t["!merges"]&&(Dr(e,177,Vf(t["!merges"].length)),t["!merges"].forEach((function(t){Dr(e,176,Gf(t))})),Dr(e,178))}function lc(e,t){t&&t["!cols"]&&(Dr(e,390),t["!cols"].forEach((function(t,r){t&&Dr(e,60,Yf(r,t))})),Dr(e,391))}function fc(e,t){t&&t["!ref"]&&(Dr(e,648),Dr(e,649,tc(Zr(t["!ref"]))),Dr(e,650))}function cc(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var n=sa(r,-1,t[1].Target.replace(/#.*$/,""),na.HLINK);Dr(e,494,zf(t,n))}})),delete t["!links"]}function hc(e,t,r,n){if(t["!comments"].length>0){var a=sa(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",na.VML);Dr(e,551,yn("rId"+a)),t["!legacy"]=a}}function uc(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i="string"===typeof a.ref?a.ref:Jr(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,o=Yr(i);o.s.r==o.e.r&&(o.e.r=Yr(t["!ref"]).e.r,i=Jr(o));for(var l=0;l<s.length;++l){var f=s[l];if("_xlnm._FilterDatabase"==f.Name&&f.Sheet==n){f.Ref="'"+r.SheetNames[n]+"'!"+i;break}}l==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),Dr(e,161,kn(Zr(i))),Dr(e,162)}}function dc(e,t,r){Dr(e,133),Dr(e,137,ec(t,r)),Dr(e,138),Dr(e,134)}function pc(){}function mc(e,t){t["!protect"]&&Dr(e,535,rc(t["!protect"]))}function gc(e,t,r,n){var a=Ir(),i=r.SheetNames[e],s=r.Sheets[i]||{},o=i;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(f){}var l=Zr(s["!ref"]||"A1");if(l.e.c>16383||l.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],Dr(a,129),(r.vbaraw||s["!outline"])&&Dr(a,147,of(o,s["!outline"])),Dr(a,148,nf(l)),dc(a,s,r.Workbook),pc(a,s),lc(a,s,e,t,r),sc(a,s,e,t,r),mc(a,s),uc(a,s,r,e),oc(a,s),cc(a,s,n),s["!margins"]&&Dr(a,476,qf(s["!margins"])),t&&!t.ignoreEC&&void 0!=t.ignoreEC||fc(a,s),hc(a,s,e,n),Dr(a,130),a.end()}function vc(e,t){e.l+=10;var r=sn(e,t-10);return{name:r}}var Tc=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function wc(e){return e.Workbook&&e.Workbook.WBProps&&kt(e.Workbook.WBProps.date1904)?"true":"false"}var bc="][*?/\\".split("");function Ec(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return bc.forEach((function(n){if(-1!=e.indexOf(n)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function Sc(e,t,r){e.forEach((function(n,a){Ec(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(95==s.charCodeAt(0)&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}}))}function Ac(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];Sc(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)Wl(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function yc(e){var t=[wt];t[t.length]=Ht("workbook",null,{xmlns:zt[0],"xmlns:r":jt.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Tc.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Ht("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length;++i){if(!a[i])break;if(!a[i].Hidden)break}i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:yt(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Ht("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=Ht("definedName",yt(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function _c(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=An(e,t-8),r.name=sn(e),r}function Oc(e,t){return t||(t=Nr(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),yn(e.strRelID,t),on(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function xc(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?sn(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}function Cc(e,t){t||(t=Nr(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),wn(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function Rc(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Sn(e),s=Cl(e,0,r),o=bn(e);e.l=n;var l={Name:i,Ptg:s};return a<268435455&&(l.Sheet=a),o&&(l.Comment=o),l}function kc(e,t){Dr(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};Dr(e,156,Oc(a))}Dr(e,144)}function Nc(e,t){t||(t=Nr(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return on("SheetJS",t),on(n.version,t),on(n.version,t),on("7262",t),t.length>t.l?t.slice(0,t.l):t}function Ic(e,t){t||(t=Nr(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Dc(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&-1==a?a=n:1==r[n].Hidden&&-1==i&&(i=n);i>a||(Dr(e,135),Dr(e,158,Ic(a)),Dr(e,136))}}function Pc(e,t){var r=Ir();return Dr(r,131),Dr(r,128,Nc()),Dr(r,153,Cc(e.Workbook&&e.Workbook.WBProps||null)),Dc(r,e,t),kc(r,e,t),Dr(r,132),r.end()}function Lc(e,t,r){return(".bin"===t.slice(-4)?Pc:yc)(e,r)}function Mc(e,t,r,n,a){return(".bin"===t.slice(-4)?gc:ql)(e,r,n,a)}function Fc(e,t,r){return(".bin"===t.slice(-4)?Ts:$i)(e,r)}function Uc(e,t,r){return(".bin"===t.slice(-4)?Ri:_i)(e,r)}function Bc(e,t,r){return(".bin"===t.slice(-4)?Bs:Is)(e,r)}function Wc(e){return(".bin"===e.slice(-4)?xs:Cs)()}function Hc(e,t){var r=[];return e.Props&&r.push(ba(e.Props,t)),e.Custprops&&r.push(Ea(e.Props,e.Custprops,t)),r.join("")}function Gc(){return""}function Vc(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var n=[];n.push(Ht("NumberFormat",null,{"ss:Format":yt(z[e.numFmtId])}));var a={"ss:ID":"s"+(21+t)};r.push(Ht("Style",n.join(""),a))})),Ht("Styles",r.join(""))}function jc(e){return Ht("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+js(e.Ref,{r:0,c:0})})}function zc(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(jc(a)))}return Ht("Names",r.join(""))}function $c(e,t,r,n){if(!e)return"";if(!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var o=a[s];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||i.push(jc(o)))}return i.join("")}function Xc(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Ht("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Ht("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Ht("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Ht("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r;++i)if(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden)break;i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Bt("ProtectContents","True")),e["!protect"].objects&&a.push(Bt("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Bt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(Bt("EnableSelection","UnlockedCells")):a.push(Bt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")}))),0==a.length?"":Ht("WorksheetOptions",a.join(""),{xmlns:$t.x})}function Kc(e){return e.map((function(e){var t=Rt(e.t||""),r=Ht("ss:Data",t,{xmlns:"http://www.w3.org/TR/REC-html40"});return Ht("Comment",r,{"ss:Author":e.a})})).join("")}function Yc(e,t,r,n,a,i,s){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+yt(js(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var l=Xr(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==s.r?"":"["+(l.r-s.r)+"]")+"C"+(l.c==s.c?"":"["+(l.c-s.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=yt(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=yt(e.l.Tooltip))),r["!merges"])for(var f=r["!merges"],c=0;c!=f.length;++c)f[c].s.c==s.c&&f[c].s.r==s.r&&(f[c].e.c>f[c].s.c&&(o["ss:MergeAcross"]=f[c].e.c-f[c].s.c),f[c].e.r>f[c].s.r&&(o["ss:MergeDown"]=f[c].e.r-f[c].s.r));var h="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=qn[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||z[14]);break;case"s":h="String",u=Ct(e.v||"");break}var d=Bl(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=s.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=Kc(e.c)),Ht("Cell",m,o)}function Jc(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Vi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function Zc(e,t,r,n){if(!e["!ref"])return"";var a=Zr(e["!ref"]),i=e["!merges"]||[],s=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,t){Bi(e);var r=!!e.width,n=Fl(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=Mi(n.width)),e.hidden&&(a["ss:Hidden"]="1"),o.push(Ht("Column",null,a))}));for(var l=Array.isArray(e),f=a.s.r;f<=a.e.r;++f){for(var c=[Jc(f,(e["!rows"]||[])[f])],h=a.s.c;h<=a.e.c;++h){var u=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h)&&!(i[s].s.r>f)&&!(i[s].e.c<h)&&!(i[s].e.r<f)){i[s].s.c==h&&i[s].s.r==f||(u=!0);break}if(!u){var d={r:f,c:h},p=Kr(d),m=l?(e[f]||[])[h]:e[p];c.push(Yc(m,p,e,t,r,n,d))}}c.push("</Row>"),c.length>2&&o.push(c.join(""))}return o.join("")}function qc(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?$c(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?Zc(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(Xc(i,t,e,r)),n.join("")}function Qc(e,t){t||(t={}),e.SSF||(e.SSF=ut(z)),e.SSF&&(He(),We(e.SSF),t.revssf=Qe(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Bl(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Hc(e,t)),r.push(Gc(e,t)),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Ht("Worksheet",qc(n,t,e),{"ss:Name":yt(e.SheetNames[n])}));return r[2]=Vc(e,t),r[3]=zc(e,t),wt+Ht("Workbook",r.join(""),{xmlns:$t.ss,"xmlns:o":$t.o,"xmlns:x":$t.x,"xmlns:ss":$t.ss,"xmlns:dt":$t.dt,"xmlns:html":$t.html})}var eh={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function th(e,t){var r,n=[],a=[],i=[],s=0,o=Ze(Xn,"n"),l=Ze(Kn,"n");if(e.Props)for(r=Je(e.Props),s=0;s<r.length;++s)(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Props[r[s]]]);if(e.Custprops)for(r=Je(e.Custprops),s=0;s<r.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},r[s])||(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Custprops[r[s]]]);var f=[];for(s=0;s<i.length;++s)ya.indexOf(i[s][0])>-1||ga.indexOf(i[s][0])>-1||null!=i[s][1]&&f.push(i[s]);a.length&&$e.utils.cfb_add(t,"/SummaryInformation",xa(a,eh.SI,l,Kn)),(n.length||f.length)&&$e.utils.cfb_add(t,"/DocumentSummaryInformation",xa(n,eh.DSI,o,Xn,f.length?f:null,eh.UDI))}function rh(e,t){var r=t||{},n=$e.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return $e.utils.cfb_add(n,a,Sh(e,r)),8==r.biff&&(e.Props||e.Custprops)&&th(e,n),8==r.biff&&e.vbaraw&&Ws(n,$e.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}var nh={0:{f:Ql},1:{f:lf},2:{f:Cf},3:{f:gf},4:{f:uf},5:{f:yf},6:{f:Df},7:{f:bf},8:{f:Wf},9:{f:Bf},10:{f:Ff},11:{f:Uf},12:{f:cf},13:{f:kf},14:{f:Tf},15:{f:pf},16:{f:Of},17:{f:Lf},18:{f:Sf},19:{f:cn},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Rc},40:{},42:{},43:{f:Yi},44:{f:Xi},45:{f:Qi},46:{f:as},47:{f:ts},48:{},49:{f:nn},50:{},51:{f:Ss},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:di},62:{f:If},63:{f:Rs},64:{f:nc},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:kr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Qf},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:sf},148:{f:rf,p:16},151:{f:$f},152:{},153:{f:xc},154:{},155:{},156:{f:_c},157:{},158:{},159:{T:1,f:Oi},160:{T:-1},161:{T:1,f:Rn},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Hf},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:bs},336:{T:-1},337:{f:_s,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:An},357:{},358:{},359:{},360:{T:1},361:{},362:{f:oi},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Xf},427:{f:Kf},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Zf},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:af},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:jf},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:An},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Fs},633:{T:1},634:{T:-1},635:{T:1,f:Ls},636:{T:-1},637:{f:un},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:vc},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:ac},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function ah(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&mr(r)&&e.push(r)}}function ih(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return ah(e,t,r,a);var i=t;if(!isNaN(i)){var s=r.parts||[],o=0,l=0,f=0;while(f+(s[o]||8224)<=8224)f+=s[o]||8224,o++;var c=e.next(4);c.write_shift(2,i),c.write_shift(2,f),e.push(r.slice(l,l+f)),l+=f;while(l<a){c=e.next(4),c.write_shift(2,60),f=0;while(f+(s[o]||8224)<=8224)f+=s[o]||8224,o++;c.write_shift(2,f),e.push(r.slice(l,l+f)),l+=f}}}function sh(e,t,r){return e||(e=Nr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function oh(e,t,r,n){var a=Nr(9);return sh(a,e,t),Da(r,n||"b",a),a}function lh(e,t,r){var n=Nr(8+2*r.length);return sh(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function fh(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a="d"==t.t?rt(ct(t.v)):t.v;return void(a==(0|a)&&a>=0&&a<65536?ah(e,2,vi(r,n,a)):ah(e,3,gi(r,n,a)));case"b":case"e":return void ah(e,5,oh(r,n,t.v,t.t));case"s":case"str":return void ah(e,4,lh(r,n,(t.v||"").slice(0,255)))}ah(e,1,sh(null,r,n))}function ch(e,t,r,n){var a,i=Array.isArray(t),s=Zr(t["!ref"]||"A1"),o="",l=[];if(s.e.c>255||s.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),a=Jr(s)}for(var f=s.s.r;f<=s.e.r;++f){o=Br(f);for(var c=s.s.c;c<=s.e.c;++c){f===s.s.r&&(l[c]=Vr(c)),a=l[c]+o;var h=i?(t[f]||[])[c]:t[a];h&&fh(e,h,f,c,n)}}}function hh(e,t){var r=t||{};null!=g&&null==r.dense&&(r.dense=g);for(var n=Ir(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(0==a&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ah(n,4==r.biff?1033:3==r.biff?521:9,$a(e,16,r)),ch(n,e.Sheets[e.SheetNames[a]],a,r,e),ah(n,10),n.end()}function uh(e,t,r){ah(e,49,qa({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function dh(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(n){for(var a=n[0];a<=n[1];++a)null!=t[a]&&ah(e,1054,ti(a,t[a],r))}))}function ph(e,t){var r=Nr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ah(e,2151,r),r=Nr(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),za(Zr(t["!ref"]||"A1"),r),r.write_shift(4,4),ah(e,2152,r)}function mh(e,t){for(var r=0;r<16;++r)ah(e,224,ni({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){ah(e,224,ni(r,0,t))}))}function gh(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];ah(e,440,ci(n)),n[1].Tooltip&&ah(e,2048,hi(n))}delete t["!links"]}function vh(e,t){if(t){var r=0;t.forEach((function(t,n){++r<=256&&t&&ah(e,125,pi(Fl(n,t),n))}))}}function Th(e,t,r,n,a){var i=16+Bl(a.cellXfs,t,a);if(null!=t.v||t.bf)if(t.bf)ah(e,6,yl(t,r,n,a,i));else switch(t.t){case"d":case"n":var s="d"==t.t?rt(ct(t.v)):t.v;ah(e,515,si(r,n,s,i,a));break;case"b":case"e":ah(e,517,ii(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var o=Ml(a.Strings,t.v,a.revStrings);ah(e,253,Qa(r,n,o,i,a))}else ah(e,516,ei(r,n,(t.v||"").slice(0,255),i,a));break;default:ah(e,513,Ga(r,n,i))}else ah(e,513,Ga(r,n,i))}function wh(e,t,r){var n,a=Ir(),i=r.SheetNames[e],s=r.Sheets[i]||{},o=(r||{}).Workbook||{},l=(o.Sheets||[])[e]||{},f=Array.isArray(s),c=8==t.biff,h="",u=[],d=Zr(s["!ref"]||"A1"),p=c?65536:16384;if(d.e.c>255||d.e.r>=p){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}ah(a,2057,$a(r,16,t)),ah(a,13,Ia(1)),ah(a,12,Ia(100)),ah(a,15,ka(!0)),ah(a,17,ka(!1)),ah(a,16,In(.001)),ah(a,95,ka(!0)),ah(a,42,ka(!1)),ah(a,43,ka(!1)),ah(a,130,Ia(1)),ah(a,128,ai([0,0])),ah(a,131,ka(!1)),ah(a,132,ka(!1)),c&&vh(a,s["!cols"]),ah(a,512,ri(d,t)),c&&(s["!links"]=[]);for(var m=d.s.r;m<=d.e.r;++m){h=Br(m);for(var g=d.s.c;g<=d.e.c;++g){m===d.s.r&&(u[g]=Vr(g)),n=u[g]+h;var v=f?(s[m]||[])[g]:s[n];v&&(Th(a,v,m,g,t),c&&v.l&&s["!links"].push([n,v.l]))}}var T=l.CodeName||l.name||i;return c&&ah(a,574,Za((o.Views||[])[0])),c&&(s["!merges"]||[]).length&&ah(a,229,fi(s["!merges"])),c&&gh(a,s),ah(a,442,Ba(T,t)),c&&ph(a,s),ah(a,10),a.end()}function bh(e,t,r){var n=Ir(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},o=8==r.biff,l=5==r.biff;if(ah(n,2057,$a(e,5,r)),"xla"==r.bookType&&ah(n,135),ah(n,225,o?Ia(1200):null),ah(n,193,Ca(2)),l&&ah(n,191),l&&ah(n,192),ah(n,226),ah(n,92,Xa("SheetJS",r)),ah(n,66,Ia(o?1200:1252)),o&&ah(n,353,Ia(0)),o&&ah(n,448),ah(n,317,mi(e.SheetNames.length)),o&&e.vbaraw&&ah(n,211),o&&e.vbaraw){var f=s.CodeName||"ThisWorkbook";ah(n,442,Ba(f,r))}ah(n,156,Ia(17)),ah(n,25,ka(!1)),ah(n,18,ka(!1)),ah(n,19,Ia(0)),o&&ah(n,431,ka(!1)),o&&ah(n,444,Ia(0)),ah(n,61,Ja(r)),ah(n,64,ka(!1)),ah(n,141,Ia(0)),ah(n,34,ka("true"==wc(e))),ah(n,14,ka(!0)),o&&ah(n,439,ka(!1)),ah(n,218,Ia(0)),uh(n,e,r),dh(n,e.SSF,r),mh(n,r),o&&ah(n,352,ka(!1));var c=n.end(),h=Ir();o&&ah(h,140,ui()),o&&r.Strings&&ih(h,252,Ya(r.Strings,r)),ah(h,10);var u=h.end(),d=Ir(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(o?12:11)+(o?2:1)*e.SheetNames[m].length;var g=c.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){var v=i[m]||{};ah(d,133,Ka({pos:g,hs:v.Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var T=d.end();if(p!=T.length)throw new Error("BS8 "+p+" != "+T.length);var w=[];return c.length&&w.push(c),T.length&&w.push(T),u.length&&w.push(u),R(w)}function Eh(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=ut(z)),e&&e.SSF&&(He(),We(e.SSF),r.revssf=Qe(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,nu(r),r.cellXfs=[],Bl(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=wh(a,r,e);return n.unshift(bh(e,n,r)),R(n)}function Sh(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(n&&n["!ref"]){var a=Yr(n["!ref"]);a.e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Eh(e,t);case 4:case 3:case 2:return hh(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Ah(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var o=0,l=0,f=0;f<a.length;++f)if(!(a[f].s.r>r||a[f].s.c>s)&&!(a[f].e.r<r||a[f].e.c<s)){if(a[f].s.r<r||a[f].s.c<s){o=-1;break}o=a[f].e.r-a[f].s.r+1,l=a[f].e.c-a[f].s.c+1;break}if(!(o<0)){var c=Kr({r:r,c:s}),h=n.dense?(e[r]||[])[s]:e[c],u=h&&null!=h.v&&(h.h||xt(h.w||(Qr(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(n.id||"sjs")+"-"+c,i.push(Ht("td",u,d))}}var p="<tr>";return p+i.join("")+"</tr>"}var yh='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',_h="</body></html>";function Oh(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function xh(e,t){var r=t||{},n=null!=r.header?r.header:yh,a=null!=r.footer?r.footer:_h,i=[n],s=Yr(e["!ref"]);r.dense=Array.isArray(e),i.push(Oh(e,s,r));for(var o=s.s.r;o<=s.e.r;++o)i.push(Ah(e,s,o,r));return i.push("</table>"+a),i.join("")}function Ch(e,t,r){var n=r||{};null!=g&&(n.dense=g);var a=0,i=0;if(null!=n.origin)if("number"==typeof n.origin)a=n.origin;else{var s="string"==typeof n.origin?Xr(n.origin):n.origin;a=s.r,i=s.c}var o=t.getElementsByTagName("tr"),l=Math.min(n.sheetRows||1e7,o.length),f={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=Yr(e["!ref"]);f.s.r=Math.min(f.s.r,c.s.r),f.s.c=Math.min(f.s.c,c.s.c),f.e.r=Math.max(f.e.r,c.e.r),f.e.c=Math.max(f.e.c,c.e.c),-1==a&&(f.e.r=a=c.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,v=0,T=0,w=0,b=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<l;++p){var E=o[p];if(Nh(E)){if(n.display)continue;d[m]={hidden:!0}}var S=E.children;for(v=T=0;v<S.length;++v){var A=S[v];if(!n.display||!Nh(A)){var y=A.hasAttribute("data-v")?A.getAttribute("data-v"):A.hasAttribute("v")?A.getAttribute("v"):Ft(A.innerHTML),_=A.getAttribute("data-z")||A.getAttribute("z");for(u=0;u<h.length;++u){var O=h[u];O.s.c==T+i&&O.s.r<m+a&&m+a<=O.e.r&&(T=O.e.c+1-i,u=-1)}b=+A.getAttribute("colspan")||1,((w=+A.getAttribute("rowspan")||1)>1||b>1)&&h.push({s:{r:m+a,c:T+i},e:{r:m+a+(w||1)-1,c:T+i+(b||1)-1}});var x={t:"s",v:y},C=A.getAttribute("data-t")||A.getAttribute("t")||"";null!=y&&(0==y.length?x.t=C||"z":n.raw||0==y.trim().length||"s"==C||("TRUE"===y?x={t:"b",v:!0}:"FALSE"===y?x={t:"b",v:!1}:isNaN(pt(y))?isNaN(gt(y).getDate())||(x={t:"d",v:ct(y)},n.cellDates||(x={t:"n",v:rt(x.v)}),x.z=n.dateNF||z[14]):x={t:"n",v:pt(y)})),void 0===x.z&&null!=_&&(x.z=_);var R="",k=A.getElementsByTagName("A");if(k&&k.length)for(var N=0;N<k.length;++N)if(k[N].hasAttribute("href")&&(R=k[N].getAttribute("href"),"#"!=R.charAt(0)))break;R&&"#"!=R.charAt(0)&&(x.l={Target:R}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][T+i]=x):e[Kr({c:T+i,r:m+a})]=x,f.e.c<T+i&&(f.e.c=T+i),T+=b}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),f.e.r=Math.max(f.e.r,m-1+a),e["!ref"]=Jr(f),m>=l&&(e["!fullref"]=Jr((f.e.r=o.length-p+m-1+a,f))),e}function Rh(e,t){var r=t||{},n=r.dense?[]:{};return Ch(n,e,t)}function kh(e,t){return en(Rh(e,t),t)}function Nh(e){var t="",r=Ih(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}function Ih(e){return e.ownerDocument.defaultView&&"function"===typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"===typeof getComputedStyle?getComputedStyle:null}var Dh=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Wt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return wt+t}}(),Ph=function(){var e=function(e){return yt(e).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t="          <table:table-cell />\n",r="          <table:covered-table-cell/>\n",n=function(n,a,i){var s=[];s.push('      <table:table table:name="'+yt(a.SheetNames[i])+'" table:style-name="ta1">\n');var o=0,l=0,f=Yr(n["!ref"]||"A1"),c=n["!merges"]||[],h=0,u=Array.isArray(n);if(n["!cols"])for(l=0;l<=f.e.c;++l)s.push("        <table:table-column"+(n["!cols"][l]?' table:style-name="co'+n["!cols"][l].ods+'"':"")+"></table:table-column>\n");var d="",p=n["!rows"]||[];for(o=0;o<f.s.r;++o)d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",s.push("        <table:table-row"+d+"></table:table-row>\n");for(;o<=f.e.r;++o){for(d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",s.push("        <table:table-row"+d+">\n"),l=0;l<f.s.c;++l)s.push(t);for(;l<=f.e.c;++l){var m=!1,g={},v="";for(h=0;h!=c.length;++h)if(!(c[h].s.c>l)&&!(c[h].s.r>o)&&!(c[h].e.c<l)&&!(c[h].e.r<o)){c[h].s.c==l&&c[h].s.r==o||(m=!0),g["table:number-columns-spanned"]=c[h].e.c-c[h].s.c+1,g["table:number-rows-spanned"]=c[h].e.r-c[h].s.r+1;break}if(m)s.push(r);else{var T=Kr({r:o,c:l}),w=u?(n[o]||[])[l]:n[T];if(w&&w.f&&(g["table:formula"]=yt(Dl(w.f)),w.F&&w.F.slice(0,T.length)==T)){var b=Yr(w.F);g["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,g["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(w){switch(w.t){case"b":v=w.v?"TRUE":"FALSE",g["office:value-type"]="boolean",g["office:boolean-value"]=w.v?"true":"false";break;case"n":v=w.w||String(w.v||0),g["office:value-type"]="float",g["office:value"]=w.v||0;break;case"s":case"str":v=null==w.v?"":w.v,g["office:value-type"]="string";break;case"d":v=w.w||ct(w.v).toISOString(),g["office:value-type"]="date",g["office:date-value"]=ct(w.v).toISOString(),g["table:style-name"]="ce1";break;default:s.push(t);continue}var E=e(v);if(w.l&&w.l.Target){var S=w.l.Target;S="#"==S.charAt(0)?"#"+Pl(S.slice(1)):S,"#"==S.charAt(0)||S.match(/^\w+:/)||(S="../"+S),E=Ht("text:a",E,{"xlink:href":S.replace(/&/g,"&amp;")})}s.push("          "+Ht("table:table-cell",Ht("text:p",E,{}),g)+"\n")}else s.push(t)}}s.push("        </table:table-row>\n")}return s.push("      </table:table>\n"),s.join("")},a=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;Bi(a),a.ods=r;var i=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n'),e.push("  </style:style>\n"),++r}}));var n=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,t){var r=[wt],i=Wt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=Wt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==t.bookType?(r.push("<office:document"+i+s+">\n"),r.push(ha().replace(/office:document-meta/g,"office:meta"))):r.push("<office:document-content"+i+">\n"),a(r,e),r.push("  <office:body>\n"),r.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)r.push(n(e.Sheets[e.SheetNames[o]],e,o,t));return r.push("    </office:spreadsheet>\n"),r.push("  </office:body>\n"),"fods"==t.bookType?r.push("</office:document>"):r.push("</office:document-content>"),r.join("")}}();function Lh(e,t){if("fods"==t.bookType)return Ph(e,t);var r=Tt(),n="",a=[],i=[];return n="mimetype",vt(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",vt(r,n,Ph(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",vt(r,n,Dh(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",vt(r,n,wt+ha()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",vt(r,n,ca(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",vt(r,n,oa(a)),r}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Mh(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Fh(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):_(Mt(e))}function Uh(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function Bh(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),n=0;return e.forEach((function(e){r.set(e,n),n+=e.length})),r}function Wh(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=255&a;e[t+15]|=r>=0?0:128}function Hh(e,t){var r=t?t[0]:0,n=127&e[r];e:if(e[r++]>=128){if(n|=(127&e[r])<<7,e[r++]<128)break e;if(n|=(127&e[r])<<14,e[r++]<128)break e;if(n|=(127&e[r])<<21,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),n}function Gh(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Vh(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function jh(e){var t=[],r=[0];while(r[0]<e.length){var n=r[0],a=Hh(e,r),i=7&a;a=Math.floor(a/8);var s,o=0;if(0==a)break;switch(i){case 0:var l=r[0];while(e[r[0]++]>=128);s=e.slice(l,r[0]);break;case 5:o=4,s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Hh(e,r),s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var f={data:s,type:i};null==t[a]?t[a]=[f]:t[a].push(f)}return t}function zh(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Gh(8*r+e.type)),2==e.type&&t.push(Gh(e.data.length)),t.push(e.data))}))})),Bh(t)}function $h(e){var t,r=[],n=[0];while(n[0]<e.length){var a=Hh(e,n),i=jh(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:Vh(i[1][0].data),messages:[]};i[2].forEach((function(t){var r=jh(t.data),a=Vh(r[3][0].data);s.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a})),(null==(t=i[3])?void 0:t[0])&&(s.merge=Vh(i[3][0].data)>>>0>0),r.push(s)}return r}function Xh(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Gh(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Gh(+!!e.merge),type:0}]);var n=[];e.messages.forEach((function(e){n.push(e.data),e.meta[3]=[{type:0,data:Gh(e.data.length)}],r[2].push({data:zh(e.meta),type:2})}));var a=zh(r);t.push(Gh(a.length)),t.push(a),n.forEach((function(e){return t.push(e)}))})),Bh(t)}function Kh(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));var r=[0],n=Hh(t,r),a=[];while(r[0]<t.length){var i=3&t[r[0]];if(0!=i){var s=0,o=0;if(1==i?(o=4+(t[r[0]]>>2&7),s=(224&t[r[0]++])<<3,s|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==i?(s=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(s=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[Bh(a)],0==s)throw new Error("Invalid offset 0");if(s>a[0].length)throw new Error("Invalid offset beyond length");if(o>=s){a.push(a[0].slice(-s)),o-=s;while(o>=a[a.length-1].length)a.push(a[a.length-1]),o-=a[a.length-1].length}a.push(a[0].slice(-s,-s+o))}else{var l=t[r[0]++]>>2;if(l<60)++l;else{var f=l-59;l=t[r[0]],f>1&&(l|=t[r[0]+1]<<8),f>2&&(l|=t[r[0]+2]<<16),f>3&&(l|=t[r[0]+3]<<24),l>>>=0,l++,r[0]+=f}a.push(t.slice(r[0],r[0]+l)),r[0]+=l}}var c=Bh(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function Yh(e){var t=[],r=0;while(r<e.length){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Kh(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Bh(t)}function Jh(e){var t=[],r=0;while(r<e.length){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=Gh(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=255&s,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return Bh(t)}function Zh(e,t){var r=new Uint8Array(32),n=Mh(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Wh(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function qh(e,t){var r=new Uint8Array(32),n=Mh(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Qh(e){var t=jh(e);return Hh(t[1][0].data)}function eu(e,t,r){var n,a,i,s;if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";var o=(null==(s=null==(i=e[8])?void 0:i[0])?void 0:s.data)&&Vh(e[8][0].data)>0||!1;if(o)throw"Math only works with normal offsets";for(var l=0,f=Mh(e[7][0].data),c=0,h=[],u=Mh(e[4][0].data),d=0,p=[],m=0;m<t.length;++m)if(null!=t[m]){var g,v;switch(f.setUint16(2*m,c,!0),u.setUint16(2*m,d,!0),typeof t[m]){case"string":g=Zh({t:"s",v:t[m]},r),v=qh({t:"s",v:t[m]},r);break;case"number":g=Zh({t:"n",v:t[m]},r),v=qh({t:"n",v:t[m]},r);break;case"boolean":g=Zh({t:"b",v:t[m]},r),v=qh({t:"b",v:t[m]},r);break;default:throw new Error("Unsupported value "+t[m])}h.push(g),c+=g.length,p.push(v),d+=v.length,++l}else f.setUint16(2*m,65535,!0),u.setUint16(2*m,65535);for(e[2][0].data=Gh(l);m<e[7][0].data.length/2;++m)f.setUint16(2*m,65535,!0),u.setUint16(2*m,65535,!0);return e[6][0].data=Bh(h),e[3][0].data=Bh(p),l}function tu(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=Yr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(Jr(n)));var i=vu(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&s.push(e)}))}));var o={},l=[],f=$e.read(t.numbers,{type:"base64"});f.FileIndex.map((function(e,t){return[e,f.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];if(2==t.type&&t.name.match(/\.iwa/)){var n=t.content,a=Yh(n),i=$h(a);i.forEach((function(e){l.push(e.id),o[e.id]={deps:[],location:r,type:Vh(e.messages[0].meta[1][0].data)}}))}})),l.sort((function(e,t){return e-t}));var c=l.filter((function(e){return e>1})).map((function(e){return[e,Gh(e)]}));f.FileIndex.map((function(e,t){return[e,f.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1];if(t.name.match(/\.iwa/)){var r=$h(Yh(t.content));r.forEach((function(e){e.messages.forEach((function(t){c.forEach((function(t){e.messages.some((function(e){return 11006!=Vh(e.meta[1][0].data)&&Uh(e.data,t[1])}))&&o[t[0]].deps.push(e.id)}))}))}))}}));for(var h,u=$e.find(f,o[1].location),d=$h(Yh(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(h=m)}var g=Qh(jh(h.messages[0].data)[1][0].data);for(u=$e.find(f,o[g].location),d=$h(Yh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);for(g=Qh(jh(h.messages[0].data)[2][0].data),u=$e.find(f,o[g].location),d=$h(Yh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);for(g=Qh(jh(h.messages[0].data)[2][0].data),u=$e.find(f,o[g].location),d=$h(Yh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);var v=jh(h.messages[0].data);v[6][0].data=Gh(n.e.r+1),v[7][0].data=Gh(n.e.c+1);for(var T=Qh(v[46][0].data),w=$e.find(f,o[T].location),b=$h(Yh(w.content)),E=0;E<b.length;++E)if(b[E].id==T)break;if(b[E].id!=T)throw"Bad ColumnRowUIDMapArchive";var S=jh(b[E].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var A=0;A<=n.e.c;++A){var y=[];y[1]=y[2]=[{type:0,data:Gh(A+420690)}],S[1].push({type:2,data:zh(y)}),S[2].push({type:0,data:Gh(A)}),S[3].push({type:0,data:Gh(A)})}S[4]=[],S[5]=[],S[6]=[];for(var _=0;_<=n.e.r;++_)y=[],y[1]=y[2]=[{type:0,data:Gh(_+726270)}],S[4].push({type:2,data:zh(y)}),S[5].push({type:0,data:Gh(_)}),S[6].push({type:0,data:Gh(_)});b[E].messages[0].data=zh(S),w.content=Jh(Xh(b)),w.size=w.content.length,delete v[46];var O=jh(v[4][0].data);O[7][0].data=Gh(n.e.r+1);var x=jh(O[1][0].data),C=Qh(x[2][0].data);if(w=$e.find(f,o[C].location),b=$h(Yh(w.content)),b[0].id!=C)throw"Bad HeaderStorageBucket";var R=jh(b[0].messages[0].data);for(_=0;_<i.length;++_){var k=jh(R[2][0].data);k[1][0].data=Gh(_),k[4][0].data=Gh(i[_].length),R[2][_]={type:R[2][0].type,data:zh(k)}}b[0].messages[0].data=zh(R),w.content=Jh(Xh(b)),w.size=w.content.length;var N=Qh(O[2][0].data);if(w=$e.find(f,o[N].location),b=$h(Yh(w.content)),b[0].id!=N)throw"Bad HeaderStorageBucket";for(R=jh(b[0].messages[0].data),A=0;A<=n.e.c;++A)k=jh(R[2][0].data),k[1][0].data=Gh(A),k[4][0].data=Gh(n.e.r+1),R[2][A]={type:R[2][0].type,data:zh(k)};b[0].messages[0].data=zh(R),w.content=Jh(Xh(b)),w.size=w.content.length;var I=Qh(O[4][0].data);(function(){for(var e,t=$e.find(f,o[I].location),r=$h(Yh(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==I&&(e=a)}var i=jh(e.messages[0].data);i[3]=[];var l=[];s.forEach((function(e,t){l[1]=[{type:0,data:Gh(t)}],l[2]=[{type:0,data:Gh(1)}],l[3]=[{type:2,data:Fh(e)}],i[3].push({type:2,data:zh(l)})})),e.messages[0].data=zh(i);var c=Xh(r),h=Jh(c);t.content=h,t.size=t.content.length})();var D=jh(O[3][0].data),P=D[1][0];delete D[2];var L=jh(P.data),M=Qh(L[2][0].data);(function(){for(var e,t=$e.find(f,o[M].location),r=$h(Yh(t.content)),a=0;a<r.length;++a){var l=r[a];l.id==M&&(e=l)}var c=jh(e.messages[0].data);delete c[6],delete D[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=jh(h);u+=eu(p,i[d],s),p[1][0].data=Gh(d),c[5].push({data:zh(p),type:2})}c[1]=[{type:0,data:Gh(n.e.c+1)}],c[2]=[{type:0,data:Gh(n.e.r+1)}],c[3]=[{type:0,data:Gh(u)}],c[4]=[{type:0,data:Gh(n.e.r+1)}],e.messages[0].data=zh(c);var m=Xh(r),g=Jh(m);t.content=g,t.size=t.content.length})(),P.data=zh(L),O[3][0].data=zh(D),v[4][0].data=zh(O),h.messages[0].data=zh(v);var F=Xh(d),U=Jh(F);return u.content=U,u.size=u.content.length,f}function ru(e){return function(t){for(var r=0;r!=e.length;++r){var n=e[r];void 0===t[n[0]]&&(t[n[0]]=n[1]),"n"===n[2]&&(t[n[0]]=Number(t[n[0]]))}}}function nu(e){ru([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function au(e,t){return"ods"==t.bookType?Lh(e,t):"numbers"==t.bookType?tu(e,t):"xlsb"==t.bookType?iu(e,t):su(e,t)}function iu(e,t){ks=1024,e&&!e.SSF&&(e.SSF=ut(z)),e&&e.SSF&&(He(),We(e.SSF),t.revssf=Qe(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ll?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=Hs.indexOf(t.bookType)>-1,a=ta();nu(t=t||{});var i=Tt(),s="",o=0;if(t.cellXfs=[],Bl(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",vt(i,s,pa(e.Props,t)),a.coreprops.push(s),sa(t.rels,2,s,na.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],f=0;f<e.SheetNames.length;++f)2!=(e.Workbook.Sheets[f]||{}).Hidden&&l.push(e.SheetNames[f]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,vt(i,s,va(e.Props,t)),a.extprops.push(s),sa(t.rels,3,s,na.EXT_PROPS),e.Custprops!==e.Props&&Je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",vt(i,s,Ta(e.Custprops,t)),a.custprops.push(s),sa(t.rels,4,s,na.CUST_PROPS)),o=1;o<=e.SheetNames.length;++o){var c={"!id":{}},h=e.Sheets[e.SheetNames[o-1]],u=(h||{})["!type"]||"sheet";switch(u){case"chart":default:s="xl/worksheets/sheet"+o+"."+r,vt(i,s,Mc(o-1,s,t,e,c)),a.sheets.push(s),sa(t.wbrels,-1,"worksheets/sheet"+o+"."+r,na.WS[0])}if(h){var d=h["!comments"],p=!1,m="";d&&d.length>0&&(m="xl/comments"+o+"."+r,vt(i,m,Bc(d,m,t)),a.comments.push(m),sa(c,-1,"../comments"+o+"."+r,na.CMNT),p=!0),h["!legacy"]&&p&&vt(i,"xl/drawings/vmlDrawing"+o+".vml",Ns(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}c["!id"].rId1&&vt(i,aa(s),ia(c))}return null!=t.Strings&&t.Strings.length>0&&(s="xl/sharedStrings."+r,vt(i,s,Uc(t.Strings,s,t)),a.strs.push(s),sa(t.wbrels,-1,"sharedStrings."+r,na.SST)),s="xl/workbook."+r,vt(i,s,Lc(e,s,t)),a.workbooks.push(s),sa(t.rels,1,s,na.WB),s="xl/theme/theme1.xml",vt(i,s,ws(e.Themes,t)),a.themes.push(s),sa(t.wbrels,-1,"theme/theme1.xml",na.THEME),s="xl/styles."+r,vt(i,s,Fc(e,s,t)),a.styles.push(s),sa(t.wbrels,-1,"styles."+r,na.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",vt(i,s,e.vbaraw),a.vba.push(s),sa(t.wbrels,-1,"vbaProject.bin",na.VBA)),s="xl/metadata."+r,vt(i,s,Wc(s)),a.metadata.push(s),sa(t.wbrels,-1,"metadata."+r,na.XLMETA),vt(i,"[Content_Types].xml",ra(a,t)),vt(i,"_rels/.rels",ia(t.rels)),vt(i,"xl/_rels/workbook."+r+".rels",ia(t.wbrels)),delete t.revssf,delete t.ssf,i}function su(e,t){ks=1024,e&&!e.SSF&&(e.SSF=ut(z)),e&&e.SSF&&(He(),We(e.SSF),t.revssf=Qe(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ll?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Hs.indexOf(t.bookType)>-1,a=ta();nu(t=t||{});var i=Tt(),s="",o=0;if(t.cellXfs=[],Bl(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",vt(i,s,pa(e.Props,t)),a.coreprops.push(s),sa(t.rels,2,s,na.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],f=0;f<e.SheetNames.length;++f)2!=(e.Workbook.Sheets[f]||{}).Hidden&&l.push(e.SheetNames[f]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,vt(i,s,va(e.Props,t)),a.extprops.push(s),sa(t.rels,3,s,na.EXT_PROPS),e.Custprops!==e.Props&&Je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",vt(i,s,Ta(e.Custprops,t)),a.custprops.push(s),sa(t.rels,4,s,na.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]],d=(u||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+o+"."+r,vt(i,s,ql(o-1,t,e,h)),a.sheets.push(s),sa(t.wbrels,-1,"worksheets/sheet"+o+"."+r,na.WS[0])}if(u){var p=u["!comments"],m=!1,g="";if(p&&p.length>0){var v=!1;p.forEach((function(e){e[1].forEach((function(e){1==e.T&&(v=!0)}))})),v&&(g="xl/threadedComments/threadedComment"+o+"."+r,vt(i,g,Ds(p,c,t)),a.threadedcomments.push(g),sa(h,-1,"../threadedComments/threadedComment"+o+"."+r,na.TCMNT)),g="xl/comments"+o+"."+r,vt(i,g,Is(p,t)),a.comments.push(g),sa(h,-1,"../comments"+o+"."+r,na.CMNT),m=!0}u["!legacy"]&&m&&vt(i,"xl/drawings/vmlDrawing"+o+".vml",Ns(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&vt(i,aa(s),ia(h))}return null!=t.Strings&&t.Strings.length>0&&(s="xl/sharedStrings."+r,vt(i,s,_i(t.Strings,t)),a.strs.push(s),sa(t.wbrels,-1,"sharedStrings."+r,na.SST)),s="xl/workbook."+r,vt(i,s,yc(e,t)),a.workbooks.push(s),sa(t.rels,1,s,na.WB),s="xl/theme/theme1.xml",vt(i,s,ws(e.Themes,t)),a.themes.push(s),sa(t.wbrels,-1,"theme/theme1.xml",na.THEME),s="xl/styles."+r,vt(i,s,$i(e,t)),a.styles.push(s),sa(t.wbrels,-1,"styles."+r,na.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",vt(i,s,e.vbaraw),a.vba.push(s),sa(t.wbrels,-1,"vbaProject.bin",na.VBA)),s="xl/metadata."+r,vt(i,s,Cs()),a.metadata.push(s),sa(t.wbrels,-1,"metadata."+r,na.XLMETA),c.length>1&&(s="xl/persons/person.xml",vt(i,s,Ps(c,t)),a.people.push(s),sa(t.wbrels,-1,"persons/person.xml",na.PEOPLE)),vt(i,"[Content_Types].xml",ra(a,t)),vt(i,"_rels/.rels",ia(t.rels)),vt(i,"xl/_rels/workbook."+r+".rels",ia(t.wbrels)),delete t.revssf,delete t.ssf,i}function ou(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=b(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function lu(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Ye(t.file,$e.write(e,{type:E?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return $e.write(e,t)}function fu(e,t){var r=ut(t||{}),n=au(e,r);return cu(n,r)}function cu(e,t){var r={},n=E?"nodebuffer":"undefined"!==typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?$e.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!==typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(O(a))}return t.password&&"undefined"!==typeof encrypt_agile?lu(encrypt_agile(a,t.password),t):"file"===t.type?Ye(t.file,a):"string"==t.type?Lt(a):a}function hu(e,t){var r=t||{},n=rh(e,r);return lu(n,r)}function uu(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return w(Mt(n));case"binary":return Mt(n);case"string":return e;case"file":return Ye(t.file,n,"utf8");case"buffer":return E?S(n,"utf8"):"undefined"!==typeof TextEncoder?(new TextEncoder).encode(n):uu(n,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function du(e,t){switch(t.type){case"base64":return w(e);case"binary":return e;case"string":return e;case"file":return Ye(t.file,e,"binary");case"buffer":return E?S(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function pu(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?w(r):"string"==t.type?Lt(r):r;case"file":return Ye(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function mu(e,t){h(),Ac(e);var r=ut(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var n=mu(e,r);return r.type="array",O(n)}var a=0;if(r.sheet&&(a="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return uu(Qc(e,r),r);case"slk":case"sylk":return uu(wi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return uu(xh(e.Sheets[e.SheetNames[a]],r),r);case"txt":return du(Eu(e.Sheets[e.SheetNames[a]],r),r);case"csv":return uu(bu(e.Sheets[e.SheetNames[a]],r),r,"\ufeff");case"dif":return uu(bi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return pu(Ti.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return uu(Si.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return uu(Ii.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return uu(Ei.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return uu(Lh(e,r),r);case"wk1":return pu(Ai.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return pu(Ai.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),pu(Sh(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),hu(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return fu(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function gu(e,t,r,n,a,i,s,o){var l=Br(r),f=o.defval,c=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=s?e[r][d]:e[n[d]+l];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=i[d]){if(null==m)if("e"==p.t&&null===m)u[i[d]]=null;else if(void 0!==f)u[i[d]]=f;else{if(!c||null!==m)continue;u[i[d]]=null}else u[i[d]]=c&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:Qr(p,m,o);null!=m&&(h=!1)}}else{if(void 0===f)continue;null!=i[d]&&(u[i[d]]=f)}}return{row:u,isempty:h}}function vu(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},c=null!=f.range?f.range:e["!ref"];switch(1===f.header?n=1:"A"===f.header?n=2:Array.isArray(f.header)?n=3:null==f.header&&(n=0),typeof c){case"string":l=Zr(c);break;case"number":l=Zr(e["!ref"]),l.s.r=c;break;default:l=c}n>0&&(a=0);var h=Br(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,w={};g&&!e[v]&&(e[v]=[]);var b=f.skipHidden&&e["!cols"]||[],E=f.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(b[T]||{}).hidden)switch(u[T]=Vr(T),r=g?e[v][T]:e[u[T]+h],n){case 1:i[T]=T-l.s.c;break;case 2:i[T]=u[T];break;case 3:i[T]=f.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=s=Qr(r,null,f),m=w[s]||0,m){do{o=s+"_"+m++}while(w[o]);w[s]=m,w[o]=1}else w[s]=1;i[T]=o}for(v=l.s.r+a;v<=l.e.r;++v)if(!(E[v]||{}).hidden){var S=gu(e,l,v,u,n,i,g,f);(!1===S.isempty||(1===n?!1!==f.blankrows:f.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var Tu=/"/g;function wu(e,t,r,n,a,i,s,o){for(var l=!0,f=[],c="",h=Br(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==d)c="";else if(null!=d.v){l=!1,c=""+(o.rawNumbers&&"n"==d.t?d.v:Qr(d,null,o));for(var p=0,m=0;p!==c.length;++p)if((m=c.charCodeAt(p))===a||m===i||34===m||o.forceQuotes){c='"'+c.replace(Tu,'""')+'"';break}"ID"==c&&(c='"ID"')}else null==d.f||d.F?c="":(l=!1,c="="+d.f,c.indexOf(",")>=0&&(c='"'+c.replace(Tu,'""')+'"'));f.push(c)}return!1===o.blankrows&&l?null:f.join(s)}function bu(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=Zr(e["!ref"]),i=void 0!==n.FS?n.FS:",",s=i.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),f=new RegExp(("|"==i?"\\|":i)+"+$"),c="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=Vr(p));for(var m=0,g=a.s.r;g<=a.e.r;++g)(d[g]||{}).hidden||(c=wu(e,a,g,h,s,l,i,n),null!=c&&(n.strip&&(c=c.replace(f,"")),(c||!1!==n.blankrows)&&r.push((m++?o:"")+c)));return delete n.dense,r.join("")}function Eu(e,t){t||(t={}),t.FS="\t",t.RS="\n";var r=bu(e,t);if("undefined"==typeof d||"string"==t.type)return r;var n=d.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function Su(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var a,i=Zr(e["!ref"]),s="",o=[],l=[],f=Array.isArray(e);for(a=i.s.c;a<=i.e.c;++a)o[a]=Vr(a);for(var c=i.s.r;c<=i.e.r;++c)for(s=Br(c),a=i.s.c;a<=i.e.c;++a)if(r=o[a]+s,t=f?(e[c]||[])[a]:e[r],n="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}l[l.length]=r+"="+n}return l}function Au(e,t,r){var n,a=r||{},i=+!a.skipHeader,s=e||{},o=0,l=0;if(s&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var f="string"==typeof a.origin?Xr(a.origin):a.origin;o=f.r,l=f.c}var c={s:{c:0,r:0},e:{c:l,r:o+t.length-1+i}};if(s["!ref"]){var h=Zr(s["!ref"]);c.e.c=Math.max(c.e.c,h.e.c),c.e.r=Math.max(c.e.r,h.e.r),-1==o&&(o=h.e.r+1,c.e.r=o+t.length-1+i)}else-1==o&&(o=0,c.e.r=t.length-1+i);var u=a.header||[],d=0;t.forEach((function(e,t){Je(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var f=e[r],c="z",h="",p=Kr({c:l+d,r:o+t+i});n=_u(s,p),!f||"object"!==typeof f||f instanceof Date?("number"==typeof f?c="n":"boolean"==typeof f?c="b":"string"==typeof f?c="s":f instanceof Date?(c="d",a.cellDates||(c="n",f=rt(f)),h=a.dateNF||z[14]):null===f&&a.nullError&&(c="e",f=0),n?(n.t=c,n.v=f,delete n.w,delete n.R,h&&(n.z=h)):s[p]=n={t:c,v:f},h&&(n.z=h)):s[p]=f}))})),c.e.c=Math.max(c.e.c,l+u.length-1);var p=Br(o);if(i)for(d=0;d<u.length;++d)s[Vr(d+l)+p]={t:"s",v:u[d]};return s["!ref"]=Jr(c),s}function yu(e,t){return Au(null,e,t)}function _u(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=Xr(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return _u(e,Kr("number"!=typeof t?t:{r:t,c:r||0}))}function Ou(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}function xu(){return{SheetNames:[],Sheets:{}}}function Cu(e,t,r,n){var a=1;if(!r)for(;a<=65535;++a,r=void 0)if(-1==e.SheetNames.indexOf(r="Sheet"+a))break;if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535;++a)if(-1==e.SheetNames.indexOf(r=s+a))break}if(Ec(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function Ru(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=Ou(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function ku(e,t){return e.z=t,e}function Nu(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function Iu(e,t,r){return Nu(e,"#"+t,r)}function Du(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})}function Pu(e,t,r,n){for(var a="string"!=typeof t?t:Zr(t),i="string"==typeof t?t:Jr(t),s=a.s.r;s<=a.e.r;++s)for(var o=a.s.c;o<=a.e.c;++o){var l=_u(e,s,o);l.t="n",l.F=i,delete l.v,s==a.s.r&&o==a.s.c&&(l.f=r,n&&(l.D=!0))}return e}var Lu={encode_col:Vr,encode_row:Br,encode_cell:Kr,encode_range:Jr,decode_col:Gr,decode_row:Ur,split_cell:$r,decode_cell:Xr,decode_range:Yr,format_cell:Qr,sheet_add_aoa:tn,sheet_add_json:Au,sheet_add_dom:Ch,aoa_to_sheet:rn,json_to_sheet:yu,table_to_sheet:Rh,table_to_book:kh,sheet_to_csv:bu,sheet_to_txt:Eu,sheet_to_json:vu,sheet_to_html:xh,sheet_to_formulae:Su,sheet_to_row_object_array:vu,sheet_get_cell:_u,book_new:xu,book_append_sheet:Cu,book_set_sheet_visibility:Ru,cell_set_number_format:ku,cell_set_hyperlink:Nu,cell_set_internal_link:Iu,cell_add_comment:Du,sheet_set_array_formula:Pu,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};n.version},"520a":function(e,t,r){"use strict";var n=r("0bfb"),a=RegExp.prototype.exec,i=String.prototype.replace,s=a,o="lastIndex",l=function(){var e=/a/,t=/b*/g;return a.call(e,"a"),a.call(t,"a"),0!==e[o]||0!==t[o]}(),f=void 0!==/()??/.exec("")[1],c=l||f;c&&(s=function(e){var t,r,s,c,h=this;return f&&(r=new RegExp("^"+h.source+"$(?!\\s)",n.call(h))),l&&(t=h[o]),s=a.call(h,e),l&&s&&(h[o]=h.global?s.index+s[0].length:t),f&&s&&s.length>1&&i.call(s[0],r,(function(){for(c=1;c<arguments.length-2;c++)void 0===arguments[c]&&(s[c]=void 0)})),s}),e.exports=s},"5f1b":function(e,t,r){"use strict";var n=r("23c6"),a=RegExp.prototype.exec;e.exports=function(e,t){var r=e.exec;if("function"===typeof r){var i=r.call(e,t);if("object"!==typeof i)throw new TypeError("RegExp exec method returned something other than an Object or null");return i}if("RegExp"!==n(e))throw new TypeError("RegExp#exec called on incompatible receiver");return a.call(e,t)}},a481:function(e,t,r){"use strict";var n=r("cb7c"),a=r("4bf8"),i=r("9def"),s=r("4588"),o=r("0390"),l=r("5f1b"),f=Math.max,c=Math.min,h=Math.floor,u=/\$([$&`']|\d\d?|<[^>]*>)/g,d=/\$([$&`']|\d\d?)/g,p=function(e){return void 0===e?e:String(e)};r("214f")("replace",2,(function(e,t,r,m){return[function(n,a){var i=e(this),s=void 0==n?void 0:n[t];return void 0!==s?s.call(n,i,a):r.call(String(i),n,a)},function(e,t){var a=m(r,e,this,t);if(a.done)return a.value;var h=n(e),u=String(this),d="function"===typeof t;d||(t=String(t));var v=h.global;if(v){var T=h.unicode;h.lastIndex=0}var w=[];while(1){var b=l(h,u);if(null===b)break;if(w.push(b),!v)break;var E=String(b[0]);""===E&&(h.lastIndex=o(u,i(h.lastIndex),T))}for(var S="",A=0,y=0;y<w.length;y++){b=w[y];for(var _=String(b[0]),O=f(c(s(b.index),u.length),0),x=[],C=1;C<b.length;C++)x.push(p(b[C]));var R=b.groups;if(d){var k=[_].concat(x,O,u);void 0!==R&&k.push(R);var N=String(t.apply(void 0,k))}else N=g(_,u,O,x,R,t);O>=A&&(S+=u.slice(A,O)+N,A=O+_.length)}return S+u.slice(A)}];function g(e,t,n,i,s,o){var l=n+e.length,f=i.length,c=d;return void 0!==s&&(s=a(s),c=u),r.call(o,c,(function(r,a){var o;switch(a.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,n);case"'":return t.slice(l);case"<":o=s[a.slice(1,-1)];break;default:var c=+a;if(0===c)return r;if(c>f){var u=h(c/10);return 0===u?r:u<=f?void 0===i[u-1]?a.charAt(1):i[u-1]+a.charAt(1):r}o=i[c-1]}return void 0===o?"":o}))}}))},b0c5:function(e,t,r){"use strict";var n=r("520a");r("5ca1")({target:"RegExp",proto:!0,forced:n!==/./.exec},{exec:n})}}]);