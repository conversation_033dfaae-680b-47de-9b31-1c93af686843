(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c4475ce0"],{"03b6":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-descriptions",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"margin-top",attrs:{column:t.columns,size:"mini",border:"",labelClassName:"app-desc-label"}},[n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        应用\n        "),n("el-tooltip",{attrs:{content:"应用名称",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",{staticStyle:{"min-width":"160px"}},[t._v("\n        "+t._s(t.appMeta.name)+"\n        "),n("clipboard-icon",{attrs:{text:t.appMeta.name}}),t._v(" "),t.appMeta.level?n("el-tooltip",{attrs:{content:"服务等级",placement:"top"}},[n("el-tag",{staticStyle:{"margin-left":"10px","font-size":"12px"},attrs:{size:"mini",type:"warning",title:"应用级别"}},[t._v(t._s(t.appMeta.level))])],1):t._e()],1)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("第一负责人\n        "),n("el-tooltip",{attrs:{content:"应用的主负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.mainOwner?t.appMeta.mainOwner:"--")+"\n      "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n      ")])],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        负责人\n        "),n("el-tooltip",{attrs:{content:"应用的负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("span",[n("el-tooltip",{attrs:{content:t.appMeta.owners&&t.appMeta.owners.length>0?t.appMeta.owners.join(","):"没有配置负责人",placement:"top"}},[n("span",[t._v("\n            "+t._s(t.appMeta.owners&&t.appMeta.owners.length>0?t.appMeta.owners.join(",").length>24?t.appMeta.owners.join(",").substring(0,24)+"...":t.appMeta.owners.join(","):"--")+"\n          ")])])],1)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        发布权限\n        "),n("el-tooltip",{attrs:{content:"如果配置了权限，则拥有权限的同学才能发布。如果没有配置，则所有人都可发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),t.appMeta.orgs&&t.appMeta.orgs.length>0?n("div",{staticStyle:{display:"inline-block"}},[n("div",{staticStyle:{"max-width":"400px"}},[t._v("\n          部门："+t._s(t.appMeta.orgs.join(","))+"\n          "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.orgPage}},[t._v("查看部门成员\n          ")])],1)]):n("div",{staticStyle:{display:"inline-block"}},[t._v("-任何人-")]),t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appAuthPage}},[t._v("编辑\n      ")])],2),t._v(" "),n("el-descriptions-item",{attrs:{span:2}},[n("template",{slot:"label"},[t._v("\n        发布时间窗口\n        "),n("el-tooltip",{attrs:{content:"如果配置了时间窗口，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[!1===this.$settings.timeWindow.open?[t._v("\n          - 系统已关闭时间窗口限制 -\n        ")]:t.appMeta.timeWindow&&t.appMeta.timeWindow.length>0?[n("el-tooltip",{attrs:{effect:"light",placement:"top"}},[n("div",{attrs:{slot:"content"},slot:"content"},[n("pre",[t._v(t._s(t.appMeta.timeWindowDesc))])]),t._v(" "),n("span",{staticStyle:{display:"inline-block"}},[t._v("\n              "+t._s(t.appMeta.timeWindowDesc.substring(0,60).replaceAll("\n","  ")+(t.appMeta.timeWindowDesc.length>60?"...":""))+"\n            ")])]),t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")]),t._v(" "),this.excludeNamespaces.length>0?n("div",{staticStyle:{color:"#d45e0c","font-weight":"bold","max-width":"800px"}},[t._v("以下环境不受限制："+t._s(this.excludeNamespaces.join(", ")))]):t._e()]:[t._v("-任何时间-\n          "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")])]],2)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        描述\n        "),n("el-tooltip",{attrs:{content:"应用描述信息",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.remark)+"\n    ")],2)],1)],1)},r=[],o=(n("c5f6"),n("b562")),i=n("da37"),p={name:"pipeline-app",components:{ClipboardIcon:i["a"]},props:{app:{type:String,default:"",required:!0},columns:{type:Number,default:2,required:!1}},mounted:function(){this.loadApp()},watch:{app:function(t){this.loadApp()}},computed:{excludeNamespaces:function(){return this.$settings.timeWindow&&this.$settings.timeWindow.excludeNamespaces?this.$settings.timeWindow.excludeNamespaces:[]}},data:function(){return{loading:!1,appMeta:{}}},methods:{loadApp:function(){var t=this;this.app?(this.loading=!0,Object(o["j"])(this.app).then((function(e){t.appMeta=e.data})).catch((function(e){t.$message.error(e.message),t.appMeta={}})).finally((function(){t.loading=!1}))):console.log(this.app)},appAuthPage:function(){var t=this.$router.resolve({name:"app-permission",query:{app:this.app}}).href;window.open(t,"_blank")},orgPage:function(){var t=this.$router.resolve({name:"auth-org",query:{}}).href;window.open(t,"_blank")},appEditPage:function(){var t=this.$router.resolve({name:"app-list",query:{showEditDialog:"true",app:this.app}}).href;window.open(t,"_blank")}}},s=p,c=(n("9df5"),n("2877")),l=Object(c["a"])(s,a,r,!1,null,null,null);e["a"]=l.exports},"11e9":function(t,e,n){var a=n("52a7"),r=n("4630"),o=n("6821"),i=n("6a99"),p=n("69a8"),s=n("c69a"),c=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?c:function(t,e){if(t=o(t),e=i(e,!0),s)try{return c(t,e)}catch(n){}if(p(t,e))return r(!a.f.call(t,e),t[e])}},"181b":function(t,e,n){},"1e42":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline"}},[n("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},r=[],o=(n("a481"),n("25ca")),i=n("21a6"),p=n.n(i),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=o["a"].table_to_book(t,{raw:!0}),n=o["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var a=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";p.a.saveAs(new Blob([n],{type:"application/octet-stream"}),a)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return n}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,l=n("2877"),u=Object(l["a"])(c,a,r,!1,null,null,null);e["a"]=u.exports},"5dbc":function(t,e,n){var a=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var o,i=e.constructor;return i!==n&&"function"==typeof i&&(o=i.prototype)!==n.prototype&&a(o)&&r&&r(t,o),t}},"71df":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[t.showHistory?n("div",{staticStyle:{"margin-bottom":"10px",height:"30px",overflow:"hidden"}},[n("label",{staticStyle:{display:"inline-block",width:"80px",color:"#999","font-size":"14px","padding-right":"12px","text-align":"right"}},[t._v("访问历史")]),t._v(" "),t._l(t.recentApps,(function(e){return n("el-button",{staticStyle:{"font-weight":"bold","margin-bottom":"5px","font-size":"14px",padding:"7px"},attrs:{size:"mini",type:"primary",plain:e!==t.currApp},on:{click:function(n){return t.changeCurrAppByBtn(e)}}},[t._v(t._s(e)+"\n    ")])}))],2):t._e(),t._v(" "),n("div",[n("el-form",[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"选择应用","label-width":"80px"}},[n("el-select",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{filterable:"",placeholder:"请选择应用"},on:{change:t.changeCurrAppBySelector},model:{value:t.currApp,callback:function(e){t.currApp=e},expression:"currApp"}},t._l(t.apps,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1)],1),t._v(" "),t.currApp&&t.showDetail?n("pipeline-app",{attrs:{app:this.currApp,columns:4}}):t._e()],1)},r=[],o=n("db72"),i=n("c24f"),p=n("b562"),s=n("03b6"),c={name:"app-selector2",components:{PipelineApp:s["a"]},props:{showDetail:{type:Boolean,default:!1},showHistory:{type:Boolean,default:!0},updateHistory:{type:Boolean,default:!1}},data:function(){return{apps:[],recentApps:[],currApp:"",reloadHistory:!1}},watch:{currApp:function(t){this.appChange()}},computed:{},mounted:function(){var t=this.$route.query.app;t&&(this.currApp=t),this.loadApps(),this.loadRecentApps()},methods:{appChange:function(){var t=this;this.$router.push({query:Object(o["a"])(Object(o["a"])({},this.$route.query),{},{tab:this.currTab})}),this.updateHistory&&Object(i["e"])(this.currApp).then((function(e){t.reloadHistory&&t.loadRecentApps()})).catch((function(t){})),this.$emit("change",this.currApp)},changeCurrAppByBtn:function(t){this.reloadHistory=!1,this.currApp=t},changeCurrAppBySelector:function(){this.reloadHistory=!0},loadRecentApps:function(){var t=this;Object(i["a"])().then((function(e){t.recentApps=e.data.recentApps,!t.currApp&&t.recentApps.length>0&&(t.currApp=t.recentApps[0])})).catch((function(t){console.log(t)}))},loadApps:function(){var t=this;Object(p["l"])().then((function(e){t.apps=e.data})).catch((function(e){t.$message.error("加载应用数据出错！ "+e.message)}))}}},l=c,u=n("2877"),d=Object(u["a"])(l,a,r,!1,null,null,null);e["a"]=d.exports},"8b97":function(t,e,n){var a=n("d3f4"),r=n("cb7c"),o=function(t,e){if(r(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:a(t,n),t}}({},!1):void 0),check:o}},9093:function(t,e,n){var a=n("ce10"),r=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return a(t,r)}},"9add":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[n("div",[n("div",{staticStyle:{width:"480px",float:"left"}},[n("app-selector2",{attrs:{"show-history":!1},on:{change:t.changeCurrApp}})],1),t._v(" "),n("div",{staticStyle:{float:"left",padding:"10px"}},[n("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[t._v("查询")]),t._v(" "),t.data.length>0?n("export-button",{staticStyle:{"margin-left":"20px"},attrs:{"table-ref":this.$refs.table001}}):t._e()],1)]),t._v(" "),n("div",[n("el-table",{ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini",stripe:!0}},[n("el-table-column",{attrs:{prop:"cluster",label:"集群"}}),t._v(" "),n("el-table-column",{attrs:{prop:"namespace",label:"命名空间"}}),t._v(" "),n("el-table-column",{attrs:{prop:"app",label:"应用","min-width":"120"}}),t._v(" "),n("el-table-column",{attrs:{prop:"name",label:"端口名"}}),t._v(" "),n("el-table-column",{attrs:{prop:"protocol",label:"协议"}}),t._v(" "),n("el-table-column",{attrs:{prop:"port",label:"Port"}}),t._v(" "),n("el-table-column",{attrs:{prop:"targetPort",label:"TargetPort"}}),t._v(" "),n("el-table-column",{attrs:{prop:"nodePort",label:"NodePort"}}),t._v(" "),n("el-table-column",{attrs:{prop:"clusterInnerAddress",label:"集群内地址","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterInnerAddress,(function(e){return n("div",[t._v("\n            "+t._s(e)+"\n          ")])}))}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"clusterOuterAddress",label:"集群外地址","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.clusterOuterAddress,(function(e){return n("div",[t._v("\n            "+t._s(e)+"\n          ")])}))}}])})],1)],1)])},r=[],o=n("71df"),i=n("b775");function p(t){return Object(i["a"])({url:"/openapi/app/address?app="+t,method:"get"})}var s=n("1e42"),c={components:{ExportButton:s["a"],AppSelector2:o["a"]},data:function(){return{currApp:"",data:[],loading:!1}},computed:{},methods:{changeCurrApp:function(t){this.currApp=t},loadData:function(){var t=this;this.currApp?(this.loading=!0,p(this.currApp).then((function(e){t.data=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.error("请选择应用")},podPage:function(t,e,n){var a=this.$router.resolve({name:"pod-index",query:{cluster:t,namespace:e,app:n}});window.open(a.href,"_blank")}}},l=c,u=n("2877"),d=Object(u["a"])(l,a,r,!1,null,"d8924494",null);e["default"]=d.exports},"9df5":function(t,e,n){"use strict";n("181b")},aa77:function(t,e,n){var a=n("5ca1"),r=n("be13"),o=n("79e5"),i=n("fdef"),p="["+i+"]",s="​",c=RegExp("^"+p+p+"*"),l=RegExp(p+p+"*$"),u=function(t,e,n){var r={},p=o((function(){return!!i[t]()||s[t]()!=s})),c=r[t]=p?e(d):i[t];n&&(r[n]=c),a(a.P+a.F*p,"String",r)},d=u.trim=function(t,e){return t=String(r(t)),1&e&&(t=t.replace(c,"")),2&e&&(t=t.replace(l,"")),t};t.exports=u},b562:function(t,e,n){"use strict";n.d(e,"p",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"l",(function(){return p})),n.d(e,"j",(function(){return s})),n.d(e,"e",(function(){return c})),n.d(e,"i",(function(){return l})),n.d(e,"h",(function(){return u})),n.d(e,"m",(function(){return d})),n.d(e,"o",(function(){return f})),n.d(e,"g",(function(){return m})),n.d(e,"f",(function(){return h})),n.d(e,"c",(function(){return v})),n.d(e,"k",(function(){return g})),n.d(e,"r",(function(){return b})),n.d(e,"n",(function(){return _})),n.d(e,"q",(function(){return y})),n.d(e,"d",(function(){return x}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function p(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function u(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function g(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function b(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}function x(){return Object(a["a"])({url:"/v1/app/count-pipelines",method:"get"})}},c5f6:function(t,e,n){"use strict";var a=n("7726"),r=n("69a8"),o=n("2d95"),i=n("5dbc"),p=n("6a99"),s=n("79e5"),c=n("9093").f,l=n("11e9").f,u=n("86cc").f,d=n("aa77").trim,f="Number",m=a[f],h=m,v=m.prototype,g=o(n("2aeb")(v))==f,b="trim"in String.prototype,_=function(t){var e=p(t,!1);if("string"==typeof e&&e.length>2){e=b?e.trim():d(e,3);var n,a,r,o=e.charCodeAt(0);if(43===o||45===o){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:a=2,r=49;break;case 79:case 111:a=8,r=55;break;default:return+e}for(var i,s=e.slice(2),c=0,l=s.length;c<l;c++)if(i=s.charCodeAt(c),i<48||i>r)return NaN;return parseInt(s,a)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(g?s((function(){v.valueOf.call(n)})):o(n)!=f)?i(new h(_(e)),n,m):_(e)};for(var y,x=n("9e1e")?c(h):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;x.length>w;w++)r(h,y=x[w])&&!r(m,y)&&u(m,y,l(h,y));m.prototype=v,v.constructor=m,n("2aba")(a,f,m)}},da37:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[n("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?n("span",[t._v(t._s(this.buttonText))]):t._e()])},r=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},i=o,p=n("2877"),s=Object(p["a"])(i,a,r,!1,null,null,null);e["a"]=s.exports},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);