(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2e55a390"],{"2fdb":function(t,e,a){"use strict";var i=a("5ca1"),l=a("d2c8"),o="includes";i(i.P+i.F*a("5147")(o),"String",{includes:function(t){return!!~l(this,t,o).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"4c4c":function(t,e,a){"use strict";a("f473")},5147:function(t,e,a){var i=a("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[i]=!1,!"/./"[t](e)}catch(l){}}return!0}},"529b":function(t,e,a){},6762:function(t,e,a){"use strict";var i=a("5ca1"),l=a("c366")(!0);i(i.P,"Array",{includes:function(t){return l(this,t,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"817d":function(t,e,a){var i,l,o;(function(n,s){l=[e,a("313e")],i=s,o="function"===typeof i?i.apply(e,l):i,void 0===o||(t.exports=o)})(0,(function(t,e){var a=function(t){"undefined"!==typeof console&&console&&console.error&&console.error(t)};if(e){var i=["#2ec7c9","#b6a2de","#5ab1ef","#ffb980","#d87a80","#8d98b3","#e5cf0d","#97b552","#95706d","#dc69aa","#07a2a4","#9a7fd1","#588dd5","#f5994e","#c05050","#59678c","#c9ab00","#7eb00a","#6f5553","#c14089"],l={color:i,title:{textStyle:{fontWeight:"normal",color:"#008acd"}},visualMap:{itemWidth:15,color:["#5ab1ef","#e0ffff"]},toolbox:{iconStyle:{normal:{borderColor:i[0]}}},tooltip:{backgroundColor:"rgba(50,50,50,0.5)",axisPointer:{type:"line",lineStyle:{color:"#008acd"},crossStyle:{color:"#008acd"},shadowStyle:{color:"rgba(200,200,200,0.2)"}}},dataZoom:{dataBackgroundColor:"#efefff",fillerColor:"rgba(182,162,222,0.2)",handleColor:"#008acd"},grid:{borderColor:"#eee"},categoryAxis:{axisLine:{lineStyle:{color:"#008acd"}},splitLine:{lineStyle:{color:["#eee"]}}},valueAxis:{axisLine:{lineStyle:{color:"#008acd"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(200,200,200,0.1)"]}},splitLine:{lineStyle:{color:["#eee"]}}},timeline:{lineStyle:{color:"#008acd"},controlStyle:{color:"#008acd",borderColor:"#008acd"},symbol:"emptyCircle",symbolSize:3},line:{smooth:!0,symbol:"emptyCircle",symbolSize:3},candlestick:{itemStyle:{color:"#d87a80",color0:"#2ec7c9"},lineStyle:{width:1,color:"#d87a80",color0:"#2ec7c9"},areaStyle:{color:"#2ec7c9",color0:"#b6a2de"}},scatter:{symbol:"circle",symbolSize:4},map:{itemStyle:{color:"#ddd"},areaStyle:{color:"#fe994e"},label:{color:"#d87a80"}},graph:{itemStyle:{color:"#d87a80"},linkStyle:{color:"#2ec7c9"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#2ec7c9"],[.8,"#5ab1ef"],[1,"#d87a80"]],width:10}},axisTick:{splitNumber:10,length:15,lineStyle:{color:"auto"}},splitLine:{length:22,lineStyle:{color:"auto"}},pointer:{width:5}}};e.registerTheme("macarons",l)}else a("ECharts is not Loaded")}))},9406:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dashboard-container"},[a("el-row",{staticClass:"panel-group",attrs:{gutter:20}},[a("el-col",{staticClass:"card-panel-col",attrs:{xs:12,sm:12,lg:6}},[a("div",{staticClass:"card-panel",on:{click:t.todo}},[a("div",{staticClass:"card-panel-icon-wrapper icon-people"},[a("svg-icon",{attrs:{"icon-class":"alert","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("\n              告警 （当天）\n            ")]),t._v(" "),a("div",{staticStyle:{"font-size":"20px"}},[t._v("todo")])])])]),t._v(" "),a("el-col",{staticClass:"card-panel-col",attrs:{xs:12,sm:12,lg:6}},[a("div",{staticClass:"card-panel"},[a("div",{staticClass:"card-panel-icon-wrapper icon-message"},[a("svg-icon",{attrs:{"icon-class":"guide","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("\n              我的发布历史\n            ")]),t._v(" "),a("div",{staticStyle:{"font-size":"20px"}},[t._v("todo")])])])]),t._v(" "),a("el-col",{staticClass:"card-panel-col",attrs:{xs:12,sm:12,lg:6}},[a("div",{staticClass:"card-panel",on:{click:t.todo}},[a("div",{staticClass:"card-panel-icon-wrapper icon-money"},[a("svg-icon",{attrs:{"icon-class":"list","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("\n              发布执行计划\n            ")]),t._v(" "),a("div",{staticStyle:{"font-size":"20px"}},[t._v("todo")])])])]),t._v(" "),a("el-col",{staticClass:"card-panel-col",attrs:{xs:12,sm:12,lg:6}},[a("div",{staticClass:"card-panel",on:{click:t.todo}},[a("div",{staticClass:"card-panel-icon-wrapper icon-shopping"},[a("svg-icon",{attrs:{"icon-class":"unlink","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("\n              摘除的实例\n            ")]),t._v(" "),a("div",{staticStyle:{"font-size":"20px"}},[t._v("todo")])])])])],1),t._v(" "),a("el-row",{staticClass:"panel-group",attrs:{gutter:20}},[a("el-col",{attrs:{xs:24,sm:8,lg:6}},[a("history-app")],1),t._v(" "),a("el-col",{attrs:{xs:24,sm:16,lg:18}},[a("div",[a("pod-draft")],1),t._v(" "),a("div",{staticStyle:{"margin-top":"20px"}},[a("resource-pool-usage",{attrs:{height:"500px"}})],1)])],1),t._v(" "),a("el-row",{staticClass:"panel-group"}),t._v(" "),a("el-dialog",{attrs:{visible:t.appDeployStatusFullScreen,width:"30%",fullscreen:!0,center:""},on:{"update:visible":function(e){t.appDeployStatusFullScreen=e}}},[a("app-deploy-status",{attrs:{height:"100%","table-height":"100%"}})],1)],1)},l=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.className,style:{height:t.height,width:t.width}})},n=[],s=a("313e"),c=a.n(s);a("817d");var r={props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},data:function(){return{chart:null}},mounted:function(){var t=this;this.$nextTick((function(){t.initChart()}))},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart:function(){this.chart=c.a.init(this.$el,"macarons"),this.chart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{left:"center",bottom:"10",data:["APP1","APP2","APP3"]},series:[{name:"CPU",type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:[{value:320,name:"APP1"},{value:240,name:"APP2"},{value:149,name:"APP3"}],animationEasing:"cubicInOut",animationDuration:1e3}]})}}},d=r,p=a("2877"),u=Object(p["a"])(d,o,n,!1,null,null,null),h=u.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resource-pool-usage-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-panel-text"},[t._v("资源池使用情况")]),t._v(" "),a("div",{staticStyle:{display:"inline-block","padding-left":"20px"}},[a("el-checkbox",{on:{change:t.hideCommonToggle},model:{value:t.hideCommon,callback:function(e){t.hideCommon=e},expression:"hideCommon"}},[t._v("隐藏【通用】资源池")])],1),t._v(" "),a("span",{staticStyle:{float:"right"}},[a("el-button",{staticStyle:{padding:"6px 10px"},attrs:{type:"text"},on:{click:t.clusterCapacityPage}},[t._v("查看详情")])],1),t._v(" "),a("el-select",{staticStyle:{float:"right","margin-right":"20px"},attrs:{placeholder:"请选择集群",size:"mini"},on:{change:t.loadData},model:{value:t.cluster,callback:function(e){t.cluster=e},expression:"cluster"}},t._l(t.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),a("div",{staticClass:"component-item"},[a("div",{style:{height:t.height,width:t.width},attrs:{id:"echarts-wrapper"}})])])],1)},f=[],v=(a("7f7f"),a("2d63")),g=a("b775");function y(){return Object(g["a"])({url:"/v1/dashboard/data",method:"get"})}function b(t){return Object(g["a"])({url:"/v1/dashboard/resource-pool?cluster="+t,method:"get"})}function x(t){return Object(g["a"])({url:"/v1/dashboard/pod-draft?cluster="+t,method:"get"})}function _(){return Object(g["a"])({url:"/v1/dashboard/app-deploy-status",method:"get"})}a("817d");var C=1e3,S={props:{width:{type:String,default:"100%"},height:{type:String,default:"300px"}},data:function(){return{chart:null,hideCommon:!1,cluster:"",pool:{name:[],cpuCapacity:[],cpuRequire:[],cpuLeft:[],memCapacity:[],memRequire:[],memLeft:[]}}},mounted:function(){var t=this;this.$nextTick((function(){t.initChart(),t.loadData()}))},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},computed:{clusterOptions:function(){return this.$settings.clusters}},methods:{clusterCapacityPage:function(){this.$message.info("todo")},hideCommonToggle:function(){this.$message.info("todo")},loadData:function(){var t=this;this.cluster?(this.chart.showLoading(),b(this.cluster).then((function(e){if(t.pool={name:[],cpuCapacity:[],cpuRequire:[],cpuLeft:[],memCapacity:[],memRequire:[],memLeft:[]},!e.data||e.data.size<1)t.chart.setOption({title:{text:"资源池使用情况",subtext:"没有任何数据",left:"center"}});else{var a,i=Object(v["a"])(e.data);try{for(i.s();!(a=i.n()).done;){var l=a.value;t.pool.name.push(l.name),t.pool.cpuCapacity.push(l.cpuCapacity),t.pool.cpuRequire.push(l.cpuRequire),t.pool.cpuLeft.push(l.cpuCapacity-l.cpuRequire),t.pool.memCapacity.push(l.memCapacity),t.pool.memRequire.push(l.memRequire),t.pool.memLeft.push(l.memCapacity-l.memRequire)}}catch(o){i.e(o)}finally{i.f()}console.log(t.pool),t.chart.setOption({xAxis:{data:t.pool.name},series:[{name:"内存-已分配",data:t.pool.memRequire},{name:"内存-剩余",data:t.pool.memLeft},{name:"CPU-已分配",data:t.pool.cpuRequire},{name:"CPU-剩余",data:t.pool.cpuLeft}]})}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.chart.hideLoading()}))):this.chart.setOption({title:{text:"请选择集群",textStyle:{color:"#666"},top:"20%",subtext:"",left:"center"}})},initChart:function(){this.chart=c.a.init(document.getElementById("echarts-wrapper"),"macarons"),this.chart.setOption({tooltip:{},legend:{},grid:{top:30,left:5,right:5,bottom:5,containLabel:!0},xAxis:[{type:"category",data:this.pool.name,axisTick:{alignWithLabel:!0}}],yAxis:[{type:"value",name:"内存值",position:"left",axisLabel:{formatter:"{value} Gi"}},{type:"value",name:"CPU值",position:"right",axisTick:{show:!0},axisLabel:{formatter:"{value} Core"}}],series:[{name:"内存-已分配",type:"bar",stack:"mem",barWidth:"30",barGap:"5%",data:this.pool.memRequire,itemStyle:{color:"#ffb980"},label:{show:!0,formatter:"{c}"},animationDuration:C},{name:"内存-剩余",type:"bar",stack:"mem",barWidth:"30",barGap:"5%",data:this.pool.memLeft,itemStyle:{color:"#2fc7c9"},label:{show:!0,formatter:"{c}"},animationDuration:C},{name:"CPU-已分配",type:"bar",stack:"cpu",barWidth:"30",yAxisIndex:1,barGap:"5%",data:this.pool.cpuRequire,itemStyle:{color:"#fc8452"},label:{show:!0,formatter:"{c}"},animationDuration:C},{name:"CPU-剩余",type:"bar",stack:"cpu",barWidth:"30",yAxisIndex:1,barGap:"5%",data:this.pool.cpuLeft,itemStyle:{color:"#91cc75"},label:{show:!0,formatter:"{c}"},animationDuration:C}]})}}},w=S,k=Object(p["a"])(w,m,f,!1,null,null,null),P=k.exports,L=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"chart-wrapper"},[a("el-card",{staticClass:"box-card",attrs:{"body-style":{overflowY:"auto",padding:"0px 5px"}}},[a("div",{staticClass:"clearfix",staticStyle:{"font-weight":"bold"},attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-aim"}),t._v(" "),a("span",{staticClass:"card-panel-text"},[t._v("访问的历史应用")])]),t._v(" "),a("div",{staticClass:"component-item"},[a("div",{class:t.className,style:{height:t.height,width:t.width}},t._l(t.apps,(function(e){return a("div",[a("el-button",{staticStyle:{padding:"5px"},attrs:{type:"text"},on:{click:function(a){return t.pipelinePage(e)}}},[t._v(t._s(e))])],1)})),0)])])],1)},D=[],$=a("c24f"),A={props:{className:{type:String,default:""},width:{type:String,default:"100%"},height:{type:String,default:"auto"}},data:function(){return{apps:[]}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;Object($["a"])().then((function(e){t.apps=e.data.recentApps})).catch((function(e){t.$message.error(e.message)}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t}})}}},O=A,q=Object(p["a"])(O,L,D,!1,null,null,null),R=q.exports,j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pod-draft-container"},[a("el-card",{staticClass:"box-card",attrs:{"body-style":{padding:"0px 5px"}}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-panel-text"},[t._v("应用实例漂移")]),t._v(" "),a("el-tooltip",[a("template",{slot:"content"},[a("div",{staticStyle:{"line-height":"1.5em"}},[t._v("\n            实例漂移定义： 实例配置了专用资源池， 但却没有运行在专用资源池上"),a("br"),t._v(" "),a("b",[t._v("配置资源池：")]),t._v("发布流程下配置的资源池"),a("br"),t._v(" "),a("b",[t._v("运行资源池：")]),t._v("实例当前所在的资源池\n          ")])]),t._v(" "),a("i",{staticClass:"el-icon-info help-text-icon"})],2),t._v(" "),a("el-switch",{staticStyle:{float:"right"},attrs:{"active-color":"#13ce66","active-text":"所有","inactive-text":"访问过的应用"},on:{change:t.onChange},model:{value:t.showAllPods,callback:function(e){t.showAllPods=e},expression:"showAllPods"}}),t._v(" "),a("el-select",{staticStyle:{float:"right","margin-right":"20px"},attrs:{placeholder:"请选择集群",size:"mini"},on:{change:t.onChange},model:{value:t.cluster,callback:function(e){t.cluster=e},expression:"cluster"}},t._l(t.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),a("div",{staticClass:"component-item"},[a("div",{class:t.className,style:{height:t.height,width:t.width}},[this.cluster?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,"max-height":300}},[a("el-table-column",{attrs:{prop:"pod","min-width":"260",label:"实例"}}),t._v(" "),a("el-table-column",{attrs:{prop:"namespace",label:"环境"}}),t._v(" "),a("el-table-column",{attrs:{prop:"shouldPool",label:"配置资源池"}}),t._v(" "),a("el-table-column",{attrs:{prop:"runPool",label:"运行资源池"}}),t._v(" "),a("el-table-column",{attrs:{label:"",fixed:"right",width:"180px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.pipelinePage(e.row)}}},[t._v("发布流程\n              ")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.podPage(e.row)}}},[t._v("实例管理\n              ")])]}}],null,!1,2125190851)})],1):a("div",{staticStyle:{"margin-top":"10px","margin-bottom":"30px","text-align":"center",color:"#909399"}},[t._v("\n          请选择集群\n        ")])],1)])])],1)},z=[],E=(a("6762"),a("2fdb"),{props:{className:{type:String,default:""},width:{type:String,default:"100%"},height:{type:String,default:"auto"}},data:function(){return{tableData:[],tableLoading:!1,showAllPods:!1,cluster:""}},mounted:function(){this.loadData()},computed:{clusterOptions:function(){return this.$settings.clusters}},methods:{loadData:function(){var t=this;this.cluster&&(this.tableLoading=!0,x(this.cluster).then((function(e){t.showAllPods&&(t.tableData=e.data),t.showAllPods||Object($["a"])().then((function(a){var i,l=[],o=Object(v["a"])(e.data);try{for(o.s();!(i=o.n()).done;){var n=i.value;a.data.recentApps.includes(n.app)&&l.push(n)}}catch(s){o.e(s)}finally{o.f()}t.tableData=l})).catch((function(e){t.$message.error(e.message())}))})).catch((function(e){t.$message.error(e.message())})).finally((function(){t.tableLoading=!1})))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},podPage:function(t){var e={cluster:t.cluster,namespace:t.namespace,app:t.app};this.$router.push({name:"pod-index",query:e})},onChange:function(t){this.loadData()}}}),N=E,T=(a("d759"),Object(p["a"])(N,j,z,!1,null,null,null)),U=T.exports,W=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-deploy-status-container"},[a("el-card",{staticClass:"box-card",attrs:{"body-style":{padding:"0px 5px"}}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-panel-text"},[t._v("应用部署情况"),a("small",{staticStyle:{"padding-left":"10px"}},[t._v("（按访问历史倒序）")])]),t._v(" "),a("div",{staticStyle:{display:"inline-block","padding-left":"120px","font-size":"12px"}},[a("el-badge",{staticStyle:{"background-color":"#dcdfe6",border:"1px solid #dcdfe6",margin:"2px 8px"},attrs:{value:"实例数",type:"primary"}},[a("div",{staticStyle:{display:"inline-block",padding:"2px 5px"}},[t._v("运行环境")]),t._v(" "),a("div",{staticStyle:{display:"inline-block",padding:"2px 5px","background-color":"white"}},[t._v("运行版本")])])],1)]),t._v(" "),a("div",{staticClass:"component-item"},[a("div",{class:t.className,style:{height:t.height,width:t.width}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,"max-height":t.tableHeight}},[a("el-table-column",{attrs:{label:"",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"el-icon-position",attrs:{type:"text"},on:{click:function(a){return t.pipelinePage(e.row)}}},[t._v("发布流程\n              ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"app",width:"280",label:"应用"}}),t._v(" "),a("el-table-column",{attrs:{prop:"namespace",label:"运行情况"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"5px"}},t._l(e.row.status,(function(e){return e&&e.replicas>0?a("el-badge",{staticStyle:{"background-color":"#dcdfe6",border:"1px solid #dcdfe6",margin:"2px 8px"},attrs:{value:e.replicas,type:"primary"}},[a("div",{staticStyle:{display:"inline-block",padding:"2px 5px"}},[t._v(t._s(e.namespace))]),t._v(" "),a("div",{staticStyle:{display:"inline-block",padding:"2px 5px","background-color":"white"}},[t._v(t._s(e.version))])]):t._e()})),1)]}}])})],1)],1)])])],1)},F=[],G={props:{className:{type:String,default:""},width:{type:String,default:"100%"},height:{type:String,default:"300px"},tableHeight:{type:String,default:"300"}},data:function(){return{tableData:[],tableLoading:!0}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.tableLoading=!0,_().then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})}}},I=G,H=(a("4c4c"),Object(p["a"])(I,W,F,!1,null,null,null)),B=H.exports,J={data:function(){return{appDeployStatusFullScreen:!1}},components:{AppDeployStatus:B,PodDraft:U,PieChart:h,ResourcePoolUsage:P,HistoryApp:R},mounted:function(){},computed:{user:function(){return this.$store.state.user}},methods:{loadData:function(){var t=this;y().then((function(t){})).catch((function(e){t.$message.error(e.message)})).finally((function(){}))},fullScreen:function(t){"appDeployStatus"===t?this.appDeployStatusFullScreen=!0:this.$message.warning("unknown view name")},todo:function(){this.$message.info("todo")},alertPage:function(){this.$message.info("todo")}}},M=J,Y=(a("e414"),Object(p["a"])(M,i,l,!1,null,"3657e22e",null));e["default"]=Y.exports},aae3:function(t,e,a){var i=a("d3f4"),l=a("2d95"),o=a("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==l(t))}},d2c8:function(t,e,a){var i=a("aae3"),l=a("be13");t.exports=function(t,e,a){if(i(e))throw TypeError("String#"+a+" doesn't accept regex!");return String(l(t))}},d759:function(t,e,a){"use strict";a("fe89")},e414:function(t,e,a){"use strict";a("529b")},f473:function(t,e,a){},fe89:function(t,e,a){}}]);