(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1377be10"],{8504:function(t,e,r){"use strict";r.d(e,"g",(function(){return o})),r.d(e,"a",(function(){return a})),r.d(e,"h",(function(){return c})),r.d(e,"c",(function(){return u})),r.d(e,"b",(function(){return s})),r.d(e,"i",(function(){return l})),r.d(e,"d",(function(){return i})),r.d(e,"f",(function(){return p})),r.d(e,"e",(function(){return d}));var n=r("b775");function o(t,e){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function a(t,e,r){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:r}})}function c(t){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function u(t,e,r){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:r}})}function s(t){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function l(t){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function i(t,e,r){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:r}})}function p(t){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,r,o,a){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:r,revision:o,deployTag:a||""}})}},"9ebb":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("div",[r("el-form",{ref:"searchForm",attrs:{model:t.searchForm}},[r("el-form-item",{attrs:{label:"部署到纯私有环境"}},[r("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:t.cloudTypeChange},model:{value:t.privateCloud,callback:function(e){t.privateCloud=e},expression:"privateCloud"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"选择集群",prop:"cluster"}},[r("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择k8s集群",disabled:t.privateCloud},on:{change:t.loadApp},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},t._l(this.clusterOptions,(function(t){return r("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"选择环境",prop:"namespace"}},[r("el-select",{staticStyle:{width:"600px"},attrs:{placeholder:"选择Namespace",filterable:"",disabled:t.privateCloud},on:{change:t.loadApp},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},t._l(this.namespaceOptions,(function(t){return r("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"选择应用",prop:"app"}},[r("el-select",{staticStyle:{width:"600px"},attrs:{filterable:"",placeholder:"选择应用",multiple:""},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app",e)},expression:"searchForm.app"}},[r("el-option",{attrs:{value:"",label:"全部"}}),t._v(" "),t._l(this.appOptions,(function(t){return r("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}))],2)],1),t._v(" "),t.privateCloud?r("el-form-item",{attrs:{label:"覆盖环境"}},[r("el-select",{staticStyle:{width:"340px"},model:{value:t.searchForm.overrideNamespace,callback:function(e){t.$set(t.searchForm,"overrideNamespace",e)},expression:"searchForm.overrideNamespace"}},[r("el-option",{attrs:{value:"cmhk-crm-di-std",label:"招商局集团测试（cmhk-crm-di-std）"}})],1)],1):t._e(),t._v(" "),r("el-form-item",[r("div",{staticStyle:{"margin-top":"20px","padding-left":"160px"}},[r("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:t.logHistory}},[t._v("查看历史结果")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:t.createHelmChartBuildJob}},[t._v("开始构建")])],1)])],1)],1),t._v(" "),t.jobResult.length>0?r("div",{staticStyle:{"margin-top":"20px",border:"1px solid #ccc","background-color":"#eee",padding:"5px",width:"760px","font-size":"12px"}},t._l(t.jobResult,(function(e){return r("div",[t._v("\n      "+t._s(e)+"\n    ")])})),0):t._e()])},o=[],a=(r("7f7f"),r("2d63")),c=r("8504"),u=r("c1ab"),s={components:{},mounted:function(){},beforeDestroy:function(){},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.searchForm.cluster){var t,e=Object(a["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var r=t.value;if(this.searchForm.cluster===r.name)return r.namespaces}}catch(n){e.e(n)}finally{e.f()}}return[]}},data:function(){return{searchForm:{cluster:"",namespace:"",app:"",overrideNamespace:""},appOptions:[],jobResult:[],privateCloud:!1}},methods:{cloudTypeChange:function(){console.log(this.privateCloud),this.privateCloud?(this.searchForm.cluster="forceecrm-k8s1",this.searchForm.namespace="forceecrm-public-prod",this.loadApp()):this.searchForm.overrideNamespace=""},loadApp:function(){var t=this.searchForm.cluster,e=this.searchForm.namespace;if(t&&e){var r=this;Object(c["g"])(t,e).then((function(t){r.appOptions=t.data})).catch((function(t){console.error(t)}))}else this.appOptions=[]},createHelmChartBuildJob:function(){var t=this;!this.privateCloud||this.searchForm.overrideNamespace?Object(u["k"])(this.searchForm.cluster,this.searchForm.namespace,this.searchForm.app,this.searchForm.overrideNamespace).then((function(e){t.jobResult.push("- 操作成功，构建完成后结果会存储到审计日志。构建服务比较多时，耗时会比较长。"+e.data)})).catch((function(e){t.$message.error(e.message)})).finally((function(){})):this.$message.warning("私有部署环境，必须选择覆盖环境")},logHistory:function(){var t=this.$router.resolve({name:"log-list",query:{operate:"helm-chart-build-job"}});window.open(t.href,"_blank")}}},l=s,i=r("2877"),p=Object(i["a"])(l,n,o,!1,null,null,null);e["default"]=p.exports},c1ab:function(t,e,r){"use strict";r.d(e,"h",(function(){return o})),r.d(e,"j",(function(){return a})),r.d(e,"z",(function(){return c})),r.d(e,"A",(function(){return u})),r.d(e,"c",(function(){return s})),r.d(e,"f",(function(){return l})),r.d(e,"E",(function(){return i})),r.d(e,"G",(function(){return p})),r.d(e,"y",(function(){return d})),r.d(e,"a",(function(){return m})),r.d(e,"e",(function(){return f})),r.d(e,"D",(function(){return h})),r.d(e,"F",(function(){return v})),r.d(e,"x",(function(){return b})),r.d(e,"H",(function(){return y})),r.d(e,"k",(function(){return O})),r.d(e,"d",(function(){return j})),r.d(e,"B",(function(){return g})),r.d(e,"i",(function(){return k})),r.d(e,"g",(function(){return F})),r.d(e,"s",(function(){return x})),r.d(e,"v",(function(){return _})),r.d(e,"w",(function(){return w})),r.d(e,"o",(function(){return C})),r.d(e,"p",(function(){return $})),r.d(e,"t",(function(){return N})),r.d(e,"u",(function(){return S})),r.d(e,"b",(function(){return A})),r.d(e,"q",(function(){return q})),r.d(e,"r",(function(){return H})),r.d(e,"n",(function(){return R})),r.d(e,"C",(function(){return J})),r.d(e,"m",(function(){return B})),r.d(e,"l",(function(){return T}));var n=r("b775");function o(t){return Object(n["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function a(t){return Object(n["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(n["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function s(){return Object(n["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function l(t){return Object(n["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function i(t){return Object(n["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(n["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,r,o,a,c,u,s){return Object(n["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(a,"&fixVersion=").concat(e,"&suffixVersion=").concat(r,"&message=").concat(o,"&dependencyCheck=").concat(c,"&parentPom=").concat(u),method:"post",data:s})}function h(t){return Object(n["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function v(t){return Object(n["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function O(t,e,r,o){return Object(n["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(r,"&overrideNamespace=").concat(o),method:"post"})}function j(t,e,r,o,a){return Object(n["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(r,"&remark=").concat(o,"&dryRun=").concat(a),method:"post"})}function g(){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function k(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function F(t){return Object(n["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function x(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function _(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function w(t){return Object(n["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function C(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function $(t){return Object(n["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function N(t){return Object(n["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function S(t,e){return Object(n["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function A(t){return Object(n["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function q(t){return Object(n["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function H(t){return Object(n["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function R(t,e,r){return Object(n["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(r),method:"get"})}function J(t){return Object(n["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function B(t,e,r){return Object(n["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(r),method:"get"})}function T(){return Object(n["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}}}]);