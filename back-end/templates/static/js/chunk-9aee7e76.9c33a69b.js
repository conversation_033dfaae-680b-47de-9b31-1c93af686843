(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9aee7e76"],{"02f4":function(e,t,n){var o=n("4588"),s=n("be13");e.exports=function(e){return function(t,n){var a,r,i=String(s(t)),l=o(n),c=i.length;return l<0||l>=c?e?"":void 0:(a=i.charCodeAt(l),a<55296||a>56319||l+1===c||(r=i.charCodeAt(l+1))<56320||r>57343?e?i.charAt(l):a:e?i.slice(l,l+2):r-56320+(a-55296<<10)+65536)}}},"0390":function(e,t,n){"use strict";var o=n("02f4")(!0);e.exports=function(e,t,n){return t+(n?o(e,t).length:1)}},"0a49":function(e,t,n){var o=n("9b43"),s=n("626a"),a=n("4bf8"),r=n("9def"),i=n("cd1c");e.exports=function(e,t){var n=1==e,l=2==e,c=3==e,p=4==e,u=6==e,d=5==e||u,f=t||i;return function(t,i,m){for(var v,h,b=a(t),g=s(b),_=o(i,m,3),x=r(g.length),y=0,k=n?f(t,x):l?f(t,0):void 0;x>y;y++)if((d||y in g)&&(v=g[y],h=_(v,y,b),e))if(n)k[y]=h;else if(h)switch(e){case 3:return!0;case 5:return v;case 6:return y;case 2:k.push(v)}else if(p)return!1;return u?-1:c||p?p:k}}},"0bfb":function(e,t,n){"use strict";var o=n("cb7c");e.exports=function(){var e=o(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},1169:function(e,t,n){var o=n("2d95");e.exports=Array.isArray||function(e){return"Array"==o(e)}},"20d6":function(e,t,n){"use strict";var o=n("5ca1"),s=n("0a49")(6),a="findIndex",r=!0;a in[]&&Array(1)[a]((function(){r=!1})),o(o.P+o.F*r,"Array",{findIndex:function(e){return s(this,e,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")(a)},"214f":function(e,t,n){"use strict";n("b0c5");var o=n("2aba"),s=n("32e9"),a=n("79e5"),r=n("be13"),i=n("2b4c"),l=n("520a"),c=i("species"),p=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),u=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();e.exports=function(e,t,n){var d=i(e),f=!a((function(){var t={};return t[d]=function(){return 7},7!=""[e](t)})),m=f?!a((function(){var t=!1,n=/a/;return n.exec=function(){return t=!0,null},"split"===e&&(n.constructor={},n.constructor[c]=function(){return n}),n[d](""),!t})):void 0;if(!f||!m||"replace"===e&&!p||"split"===e&&!u){var v=/./[d],h=n(r,d,""[e],(function(e,t,n,o,s){return t.exec===l?f&&!s?{done:!0,value:v.call(t,n,o)}:{done:!0,value:e.call(n,t,o)}:{done:!1}})),b=h[0],g=h[1];o(String.prototype,e,b),s(RegExp.prototype,d,2==t?function(e,t){return g.call(e,this,t)}:function(e){return g.call(e,this)})}}},"26ef":function(e,t,n){},"28a5":function(e,t,n){"use strict";var o=n("aae3"),s=n("cb7c"),a=n("ebd6"),r=n("0390"),i=n("9def"),l=n("5f1b"),c=n("520a"),p=n("79e5"),u=Math.min,d=[].push,f="split",m="length",v="lastIndex",h=4294967295,b=!p((function(){RegExp(h,"y")}));n("214f")("split",2,(function(e,t,n,p){var g;return g="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[m]||2!="ab"[f](/(?:ab)*/)[m]||4!="."[f](/(.?)(.?)/)[m]||"."[f](/()()/)[m]>1||""[f](/.?/)[m]?function(e,t){var s=String(this);if(void 0===e&&0===t)return[];if(!o(e))return n.call(s,e,t);var a,r,i,l=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),u=0,f=void 0===t?h:t>>>0,b=new RegExp(e.source,p+"g");while(a=c.call(b,s)){if(r=b[v],r>u&&(l.push(s.slice(u,a.index)),a[m]>1&&a.index<s[m]&&d.apply(l,a.slice(1)),i=a[0][m],u=r,l[m]>=f))break;b[v]===a.index&&b[v]++}return u===s[m]?!i&&b.test("")||l.push(""):l.push(s.slice(u)),l[m]>f?l.slice(0,f):l}:"0"[f](void 0,0)[m]?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,o){var s=e(this),a=void 0==n?void 0:n[t];return void 0!==a?a.call(n,s,o):g.call(String(s),n,o)},function(e,t){var o=p(g,e,this,t,g!==n);if(o.done)return o.value;var c=s(e),d=String(this),f=a(c,RegExp),m=c.unicode,v=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(b?"y":"g"),_=new f(b?c:"^(?:"+c.source+")",v),x=void 0===t?h:t>>>0;if(0===x)return[];if(0===d.length)return null===l(_,d)?[d]:[];var y=0,k=0,S=[];while(k<d.length){_.lastIndex=b?k:0;var C,w=l(_,b?d:d.slice(k));if(null===w||(C=u(i(_.lastIndex+(b?0:k)),d.length))===y)k=r(d,k,m);else{if(S.push(d.slice(y,k)),S.length===x)return S;for(var P=1;P<=w.length-1;P++)if(S.push(w[P]),S.length===x)return S;k=y=C}}return S.push(d.slice(y)),S}]}))},3846:function(e,t,n){n("9e1e")&&"g"!=/./g.flags&&n("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:n("0bfb")})},5147:function(e,t,n){var o=n("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[o]=!1,!"/./"[e](t)}catch(s){}}return!0}},"51a9":function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"m",(function(){return l})),n.d(t,"a",(function(){return c})),n.d(t,"f",(function(){return p})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"n",(function(){return m})),n.d(t,"g",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"h",(function(){return b})),n.d(t,"o",(function(){return g}));var o=n("b775");function s(e){return Object(o["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function a(e){return Object(o["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function r(e,t,n){return Object(o["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:n}})}function i(e){return Object(o["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(o["a"])({url:"/v1/pipeline/all",method:"get"})}function p(e){return Object(o["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function u(e){return Object(o["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(o["a"])({url:"/v1/pipeline",method:"post",data:e})}function f(e){return Object(o["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function m(e){return Object(o["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function v(e,t,n,s){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:s}})}function h(e){return Object(o["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function b(e,t,n,s){return Object(o["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:n,targetNamespace:s}})}function g(e){return Object(o["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"520a":function(e,t,n){"use strict";var o=n("0bfb"),s=RegExp.prototype.exec,a=String.prototype.replace,r=s,i="lastIndex",l=function(){var e=/a/,t=/b*/g;return s.call(e,"a"),s.call(t,"a"),0!==e[i]||0!==t[i]}(),c=void 0!==/()??/.exec("")[1],p=l||c;p&&(r=function(e){var t,n,r,p,u=this;return c&&(n=new RegExp("^"+u.source+"$(?!\\s)",o.call(u))),l&&(t=u[i]),r=s.call(u,e),l&&r&&(u[i]=u.global?r.index+r[0].length:t),c&&r&&r.length>1&&a.call(r[0],n,(function(){for(p=1;p<arguments.length-2;p++)void 0===arguments[p]&&(r[p]=void 0)})),r}),e.exports=r},"530d":function(e,t,n){"use strict";n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return l}));var o=n("b775");function s(){return Object(o["a"])({url:"/v1/artifact/all",method:"get"})}function a(e){return Object(o["a"])({url:"/v1/artifact/search",method:"get",params:e})}function r(e){return Object(o["a"])({url:"/v1/artifact/analysis",method:"get",params:e})}function i(e){return Object(o["a"])({url:"/v1/artifact",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/v1/artifact",method:"delete",params:{id:e}})}},"5f1b":function(e,t,n){"use strict";var o=n("23c6"),s=RegExp.prototype.exec;e.exports=function(e,t){var n=e.exec;if("function"===typeof n){var a=n.call(e,t);if("object"!==typeof a)throw new TypeError("RegExp exec method returned something other than an Object or null");return a}if("RegExp"!==o(e))throw new TypeError("RegExp#exec called on incompatible receiver");return s.call(e,t)}},"6b54":function(e,t,n){"use strict";n("3846");var o=n("cb7c"),s=n("0bfb"),a=n("9e1e"),r="toString",i=/./[r],l=function(e){n("2aba")(RegExp.prototype,r,e,!0)};n("79e5")((function(){return"/a/b"!=i.call({source:"a",flags:"b"})}))?l((function(){var e=o(this);return"/".concat(e.source,"/","flags"in e?e.flags:!a&&e instanceof RegExp?s.call(e):void 0)})):i.name!=r&&l((function(){return i.call(this)}))},"8b9c":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container pipeline-edit-container",attrs:{"element-loading-text":"拼命加载中"}},[n("div",{staticStyle:{"max-width":"960px"}},[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"120px"}},[n("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"ID"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"所属应用",prop:"app"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.app,callback:function(t){e.$set(e.form,"app",t)},expression:"form.app"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"运行环境"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-form-item",{attrs:{prop:"cluster"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",disabled:e.isEdit||e.isClone,filterable:""},on:{change:e.clusterChange},model:{value:e.form.cluster,callback:function(t){e.$set(e.form,"cluster",t)},expression:"form.cluster"}},e._l(e.clusterOptions,(function(t){return t.enable?n("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}}):e._e()})),1)],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"namespace"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",disabled:e.isEdit||e.isClone,filterable:""},on:{change:e.namespaceChange},model:{value:e.form.namespace,callback:function(t){e.$set(e.form,"namespace",t)},expression:"form.namespace"}},e._l(e.namespaceOptions,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                k8s集群：一般保持默认即可。如果是专属云，一般都有自己专有的k8s集群，请选择对应的集群\n              ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),e._v(" "),n("el-form-item",{attrs:{label:"基础镜像",prop:"baseImage"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:e.form.baseImage,callback:function(t){e.$set(e.form,"baseImage",t)},expression:"form.baseImage"}},e._l(e.baseImageOptions,(function(e){return n("el-option",{key:e,attrs:{label:e.split("/").reverse()[0],value:e}})})),1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              基础镜像包含应用需要的运行环境，比如jdk版本, tomcat版本。一般保持默认即可\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"部署策略"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:e.form.deployStrategy,callback:function(t){e.$set(e.form,"deployStrategy",t)},expression:"form.deployStrategy"}},e._l(e.deployStrategyOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("b",[e._v("滚动：")]),e._v("启动一个新实例，等达到健康状态后删除一个旧实例。如此循环，直到所有旧实例都被替换掉（平滑度好，推荐）"),n("br"),e._v(" "),n("b",[e._v("重建：")]),e._v("删除所有旧实例 -> 启动所有新实例（平滑度低，不会有多版本并存，发版速度快）\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"实例数"}},[n("el-input-number",{attrs:{step:1,min:0,max:50},on:{change:e.replicaChange},model:{value:e.form.replicas,callback:function(t){e.$set(e.form,"replicas",t)},expression:"form.replicas"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"资源（最大值）"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"0.1",min:"0.2",max:"15"},on:{change:e.limitCPUChange},model:{value:e.form.resources.limitCPU,callback:function(t){e.$set(e.form.resources,"limitCPU",e._n(t))},expression:"form.resources.limitCPU"}},[n("template",{slot:"prepend"},[e._v("CPU")])],2)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"128",min:"128",max:"30720"},on:{change:e.limitMemChange},model:{value:e.form.resources.limitMemory,callback:function(t){e.$set(e.form.resources,"limitMemory",e._n(t))},expression:"form.resources.limitMemory"}},[n("template",{slot:"prepend"},[e._v("内存")]),e._v(" "),n("template",{slot:"append"},[e._v("MB")])],2)],1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                CPU：单个实例可使用的最大CPU核心数 "),n("br"),e._v("\n                内存：单个实例可使用的最大内存\n              ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),e._v(" "),n("el-form-item",{attrs:{label:"资源（最小值）"}},[n("el-row",[n("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"0.1",min:"0.2",max:"15",disabled:!this.userIsAdmin},model:{value:e.form.resources.requestCPU,callback:function(t){e.$set(e.form.resources,"requestCPU",e._n(t))},expression:"form.resources.requestCPU"}},[n("template",{slot:"prepend"},[e._v("CPU")])],2)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{type:"number",step:"128",min:"128",max:"30720",disabled:!this.userIsAdmin},model:{value:e.form.resources.requestMemory,callback:function(t){e.$set(e.form.resources,"requestMemory",e._n(t))},expression:"form.resources.requestMemory"}},[n("template",{slot:"prepend"},[e._v("内存")]),e._v(" "),n("template",{slot:"append"},[e._v("MB")])],2)],1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                CPU：分配给单个实例的最低保障CPU "),n("br"),e._v("\n                内存：分配给单个实例的最低保障内存\n              ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1)],1),e._v(" "),n("el-form-item",{staticStyle:{"margin-top":"-22px"}},[n("div",{staticStyle:{color:"#dc5907"}},[n("b",[e._v("提示：")]),e._v("您可以降低资源配置（CPU/内存/副本数）。如需上调配置，请走审批进行申请（管理员无此限制）。具体请参考\n          "),n("a",{staticClass:"el-link",staticStyle:{color:"#409EFF"},attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404592"}},[e._v("操作文档")])])]),e._v(" "),n("el-form-item",{attrs:{label:"部署模块"}},[n("el-table",{attrs:{data:e.form.appModules,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[n("template",{slot:"empty"},[n("span",{staticStyle:{color:"#F56C6C"}},[e._v("请添加部署模块")])]),e._v(" "),n("el-table-column",{attrs:{label:"Git地址","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.gitUrl)+"\n            ")]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"子模块"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.module)+"\n            ")]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"ContextPath"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.contextPath)+"\n            ")]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"60px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:function(n){return e.deleteAppModule(t.row.gitUrl,t.row.module)}}},[n("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("删除")])],1)]}}])})],2),e._v(" "),n("div",{staticStyle:{"padding-top":"10px","text-align":"center"}},[n("el-button",{attrs:{icon:"el-icon-plus",size:"small"},on:{click:function(t){e.dialogAppModuleVisible=!0}}},[e._v("添加模块")])],1)],1),e._v(" "),n("el-form-item",{attrs:{label:"JVM参数"}},[n("el-input",{staticStyle:{"word-break":"break-all"},attrs:{type:"textarea",rows:3},model:{value:e.form.jvmOpts,callback:function(t){e.$set(e.form,"jvmOpts",t)},expression:"form.jvmOpts"}}),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("p",{staticStyle:{"font-weight":"bold"}},[e._v("Java进程参数")]),e._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0","font-weight":"bold"}},[e._v("● 内部参数说明")]),e._v(" "),n("div",[e._v("\n                -Dprocess.profile.candidates：配置中心的候选配置组，用户跨配置组加载配置文件。详情参考配置中心文档"),n("br"),e._v("\n                -Dserver.tomcat.max-keep-alive-requests：配置 Tomcat Connector maxKeepAliveRequests参数，默认值为500"),n("br"),e._v("\n                -Dserver.tomcat.protocol：配置 Tomcat Connector protocol 参数，默认值为org.apache.coyote.http11.Http11Nio2Protocol"),n("br"),e._v("\n                -Dserver.tomcat.max-threads: 配置 Tomcat Executor maxThreads 参数，默认值为500"),n("br")]),e._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[e._v("● OpenJDK8的默认值:")]),e._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/no-jdk/files/fs-jvm-options.sh\n              ")]),e._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[e._v("● DragonWell8的默认值:")]),e._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/ali-dragonwell8/files/fs-jvm-options.sh\n              ")]),e._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[e._v("● OpenJDK17的默认值:")]),e._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk17/files/fs-jvm-options.sh\n              ")]),e._v(" "),n("p",{staticStyle:{margin:"10px 0 0 0"}},[n("b",[e._v("● OpenJDK21的默认值:")]),e._v(" https://git.firstshare.cn/devops/fs-docker-base-image/-/blob/master/fs-tomcat8/openjdk21/files/fs-jvm-options.sh\n              ")])])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"关闭前回调地址",prop:"preStopWebhook"}},[n("el-input",{model:{value:e.form.preStopWebhook,callback:function(t){e.$set(e.form,"preStopWebhook",t)},expression:"form.preStopWebhook"}}),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              Pod在关闭之前的回调的地址。localhost代表Pod本身，比如： http://localhost/fs-app/shutdown"),n("br"),e._v("\n              Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → "),n("b",[e._v("call回调地址")]),e._v(" → 等待一段时间 → 关闭\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"关闭前保留时间"}},[n("el-input-number",{attrs:{max:3600,min:10,step:10},model:{value:e.form.preStopRetainSeconds,callback:function(t){e.$set(e.form,"preStopRetainSeconds",t)},expression:"form.preStopRetainSeconds"}}),e._v(" "),n("el-tooltip",{staticStyle:{float:"unset"},attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              Pod在关闭之前的保留时间（单位：秒）。主要用在需要对实例进行优雅关闭的场景"),n("br"),e._v("\n              Pod关闭流程： 摘除HTTP/Dubbo/MQ流量 → call回调地址 → "),n("b",[e._v("等待当前配置的时间")]),e._v(" → 关闭\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"伙伴应用"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",multiple:"",placeholder:"请选择应用"},model:{value:e.form.partnerApps,callback:function(t){e.$set(e.form,"partnerApps",t)},expression:"form.partnerApps"}},e._l(e.appOptions,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              调度时，会尽量选择伙伴应用实例所在的宿主机\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"排斥应用"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",multiple:"",placeholder:"请选择应用"},model:{value:e.form.exclusiveApps,callback:function(t){e.$set(e.form,"exclusiveApps",t)},expression:"form.exclusiveApps"}},e._l(e.appOptions,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              调度时，会尽量避开排斥应用实例所在的宿主机"),n("br"),e._v("\n              默认会添加自己，这样可以让应用的多个实例尽量放到不同的宿主机上\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"扩展容器"}},[n("el-input",{model:{value:e.form.extInitContainer,callback:function(t){e.$set(e.form,"extInitContainer",t)},expression:"form.extInitContainer"}}),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n              额外追加自定义的initContainer，输入镜像全称，多个英文逗号分割\n            ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-right question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("b",{staticClass:"box-card-title"},[e._v("一些选项开关")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[n("div",[e._v("\n                    【内核参数修改】包含："),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_time=600 "),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_intvl=20 "),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_probes=3\n                  ")]),e._v(" "),n("div",[e._v("\n                    【只允许发布Tag】: 发版时将只能选择tag, 不能选择branch。线上业务应用建议开启该选项，当发布代码出现问题是能够更好地回滚版本\n                  ")])])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1)],1),e._v(" "),n("div",[n("el-row",{attrs:{gutter:5}},[n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:e.form.options.onlyDeployTag,callback:function(t){e.$set(e.form.options,"onlyDeployTag",t)},expression:"form.options.onlyDeployTag"}},[e._v(e._s(e._f("optionDesc")("onlyDeployTag")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"发布时只允许发tag，不允许发分支。线上建议开启，便于服务版本回滚"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:e.form.options.jvmGcLog,callback:function(t){e.$set(e.form.options,"jvmGcLog",t)},expression:"form.options.jvmGcLog"}},[e._v(e._s(e._f("optionDesc")("jvmGcLog")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"开启jvm的gc日志并保存到日志文件里"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{attrs:{disabled:""},model:{value:e.form.options.isCoreApp,callback:function(t){e.$set(e.form.options,"isCoreApp",t)},expression:"form.options.isCoreApp"}},[e._v(e._s(e._f("optionDesc")("isCoreApp")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"目前只做服务标记"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:e.form.options.appLogToKafka,callback:function(t){e.$set(e.form.options,"appLogToKafka",t)},expression:"form.options.appLogToKafka"}},[e._v(e._s(e._f("optionDesc")("appLogToKafka")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"业务日志上报到ClickHouse日志中心"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:e.form.options.buildUseRuntimeJDK,callback:function(t){e.$set(e.form.options,"buildUseRuntimeJDK",t)},expression:"form.options.buildUseRuntimeJDK"}},[e._v(e._s(e._f("optionDesc")("buildUseRuntimeJDK")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                    默认使用jdk8编译源码。勾选后，则使用运行环境（基础镜像）的jdk版本去编译源码\n                    "),n("div",{staticStyle:{"padding-top":"5px"}},[n("b",[e._v("jdk升级参考资料")]),n("br"),e._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://blogs.oracle.com/javamagazine/post/its-time-to-move-your-applications-to-java-17-heres-why-and-heres-how",target:"_blank"}},[e._v("\n                        It’s time to move your applications to Java 17. Here’s why—and how\n                      ")]),n("br"),e._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://javaalmanac.io/",target:"_blank"}},[e._v("\n                        The Java Version Almanac\n                      ")]),n("br")]),e._v(" "),n("div",{staticStyle:{"padding-top":"5px"}},[n("b",[e._v("Java交叉编译参考资料")]),n("br"),e._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://www.sunmoonblog.com/2018/08/27/javac-source/",target:"_blank"}},[e._v("\n                        如何使用Javac的source参数\n                      ")]),n("br"),e._v(" "),n("a",{staticStyle:{color:"#409EFF"},attrs:{href:"https://stackoverflow.com/questions/38882080/specifying-java-version-in-maven-differences-between-properties-and-compiler-p",target:"_blank"}},[e._v("\n                        Specifying Java version in maven\n                      ")]),n("br")])])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),e._v(" "),n("el-col",{staticStyle:{padding:"5px 0"},attrs:{span:6}},[n("el-checkbox",{model:{value:e.form.options.addSysctlKeepalive,callback:function(t){e.$set(e.form.options,"addSysctlKeepalive",t)},expression:"form.options.addSysctlKeepalive"}},[e._v(e._s(e._f("optionDesc")("addSysctlKeepalive")))]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                    【内核参数修改】包含："),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_time=600 "),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_intvl=20 "),n("br"),e._v("\n                    net.ipv4.tcp_keepalive_probes=3\n                  ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1)],1)],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[e._v("健康检查")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                在Kubernetes中，健康检查是确保应用程序正常运行的关键。通过不同类型的健康检查，您可以有效地管理应用程序的启动、运行和可用性\n              ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2),e._v(" "),n("div",{staticStyle:{display:"inline-block","margin-left":"10px","font-size":"12px"}},[n("span",{staticStyle:{color:"#666"}},[e._v(" （如果没有特殊需求，保持系统默认配置即可） ")]),e._v(" "),n("a",{staticStyle:{color:"#409EFF",display:"inline-block","margin-left":"10px"},attrs:{href:"https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/",target:"_blank"}},[e._v("\n              查看k8s官方文档\n            ")])])],1),e._v(" "),n("div",[n("el-row",[n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[e._v("就绪检查")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败时，则k8s会将pod从服务负载均衡中剔除(摘除HTTP流量），直到pod再次就绪。建议配置该检查。"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),e._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[e._v("启用")]),e._v(" "),n("el-switch",{model:{value:e.form.readinessProbe&&e.form.readinessProbe.enable,callback:function(t){e.$set(e.form.readinessProbe&&e.form.readinessProbe,"enable",t)},expression:"form.readinessProbe && form.readinessProbe.enable"}})],1)],1),e._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"10",max:"300"},model:{value:e.form.readinessProbe.initialDelaySeconds,callback:function(t){e.$set(e.form.readinessProbe,"initialDelaySeconds",e._n(t))},expression:"form.readinessProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("初始延迟")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"20",max:"300"},model:{value:e.form.readinessProbe.periodSeconds,callback:function(t){e.$set(e.form.readinessProbe,"periodSeconds",e._n(t))},expression:"form.readinessProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("检查间隔")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:e.form.readinessProbe.timeoutSeconds,callback:function(t){e.$set(e.form.readinessProbe,"timeoutSeconds",e._n(t))},expression:"form.readinessProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("超时时间")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"60"},model:{value:e.form.readinessProbe.failureThreshold,callback:function(t){e.$set(e.form.readinessProbe,"failureThreshold",e._n(t))},expression:"form.readinessProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("失败阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"2",disabled:""},model:{value:e.form.readinessProbe.successThreshold,callback:function(t){e.$set(e.form.readinessProbe,"successThreshold",e._n(t))},expression:"form.readinessProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("成功阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2)],1)])],1),e._v(" "),n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[e._v("存活检查")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"pod运行期间，定期检查pod是否处于健康的运行状态。如果检查失败，则k8s会对pod进行重启"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),e._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[e._v("启用")]),e._v(" "),n("el-switch",{model:{value:e.form.livenessProbe&&e.form.livenessProbe.enable,callback:function(t){e.$set(e.form.livenessProbe&&e.form.livenessProbe,"enable",t)},expression:"form.livenessProbe && form.livenessProbe.enable"}})],1)],1),e._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1200",max:"3600"},model:{value:e.form.livenessProbe.initialDelaySeconds,callback:function(t){e.$set(e.form.livenessProbe,"initialDelaySeconds",e._n(t))},expression:"form.livenessProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("初始延迟")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"20",max:"300"},model:{value:e.form.livenessProbe.periodSeconds,callback:function(t){e.$set(e.form.livenessProbe,"periodSeconds",e._n(t))},expression:"form.livenessProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("检查间隔")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:e.form.livenessProbe.timeoutSeconds,callback:function(t){e.$set(e.form.livenessProbe,"timeoutSeconds",e._n(t))},expression:"form.livenessProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("超时时间")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"60"},model:{value:e.form.livenessProbe.failureThreshold,callback:function(t){e.$set(e.form.livenessProbe,"failureThreshold",e._n(t))},expression:"form.livenessProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("失败阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"5",disabled:""},model:{value:e.form.livenessProbe.successThreshold,callback:function(t){e.$set(e.form.livenessProbe,"successThreshold",e._n(t))},expression:"form.livenessProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("成功阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2)],1)])],1),e._v(" "),n("el-col",{staticStyle:{padding:"0 5px"},attrs:{span:8}},[n("el-card",{staticClass:"health-check-card",attrs:{shadow:"never"}},[n("div",{staticClass:"clearfix",staticStyle:{padding:"5px"},attrs:{slot:"header"},slot:"header"},[n("span",[e._v("启动检查")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"在pod启动时执行，检查pod是否已准备好接收流量(tomcat是否已经完成启动）。容器在尝试其他健康检查之前，将等待此检查成功。"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1),e._v(" "),n("div",{staticStyle:{float:"right"}},[n("span",{staticStyle:{"font-size":"14px"}},[e._v("启用")]),e._v(" "),n("el-switch",{model:{value:e.form.startupProbe&&e.form.startupProbe.enable,callback:function(t){e.$set(e.form.startupProbe&&e.form.startupProbe,"enable",t)},expression:"form.startupProbe && form.startupProbe.enable"}})],1)],1),e._v(" "),n("div",[n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"10",max:"60"},model:{value:e.form.startupProbe.initialDelaySeconds,callback:function(t){e.$set(e.form.startupProbe,"initialDelaySeconds",e._n(t))},expression:"form.startupProbe.initialDelaySeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("初始延迟")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"5",max:"30"},model:{value:e.form.startupProbe.periodSeconds,callback:function(t){e.$set(e.form.startupProbe,"periodSeconds",e._n(t))},expression:"form.startupProbe.periodSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("检查间隔")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"30"},model:{value:e.form.startupProbe.timeoutSeconds,callback:function(t){e.$set(e.form.startupProbe,"timeoutSeconds",e._n(t))},expression:"form.startupProbe.timeoutSeconds"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("超时时间")])]),e._v(" "),n("template",{slot:"append"},[e._v("秒")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"300",max:"1000"},model:{value:e.form.startupProbe.failureThreshold,callback:function(t){e.$set(e.form.startupProbe,"failureThreshold",e._n(t))},expression:"form.startupProbe.failureThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("失败阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",attrs:{type:"number",min:"1",max:"5",disabled:""},model:{value:e.form.startupProbe.successThreshold,callback:function(t){e.$set(e.form.startupProbe,"successThreshold",e._n(t))},expression:"form.startupProbe.successThreshold"}},[n("template",{slot:"prepend"},[n("div",{staticClass:"health-check-slot"},[e._v("成功阈值")])]),e._v(" "),n("template",{slot:"append"},[e._v("次")])],2)],1)])],1)],1)],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[e._v("资源池调度")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                调度策略：服务实例在调度时选择资源池的策略\n                资源池：服务实例运行的宿主机集合\n              ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),e._v(" "),n("div",[n("el-form-item",{attrs:{label:"调度策略：","label-width":"100"}},[n("el-select",{staticStyle:{width:"600px"},model:{value:e.form.schedule.strategy,callback:function(t){e.$set(e.form.schedule,"strategy",t)},expression:"form.schedule.strategy"}},e._l(e.scheduleStrategyOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                  【必须调度到选择的资源池】: 当选择的资源池资源不足时会调度失败，这时需要联系管理员对资源池做扩容 "),n("br"),e._v("\n                  【优先调度到选择的资源池】: 当选择的资源池资源不足时会把实例调度到【通用】宿主机上运行\n                ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),e._v(" "),n("el-form-item",{attrs:{label:"资源池：","label-width":"100"}},e._l(e.nodeOptions,(function(t){return n("div",{staticStyle:{"padding-left":"85px"}},[n("el-radio",{attrs:{label:t.value},model:{value:e.form.schedule.node,callback:function(t){e.$set(e.form.schedule,"node","string"===typeof t?t.trim():t)},expression:"form.schedule.node"}},[e._v(e._s(t.name))])],1)})),0)],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[e._v("持久存储")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement}},[n("template",{slot:"content"},[n("div",{staticClass:"tooltip-wrapper"},[e._v("\n                    给应用容器挂载磁盘，该磁盘下的文件不会随着应用的重新发版或重启而删除掉。一般在以下场景才需要开启持久存储"),n("br"),e._v("\n                    1. 应用需要比较大的存储空间"),n("br"),e._v("\n                    2. 应用需要永久存储一些过程中产生的文件(重新发版/重启应用实例不会对这些文件进行删除)"),n("br"),e._v("\n                    3. 应用的多个实例需要共享读写一个存储盘\n                  ")])]),e._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)],1),e._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[n("el-switch",{attrs:{"active-text":"使用","inactive-text":"未使用"},model:{value:e.form.pvc&&e.form.pvc.enable,callback:function(t){e.$set(e.form.pvc&&e.form.pvc,"enable",t)},expression:"form.pvc && form.pvc.enable"}})],1)],1)],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.form.pvc&&e.form.pvc.enable,expression:"form.pvc && form.pvc.enable"}]},[n("el-input",{staticClass:"input-list-item",model:{value:e.form.pvc.name,callback:function(t){e.$set(e.form.pvc,"name","string"===typeof t?t.trim():t)},expression:"form.pvc.name"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[e._v("PVC名称")])])],2),e._v(" "),n("el-input",{staticClass:"input-list-item",model:{value:e.form.pvc.mountPath,callback:function(t){e.$set(e.form.pvc,"mountPath","string"===typeof t?t.trim():t)},expression:"form.pvc.mountPath"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[e._v("挂载目录路径")])])],2)],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[e._v("性能监控")]),e._v(" "),e.currentCluster&&e.currentCluster.apm.enable&&e.currentCluster.apm.skyWalkingUI?n("a",{staticStyle:{"padding-left":"10px",color:"#409EFF","font-size":"14px"},attrs:{href:e.currentCluster.apm.skyWalkingUI,target:"_blank"}},[e._v("UI页面")]):e._e()]),e._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[e.currentCluster&&e.currentCluster.apm.enable?n("div",[n("el-switch",{attrs:{"active-text":"开启","inactive-text":"关闭"},model:{value:e.form.options.skyWalkingAgent,callback:function(t){e.$set(e.form.options,"skyWalkingAgent",t)},expression:"form.options.skyWalkingAgent"}})],1):n("div",[n("small",{staticStyle:{"padding-left":"10px",color:"#777"}},[e._v("（暂未开放）")])])])],1)],1),e._v(" "),n("div",{staticStyle:{"font-size":"14px",color:"#777"}},[e._v("\n          性能监控管理(APM)：使用Skywalking搭建，开启后会使用Skywalking Agent来收集应用的一些性能指标信息（如：请求耗时，分布式追踪等）"),n("br")])]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[e._v("接口测试（Eolinker）")])])],1)],1),e._v(" "),n("div",[n("div",{staticStyle:{"font-size":"14px",color:"#777","padding-bottom":"10px"}},[e._v("\n            可以选择一个Eolinker任务，那么应用发布成功后会调用执行该任务进行测试，格式： 项目名 / 任务名\n          ")]),e._v(" "),n("el-cascader",{key:e.eolinkerFlushCascader,staticStyle:{width:"100%"},attrs:{clearable:"",options:e.eolinkerOptions,props:e.eolinkerProps,separator:" / "},model:{value:e.eolinkerModel,callback:function(t){e.eolinkerModel=t},expression:"eolinkerModel"}})],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("el-row",[n("el-col",{attrs:{span:16}},[n("b",{staticClass:"box-card-title"},[e._v("发布后回调（Webhook）")])]),e._v(" "),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}})],1)],1),e._v(" "),n("div",[n("div",{staticStyle:{"font-size":"14px",color:"#777","padding-bottom":"10px"}},[e._v("\n            发布成功后，会调用一次Webhook地址\n          ")]),e._v(" "),n("el-input",{staticClass:"input-list-item",model:{value:e.form.webhook.url,callback:function(t){e.$set(e.form.webhook,"url","string"===typeof t?t.trim():t)},expression:"form.webhook.url"}},[n("template",{slot:"prepend"},[n("div",{staticStyle:{width:"80px"}},[e._v("URL")])])],2)],1)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[e._v("环境变量")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"系统环境变量，发布系统维护的变量不允许修改"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("div",[e._l(e.form.envs,(function(t){return n("div",{staticStyle:{margin:"5px 0"}},[n("el-input",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"变量名",clearable:"",disabled:"SYSTEM"===t.type},model:{value:t.name,callback:function(n){e.$set(t,"name","string"===typeof n?n.trim():n)},expression:"item.name"}}),e._v("\n            =\n            "),n("el-input",{staticStyle:{display:"inline-block",width:"400px"},attrs:{disabled:"SYSTEM"===t.type},model:{value:t.value,callback:function(n){e.$set(t,"value","string"===typeof n?n.trim():n)},expression:"item.value"}}),e._v(" "),n("el-button",{directives:[{name:"show",rawName:"v-show",value:"SYSTEM"!==t.type,expression:"item.type !== 'SYSTEM'"}],attrs:{icon:"el-icon-delete"},on:{click:function(n){return n.preventDefault(),e.removeEnv(t)}}},[e._v("删除\n            ")])],1)})),e._v(" "),n("div",{staticStyle:{"padding-top":"10px","padding-left":"310px"}},[n("el-button",{attrs:{icon:"el-icon-plus"},on:{click:e.addEnv}},[e._v("添加变量")]),e._v(" "),n("br"),e._v(" "),n("el-popover",{attrs:{placement:"top",title:"一些特殊用途的环境变量",width:"600",trigger:"click"}},[n("div",[n("div",[e._v("JEMALLOC_ENABLE=true")]),e._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[e._v("启用jemalloc内存分配器")]),e._v(" "),n("el-divider",{staticClass:"env-divider"}),e._v(" "),n("div",[e._v("SPRING_BOOT_JAR_APP=true")]),e._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[e._v("如果需要使用Jar包类型的SpringBoot应用，请开启这个变量")]),e._v(" "),n("el-divider",{staticClass:"env-divider"}),e._v(" "),n("div",[e._v("CMS_STARTER_PRIVATE_KEY=[字符串内容]")]),e._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[e._v("使用自定义的秘钥对配置中心内容加解密")]),e._v(" "),n("el-divider",{staticClass:"env-divider"}),e._v(" "),n("div",[e._v("SPRING_BOOT_METRICS_ENABLE=true")]),e._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[e._v("开启Prometheus PodMonitor")]),e._v(" "),n("el-divider",{staticClass:"env-divider"}),e._v(" "),n("div",[e._v("SPRING_PROFILER_AGENT_ENABLE=true")]),e._v(" "),n("div",{staticStyle:{"font-size":"12px",color:"#666"}},[e._v("开启应用启动时间分析，详情请查阅spring-startup-analyzer资料")])],1),e._v(" "),n("el-button",{attrs:{slot:"reference",icon:"el-icon-search",type:"text"},slot:"reference"},[e._v("查看特殊环境变量")])],1)],1)],2)]),e._v(" "),n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("b",{staticClass:"box-card-title"},[e._v("端口")]),e._v(" "),n("el-tooltip",{attrs:{effect:e.tooltip.effect,placement:e.tooltip.placement,content:"应用需要暴露的端口，如FCP服务可添加：fcp = 5432 。发布系统维护的端口不允许修改"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),n("div",[e._l(e.form.ports,(function(t){return n("div",{staticStyle:{margin:"5px 0"}},[n("el-input",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"端口名",clearable:"",disabled:"SYSTEM"===t.type},model:{value:t.name,callback:function(n){e.$set(t,"name","string"===typeof n?n.trim():n)},expression:"item.name"}}),e._v("\n            =\n            "),n("el-input-number",{staticStyle:{display:"inline-block",width:"260px"},attrs:{placeholder:"端口值","controls-position":"right",min:0,max:65535,step:1e3,disabled:"SYSTEM"===t.type},model:{value:t.value,callback:function(n){e.$set(t,"value",n)},expression:"item.value"}}),e._v(" "),n("el-button",{directives:[{name:"show",rawName:"v-show",value:"SYSTEM"!==t.type,expression:"item.type !== 'SYSTEM'"}],attrs:{icon:"el-icon-delete"},on:{click:function(n){return n.preventDefault(),e.removePort(t)}}},[e._v("删除\n            ")])],1)})),e._v(" "),n("div",{staticStyle:{"padding-top":"10px","padding-left":"310px"}},[n("el-button",{attrs:{icon:"el-icon-plus"},on:{click:e.addPort}},[e._v("添加端口")])],1)],2)]),e._v(" "),n("div",{staticStyle:{padding:"20px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("div",[n("el-button",{attrs:{type:"warning"},on:{click:e.onAbort}},[e._v("取消")])],1)]),e._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{staticStyle:{"padding-right":"30px","padding-left":"30px"},attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.onSubmit("form")}}},[e._v("提交\n              ")])],1)])],1)],1)],1),e._v(" "),n("el-dialog",{attrs:{title:"添加部署模块",visible:e.dialogAppModuleVisible,width:"840px","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogAppModuleVisible=t}}},[n("el-form",{attrs:{model:e.dialogAppModuleForm,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"部署模块"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择部署模块。格式：Git地址 --- 子模块"},on:{change:e.deployModuleSelChanged},model:{value:e.dialogAppModuleForm.module,callback:function(t){e.$set(e.dialogAppModuleForm,"module",t)},expression:"dialogAppModuleForm.module"}},e._l(e.artifactOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.gitUrl+" --- "+e.module,value:e.gitUrl+"@@"+e.module}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"ContextPath"}},[n("el-input",{model:{value:e.dialogAppModuleForm.contextPath,callback:function(t){e.$set(e.dialogAppModuleForm,"contextPath","string"===typeof t?t.trim():t)},expression:"dialogAppModuleForm.contextPath"}})],1)],1),e._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogAppModuleVisible=!1}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAppModule()}}},[e._v("确 定")])],1)],1)],1)])},s=[],a=(n("6b54"),n("28a5"),n("a481"),n("20d6"),n("96cf"),n("3b8d")),r=(n("f559"),n("7f7f"),n("2d63")),i=n("51a9"),l=n("530d"),c=n("b562"),p=n("b775");function u(){return Object(p["a"])({url:"/v1/eolinker/project",method:"get"})}function d(e){return Object(p["a"])({url:"/v1/eolinker/timed-task",method:"get",params:e})}var f=n("c24f"),m=n("b144"),v={data:function(){var e=this,t=function(e,t,n){var o,s=Object(r["a"])(t);try{for(s.s();!(o=s.n()).done;){var a=o.value;if(console.log(a),!a.name||!a.value)return void n(new Error("请输入所有端口的名称和值"))}}catch(i){s.e(i)}finally{s.f()}n()},n=function(e,t,n){var o,s=Object(r["a"])(t);try{for(s.s();!(o=s.n()).done;){var a=o.value;if(console.log(a),!a.name)return void n(new Error("部分环境的变量名未填写"))}}catch(i){s.e(i)}finally{s.f()}n()},o=function(e,t,n){!t||t.startsWith("http://")||t.startsWith("https://")?n():n(new Error("Pod关闭前回调地址必须使用 http:// 或者 https:// 开头"))};return{loading:!0,submitLoading:!1,userIsAdmin:!1,appOptions:[],artifactOptions:[],artifactSelected:{},tooltip:{effect:"light",placement:"right-start"},formOrigin:{},form:{appModules:[],resources:{},livenessProbe:{},readinessProbe:{},startupProbe:{},schedule:{},pvc:{},envs:[],ports:[],options:{},eolinkerIDs:[],partnerApps:[],exclusiveApps:[],webhook:{},preStopWebhook:"",preStopRetainSeconds:20},rules:{app:[{required:!0,message:"请输入应用名称"},{min:4,message:"最少4个字符"}],cluster:[{required:!0,message:"请选择集群"}],namespace:[{required:!0,message:"请选择运行环境"}],baseImage:[{required:!0,message:"请选择基础镜像"}],envs:[{validator:n,message:"部分环境的变量名未填写"}],ports:[{validator:t,message:"请输入所有端口的名称和值"}],preStopWebhook:[{validator:o,message:"Pod关闭前回调地址必须使用 http:// 或 https:// 开头"}]},dialogAppModuleVisible:!1,dialogAppModuleForm:{module:"",contextPath:"/"},eolinkerProps:{multiple:!0,lazy:!0,lazyLoad:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(n,o){var s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:s=n.level,t.t0=s,t.next=0===t.t0?4:1===t.t0?5:11;break;case 4:return t.abrupt("break",13);case 5:return t.t1=o,t.next=8,e.listEolinkerTimedTask(n.data.value);case 8:return t.t2=t.sent,(0,t.t1)(t.t2),t.abrupt("break",13);case 11:return o([]),t.abrupt("return");case 13:case"end":return t.stop()}}),t)})));function n(e,n){return t.apply(this,arguments)}return n}()},eolinkerOptions:[],eolinkerFlushCascader:Math.random()}},filters:{optionDesc:function(e,t){return Object(m["b"])(e)}},mounted:function(){var e=this,t=this.$route.query.app,n=this.$route.query.pipelineId;n?this.loadPipeline(n):this.initPipeline(t),this.loadArtifacts(),this.loadApps(),Object(f["b"])().then((function(t){e.userIsAdmin=t.data})).catch((function(e){console.log(e)}))},computed:{isEdit:function(){return this.form.id>0},isClone:function(){return"clone"===this.$route.query.operate},isAudit:function(){return"audit"===this.$route.query.operate},clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.form.cluster){var e,t=Object(r["a"])(this.$settings.clusters);try{for(t.s();!(e=t.n()).done;){var n=e.value;if(this.form.cluster===n.name)return n.namespaces}}catch(o){t.e(o)}finally{t.f()}}return[]},currentCluster:function(){var e=this.form.cluster;if(e){var t,n=Object(r["a"])(this.$settings.clusters);try{for(n.s();!(t=n.n()).done;){var o=t.value;if(o.name===e)return o}}catch(s){n.e(s)}finally{n.f()}}},nodeOptions:function(){if(this.form.cluster)for(var e in this.$settings.clusters){var t=this.$settings.clusters[e];if(this.form.cluster===t.name)return t.nodes}return[]},scheduleStrategyOptions:function(){return[{value:"PREFERRED",label:"优先调度到选择的资源池"},{value:"REQUIRED",label:"必须调度到选择的资源池"}]},baseImageOptions:function(){var e=this.currentCluster;return e?e.baseImages:[]},armBaseImageOptions:function(){return this.$settings.armBaseImages},deployStrategyOptions:function(){return this.$settings.deployStrategies},eolinkerModel:{get:function(){for(var e=[],t=0;t<this.form.eolinkerIDs.length;t++){var n=[];n.push(this.form.eolinkerIDs[t].projectID),n.push(this.form.eolinkerIDs[t].timedTaskID),e.push(n)}return e},set:function(e){for(var t=[],n=0;n<e.length;n++)e[n]&&2===e[n].length&&t.push({projectID:e[n][0],timedTaskID:e[n][1]});this.form.eolinkerIDs=t}}},methods:{loadApps:function(){var e=this;Object(c["l"])().then((function(t){e.appOptions=t.data})).catch((function(t){e.$message.warning("加载应用数据出错！ "+t.message)}))},loadPipeline:function(e){var t=this;Object(i["e"])(e).then((function(e){if(t.form=e.data,t.formOrigin=Object(m["a"])(t.form),t.isAudit)console.log("pipeline audit, set status to enabled"),t.form.status="enabled";else if(t.isClone){console.log("pipeline clone, set cluster and namespace");var n=t.form.namespace;t.form.id=0,t.form.status="audit",t.form.cluster=t.$route.query.targetCluster,t.form.namespace=t.$route.query.targetNamespace,0===t.nodeOptions.filter((function(e){return e.value===t.form.schedule.node})).length&&(t.form.schedule.node=""),n!==t.form.namespace&&t.namespaceChange(t.form.namespace)}t.loadEolinkerOptions()})).catch((function(e){t.$message.error("加载数据出错！ "+e.message)})).finally((function(){t.loading=!1}))},loadArtifacts:function(){var e=this;Object(l["d"])().then((function(t){e.artifactOptions=t.data})).catch((function(t){e.$message.error("加载数据出错！ "+t.message)}))},initPipeline:function(e){var t=this;console.log(e),Object(i["i"])(e).then((function(e){t.form=e.data,t.loadEolinkerOptions()})).catch((function(e){t.$message.error("加载数据出错！ "+e.message)})).finally((function(){t.loading=!1}))},deleteAppModule:function(e,t){var n=this.form.appModules;n.splice(n.findIndex((function(n){return n.gitUrl===e&&n.module===t})),1)},clusterChange:function(){this.form.namespace="",this.form.baseImage=""},namespaceChange:function(e){if(!(e.indexOf("-")<1)){this.form.jvmOpts=this.form.jvmOpts.replace(/-Dprocess.profile.candidates=\S+/gi,"").trim();var t="";t=e.startsWith("fstest-")?"fstest":e.startsWith("firstshare-")?"firstshare":e.startsWith("foneshare-")||e.startsWith("foneshare")&&"foneshare"!==e?"foneshare":"cloud,foneshare",this.form.jvmOpts=(this.form.jvmOpts+" -Dprocess.profile.candidates="+t).trim()}},replicaChange:function(e,t){var n=this;!this.userIsAdmin&&e>this.formOrigin.replicas&&(this.$message.warning("如果需要增加实例数，请发审批"),this.$nextTick((function(){n.form.replicas=t})))},limitCPUChange:function(e){if(!this.userIsAdmin&&e>this.formOrigin.resources.limitCPU)return this.$message.warning("如果需要增加CPU，请发审批"),void(this.form.resources.limitCPU=this.formOrigin.resources.limitCPU);var t=parseFloat((e/4).toFixed(1));this.form.resources.requestCPU=t<=.1?.1:t},limitMemChange:function(e){if(!this.userIsAdmin&&e>this.formOrigin.resources.limitMemory)return this.$message.warning("如果需要增加内存，请发审批"),void(this.form.resources.limitMemory=this.formOrigin.resources.limitMemory);var t=parseInt(e);this.form.resources.requestMemory=t<64?64:t},deployModuleSelChanged:function(){var e=this.dialogAppModuleForm,t=e.module.split("@@");2===t.length&&(e.contextPath="/"+t[1])},addAppModule:function(){var e=this.dialogAppModuleForm;if(e.module&&e.contextPath)if(e.contextPath.startsWith("/")){var t=e.module.split("@@");if(2!==t.length)return this.$message.error("无法解析模块数据"),!1;var n={gitUrl:t[0],module:t[1],contextPath:e.contextPath};this.form.appModules.filter((function(e){return e.gitUrl===n.gitUrl&&e.module===n.module})).length>0?this.$message.error("模块已经存在，不能重复添加"):this.form.appModules.filter((function(e){return e.contextPath===n.contextPath})).length>0?this.$message.error("ContextPath 出现重复"):(this.form.appModules.push(n),this.dialogAppModuleVisible=!1)}else this.$message.error("ContextPath 必须以斜线开头");else this.$message.error("请填写完整数据")},isProdEnv:function(){return window.location.host.indexOf("foneshare")>-1},onSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(e)if(!t.form.appModules||t.form.appModules.length<1)t.$message.error("没有配置部署模块");else{var n=t.form.webhook.url;!n||n.startsWith("http://")||n.startsWith("https://")?0===t.form.id&&t.isProdEnv()&&"k8s1"===t.form.cluster&&"yes"!==t.$route.query.allowCreate?t.$message.error("k8s1集群不允许创建新的发布流程"):t.$confirm("是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.submitLoading=!0,Object(i["j"])(t.form).then((function(e){t.$message.success("操作成功"),t.pipelinePage(),t.submitLoading=!1})).catch((function(e){t.$message({dangerouslyUseHTMLString:!0,message:e.message,type:"error"}),t.submitLoading=!1}))})):t.$message.error("WebHook Url地址必须以 http:// 或者 https:// 开头")}}))},onAbort:function(){var e=this;this.$confirm("确认取消吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pipelinePage()}))},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.form.app}})},addPort:function(){this.form.ports.push({name:"",value:0,type:"USER"})},removePort:function(e){if("SYSTEM"!==e.type){var t=this.form.ports.indexOf(e);-1!==t&&this.form.ports.splice(t,1)}else this.$message.error("不允许删除该端口")},addEnv:function(){this.form.envs.push({name:"",value:"",type:"USER"})},removeEnv:function(e){if("SYSTEM"!==e.type){var t=this.form.envs.indexOf(e);-1!==t&&this.form.envs.splice(t,1)}else this.$message.error("不允许删除该变量")},loadEolinkerOptions:function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,n,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.listEolinkerProject();case 2:this.eolinkerOptions=e.sent,t=0;case 4:if(!(t<this.form.eolinkerIDs.length)){e.next=20;break}if(n=this.form.eolinkerIDs[t].projectID,!n){e.next=17;break}o=0;case 8:if(!(o<this.eolinkerOptions.length)){e.next=16;break}if(this.eolinkerOptions[o].value!==n||this.eolinkerOptions[o].children){e.next=13;break}return e.next=12,this.listEolinkerTimedTask(n);case 12:this.eolinkerOptions[o].children=e.sent;case 13:o++,e.next=8;break;case 16:this.eolinkerFlushCascader=Math.random();case 17:t++,e.next=4;break;case 20:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),listEolinkerProject:function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=[],e.next=3,u().then((function(e){t=e.data.map((function(e,t){return{value:e.project_id,label:e.project_name}}))})).catch((function(e){n.$message.error("加载测试接口（Eolinker）数据出错！ "+e.message)}));case 3:return e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),listEolinkerTimedTask:function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var n,o,s,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=this.eolinkerOptions.filter((function(e){return e.value===t})),!n||!n[0].children){e.next=3;break}return e.abrupt("return");case 3:return o={},s=[],o["project_id"]=t,e.next=8,d(o).then((function(e){s=e.data.map((function(e,t){return{value:e.task_id.toString(),label:e.task_name,leaf:!0}}))})).catch((function(e){a.$message.error("加载测试接口（Eolinker）数据出错！ "+e.message)}));case 8:return e.abrupt("return",s);case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},h=v,b=(n("fbd2"),n("2877")),g=Object(b["a"])(h,o,s,!1,null,null,null);t["default"]=g.exports},a481:function(e,t,n){"use strict";var o=n("cb7c"),s=n("4bf8"),a=n("9def"),r=n("4588"),i=n("0390"),l=n("5f1b"),c=Math.max,p=Math.min,u=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,f=/\$([$&`']|\d\d?)/g,m=function(e){return void 0===e?e:String(e)};n("214f")("replace",2,(function(e,t,n,v){return[function(o,s){var a=e(this),r=void 0==o?void 0:o[t];return void 0!==r?r.call(o,a,s):n.call(String(a),o,s)},function(e,t){var s=v(n,e,this,t);if(s.done)return s.value;var u=o(e),d=String(this),f="function"===typeof t;f||(t=String(t));var b=u.global;if(b){var g=u.unicode;u.lastIndex=0}var _=[];while(1){var x=l(u,d);if(null===x)break;if(_.push(x),!b)break;var y=String(x[0]);""===y&&(u.lastIndex=i(d,a(u.lastIndex),g))}for(var k="",S=0,C=0;C<_.length;C++){x=_[C];for(var w=String(x[0]),P=c(p(r(x.index),d.length),0),O=[],$=1;$<x.length;$++)O.push(m(x[$]));var j=x.groups;if(f){var q=[w].concat(O,P,d);void 0!==j&&q.push(j);var E=String(t.apply(void 0,q))}else E=h(w,d,P,O,j,t);P>=S&&(k+=d.slice(S,P)+E,S=P+w.length)}return k+d.slice(S)}];function h(e,t,o,a,r,i){var l=o+e.length,c=a.length,p=f;return void 0!==r&&(r=s(r),p=d),n.call(i,p,(function(n,s){var i;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,o);case"'":return t.slice(l);case"<":i=r[s.slice(1,-1)];break;default:var p=+s;if(0===p)return n;if(p>c){var d=u(p/10);return 0===d?n:d<=c?void 0===a[d-1]?s.charAt(1):a[d-1]+s.charAt(1):n}i=a[p-1]}return void 0===i?"":i}))}}))},aae3:function(e,t,n){var o=n("d3f4"),s=n("2d95"),a=n("2b4c")("match");e.exports=function(e){var t;return o(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==s(e))}},b0c5:function(e,t,n){"use strict";var o=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:o!==/./.exec},{exec:o})},b144:function(e,t,n){"use strict";function o(e){return JSON.parse(JSON.stringify(e))}function s(e){if(!e||!(e instanceof Date))return"";var t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds();return t}function a(e){return"isCoreApp"===e?"核心服务":"onlyDeployTag"===e?"只允许部署Tag":"addSysctlKeepalive"===e?"调整内核参数":"skyWalkingAgent"===e?"性能跟踪":"appLogToKafka"===e?"接入ClickHouse日志":"buildUseRuntimeJDK"===e?"镜像JDK版本编译代码":"jvmGcLog"===e?"GC日志":e}n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return s})),n.d(t,"b",(function(){return a}))},b562:function(e,t,n){"use strict";n.d(t,"p",(function(){return s})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"j",(function(){return l})),n.d(t,"e",(function(){return c})),n.d(t,"i",(function(){return p})),n.d(t,"h",(function(){return u})),n.d(t,"m",(function(){return d})),n.d(t,"o",(function(){return f})),n.d(t,"g",(function(){return m})),n.d(t,"f",(function(){return v})),n.d(t,"c",(function(){return h})),n.d(t,"k",(function(){return b})),n.d(t,"r",(function(){return g})),n.d(t,"n",(function(){return _})),n.d(t,"q",(function(){return x})),n.d(t,"d",(function(){return y}));var o=n("b775");function s(e){return Object(o["a"])({url:"/v1/app/search",method:"get",params:e})}function a(){return Object(o["a"])({url:"/v1/app/apps-with-env",method:"get"})}function r(){return Object(o["a"])({url:"/v1/app/all",method:"get"})}function i(){return Object(o["a"])({url:"/v1/app/names",method:"get"})}function l(e){return Object(o["a"])({url:"/v1/app/detail",method:"get",params:{name:e}})}function c(e){return Object(o["a"])({url:"/v1/app",method:"post",data:e})}function p(e){return Object(o["a"])({url:"/v1/app",method:"put",data:e})}function u(e){return Object(o["a"])({url:"/v1/app/",method:"delete",params:{name:e}})}function d(e,t,n){return Object(o["a"])({url:"/v1/app/address",method:"get",params:{cluster:e,namespace:t,app:n}})}function f(e){return Object(o["a"])({url:"/v1/app/git-tag",method:"get",params:{app:e}})}function m(e){return Object(o["a"])({url:"/v1/app/git-tag",method:"post",data:e})}function v(e){return Object(o["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:e})}function h(e){return Object(o["a"])({url:"/v1/app/git-tag",method:"delete",data:e})}function b(e,t){return Object(o["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:e,search_name:t}})}function g(e,t){return Object(o["a"])({url:"/v1/app/permission",method:"put",data:{app:e,orgs:t}})}function _(e,t){return Object(o["a"])({url:"/v1/app/git-modules",method:"get",params:{app:e,pipelineId:t||""}})}function x(){return Object(o["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}function y(){return Object(o["a"])({url:"/v1/app/count-pipelines",method:"get"})}},cd1c:function(e,t,n){var o=n("e853");e.exports=function(e,t){return new(o(e))(t)}},d2c8:function(e,t,n){var o=n("aae3"),s=n("be13");e.exports=function(e,t,n){if(o(t))throw TypeError("String#"+n+" doesn't accept regex!");return String(s(e))}},e853:function(e,t,n){var o=n("d3f4"),s=n("1169"),a=n("2b4c")("species");e.exports=function(e){var t;return s(e)&&(t=e.constructor,"function"!=typeof t||t!==Array&&!s(t.prototype)||(t=void 0),o(t)&&(t=t[a],null===t&&(t=void 0))),void 0===t?Array:t}},f559:function(e,t,n){"use strict";var o=n("5ca1"),s=n("9def"),a=n("d2c8"),r="startsWith",i=""[r];o(o.P+o.F*n("5147")(r),"String",{startsWith:function(e){var t=a(this,e,r),n=s(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),o=String(e);return i?i.call(t,o,n):t.slice(n,n+o.length)===o}})},fbd2:function(e,t,n){"use strict";n("26ef")}}]);