(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-59d2e235"],{"1e42":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{display:"inline"}},[i("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},o=[],r=(i("a481"),i("25ca")),n=i("21a6"),l=i.n(n),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=r["a"].table_to_book(t,{raw:!0}),i=r["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var a=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";l.a.saveAs(new Blob([i],{type:"application/octet-stream"}),a)}catch(o){this.$message.error("导出失败, err: "+o.message),console.error(o)}return i}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},d=s,c=i("2877"),u=Object(c["a"])(d,a,o,!1,null,null,null);e["a"]=u.exports},"24fd":function(t,e,i){"use strict";i.d(e,"a",(function(){return o})),i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return n}));var a=i("b775");function o(){return Object(a["a"])({url:"/v1/org/all",method:"get"})}function r(t){return Object(a["a"])({url:"/v1/org",method:"post",data:t})}function n(t){return Object(a["a"])({url:"/v1/org",method:"put",data:t})}},"4d45":function(t,e,i){"use strict";i("8829")},"624b":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[t._m(0),t._v(" "),i("div",[i("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")]),t._v(" "),i("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),t._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{type:"index"}}),t._v(" "),i("el-table-column",{attrs:{label:"部门",width:"220px",prop:"name"}}),t._v(" "),i("el-table-column",{attrs:{label:"成员",prop:"users"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",{staticClass:"org-users"},t._l(e.row.users,(function(e){return i("el-tag",{attrs:{type:"info",effect:"dark"}},[t._v(t._s(e))])})),1)]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),i("el-table-column",{attrs:{label:"操作",width:"180px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(i){return t.editPage(e.row)}}},[t._v("修改\n        ")]),t._v(" "),i("el-popconfirm",{attrs:{title:"确定要删除【 "+e.row.name+" 】吗？"},on:{confirm:function(i){return t.deleteApp(e.$index,e.row)}}},[i("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1)]}}])})],1),t._v(" "),i("el-dialog",{attrs:{title:t.dialogEditTitle,visible:t.dialogEditVisible,width:"900px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[i("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[i("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"ID"}},[i("el-input",{attrs:{disabled:!0},model:{value:t.dialogEditForm.id,callback:function(e){t.$set(t.dialogEditForm,"id",e)},expression:"dialogEditForm.id"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"名称",prop:"name"}},[i("el-input",{attrs:{type:"text",disabled:t.dialogEditForm.id>0},model:{value:t.dialogEditForm.name,callback:function(e){t.$set(t.dialogEditForm,"name",e)},expression:"dialogEditForm.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"描述",prop:"remark"}},[i("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"成员",prop:"users"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"","filter-method":function(e){t.userPinYinMatch(e)},placeholder:"请选择"},model:{value:t.dialogEditForm.users,callback:function(e){t.$set(t.dialogEditForm,"users",e)},expression:"dialogEditForm.users"}},t._l(t.userOptions,(function(t){return i("el-option",{attrs:{label:t,value:t}})})),1)],1)],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createOrUpdate()}}},[t._v("确 定")])],1)],1)],1)},o=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[i("tr",[i("th",{staticStyle:{width:"70px","text-align":"right","vertical-align":"top"}},[t._v("说明:")]),t._v(" "),i("td",{staticStyle:{width:"1000px"}},[t._v("发布系统基于部门来给应用进行授权。比如只允许部门A的同学才能发布应用app1")])]),t._v(" "),i("tr",[i("th",{staticStyle:{width:"70px","text-align":"right","vertical-align":"top"}},[t._v("部门新建:")]),t._v(" "),i("td",{staticStyle:{width:"1000px"}},[t._v("只有系统管理员拥有部门新建权限")])]),t._v(" "),i("tr",[i("th",{staticStyle:{width:"70px","text-align":"right","vertical-align":"top"}},[t._v("成员修改:")]),t._v(" "),i("td",{staticStyle:{width:"1000px"}},[t._v("部门内的所有成员可以把其他同学添加到部门内")])])])}],r=(i("6762"),i("2fdb"),i("4917"),i("24fd")),n=i("c24f"),l=i("b144"),s=i("d22a"),d=i("1e42"),c={components:{ExportButton:d["a"]},data:function(){return{tableData:[],tableLoading:!1,dialogEditTitle:"",dialogEditVisible:!1,dialogEditForm:{id:0,name:"",remark:"",users:[]},userOptions:[],userAllOptions:[],dialogEditFormRules:{name:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}},computed:{},watch:{},mounted:function(){this.loadUserNames(),this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(r["a"])().then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},loadUserNames:function(){var t=this;Object(n["f"])().then((function(e){t.userOptions=e.data,t.userAllOptions=e.data})).catch((function(e){t.$message.error("加载用户数据出错：",e.message)}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.name="",this.dialogEditForm.remark="",this.dialogEditForm.users=[]},createPage:function(){this.dialogEditTitle="新建",this.dialogEditVisible=!0,this.resetEditForm()},deleteApp:function(t,e){this.$message.info("todo")},editPage:function(t){this.dialogEditForm=Object(l["a"])(t),this.dialogEditVisible=!0},createOrUpdate:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;var i=t.dialogEditForm.id>0?r["c"]:r["b"];i(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))},userPinYinMatch:function(t){t&&(this.userOptions=this.userAllOptions.reduce((function(e,i){return(s["a"].match(i,t)||i.includes(t))&&e.push(i),e}),[]))}}},u=c,p=(i("4d45"),i("2877")),f=Object(p["a"])(u,a,o,!1,null,null,null);e["default"]=f.exports},8829:function(t,e,i){},b144:function(t,e,i){"use strict";function a(t){return JSON.parse(JSON.stringify(t))}function o(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function r(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}i.d(e,"a",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return r}))}}]);