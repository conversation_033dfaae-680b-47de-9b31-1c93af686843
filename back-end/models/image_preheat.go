package models

import (
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
)

type ImagePreheat struct {
	gorm.Model
	Name    string           `gorm:"size:128;not null;index" json:"name"`    // 任务名称
	Remark  string           `gorm:"size:256" json:"remark"`                 // 备注
	Query   datatype.Map     `gorm:"type:text;default:'{}'" json:"query"`    // 筛选镜像的查询条件
	Images  datatype.StrList `gorm:"type:text;default:'[]'"   json:"images"` //预热的镜像
	Creator string           `gorm:"size:64;not null" json:"creator"`        // 创建人
}
