package models

import (
	"strings"

	"gorm.io/gorm"
)

type ParentPom struct {
	gorm.Model
	Name       string `gorm:"size:128;<-:create;index;not null" json:"name"`
	ShowName   string `gorm:"size:128;not null" json:"showName"`
	Remark     string `gorm:"size:512" json:"remark"`
	IsArchive  bool   `gorm:"default:false" json:"isArchive"`
	Enable     bool   `gorm:"default:true" json:"enable"`
	SortRank   int64  `gorm:"default:0" json:"sortRank"`
	Namespaces string `gorm:"size:2048" json:"namespaces" default:""`
}

func (p ParentPom) GetNamespaces() []string {
	ret := make([]string, 0, 5)
	if strings.Contains(p.Namespaces, "_all_") {
		return ret
	}
	for _, namespace := range strings.Split(p.Namespaces, ",") {
		it := strings.TrimSpace(namespace)
		if it != "" {
			ret = append(ret, it)
		}
	}
	return ret
}

func (p ParentPom) IsAllowNamespace(namespace string) bool {
	namespaces := p.GetNamespaces()
	if len(namespaces) < 1 {
		return true
	}
	for _, it := range namespaces {
		if it == namespace {
			return true
		}
	}
	return false
}
