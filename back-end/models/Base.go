package models

import (
	"database/sql/driver"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type DBTime time.Time
type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt DBTime         `json:"createdAt"`
	UpdatedAt DBTime         `json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deletedAt"`
}

func (t DBTime) Time() time.Time {
	return time.Time(t)
}

func (t *DBTime) UnmarshalJSON(b []byte) error {
	if len(b) == 4 && string(b) == "null" {
		*t = DBTime(time.Time{})
		return nil
	}
	parsedTime, err := time.Parse(`"2006-01-02 15:04:05"`, string(b))
	if err != nil {
		return err
	}
	*t = DBTime(parsedTime)
	return nil
}

func (t DBTime) MarshalJSON() ([]byte, error) {
	if time.Time(t).IsZero() {
		return []byte("null"), nil
	}
	formattedTime := time.Time(t).Format("2006-01-02 15:04:05")
	return []byte(fmt.Sprintf(`"%s"`, formattedTime)), nil
}

// Scan implements the Scanner interface.
func (n *DBTime) Scan(value any) error {
	if value == nil {
		*n = DBTime(time.Time{})
		return nil
	}
	switch v := value.(type) {
	case time.Time:
		*n = DBTime(v)
		return nil
	default:
		return fmt.Errorf("unsupported Scan source: %T", value)
	}
}

// Value implements the driver Valuer interface.
func (n DBTime) Value() (driver.Value, error) {
	return time.Time(n), nil
}
