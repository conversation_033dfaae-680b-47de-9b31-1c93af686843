package models

import (
	"gorm.io/gorm"
)

// ScaleLog 扩缩容日志
type ScaleLog struct {
	gorm.Model
	Operate     string `gorm:"size:64;index;not null"`
	App         string `gorm:"size:64;index;not null"`
	Cluster     string `gorm:"size:32;index;not null"`
	Namespace   string `gorm:"size:32;index;not null"`
	OldReplicas int32  `gorm:"size:3"`
	NewReplicas int32  `gorm:"size:3"`
	Author      string `gorm:"size:32"`
	Remark      string `gorm:"size:2048;index;not null"`
}
