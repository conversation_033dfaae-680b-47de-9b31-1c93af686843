package datatype

import (
	"database/sql/driver"
	"strings"
)

type Webhook struct {
	Url string `json:"url" form:"url"`
}

func (e Webhook) IsEmpty() bool {

	return strings.TrimSpace(e.Url) == ""
}

// Value return json value, implement driver.Valuer interface
func (e Webhook) Value() (driver.Value, error) {
	return ValueOfJson(e)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (e *Webhook) Scan(value interface{}) error {
	return ScanOfJson(e, value)
}
