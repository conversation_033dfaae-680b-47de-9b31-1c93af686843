package datatype

import (
	"github.com/golang-module/carbon"
	"testing"
)

func TestTimePeriod_IsIncluded(t *testing.T) {
	testData := TimePeriod{
		// 周一和周五
		DaysOfWeek: []int{1, 5},
		TimeRange:  TimeRange{"22:00", "00:00"},
	}
	// 伪造数据当前时间
	now := carbon.Parse("2021-09-03 23:59:05")
	if !testData.isIncludedCurrentDaysOfWeek(now) {
		t.Fatalf("星期: %v,当前时间不在区间内: %s", testData.DaysOfWeek, now)
		return
	}
	if !testData.isIncludedCurrentTimeRanges(now) {
		t.Fatalf("时间范围: %v，当前时间不在区间内: %s", testData.TimeRange, now)
	}

	// 测试时间不在区间内
	testData2 := TimePeriod{
		// 周一和周五
		DaysOfWeek: []int{2, 6},
		TimeRange:  TimeRange{"00:00", "07:00"},
	}
	if testData2.isIncludedCurrentDaysOfWeek(now) {
		t.Fatalf("星期: %v,当前时间在区间内: %s", testData.DaysOfWeek, now)
		return
	}
	if testData2.isIncludedCurrentTimeRanges(now) {
		t.Fatalf("时间范围: %v，当前时间在区间内: %s", testData.TimeRange, now)
	}
}
