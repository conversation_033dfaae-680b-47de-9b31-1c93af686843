package datatype

import (
	"database/sql/driver"
	"fmt"
	"github.com/golang-module/carbon"
	"sort"
	"strings"
)

const (
	timeLayout = "15:04"
	dateLayout = "2006-01-02 15:04:05"
)

type TimePeriod struct {
	DaysOfWeek []int     `json:"daysOfWeek"`
	TimeRange  TimeRange `json:"timeRange"`
}

type TimeRange [2]string

func (t TimeRange) StartTime() string {
	return t[0]
}
func (t TimeRange) EndTime() string {
	return t[1]
}

func (t *TimePeriod) IsIncluded() bool {
	now := carbon.Now()
	if !t.isIncludedCurrentDaysOfWeek(now) {
		return false
	}
	return t.isIncludedCurrentTimeRanges(now)
}

// 判断当前时间是否包含在时间范围内
func (t *TimePeriod) isIncludedCurrentTimeRanges(now carbon.Carbon) bool {
	n := carbon.ParseByLayout(now.Layout(timeLayout), timeLayout)
	startTime := carbon.ParseByLayout(t.TimeRange.StartTime(), timeLayout)
	endTime := carbon.ParseByLayout(t.TimeRange.EndTime(), timeLayout)
	return n.BetweenIncludedBoth(startTime, endTime)
}

// 判断当前时间是否包含星期数
func (t *TimePeriod) isIncludedCurrentDaysOfWeek(now carbon.Carbon) bool {
	for _, v := range t.DaysOfWeek {
		if v == now.DayOfWeek() {
			return true
		}
	}
	return false
}

type TimePeriods []TimePeriod

// Value return json value, implement driver.Valuer interface
func (t TimePeriods) Value() (driver.Value, error) {
	return ValueOfJson(t)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (t *TimePeriods) Scan(value interface{}) error {
	return ScanOfJson(t, value)
}

func (t TimePeriods) IsIncluded() bool {
	if len(t) < 1 {
		return true
	}
	for _, v := range t {
		if v.IsIncluded() {
			return true
		}
	}
	return false
}

func (t TimePeriods) Desc() string {
	if len(t) < 1 {
		return ""
	}
	descItems := make([]string, 0, 5)
	days := [7]string{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"}
	for _, it := range t {
		for _, day := range it.DaysOfWeek {
			descItems = append(descItems, fmt.Sprintf("%d%s: %s 至 %s", day, days[day-1], it.TimeRange[0], it.TimeRange[1]))
		}
	}
	sort.Strings(descItems)
	ret := make([]string, 0, len(descItems))
	//去掉排序用的星期数字
	for _, it := range descItems {
		ret = append(ret, it[1:])
	}
	//可优化：同一天的时间段做聚合
	return strings.Join(ret, "\n")
}
