package datatype

import (
	"database/sql/driver"
)

type BuildModule struct {
	GitUrl            string `json:"gitUrl"`
	Module            string `json:"module"`
	Tag               string `json:"tag"`
	ArtifactBaseImage string `json:"artifactBaseImage"`
	ArtifactImage     string `json:"artifactImage"`
	ArtifactPathSrc   string `json:"artifactPathSrc"`
	ArtifactPathDst   string `json:"artifactPathDst"`
}

type BuildModules []BuildModule

// Value return json value, implement driver.Valuer interface
func (j BuildModule) Value() (driver.Value, error) {
	return ValueOfJson(j)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (j *BuildModule) Scan(value interface{}) error {
	return ScanOfJson(j, value)
}
