package datatype

import (
	"database/sql/driver"
)

type EolinkerID struct {
	ProjectID   string `json:"projectID" form:"projectID"`
	TimedTaskID string `json:"timedTaskID" form:"timedTaskID"`
}

type EolinkerIDs []EolinkerID

func (e EolinkerIDs) IsEmpty() bool {
	for _, v := range e {
		if v.ProjectID != "" && v.TimedTaskID != "" {
			return false
		}
	}
	return true
}

// Value return json value, implement driver.Valuer interface
func (e EolinkerIDs) Value() (driver.Value, error) {
	return ValueOfJson(e)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (e *EolinkerIDs) Scan(value interface{}) error {
	return ScanOfJson(e, value)
}
