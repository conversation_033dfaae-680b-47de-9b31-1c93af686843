package datatype

import (
	"database/sql/driver"
	"encoding/json"
	"fs-k8s-app-manager/pkg/util/docker"
	"fs-k8s-app-manager/pkg/util/strslice"
	"strings"
)

type DeployModule struct {
	GitUrl            string `json:"gitUrl"`
	Module            string `json:"module"`
	ContextPath       string `json:"contextPath"`
	Tag               string `json:"tag"`
	CommitID          string `json:"commitId"`
	ArtifactBaseImage string `json:"artifactBaseImage"`
	ArtifactImage     string `json:"artifactImage"`
	ArtifactPathSrc   string `json:"artifactPathSrc"`
	ArtifactPathDst   string `json:"artifactPathDst"`
}

type DeployModules []DeployModule

func (j DeployModules) GetTag(gitUrl string) string {
	for _, it := range j {
		if it.GitUrl == gitUrl {
			return it.Tag
		}
	}
	return ""
}

// Value return json value, implement driver.Valuer interface
func (j DeployModules) Value() (driver.Value, error) {
	return ValueOfJson(j)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (j *DeployModules) Scan(value interface{}) error {
	return ScanOfJson(j, value)
}

func (j DeployModules) ToJson() (string, error) {
	modules, err := json.Marshal(j)
	if err != nil {
		return "", err
	}
	return string(modules), nil
}
func (j *DeployModules) FromJson(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), j)
}

func (j DeployModules) GitTags() string {
	ret := make([]string, 0, len(j))
	for _, it := range j {
		if !strslice.Find(ret, it.Tag) {
			ret = append(ret, it.Tag)
		}
	}
	if len(ret) < 1 {
		return ""
	}
	return strings.Join(ret, " | ")
}

func (j DeployModules) Version() string {
	ret := make([]string, 0, len(j))
	for _, it := range j {
		imageTag := docker.GetDockerImageTag(it.ArtifactImage)
		if !strslice.Find(ret, imageTag) {
			ret = append(ret, imageTag)
		}
	}
	if len(ret) < 1 {
		return ""
	}
	return strings.Join(ret, " | ")
}
