package datatype

import (
	"database/sql/driver"
	"fs-k8s-app-manager/pkg/constant"
)

type Env struct {
	Name  string           `json:"name" form:"name" yaml:"name"`
	Value string           `json:"value" form:"value" yaml:"value"`
	Type  constant.EnvType `json:"type" form:"type" yaml:"type"`
}

type Envs []Env

// Value return json value, implement driver.Valuer interface
func (j Envs) Value() (driver.Value, error) {
	return ValueOfJson(j)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (j *Envs) Scan(value interface{}) error {
	return ScanOfJson(j, value)
}

func (j Envs) FindByName(name string) *Env {
	for _, it := range j {
		if it.Name == name {
			return &it
		}
	}
	return nil
}

func (j Envs) IndexOf(name string) (found bool, index int) {
	for idx, it := range j {
		if it.Name == name {
			return true, idx
		}
	}
	return false, -1
}

//func RemoveEnvItem(envs Envs, name string) (Envs, bool) {
//	idx := -1
//	for i, it := range envs {
//		if it.Name == name {
//			idx = i
//			break
//		}
//	}
//	if idx >= 0 {
//		ret := append(envs[:idx], envs[idx+1:]...)
//		return ret, true
//	}
//	return envs, false
//}
