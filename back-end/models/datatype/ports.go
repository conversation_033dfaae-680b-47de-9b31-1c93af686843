package datatype

import (
	"database/sql/driver"
	"fs-k8s-app-manager/pkg/constant"
)

type Port struct {
	Name  string            `json:"name" form:"name" yaml:"name"`
	Value uint              `json:"value" form:"value" yaml:"value"`
	Type  constant.PortType `json:"type" form:"type" yaml:"type"`
}

type Ports []Port

// Value return json value, implement driver.Valuer interface
func (j Ports) Value() (driver.Value, error) {
	return ValueOfJson(j)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (j *Ports) Scan(value interface{}) error {
	return ScanOfJson(j, value)
}
