package datatype

import (
	"database/sql/driver"
)

type AppModule struct {
	GitUrl      string `json:"gitUrl" binding:"required"`
	Module      string `json:"module"`
	ContextPath string `json:"contextPath" binding:"required"`
}

func (m AppModule) Equals(m2 AppModule) bool {
	if m.GitUrl != m2.GitUrl ||
		m.Module != m2.Module ||
		m.ContextPath != m2.ContextPath {
		return false
	}
	return true
}

func (m AppModule) ToString() string {
	return m.GitUrl + "#" + m.Module + "#" + m.ContextPath
}

type AppModules []AppModule

func (m AppModules) Equals(m2 AppModules) bool {
	if len(m) != len(m2) {
		return false
	}
	//todo: 做排序处理
	for i := 0; i < len(m); i++ {
		if !m[i].Equals(m2[i]) {
			return false
		}
	}
	return true
}

// Value return json value, implement driver.Valuer interface
func (m AppModules) Value() (driver.Value, error) {
	return ValueOfJson(m)
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (m *AppModules) Scan(value interface{}) error {
	return ScanOfJson(m, value)
}
