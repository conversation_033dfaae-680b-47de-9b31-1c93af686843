package datatype

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
)

func ValueOfJson(dt interface{}) (driver.Value, error) {
	if dt == nil {
		return nil, nil
	}
	jsonBytes, err := json.Marshal(dt)
	if err != nil {
		return nil, err
	}
	return string(jsonBytes), nil
}

func ScanOfJson(dt interface{}, value interface{}) error {
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSON value:", value))
	}
	var err error
	if len(bytes) > 0 {
		err = json.Unmarshal(bytes, dt)
	}
	return err
}
