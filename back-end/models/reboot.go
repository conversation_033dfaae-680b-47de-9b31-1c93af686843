package models

import (
	"gorm.io/gorm"
	"time"
)

// Reboot 服务定时重启
type Reboot struct {
	gorm.Model
	Cluster    string `gorm:"size:64;<-:create;index;not null"`
	Namespace  string `gorm:"size:64;<-:create;index;not null"`
	App        string `gorm:"size:64;<-:create;index;not null"`
	Author     string `gorm:"size:32"`
	Remark     string `gorm:"size:512"`
	RebootHour int    `gorm:"size:2;default:0"`
}

func (r Reboot) InRebootTime() bool {
	return time.Now().Hour() == r.RebootHour
}
