package models

import "gorm.io/gorm"

type CommonDataType string

const (
	AppVersionSnapshot CommonDataType = "AppVersionSnapshot"
)

type CommonData struct {
	gorm.Model
	Owner    string `gorm:"index;size:32"`
	DataType string `gorm:"index;size:32"`
	Column01 string `gorm:"index;size:32"`
	Column02 string `gorm:"index;size:32"`
	Column03 string `gorm:"index;size:32"`
	Column04 string `gorm:"index;size:32"`
	Column05 string `gorm:"index;size:32"`
	Content  string `gorm:"type:text"`
}
