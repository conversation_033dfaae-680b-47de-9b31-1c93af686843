package models

import (
	"fmt"
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
	"time"
)

type TaskPhase string

const (
	TASK_PHASE_WAIT    TaskPhase = "WAIT"
	TASK_PHASE_SKIP    TaskPhase = "SKIP"
	TASK_PHASE_RUNNING TaskPhase = "RUNNING"
	TASK_PHASE_CANCEL  TaskPhase = "CANCEL"
	TASK_PHASE_FAIL    TaskPhase = "FAIL"
	TASK_PHASE_SUCCESS TaskPhase = "SUCCESS"
)

type TaskType string

const (
	TASK_TYPE_BUILD    TaskType = "BUILD"
	TASK_TYPE_DEPLOY   TaskType = "DEPLOY"
	TASK_TYPE_EOLINKER TaskType = "EOLINKER"
	TASK_TYPE_WEBHOOK  TaskType = "WEBHOOK"
)

type Task struct {
	BaseModel
	Status         TaskPhase    `gorm:"size:64;index;default:'WAIT'" json:"status"`
	JobID          uint         `gorm:"<-:create;index;not null" json:"jobId"`
	Title          string       `gorm:"size:64" json:"title"`
	Summary        string       `gorm:"size:128" json:"summary"`
	Type           TaskType     `gorm:"size:16;index" json:"type"`
	Params         datatype.Map `gorm:"type:jsonb;default:'{}';index" json:"params"`
	Attributes     datatype.Map `gorm:"type:jsonb;default:'{}'" json:"attributes"`
	Output         string       `gorm:"type:text" json:"output"`
	Remark         string       `gorm:"size:1024" json:"remark"`
	Author         string       `gorm:"size:64;index" json:"author"`
	TimeoutSeconds int          `gorm:"default:600" json:"timeoutSeconds"`
	StatusDesc     string       `gorm:"-:all" json:"statusDesc"`
	TimeCost       int          `gorm:"-:all" json:"timeCost"`
	StartTime      DBTime       `json:"startTime"`
	EndTime        DBTime       `json:"endTime"`
}

func (s Task) IsEnd() bool {
	return !s.IsWait() && !s.IsRunning()
}
func (s Task) IsWait() bool {
	return s.Status == TASK_PHASE_WAIT
}
func (s Task) IsRunning() bool {
	return s.Status == TASK_PHASE_RUNNING
}
func (s Task) IsSuccess() bool {
	return s.Status == TASK_PHASE_SUCCESS
}
func (s Task) IsCancel() bool {
	return s.Status == TASK_PHASE_CANCEL
}
func (s Task) IsFail() bool {
	return s.Status == TASK_PHASE_FAIL
}

func (s Task) IsSkip() bool {
	return s.Status == TASK_PHASE_SKIP
}

func (s *Task) AfterFind(tx *gorm.DB) (err error) {
	s.TimeCost = s.GetTimeCost()
	s.StatusDesc = s.GetStatusDesc()
	return
}

func (s Task) GetStatusDesc() string {
	ret := "未知"
	switch s.Status {
	case TASK_PHASE_WAIT:
		ret = "等待中"
	case TASK_PHASE_SKIP:
		ret = "跳过"
	case TASK_PHASE_RUNNING:
		ret = "执行中..."
	case TASK_PHASE_SUCCESS:
		ret = "成功"
	case TASK_PHASE_FAIL:
		ret = "失败"
	case TASK_PHASE_CANCEL:
		ret = "已取消"
	default:
	}
	costTime := s.GetTimeCost()
	if costTime > 0 {
		ret = fmt.Sprintf("%s (耗时：%d秒)", ret, costTime)
	}
	return ret
}

func (s Task) GetTimeCost() (ret int) {
	if s.StartTime.Time().IsZero() {
		return
	}
	endTime := time.Now()
	if s.IsEnd() {
		endTime = s.EndTime.Time()
	}
	ret = int(endTime.Unix() - s.StartTime.Time().Unix())
	return
}
