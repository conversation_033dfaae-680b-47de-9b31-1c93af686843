package models

import (
	"gorm.io/gorm"
)

type PodEvent struct {
	gorm.Model
	Pod       string `json:"pod" gorm:"index:pod_nme"`
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	IP        string `json:"ip"`
	Type      string `json:"type"`
	Reason    string `json:"reason" gorm:"index:reason"`
	Desc      string `json:"desc"`
}

func Create(entity *PodEvent) error {
	if err := db.Create(entity).Error; err != nil {
		return err
	}
	return nil
}
