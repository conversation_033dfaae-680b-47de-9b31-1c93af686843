package models

import (
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
	"time"
)

type JobPhase string

const (
	JOB_PHASE_WAIT    JobPhase = "WAIT"
	JOB_PHASE_SKIP    JobPhase = "SKIP"
	JOB_PHASE_RUNNING JobPhase = "RUNNING"
	JOB_PHASE_CANCEL  JobPhase = "CANCEL"
	JOB_PHASE_FAIL    JobPhase = "FAIL"
	JOB_PHASE_SUCCESS JobPhase = "SUCCESS"
)

func (s JobPhase) Desc() string {
	switch s {
	case JOB_PHASE_WAIT:
		return "等待中"
	case JOB_PHASE_RUNNING:
		return "执行中"
	case JOB_PHASE_SUCCESS:
		return "成功"
	case JOB_PHASE_FAIL:
		return "失败"
	case JOB_PHASE_CANCEL:
		return "取消"
	case JOB_PHASE_SKIP:
		return "跳过"
	default:
		return "Unknown"
	}
}

type JobType string

const (
	JOB_TYPE_CI JobType = "CI"
	JOB_TYPE_CD JobType = "CD"
)

func (s JobType) Desc() string {
	switch s {
	case JOB_TYPE_CI:
		return "构建"
	case JOB_TYPE_CD:
		return "发布"
	default:
		return "Unknown"
	}
}

type Job struct {
	BaseModel
	Status      JobPhase     `gorm:"size:64;index;default:'WAIT'" json:"status"`
	BeforeJobId uint         `gorm:"index;" json:"beforeJobId"`
	App         string       `gorm:"size:128;<-:create;index" json:"app"`
	PipelineId  uint         `gorm:"<-:create;index" json:"pipelineId"`
	Type        JobType      `gorm:"size:16;index" json:"type"`
	Params      datatype.Map `gorm:"type:jsonb;default:'{}';index" json:"params"`
	Remark      string       `gorm:"size:1024" json:"remark"`
	Author      string       `gorm:"size:64;index" json:"author"`
	TimeCost    int          `gorm:"-:all" json:"timeCost"`
	StartTime   DBTime       `json:"startTime"`
	EndTime     DBTime       `json:"endTime"`
}

func (s Job) IsEnd() bool {
	return !s.IsWait() && !s.IsRunning()
}
func (s Job) IsWait() bool {
	return s.Status == JOB_PHASE_WAIT
}
func (s Job) IsRunning() bool {
	return s.Status == JOB_PHASE_RUNNING
}
func (s Job) IsSuccess() bool {
	return s.Status == JOB_PHASE_SUCCESS
}
func (s Job) IsCancel() bool {
	return s.Status == JOB_PHASE_CANCEL
}
func (s Job) IsFail() bool {
	return s.Status == JOB_PHASE_FAIL
}

func (s Job) IsSkip() bool {
	return s.Status == JOB_PHASE_SKIP
}

func (s *Job) AfterFind(tx *gorm.DB) (err error) {
	s.TimeCost = s.GetTimeCost()
	return
}

func (s Job) GetTimeCost() (ret int) {
	if s.StartTime.Time().IsZero() {
		return
	}
	endTime := time.Now()
	if s.IsEnd() {
		endTime = s.EndTime.Time()
	}
	ret = int(endTime.Unix() - s.StartTime.Time().Unix())
	return
}
