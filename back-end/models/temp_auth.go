package models

import (
	"fs-k8s-app-manager/pkg/constant"
	"gorm.io/gorm"
	"time"
)

/*
*
临时授权, 只能授权当天时间段
*/
type TempAuth struct {
	gorm.Model
	App        string `gorm:"size:64;<-:create;index;not null"`
	User       string `gorm:"size:32;index"`
	Operate    string `gorm:"size:32;index"`
	Approver   string `gorm:"size:32;index"`
	Approved   bool   `gorm:"not null;default:false"`
	StartTime  time.Time
	EndTime    time.Time
	Authorizer string `gorm:"size:32"`
	Remark     string `gorm:"size:256"`
}

func (t TempAuth) NowInTimeRange() bool {
	now := time.Now()
	return now.After(t.StartTime) && now.Before(t.EndTime)
}

func (t TempAuth) OperateDesc() string {
	if t.Operate == constant.TEMP_AUTH_OPERATE_DEPLOY {
		return "镜像构建+发布"
	}
	return "未知"
}
