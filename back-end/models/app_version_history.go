package models

import (
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
)

// AppVersionHistory 应用历史版本记录
type AppVersionHistory struct {
	gorm.Model
	App           string                 `gorm:"size:64;index;not null"`
	Cluster       string                 `gorm:"size:32;index;not null"`
	Namespace     string                 `gorm:"size:32;index;not null"`
	Version       string                 `gorm:"size:256"`
	RecordDate    string                 `gorm:"size:16;index;not null"`
	DeployModules datatype.DeployModules `gorm:"type:text"`
	DeployRemark  string                 `gorm:"size:256"`
	DeployAuthor  string                 `gorm:"size:32"`
	DeployTime    string                 `gorm:"size:32"`
}
