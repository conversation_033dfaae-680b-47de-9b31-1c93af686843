package models

import (
	"fmt"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"time"
)

/**
定时扩缩容
*/
type CronScale struct {
	gorm.Model
	App       string `gorm:"size:64;<-:create;index;not null"`
	Cluster   string `gorm:"size:32;<-:create;index;not null"`
	Namespace string `gorm:"size:32;<-:create;index;not null"`
	StartTime string `gorm:"size:12"`
	EndTime   string `gorm:"size:12"`
	Replicas  uint   `gorm:"size:3"`
	Author    string `gorm:"size:32"`
}

func (c CronScale) InTimeRange() bool {
	time1, err := time.Parse("15:04", c.StartTime)
	if err != nil {
		log.Info("StartTime parse fail ", err.Error())
		return false
	}
	time2, err := time.Parse("15:04", c.EndTime)
	if err != nil {
		log.Info("EndTime parse fail ", err.Error())
		return false
	}
	n := time.Now()
	startTime := time.Date(n.Year(), n.Month(), n.Day(), time1.Hour(), time1.Minute(), 0, 0, n.Location())
	endTime := time.Date(n.Year(), n.Month(), n.Day(), time2.Hour(), time2.Minute(), 0, 0, n.Location())

	return n.After(startTime) && n.Before(endTime)
}

func (c CronScale) AppFullName() string {
	return fmt.Sprintf("%s/%s/%s", c.Cluster, c.Namespace, c.App)
}
