package models

import (
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
)

type User struct {
	gorm.Model
	Username   string           `gorm:"size:64;index" json:"username"`
	RealName   string           `gorm:"size:64;index" json:"realName"`
	PinYin     string           `gorm:"size:64;index" json:"pinYin"`
	Email      string           `gorm:"size:64" json:"email"`
	EmployeeId int              `json:"employeeId"`
	Roles      datatype.StrList `gorm:"type:text;default:'[]'" json:"roles"`
	RecentApps datatype.StrList `gorm:"type:text;default:'[]'" json:"recentApps"`
	RecentPods datatype.StrList `gorm:"type:text;default:'[]'" json:"recentPods"`
}

//func (u User) FullName() string {
//	return fmt.Sprintf("%s(%s)", u.RealName, u.Username)
//}
