package data

import (
	"database/sql"
	"fs-k8s-app-manager/pkg/constant"
)

type Resources struct {
	RequestCPU    float32 `json:"requestCPU" form:"requestCPU" yaml:"requestCPU"`
	RequestMemory uint    `json:"requestMemory" form:"requestMemory" yaml:"requestMemory"`
	LimitCPU      float32 `json:"limitCPU" form:"limitCPU" yaml:"limitCPU"`
	LimitMemory   uint    `json:"limitMemory" form:"limitMemory" yaml:"limitMemory"`
}

type Schedule struct {
	Strategy constant.ScheduleStrategy `json:"strategy" form:"strategy" gorm:"size:32" yaml:"strategy"`
	Node     string                    `json:"node" form:"node" gorm:"size:64" yaml:"node"`
}

type PVC struct {
	Enable    bool   `json:"enable" form:"enable" gorm:"default:false" yaml:"enable"`
	Name      string `json:"name" form:"name" gorm:"size:64" yaml:"name"`
	MountPath string `json:"mountPath" form:"mountPath" gorm:"size:128" yaml:"mountPath"`
}

type Probe struct {
	Enable              bool `json:"enable" form:"enable" gorm:"default:true" yaml:"enable"`
	InitialDelaySeconds uint `json:"initialDelaySeconds" form:"initialDelaySeconds" yaml:"initialDelaySeconds"`
	PeriodSeconds       uint `json:"periodSeconds" form:"periodSeconds" yaml:"periodSeconds"`
	TimeoutSeconds      uint `json:"timeoutSeconds" form:"timeoutSeconds" yaml:"timeoutSeconds"`
	FailureThreshold    uint `json:"failureThreshold" form:"failureThreshold" yaml:"failureThreshold"`
	SuccessThreshold    uint `json:"successThreshold" form:"successThreshold" gorm:"default:1" yaml:"successThreshold"`
}

func (p Probe) Equals(other Probe) bool {
	return p.Enable == other.Enable &&
		p.InitialDelaySeconds == other.InitialDelaySeconds &&
		p.PeriodSeconds == other.PeriodSeconds &&
		p.TimeoutSeconds == other.TimeoutSeconds &&
		p.FailureThreshold == other.FailureThreshold &&
		p.SuccessThreshold == other.SuccessThreshold
}

func ToBool(n sql.NullBool) bool {
	if !n.Valid {
		return false
	}
	return n.Bool
}
func ToNullBool(b bool) sql.NullBool {
	return sql.NullBool{
		Bool:  b,
		Valid: true,
	}
}

type PipelineOption struct {
	IsCoreApp          bool `gorm:"default:false" json:"isCoreApp"`
	OnlyDeployTag      bool `gorm:"default:false" json:"onlyDeployTag"`
	AddSysctlKeepalive bool `gorm:"default:false" json:"addSysctlKeepalive"`
	SkyWalkingAgent    bool `gorm:"default:false" json:"skyWalkingAgent"`
	AppLogToKafka      bool `gorm:"default:false" json:"appLogToKafka"`
	BuildUseRuntimeJDK bool `gorm:"default:false" json:"buildUseRuntimeJDK"`
	JvmGcLog           bool `gorm:"default:false" json:"jvmGcLog"`
}
