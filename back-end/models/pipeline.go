package models

import (
	"fmt"
	"fs-k8s-app-manager/models/data"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"gorm.io/gorm"
)

type Pipeline struct {
	gorm.Model
	Status               string                  `gorm:"size:64;index"`
	Author               string                  `gorm:"size:128"`
	App                  string                  `gorm:"size:64;<-:create;index;not null"`
	Cluster              string                  `gorm:"size:64;index;not null"`
	Namespace            string                  `gorm:"size:64;index;not null"`
	BaseImage            string                  `gorm:"size:128;not null"`
	Replicas             uint                    `gorm:"not null"`
	DeployStrategy       constant.DeployStrategy `gorm:"size:64;not null"`
	AppModules           datatype.AppModules     `gorm:"size:20480;type:text;default:'[]'"`
	Resources            data.Resources          `gorm:"embedded;embeddedPrefix:resources_"`
	LivenessProbe        data.Probe              `gorm:"embedded;embeddedPrefix:liveness_"`
	ReadinessProbe       data.Probe              `gorm:"embedded;embeddedPrefix:readiness_"`
	StartupProbe         data.Probe              `gorm:"embedded;embeddedPrefix:startup_"`
	Schedule             data.Schedule           `gorm:"embedded;embeddedPrefix:schedule_"`
	PVC                  data.PVC                `gorm:"embedded;embeddedPrefix:pvc_"`
	Envs                 datatype.Envs           `gorm:"size:4096;type:text;default:'[]'"`
	Ports                datatype.Ports          `gorm:"size:2048;type:text;default:'[]'"`
	PartnerApps          datatype.StrList        `gorm:"size:1024;type:text;default:'[]'"`
	ExclusiveApps        datatype.StrList        `gorm:"size:1024;type:text;default:'[]'"`
	PreStopWebhook       string                  `gorm:"size:1024"`  //关闭前的webhook
	PreStopRetainSeconds uint                    `gorm:"default 20"` //关闭前的保留时间
	EolinkerIDs          datatype.EolinkerIDs    `gorm:"size:2048;type:text;default:'[]'"`
	Webhook              datatype.Webhook        `gorm:"size:4096;type:text;default:'{}'"`
	Options              data.PipelineOption     `gorm:"embedded;embeddedPrefix:options_"`
	Remark               string                  `gorm:"size:256"`
	ExtInitContainer     string                  `gorm:"size:2048"`
}

func (p Pipeline) StatusDesc() string {
	if p.Status == constant.PIPELINE_STATUS_ENABLED {
		return "正常"
	} else if p.Status == constant.PIPELINE_STATUS_DISABLED {
		return "禁用"
	} else if p.Status == constant.PIPELINE_STATUS_AUDIT {
		return "审核中"
	} else if p.Status == constant.PIPELINE_STATUS_MIGRATED {
		return "已迁移"
	} else {
		return p.Status
	}
}

func (p Pipeline) FullName() string {
	return fmt.Sprintf("%s/%s/%s", p.Cluster, p.Namespace, p.App)
}

func (p Pipeline) EnvValue(name string) string {
	env := p.Envs.FindByName(name)
	if env == nil {
		return ""
	}
	return env.Value
}
