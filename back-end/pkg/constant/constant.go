package constant

type ScheduleStrategy string

const (
	SCHEDULE_STRATEGY_PREFERRED ScheduleStrategy = "PREFERRED"
	SCHEDULE_STRATEGY_REQUIRED  ScheduleStrategy = "REQUIRED"
)

type EnvType string

const (
	ENV_TYPE_SYSTEM EnvType = "SYSTEM"
	ENV_TYPE_USER   EnvType = "USER"
)

type PortType string

const (
	PORT_TYPE_SYSTEM PortType = "SYSTEM"
	PORT_TYPE_USER   PortType = "USER"
)

type DeployStrategy string

const (
	DEPLOY_STRATEGY_ROLL_UPDATE DeployStrategy = "ROLL_UPDATE"
	DEPLOY_STRATEGY_RECREATE    DeployStrategy = "RECREATE"
)

const (
	ENV_KEY_MALLOC_ARENA_MAX           string = "MALLOC_ARENA_MAX"
	ENV_KEY_APP_LOG_TO_KAFKA           string = "APP_LOG_TO_KAFKA"
	ENV_KEY_SKY_WALKING_ENABLE         string = "SKY_WALKING_ENABLE"
	ENV_KEY_JACOCO_AGENT_ENABLE        string = "JACOCO_AGENT_ENABLE"
	ENV_KEY_JVM_GC_LOG_ENABLE          string = "JVM_GC_LOG_ENABLE"
	ENV_KEY_SPRING_BOOT_JAR_APP        string = "SPRING_BOOT_JAR_APP"
	ENV_KEY_SPRING_BOOT_METRICS_ENABLE string = "SPRING_BOOT_METRICS_ENABLE"
	SPRING_BOOT_ACTUATOR_PORT          uint   = 8081
	JMX_AGENT_METRICS_PORT             uint   = 8090
	METRICS_PORT_NAME                  string = "metrics"
)

const (
	PIPELINE_STATUS_ENABLED  = "enabled"
	PIPELINE_STATUS_DISABLED = "disabled"
	PIPELINE_STATUS_AUDIT    = "audit"
	PIPELINE_STATUS_MIGRATED = "migrated"
)

type TempAuthOperate string

const (
	TEMP_AUTH_OPERATE_DEPLOY = "deploy"
)

func PipelineStatusIsValid(status string) bool {
	return PIPELINE_STATUS_ENABLED == status ||
		PIPELINE_STATUS_DISABLED == status ||
		PIPELINE_STATUS_AUDIT == status ||
		PIPELINE_STATUS_MIGRATED == status
}
