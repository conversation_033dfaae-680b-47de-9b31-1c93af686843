package eolinker

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"github.com/8treenet/freedom/infra/requests"
	log "github.com/sirupsen/logrus"
	"net/http"
)

const (
	searchProjectPath    = "/automated_test/project/search"
	searchTimedTaskPath  = "/automated_test/timed_task/search"
	executeTimedTaskPath = "/automated_test/timed_task/execute"
	getReportPath        = "/automated_test/report/get"
	taskEnabled          = "启用"
)

type baseResult struct {
	Type      string `json:"type"`
	Status    string `json:"status"`
	ErrorInfo string `json:"error_info"`
}

type project struct {
	ProjectName string `json:"project_name"`
	ProjectID   string `json:"project_id"`
}

type searchProjectResult struct {
	baseResult
	Result []project `json:"result"`
}

type timedTask struct {
	TaskID     uint   `json:"task_id"`
	TaskName   string `json:"task_name"`
	TaskStatus string `json:"task_status"`
}

type searchTimedTaskResult struct {
	baseResult
	Result []timedTask `json:"result"`
}

type getReportResult struct {
	baseResult
	Result Report `json:"result"`
}

type Report struct {
	TestStatus            string        `json:"test_status"`
	SuccessRate           string        `json:"success_rate"`
	ReportDownloadURL     string        `json:"report_download_url"`
	CaseNum               int           `json:"case_num"`
	SuccessCaseNum        int           `json:"success_case_num"`
	FailureCaseNum        int           `json:"failure_case_num"`
	TestTimeConsume       string        `json:"test_time_consume"`
	SceneNum              int           `json:"scene_num"`
	SuccessSceneNum       int           `json:"success_scene_num"`
	FailureSceneNum       int           `json:"failure_scene_num"`
	SuccessSceneRate      string        `json:"success_scene_rate"`
	SingleCaseNum         int           `json:"single_case_num"`
	SuccessSingleCaseNum  int           `json:"success_single_case_num"`
	FailureSingleCaseNum  int           `json:"failure_single_case_num"`
	SuccessSingleCaseRate interface{}   `json:"success_single_case_rate"`
	FailureCaseList       []interface{} `json:"failure_case_list"`
	FailureEnvScriptList  []interface{} `json:"failure_env_script_list"`
}

type executeTimedTaskResult struct {
	baseResult
	ReportID int `json:"report_id"`
}

type eolinkerClient struct {
	BaseUrl     string
	EoSecretKey string
}

func New(url string, eoSecretKey string) *eolinkerClient {
	return &eolinkerClient{
		BaseUrl:     url + "/index.php/v2/api_studio",
		EoSecretKey: eoSecretKey,
	}
}

func Default() *eolinkerClient {
	return New(config.GetConfig().Eolinker.Host, config.GetConfig().Eolinker.EoSecretKey)
}

func (e *eolinkerClient) ListProject() ([]project, error) {
	var (
		ret      searchProjectResult
		formData = make(map[string]interface{})
	)
	url := e.BaseUrl + searchProjectPath
	// 头
	header := make(http.Header)
	header.Add("Eo-Secret-Key", e.EoSecretKey)
	// 空间id，只有1个fxiaoke写死了
	formData["space_id"] = config.GetConfig().Eolinker.SpaceIdDefault

	resp := requests.NewHTTPRequest(url).SetHeader(header).SetQueryParams(formData).ToJSON(&ret)
	if resp.Error != nil {
		return nil, resp.Error
	}
	if ret.Status != "success" {
		return nil, fmt.Errorf("url:%s"+ret.ErrorInfo, url)
	}
	return ret.Result, nil
}

func (e *eolinkerClient) ListTimedTask(projectID string) ([]timedTask, error) {
	var (
		respData searchTimedTaskResult
		formData = make(map[string]interface{})
	)
	url := e.BaseUrl + searchTimedTaskPath

	header := make(http.Header)
	header.Add("Eo-Secret-Key", e.EoSecretKey)

	// 空间id，只有1个fxiaoke写死了
	formData["space_id"] = config.GetConfig().Eolinker.SpaceIdDefault
	formData["project_id"] = projectID

	resp := requests.NewHTTPRequest(url).SetHeader(header).SetQueryParams(formData).ToJSON(&respData)
	if resp.Error != nil {
		return nil, resp.Error
	}
	if respData.Status != "success" {
		return nil, fmt.Errorf("url:%s"+respData.ErrorInfo, url)
	}
	var ret []timedTask
	for _, v := range respData.Result {
		if v.TaskStatus == taskEnabled {
			ret = append(ret, v)
		}
	}
	return ret, nil
}

func (e *eolinkerClient) ExecuteTimedTask(projectID string, taskID string) (int, error) {
	var (
		ret      executeTimedTaskResult
		formData = make(map[string]interface{})
	)
	url := e.BaseUrl + executeTimedTaskPath

	header := make(http.Header)
	header.Add("Eo-Secret-Key", e.EoSecretKey)

	// 空间id，只有1个fxiaoke写死了
	formData["space_id"] = config.GetConfig().Eolinker.SpaceIdDefault
	formData["project_id"] = projectID
	formData["task_id"] = taskID

	resp := requests.NewHTTPRequest(url).SetHeader(header).SetQueryParams(formData).ToJSON(&ret)
	if resp.Error != nil {
		return 0, resp.Error
	}
	log.Infof("eolinker projectID: %s, taskID: %s request: %s response: %v", projectID, taskID, url, ret)
	if ret.Status != "success" {
		return 0, fmt.Errorf("url:%s, status: %s, msg: %s", url, ret.Status, ret.ErrorInfo)
	}
	if ret.ReportID == 0 {
		return 0, fmt.Errorf("获取报告ID失败")
	}
	return ret.ReportID, nil
}

func (e *eolinkerClient) GetReport(projectID, taskID string, reportID int) (Report, error) {
	var (
		ret      getReportResult
		formData = make(map[string]interface{})
	)
	url := e.BaseUrl + getReportPath
	// 空间id，只有1个fxiaoke写死了
	formData["space_id"] = config.GetConfig().Eolinker.SpaceIdDefault
	formData["project_id"] = projectID
	formData["task_id"] = taskID
	formData["report_type"] = "timed_task"
	formData["report_id"] = reportID

	header := make(http.Header)
	header.Add("Eo-Secret-Key", e.EoSecretKey)
	// 空间id，只有1个fxiaoke写死了

	resp := requests.NewHTTPRequest(url).SetHeader(header).SetQueryParams(formData).ToJSON(&ret)
	if resp.Error != nil {
		return Report{}, resp.Error
	}
	if ret.Status != "success" {
		return Report{}, fmt.Errorf("url:%s, err: %s", url, ret.ErrorInfo)
	}
	return ret.Result, nil
}
