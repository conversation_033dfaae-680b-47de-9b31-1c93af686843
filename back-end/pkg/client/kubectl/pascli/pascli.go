package pascli

import (
	"encoding/json"
	"errors"
	"fmt"
	k8s_templates "fs-k8s-app-manager/k8s-templates"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/dto"
	"strings"
	"time"
)

func GetPodAutoScaler(cluster, namespace, name string) (string, error) {
	if !IsInstalledPodAutoScaler(cluster) {
		return "", errors.New("pas is not installed in cluster of " + cluster)
	}
	if strings.TrimSpace(name) == "" {
		return "", errors.New("name is empty")
	}
	command := "get podautoscaler -o json"
	if namespace != "" {
		command = command + " -n " + namespace
	} else {
		command = command + " --all-namespaces"
	}
	command = command + " " + name
	return kubectl.Cmd(cluster, command)
}

func IsInstalledPodAutoScaler(cluster string) bool {
	command := "get crd podautoscalers.autoscaling.fxiaoke.com"
	_, err := kubectl.CmdWithTimeout(cluster, command, 10*time.Second)
	return err == nil
}

func ExistPodAutoScaler(cluster, namespace, name string) (bool, error) {
	if !IsInstalledPodAutoScaler(cluster) {
		return false, nil
	}
	if strings.TrimSpace(namespace) == "" {
		return false, errors.New("namespace is empty")
	}

	if strings.TrimSpace(name) == "" {
		return false, errors.New("name is empty")
	}
	output, err := GetPodAutoScaler(cluster, namespace, name)
	//当找不到的时候， 会出现not found的错误
	return err == nil && strings.TrimSpace(output) != "", nil
}

func UpdatePodAutoScalerMinReplicas(cluster, namespace, name string, minReplicaCount uint) (string, error) {
	defer deleteCache(cluster, namespace)
	if !IsInstalledPodAutoScaler(cluster) {
		return "", errors.New("pas is not installed in cluster of " + cluster)
	}
	command := fmt.Sprintf("patch -n %s pas %s --type=merge -p '{\"spec\":{\"minReplicaCount\":%d}}'",
		namespace, name, minReplicaCount)
	return kubectl.Cmd(cluster, command)
}

//func UpdatePodAutoScalerScaleupHourWindow(cluster, namespace, name string, hourWindow []int32) (string, error) {
//	defer deleteCache(cluster, namespace)
//	if !IsInstalledPodAutoScaler(cluster) {
//		return "", errors.New("pas is not installed in cluster of " + cluster)
//	}
//	hourWindowJson, err := json.Marshal(hourWindow)
//	if err != nil {
//		return "", err
//	}
//	command := fmt.Sprintf("patch -n %s pas %s --type=merge -p '{\"spec\":{\"hourWindow\":%s}}'",
//		namespace, name, string(hourWindowJson))
//	return kubectl.Cmd(cluster, command)
//}

func listPodAutoScaler(cluster, namespace string) (string, error) {
	if !IsInstalledPodAutoScaler(cluster) {
		return "", errors.New("pas is not installed in cluster of " + cluster)
	}
	command := "get podautoscaler -o json"
	if namespace != "" {
		command = command + " -n " + namespace
	} else {
		command = command + " --all-namespaces"
	}
	return kubectl.Cmd(cluster, command)
}

func DeletePodAutoScaler(cluster, namespace, app string) (string, error) {
	defer deleteCache(cluster, namespace)
	if !IsInstalledPodAutoScaler(cluster) {
		return "", errors.New("pas is not installed in cluster of " + cluster)
	}
	command := fmt.Sprintf("delete podautoscaler -n %s %s", namespace, app)
	return kubectl.Cmd(cluster, command)
}

func ListPodAutoScalerFromK8s(cluster, namespace string) (dto.PodAutoScalerList, error) {
	out, err := listPodAutoScaler(cluster, namespace)
	if err != nil {
		return dto.PodAutoScalerList{}, err
	}

	var ret dto.PodAutoScalerList
	err = json.Unmarshal([]byte(out), &ret)
	if err != nil {
		return dto.PodAutoScalerList{}, err
	}
	return ret, nil
}

func ListPodAutoScalerFromK8sWithCache(cluster, namespace string, expire time.Duration) (dto.PodAutoScalerList, error) {
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("pas-cache@@%s@@%s", cluster, namespace))
	var ret dto.PodAutoScalerList
	if data, found := cache.GetStruct(cacheKey, dto.PodAutoScalerList{}); found {
		return data, nil
	}
	ret, err := ListPodAutoScalerFromK8s(cluster, namespace)
	if err == nil {
		_ = cache.SetStruct(cacheKey, ret, expire)
	}
	return ret, err
}

func ApplyPodAutoScaler(p dto.PodAutoScalerDTO) (yaml string, err error) {
	defer deleteCache(p.Cluster, p.Namespace)
	iYaml, err := k8s_templates.BuildPodAutoScaler(p)
	if err != nil {
		return iYaml, err
	}
	err = kubectl.Apply(p.Cluster, p.Namespace, iYaml)
	return iYaml, err
}

func deleteCache(cluster, namespace string) {
	cacheKey := key.Pre().K8S.Key(fmt.Sprintf("pas-cache@@%s@@%s", cluster, namespace))
	cache.Delete(cacheKey)
}
