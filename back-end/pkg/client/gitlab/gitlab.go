package gitlab

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/dto"
	log "github.com/sirupsen/logrus"
	"github.com/xanzy/go-gitlab"
	"net/url"
	"sort"
	"strings"
)

var cli *gitlab.Client

func init() {
	log.Infoln("gitlab client init...")
	var err error
	cli, err = gitlab.NewClient(config.GetConfig().Gitlab.Token,
		gitlab.WithBaseURL(fmt.Sprintf("%s/api/v4", config.GetConfig().Gitlab.Host)))
	if err != nil {
		log.Panicln("gitlab client init failed", err)
	}
}

func parseProjectId(gitUrl string) string {
	u, err := url.Parse(gitUrl)
	if err != nil {
		//todo: log
		return ""
	}
	path := u.Path
	if strings.HasSuffix(path, ".git") {
		path = path[1 : len(path)-4]
	}
	return path
}

func SearchTag(url string, limit int, search *string) ([]*dto.GitTag, error) {
	listTagsOptions := gitlab.ListTagsOptions{
		ListOptions: gitlab.ListOptions{Page: 0, PerPage: limit},
		Search:      search,
	}
	tags, _, err := cli.Tags.ListTags(parseProjectId(url), &listTagsOptions)
	if err != nil {
		return nil, err
	}
	ret := make([]*dto.GitTag, 0, len(tags))
	for _, it := range tags {
		ret = append(ret, &dto.GitTag{
			Name:            it.Name,
			Message:         it.Message,
			CommitID:        it.Commit.ID,
			CommitAuthor:    it.Commit.AuthorName,
			CommitCreatedAt: it.Commit.CreatedAt,
			CommitMessage:   it.Commit.Message,
		})
	}
	return ret, err
}

func ListTagName(url string, limit int) ([]string, error) {
	items, err := SearchTag(url, limit, nil)
	if err != nil {
		return nil, err
	}
	ret := make([]string, 0, len(items))
	for _, it := range items {
		ret = append(ret, it.Name)
	}
	return ret, nil
}

func SearchBranch(url string, limit int, search *string) ([]*dto.GitBranch, error) {
	//操作成本比较高，这里使用cache
	//cacheKey := fmt.Sprintf("GitBranches@@%s@@%d", url, limit)
	//if v, ok := cache.Get(cacheKey); ok {
	//	if d, ok := v.([]*dto.GitBranch); ok {
	//		return d, nil
	//	}
	//}

	//扯淡1： list branches接口无法自定义排序，这里只能先把所有的取出来，然后自己做排序。ref: https://docs.gitlab.com/ee/api/branches.html
	//扯淡2： 分页最大只可获取100个数据，这里通过多次分页查询拿取我们需要量的数据.https://docs.gitlab.com/ce/api/#offset-based-pagination
	count := 2000
	branches := make([]*gitlab.Branch, 0, count)
	//注意：gitlab允许单页最大数为100， 大于改值无效
	perPage := 100
	page := 0

	for {
		page++
		bs, _, err := cli.Branches.ListBranches(parseProjectId(url), &gitlab.ListBranchesOptions{
			ListOptions: gitlab.ListOptions{Page: page, PerPage: perPage},
			Search:      search,
		})
		if err != nil {
			return nil, err
		}
		branches = append(branches, bs...)
		if len(bs) < perPage || len(branches) >= count {
			break
		}
	}

	sort.Slice(branches, func(i, j int) bool {
		//主分支排在最前
		if strings.EqualFold(branches[i].Name, "master") || strings.EqualFold(branches[i].Name, "main") {
			return true
		}
		if strings.EqualFold(branches[j].Name, "master") || strings.EqualFold(branches[j].Name, "main") {
			return false
		}
		return branches[i].Commit.CommittedDate.After(*branches[j].Commit.CommittedDate)
	})
	if len(branches) > limit {
		branches = branches[0:limit]
	}
	ret := make([]*dto.GitBranch, 0, len(branches))
	for _, it := range branches {
		ret = append(ret, &dto.GitBranch{
			Name:            it.Name,
			Message:         "",
			CommitID:        it.Commit.ID,
			CommitAuthor:    it.Commit.AuthorName,
			CommitCreatedAt: it.Commit.CreatedAt,
			CommitMessage:   it.Commit.Message,
		})
	}
	//cache.Set(cacheKey, ret, 30*time.Minute)
	return ret, nil
}

func ListBranchName(url string, limit int) ([]string, error) {
	items, err := SearchBranch(url, limit, nil)
	if err != nil {
		return nil, err
	}
	ret := make([]string, 0, len(items))
	for _, it := range items {
		ret = append(ret, it.Name)
	}
	return ret, nil
}

//	func CreateTagFromTag(gitURL, oldTag, newTag, message string) error {
//		tagInfo, err := TagInfo(gitURL, oldTag)
//		if err != nil {
//			return err
//		}
//		return CreateTag(gitURL, newTag, tagInfo.CommitID, message)
//	}

func TagIsExist(gitURL, tagName string) (bool, error) {
	_, repo, err := cli.Tags.GetTag(parseProjectId(gitURL), tagName)
	if repo != nil && repo.StatusCode == 404 {
		return false, nil
	}
	return true, err
}
func TagInfo(gitURL, tagName string) (dto.GitTag, error) {
	it, _, err := cli.Tags.GetTag(parseProjectId(gitURL), tagName)
	if err != nil {
		return dto.GitTag{}, err
	}
	return dto.GitTag{
		Name:            it.Name,
		Message:         it.Message,
		CommitID:        it.Commit.ID,
		CommitAuthor:    it.Commit.AuthorName,
		CommitCreatedAt: it.Commit.CreatedAt,
		CommitMessage:   it.Commit.Message,
	}, nil
}

func CreateTag(gitURL, tagName, Ref, message string) error {
	var createOptions = gitlab.CreateTagOptions{
		TagName: &tagName,
		Ref:     &Ref,
		Message: &message,
	}
	_, _, err := cli.Tags.CreateTag(parseProjectId(gitURL), &createOptions)

	return err
}

func CreateBranch(gitURL, branchName, ref string) error {
	var createOptions = gitlab.CreateBranchOptions{
		Branch: &branchName,
		Ref:    &ref,
	}
	_, _, err := cli.Branches.CreateBranch(parseProjectId(gitURL), &createOptions)

	return err
}

// IsTag 是否为tag
func IsTag(gitURL, name string) bool {
	_, err := TagInfo(gitURL, name)
	return err == nil
}

func DeleteTag(gitURL, tagName string) error {
	_, err := cli.Tags.DeleteTag(parseProjectId(gitURL), tagName)
	return err
}

func DeleteBranch(gitURL, branchName string) error {
	_, err := cli.Branches.DeleteBranch(parseProjectId(gitURL), branchName)
	return err
}

func GetFileContent(url, ref, filePath string) (string, error) {
	if bs, _, err := cli.RepositoryFiles.GetRawFile(parseProjectId(url), filePath, &gitlab.GetRawFileOptions{Ref: &ref}); err != nil {
		return "", err
	} else {
		return string(bs), nil
	}
}

// refName: The commit hash or name of a repository branch or tag
func GetCommit(gitURL, refName string) (*dto.GitCommit, error) {
	commit, _, err := cli.Commits.GetCommit(parseProjectId(gitURL), refName, nil)
	if err != nil {
		return nil, err
	}
	return &dto.GitCommit{
		ID:        commit.ID,
		ShortID:   commit.ShortID,
		Author:    commit.AuthorName,
		CreatedAt: commit.CreatedAt,
		Message:   commit.Message,
	}, nil
}

func UpdateFile(gitUrl, ref, filePath, content, message string) error {
	_, _, err := cli.RepositoryFiles.UpdateFile(parseProjectId(gitUrl), filePath, &gitlab.UpdateFileOptions{
		Branch:        &ref,
		Content:       &content,
		AuthorName:    str2Pointer("fs-devops"),
		AuthorEmail:   str2Pointer("<EMAIL>"),
		CommitMessage: &message,
	})
	return err
}

func str2Pointer(b string) *string {
	return &b
}
