package kubecap

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/util/exec"
	"fs-k8s-app-manager/pkg/util/file"
	"path/filepath"
	"strings"
	"time"
)

func Node(cluster string, nodeType string) (string, error) {
	err := paramIsValid(nodeType)
	if err != nil {
		return "", err
	}
	if nodeType == "" {
		return cmd(cluster, "--util")
	}
	return cmd(cluster, fmt.Sprintf("--util --node-labels fxiaoke.com/dedicated=%s", nodeType))
}
func Pod(cluster, node string) (string, error) {
	err := paramIsValid(node)
	if err != nil {
		return "", err
	}
	return cmd(cluster, fmt.Sprintf("--node-labels kubernetes.io/hostname=%s --pods --util", node))
}

func cmd(cluster, param string) (output string, err error) {
	return cmdWithTimeout(cluster, param, 30*time.Second)
}
func cmdWithTimeout(cluster, param string, timeout time.Duration) (output string, err error) {
	err = paramIsValid(cluster)
	if err != nil {
		return "", err
	}
	command := fmt.Sprintf("kube-capacity --kubeconfig %s -o json %s",
		getKubeConfig(cluster),
		param)
	output, err = exec.CommandExecWithTimeout(command, timeout)
	return
}

func getKubeConfig(cluster string) string {
	path := filepath.Join(config.GetConfig().App.KubeConfDir, cluster)
	return file.AbsPath(path)
}

func paramIsValid(input string) error {
	if strings.Contains(input, "<") {
		return errors.New("param value can not contain '<'")
	}
	return nil
}
