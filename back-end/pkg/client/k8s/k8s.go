package k8s

import (
	"fs-k8s-app-manager/config"
	"github.com/patrickmn/go-cache"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"path/filepath"
	"time"
)

var clientCache = cache.New(2*time.Hour, 10*time.Minute)

func inClusterClient() (ret *kubernetes.Clientset, err error) {
	conf, err := rest.InClusterConfig()
	if err != nil {
		return
	}
	clientSet, err := kubernetes.NewForConfig(conf)
	if err != nil {
		return
	}
	ret = clientSet
	return
}
func outClusterClient(cluster string) (ret *kubernetes.Clientset, err error) {
	conf, err := GetConfig(cluster)
	if err != nil {
		return
	}
	clientSet, err := kubernetes.NewForConfig(conf)
	if err != nil {
		return
	}
	ret = clientSet
	return
}

func GetKubernetesClient(cluster string) (client *kubernetes.Clientset, err error) {
	if x, found := clientCache.Get(cluster); found {
		client = x.(*kubernetes.Clientset)
	} else {
		client, err = outClusterClient(cluster)
		if err != nil {
			return
		}
		clientCache.SetDefault(cluster, client)
	}
	return
}

func GetConfig(cluster string) (*restclient.Config, error) {
	var configDirPath = config.GetConfig().App.KubeConfDir
	kConfig := filepath.Join(configDirPath, cluster)
	return clientcmd.BuildConfigFromFlags("", kConfig)
}

func GetDeploymentList(cluster, namespace string) (ret *v1.DeploymentList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.AppsV1().Deployments(namespace).List(metav1.ListOptions{})
}

func GetNodeList(cluster string) (ret *corev1.NodeList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.CoreV1().Nodes().List(metav1.ListOptions{})
}

func GetServiceList(cluster, namespace string) (ret *corev1.ServiceList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.CoreV1().Services(namespace).List(metav1.ListOptions{})
}

func GetReplicaSetList(cluster, namespace string) (ret *v1.ReplicaSetList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.AppsV1().ReplicaSets(namespace).List(metav1.ListOptions{})
}

func GetPodList(cluster, namespace, app string) (ret *corev1.PodList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	labelSel := ""
	if app != "" {
		labelSel = labels.SelectorFromSet(map[string]string{"app": app}).String()
	}
	return c.CoreV1().Pods(namespace).List(metav1.ListOptions{LabelSelector: labelSel})
}

func ListPodByIP(cluster, namespace, ip string) (ret *corev1.PodList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	selector := fields.SelectorFromSet(map[string]string{"status.podIP": ip}).String()
	return c.CoreV1().Pods(namespace).List(metav1.ListOptions{FieldSelector: selector})
}

func ListPodByNode(cluster, node string) (ret *corev1.PodList, err error) {
	c, err := GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	selector := fields.SelectorFromSet(map[string]string{"spec.nodeName": node}).String()
	return c.CoreV1().Pods("").List(metav1.ListOptions{FieldSelector: selector})
}
