package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"fs-k8s-app-manager/config"
	"github.com/segmentio/kafka-go"
	log "github.com/sirupsen/logrus"
	"time"
)

type DevopsEvent struct {
	Cluster      string `json:"cluster"`
	Profile      string `json:"profile"`
	Creator      string `json:"creator"`
	ResourceType string `json:"resourceType"`
	ResourceId   string `json:"resourceId"`
	Title        string `json:"title"`
	Message      string `json:"message"`
	Level        string `json:"level"`
	Extra        string `json:"extra"`
	StartTime    int64  `json:"startTime"`
	EndTime      int64  `json:"endTime"`
}

type OncallEvent struct {
	DevopsEvent
	Source     string `json:"source"`
	CreateTime int64  `json:"createTime"`
}

var cli *kafka.Writer

func init() {
	cli = &kafka.Writer{
		Addr:                   kafka.TCP(config.GetConfig().Kafka.Addr...),
		AllowAutoTopicCreation: true,
		Balancer:               &kafka.LeastBytes{},
	}
}

func buildHref(cluster, namespace, app string) string {
	host := config.GetConfig().K8sAppManager.Host
	if host == "" {
		return ""
	}
	return fmt.Sprintf("%s/#/pod/index?cluster=%s&namespace=%s&app=%s", host, cluster, namespace, app)
}

func SendDevopsEvent(event DevopsEvent) error {
	if cli.Addr == nil {
		log.Debug("kafka address is not set, skip sending devops event")
		return nil
	}
	if event.Extra == "" && event.ResourceType == "app" {
		event.Extra = "实例管理页: " + buildHref(event.Cluster, event.Profile, event.ResourceId)
	}

	if event.StartTime == 0 {
		event.StartTime = time.Now().UnixMilli()
	}
	if event.EndTime == 0 {
		event.EndTime = time.Now().UnixMilli()
	}
	if event.Level == "" {
		event.Level = "info"
	}
	oncallEvent := OncallEvent{
		DevopsEvent: event,
		Source:      "devops",
		CreateTime:  time.Now().UnixMilli(),
	}

	val, err := json.Marshal(oncallEvent)
	if err != nil {
		return err
	}
	err = cli.WriteMessages(context.Background(),
		kafka.Message{
			Topic: "event-center",
			Key:   []byte("fs-k8s-app-manager"),
			Value: val,
		},
	)
	return err
}
