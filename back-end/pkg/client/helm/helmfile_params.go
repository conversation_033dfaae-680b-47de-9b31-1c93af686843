package helm

import (
	k8sV1 "k8s.io/api/core/v1"
)

type Release struct {
	Name           string         `yaml:"name"`
	Chart          string         `yaml:"chart"`
	Version        string         `yaml:"version"`
	HelmfileValues HelmfileValues `yaml:"values"`
}

// HelmfileValues helmfile template values
type HelmfileValues struct {
	AppName string `yaml:"appName"`
	//编译helm chart时方便定义版本
	ChartVersion    string `yaml:"chartVersion"`
	ChartAppVersion string `yaml:"chartAppVersion"`
	//ReplicaCount   *int32             `yaml:"replicaCount"`
	Image          Image              `yaml:"image"`
	Deployment     HelmfileDeployment `yaml:"deployment"`
	Ports          []HelmfilePort     `yaml:"ports"`
	LifecycleHooks *Lifecycle         `yaml:"lifecycleHooks,omitempty"`
	ExtraEnvVars   []EnvVar           `yaml:"extraEnvVars"`
}

type HelmfilePort struct {
	Name          string `yaml:"name"`
	ServicePort   int32  `yaml:"servicePort"`
	ContainerPort int32  `yaml:"containerPort"`
}

type HelmfileDeployment struct {
	InitContainers []Container `yaml:"initContainers"`
}

// Image docker image
type Image struct {
	Repository string `yaml:"repository"`
	Tag        string `yaml:"tag"`
}

type Container struct {
	Name            string           `yaml:"name"`
	Image           string           `yaml:"image"`
	ImagePullPolicy k8sV1.PullPolicy `yaml:"imagePullPolicy"`
	Env             []EnvVar         `yaml:"env"`
	VolumeMounts    []VolumeMount    `yaml:"volumeMounts"`
}

type Lifecycle struct {
	PreStop map[string]interface{} `yaml:"preStop,omitempty"`
}

type ExecAction struct {
	Command []string `yaml:"command"`
}

type HTTPGetAction struct {
	Path   string          `yaml:"path"`
	Port   string          `yaml:"port"`
	Scheme k8sV1.URIScheme `yaml:"scheme"`
}

type EnvVar struct {
	Name  string `yaml:"name"`
	Value string `yaml:"value"`
}

type VolumeMount struct {
	Name      string `yaml:"name"`
	MountPath string `yaml:"mountPath"`
}
