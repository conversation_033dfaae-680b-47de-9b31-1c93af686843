package helm

import (
	"encoding/base64"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/util/file"
	"fs-k8s-app-manager/service/k8s_service"
	"k8s.io/api/core/v1"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

var registryConfig = ""
var registryRepo = ""
var harborHost = ""

func init() {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		log.Fatalf("get user home dir error: %v", err)
	}
	dir := filepath.Join(homeDir, ".helm", "registry")
	_ = file.IsNotExistMkDir(dir)
	registryConfig = filepath.Join(dir, "config.json")
	harbor := config.GetConfig().Harbor
	harborHost = harbor.Host
	v := fmt.Sprintf(`{"auths":{"%s":{"auth":"%s"}}}`,
		config.GetConfig().Harbor.Host,
		base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", harbor.Username, harbor.Password))))
	err = file.Write(registryConfig, v)
	if err != nil {
		log.Fatalf("helm registry config write error: %v", err)
	}

	registryRepo = fmt.Sprintf("oci://%s/%s", harbor.Host, harbor.HelmChartProject)
}

func createImage(ct v1.Container) (*Image, error) {
	// image 类似 harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk8
	image := ct.Image
	lastIndex := strings.LastIndex(image, ":")
	//去掉harbor域名前缀，只保留最后两级，比如 base/fs-tomcat8
	repArr := strings.Split(image[0:lastIndex], "/")
	repo := ""
	if len(repArr) <= 2 {
		repo = repArr[1]
	} else {
		repo = repArr[len(repArr)-2] + "/" + repArr[len(repArr)-1]
	}
	p := Image{
		Repository: repo,
		Tag:        image[lastIndex+1:],
	}
	return &p, nil
}

func BuildHelmfileValuesAndChart(pipe models.Pipeline) (value HelmfileValues, chart Chart, err error) {
	deploy, err := k8s_service.DeploymentDetail(pipe.Cluster, pipe.Namespace, pipe.App)
	if err != nil {
		return
	}
	podSpec := deploy.Spec.Template.Spec
	//一些老的服务可能没有部署过
	if len(podSpec.Containers) == 0 || len(podSpec.InitContainers) == 0 {
		return
	}
	container := podSpec.Containers[0]
	image, err := createImage(container)
	if err != nil {
		return
	}
	ports := make([]HelmfilePort, 0, len(container.Ports))
	for _, it := range container.Ports {
		portName := it.Name
		if len(it.Name) == 0 {
			portName = "http-" + string(it.ContainerPort)
		}
		// 私有化部署的metrics不受我们控制，由Helm chart starter独立控制
		if constant.METRICS_PORT_NAME == portName {
			continue
		}
		ports = append(ports, HelmfilePort{
			Name: portName,
			//当前service和Container都是同样的port
			ServicePort:   it.ContainerPort,
			ContainerPort: it.ContainerPort,
		})
	}

	envVars := convertEnvVars(container.Env)

	initContainers := make([]Container, 0, len(podSpec.InitContainers))
	for _, it := range podSpec.InitContainers {
		initContainers = append(initContainers, Container{
			Name:            it.Name,
			Image:           it.Image,
			ImagePullPolicy: v1.PullIfNotPresent,
			Env:             convertEnvVars(it.Env),
			VolumeMounts:    convertVolumeMounts(it.VolumeMounts),
		})
	}

	lifeHook := &Lifecycle{
		PreStop: make(map[string]interface{}),
	}
	if c := container.Lifecycle; c != nil && c.PreStop != nil {
		if c.PreStop.HTTPGet != nil {
			httpGet := container.Lifecycle.PreStop.HTTPGet
			port := httpGet.Port.StrVal
			if len(port) == 0 {
				port = "http"
			}
			lifeHook.PreStop["httpGet"] = HTTPGetAction{
				Path:   httpGet.Path,
				Port:   port,
				Scheme: httpGet.Scheme,
			}

		}
		if c.PreStop.Exec != nil {
			lifeHook.PreStop["exec"] = ExecAction{
				Command: c.PreStop.Exec.Command,
			}
		}
	}

	// helm chart version and app version
	version := config.GetSetting().HelmChart.VersionPrefix + time.Now().Format("200601021504")

	value = HelmfileValues{
		AppName:         pipe.App,
		ChartAppVersion: version,
		ChartVersion:    version,
		//ReplicaCount: deploy.Spec.Replicas,
		Image: *image,
		Deployment: HelmfileDeployment{
			InitContainers: initContainers,
		},
		Ports:          ports,
		LifecycleHooks: lifeHook,
		ExtraEnvVars:   envVars,
	}

	chart = Chart{
		ApiVersion:  "v2",
		Name:        pipe.App,
		Description: "A Helm chart for Kubernetes",
		Type:        "application",
		Version:     version,
		AppVersion:  version,
	}

	return

}

func convertVolumeMounts(vs []v1.VolumeMount) []VolumeMount {
	vms := make([]VolumeMount, 0, len(vs))
	for _, it := range vs {
		vms = append(vms, VolumeMount{
			Name:      it.Name,
			MountPath: it.MountPath,
		})
	}
	return vms
}

func convertEnvVars(vs []v1.EnvVar) []EnvVar {
	envVars := make([]EnvVar, 0, len(vs))
	for _, it := range vs {
		if len(it.Value) == 0 {
			continue
		}
		//这些由基本chart系统指定，不导出
		if it.Name == "APP_LOG_TO_KAFKA" || it.Name == "SKY_WALKING_ENABLE" {
			continue
		}
		//这些由metadata指定，不导出
		if it.Name == "K8S_NAMESPACE" || it.Name == "K8S_APP" || it.Name == "K8S_NODE_IP" || it.Name == "K8S_POD" || it.Name == "K8S_POD_IP" {
			continue
		}
		envVars = append(envVars, EnvVar{
			Name:  it.Name,
			Value: it.Value,
		})
	}
	return envVars
}
