package helm

import (
	"bytes"
	"fmt"
	"fs-k8s-app-manager/pkg/client/gitlab"
	"gopkg.in/yaml.v3"
	"strings"
)

func init() {

}

func BuildHelmfileValue(release []Release) (string, error) {

	rs := "# see git readme for detail \n"
	names := ""
	gitResult := ""

	for _, it := range release {
		// add tips
		rs += fmt.Sprintf("# put this in helmfiles/apps/%s/values.yaml.gotmpl  \n", it.Name)
		rs += fmt.Sprintf("mkdir -p helmfiles/apps/%s \n", it.Name)
		rs += fmt.Sprintf("rm -rf helmfiles/apps/%s/values.yaml.gotmpl \n", it.Name)
		rs += fmt.Sprintf("cat << EOF > helmfiles/apps/%s/values.yaml.gotmpl  \n", it.Name)

		buffer := new(bytes.Buffer)
		encoder := yaml.NewEncoder(buffer)
		encoder.SetIndent(2)
		if err := encoder.Encode(it.HelmfileValues); err != nil {
			return "", err
		}
		// convert the buffer to a string
		//单引号转双引号，实际上yaml是模板，双引号才起作用
		valYaml := strings.ReplaceAll(buffer.String(), "'", "\"")
		rs += valYaml
		rs += "EOF"
		rs += "\n  ######  \n "
		names = names + " " + it.Name

		//提交到gitlab库
		filePath := fmt.Sprintf("helmfiles/apps/%s/values.yaml.gotmpl", it.Name)
		gitMsg := fmt.Sprintf("feat: update helm chart value for %s", it.Name)
		err := gitlab.UpdateFile("devops/fs-helmfile", "init", filePath, valYaml, gitMsg)
		if err != nil {
			gitResult = gitResult + "\n" + fmt.Sprintf("# %s git push fail, err: %s", it.Name, err.Error())
		} else {
			gitResult = gitResult + "\n" + fmt.Sprintf("# %s git push success", it.Name)
		}
	}

	rs += "# all service names \n #" + names
	rs += "# git push result \n" + gitResult

	return rs, nil
}
