package key

import "strings"

type cachePrefix string

func (c cachePrefix) Key(k string) string {
	return string(c) + "@@" + k
}

func (c cachePrefix) Belong(k string) bool {
	return strings.HasPrefix(k, string(c)+"@@")
}

type pre struct {
	CAS       cachePrefix
	SESSION   cachePrefix
	APP       cachePrefix
	TOOL      cachePrefix
	OPERATION cachePrefix
	CMDB      cachePrefix
	PERM      cachePrefix
	ARTIFACT  cachePrefix
	EVENT     cachePrefix
	POD       cachePrefix
	USER      cachePrefix
	JOB       cachePrefix
	WATCHER   cachePrefix
	K8S       cachePrefix
	CONFIG    cachePrefix
	ONCALL    cachePrefix
}

func Pre() pre {
	return pre{
		CAS:       "cas",
		SESSION:   "session",
		APP:       "app",
		TOOL:      "tool",
		OPERATION: "operation",
		CMDB:      "cmdb",
		PERM:      "perm",
		ARTIFACT:  "artifact",
		EVENT:     "event",
		POD:       "pod",
		USER:      "user",
		JOB:       "job",
		WATCHER:   "watcher",
		K8S:       "k8s",
		CONFIG:    "config",
		ONCALL:    "oncall",
	}
}
