package gocache

import (
	"fmt"
	"fs-k8s-app-manager/pkg/cache/key"
	go_cache "github.com/patrickmn/go-cache"
	"time"
)

var _cache_ = go_cache.New(1*time.Hour, 1*time.Minute)

func Delete(key string) {
	_cache_.Delete(key)
}
func ClearAll() {
	for k, _ := range _cache_.Items() {
		//不要清理Session数据
		if key.Pre().SESSION.Belong(k) {
			continue
		}
		_cache_.Delete(k)
	}
	for k, _ := range _cache_.Items() {
		fmt.Println(k)
	}
}

func SetStruct(key string, val interface{}, expire time.Duration) error {
	_cache_.Set(key, val, expire)
	return nil
}

func GetStruct[T any](key string, t T) (ret T, found bool) {
	if data, f := _cache_.Get(key); f {
		if v, ok := data.(T); ok {
			return v, true
		}
	}
	return t, false
}

func SetStr(key string, value string, expire time.Duration) error {
	_cache_.Set(key, value, expire)
	return nil

}
func GetStr(key string) (string, bool) {
	if data, f := _cache_.Get(key); f {
		if v, ok := data.(string); ok {
			return v, true
		}
	}
	return "", false
}
