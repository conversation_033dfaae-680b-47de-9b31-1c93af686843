// Deprecated: 弃用， 使用 https://github.com/bndr/gojenkins
package cas

import (
	"crypto/tls"
	"encoding/xml"
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
	"strings"
)

type ServiceResponse struct {
	XMLName               xml.Name              `xml:"serviceResponse"`
	AuthenticationSuccess AuthenticationSuccess `xml:"authenticationSuccess"`
}

type AuthenticationSuccess struct {
	Attributes User `xml:"attributes"`
}

type User struct {
	Username    string `xml:"username"`
	RealName    string `xml:"realname"`
	Email       string `xml:"email"`
	EmployeeId  int    `xml:"employeeId"`
	Permissions string `xml:"permissions"`
	Roles       string `xml:"roles"`
}

func Validate(ticket, service string) (ret *User, err error) {
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	url := fmt.Sprintf("%s?ticket=%s&service=%s", config.GetConfig().CAS.ValidatePath, ticket, service)
	log.Debug("cas validate url: ", url)
	resp, err := client.R().
		SetHeader("Content-Type", "text/html; charset=utf-8").
		Get(url)
	if err != nil {
		err = errors.New("从CAS获取用户失败：" + err.Error())
		return
	}
	if resp.StatusCode() != 200 {
		err = errors.New(fmt.Sprintf("从CAS获取用户失败： code: %d, body: %s", resp.StatusCode(), string(resp.Body())))
		return
	}
	body := string(resp.Body())
	if strings.Contains(body, "authenticationFailure") {
		err = errors.New(fmt.Sprintf("CAS认证失败 : %s", body))
		return
	}
	//移除命名空间标记
	body = strings.ReplaceAll(body, "cas:", "")
	repo := &ServiceResponse{}
	err = xml.Unmarshal([]byte(body), repo)
	if err != nil {
		err = errors.New(fmt.Sprintf("解析CAS数据失败：err:%s, xml: %s", err.Error(), body))
		return
	}
	ret = &repo.AuthenticationSuccess.Attributes
	return
}
