package strslice

func Find(slice []string, val string) bool {
	for _, item := range slice {
		if item == val {
			return true
		}
	}
	return false
}
func FirstNotEmpty(items ...string) string {
	for _, it := range items {
		if it != "" {
			return it
		}
	}
	return ""
}

func Filter(vs []string, f func(string) bool) []string {
	vsf := make([]string, 0)
	for _, v := range vs {
		if f(v) {
			vsf = append(vsf, v)
		}
	}
	return vsf
}

func Map(vs []string, f func(string) string) []string {
	vsm := make([]string, len(vs))
	for i, v := range vs {
		vsm[i] = f(v)
	}
	return vsm
}

// FindStringsNotInArray 从一个字符串数组中查找不在另一个字符串数组的函数
func FindStringsNotInArray(arr1 []string, from []string) []string {
	var results []string
	for _, str1 := range arr1 {
		found := false
		for _, str2 := range from {
			if str1 == str2 {
				found = true
				break
			}
		}
		if !found {
			results = append(results, str1)
		}
	}
	return results
}

func RemoveDuplicates(elements []string) []string {
	encountered := map[string]bool{}
	var result []string
	for _, v := range elements {
		if !encountered[v] {
			encountered[v] = true
			result = append(result, v)
		}
	}
	return result
}
