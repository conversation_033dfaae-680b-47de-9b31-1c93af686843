package times

import (
	"fmt"
	log "github.com/sirupsen/logrus"
	"time"
)

func FuncTimeCost() func(funcName string) {
	start := time.Now()
	return func(funcName string) {
		tc := time.Since(start)
		log.Info(fmt.Sprintf("function: %s, time cost: %v", funcName, tc))
	}
}

func ConvertUTCToChinaTime(timeStr string) (ret time.Time, err error) {
	ret, err = time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return
	}
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return
	}
	ret = ret.In(location)
	return ret, nil
}
