package exec

import (
	"context"
	"errors"
	"fmt"
	log "github.com/sirupsen/logrus"
	"os/exec"
	"runtime"
	"time"
)

/**
执行命令，支持管道
*/
func CommandExec(cmdStr string) (string, error) {
	return CommandExecWithTimeout(cmdStr, 30*time.Second)
}

/**
执行命令，支持管道
*/
func CommandExecInWorkDir(workDir, cmdStr string) (string, error) {
	return CommandExecWithTimeoutAndWorkdir(workDir, cmdStr, 30*time.Second)
}

/**
执行命令，支持管道
关于Timeout的代码理解，请参考：https://medium.com/@vCabbage/go-timeout-commands-with-os-exec-commandcontext-ba0c861ed738
*/
func CommandExecWithTimeout(cmdStr string, timeout time.Duration) (string, error) {
	return CommandExecWithTimeoutAndWorkdir("", cmdStr, timeout)
}

func CommandExecWithTimeoutAndWorkdir(workDir, cmdStr string, timeout time.Duration) (string, error) {
	start := time.Now()
	// Create a new context and add a timeout to it
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel() // The cancel should be deferred so resources are cleaned up

	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.CommandContext(ctx, "cmd", "/C", cmdStr)
	} else {
		cmd = exec.CommandContext(ctx, "bash", "-c", cmdStr)
	}
	if workDir != "" {
		cmd.Dir = workDir
	}
	out, err := cmd.Output()
	log.Infof("os process, wk: %s, cost: %s, command: %s", cmd.Dir, time.Since(start), cmdStr)

	if ctx.Err() == context.DeadlineExceeded {
		return "", errors.New("Command timed out: " + cmdStr)
	}
	if err != nil {
		//如果程序执行非正常退出， 把错误输出信息添加到err信息里
		if e, ok := err.(*exec.ExitError); ok {
			err = errors.New(fmt.Sprint(err.Error(), " -- ", string(e.Stderr)))
		}
		return "", err
	}
	return string(out), nil
}

/**
执行命令, 通过 exec.Command args方式
场景： windows下，如果执行命令里包含引号，则会被逃逸掉，导致命令会执行失败，可通过args方式执行
*/
func CommandExecByArgs(args ...string) (output string, err error) {
	var out []byte
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		args = append([]string{"/C"}, args...)
		cmd = exec.Command("cmd", args...)
	} else {
		args = append([]string{"-c"}, args...)
		cmd = exec.Command("bash", args...)
	}
	log.Info("os process: ", args)
	if out, err = cmd.Output(); err != nil {
		if e, ok := err.(*exec.ExitError); ok {
			err = errors.New(fmt.Sprint(err.Error(), " -- ", string(e.Stderr)))
		}
		return
	}
	output = string(out)
	return
}
