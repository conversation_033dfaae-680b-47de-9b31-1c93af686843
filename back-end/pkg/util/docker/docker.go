package docker

import "strings"

func GitTag2DockerTag(gitTag string) string {
	return strings.ReplaceAll(gitTag, "/", "---")
}

func DockerTag2GitTag(gitTag string) string {
	return strings.ReplaceAll(gitTag, "---", "/")
}

func GetDockerImageTag(image string) string {
	imageParts := strings.Split(image, ":")
	return imageParts[len(imageParts)-1]
}

func GetImageSimpleName(image string) string {
	imageParts := strings.Split(image, "/")
	return imageParts[len(imageParts)-1]
}
