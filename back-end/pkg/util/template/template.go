package template

import (
	"bytes"
	"github.com/Masterminds/sprig"
	"path/filepath"
	"text/template"
)

func ParseTemplate(text string, data interface{}) (ret string, err error) {
	t := template.Must(template.New("").Funcs(sprig.FuncMap()).Parse(text))
	return renderTemplate(t, data)
}
func ParseTemplateFile(templateFile string, data interface{}) (ret string, err error) {
	t := template.Must(template.New(filepath.Base(templateFile)).Funcs(sprig.FuncMap()).ParseFiles(templateFile))
	return renderTemplate(t, data)
}

func renderTemplate(t *template.Template, data interface{}) (ret string, err error) {
	var tpl bytes.Buffer
	err = t.Execute(&tpl, data)
	if err != nil {
		return
	}
	ret = tpl.String()
	return
}
