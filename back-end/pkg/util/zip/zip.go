package zip

import (
	"archive/zip"
	file2 "fs-k8s-app-manager/pkg/util/file"
	"os"
	"path/filepath"
)

type FileEntry struct {
	Name string
	Body string
}

//code ref : https://golang.org/src/archive/zip/example_test.go
func ZipArchive(file string, entries []FileEntry) error {
	fw, err := os.Create(file)
	defer fw.Close()
	if err != nil {
		return err
	}
	w := zip.NewWriter(fw)
	for _, file := range entries {
		f, err := w.Create(file.Name)
		if err != nil {
			return err
		}
		_, err = f.Write([]byte(file.Body))
		if err != nil {
			return err
		}
	}
	return w.Close()
}

func ZipArchiveDir(file string, srcDir string) (err error) {

	entries := make([]FileEntry, 0)
	err = filepath.Walk(srcDir, func(path string, f os.FileInfo, err error) error {
		if f.IsDir() {
			return nil
		}
		body, err := file2.Read(path)
		if err != nil {
			return err
		}
		entries = append(entries, FileEntry{Name: path, Body: body})
		return nil
	})
	if err != nil {
		return
	}
	return ZipArchive(file, entries)
}
