package file

import (
	"fmt"
	"fs-k8s-app-manager/config"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"os"
	"path"
	"path/filepath"
	"time"
)

func GetSize(filePath string) (int64, error) {
	fi, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return fi.Size(), nil
}

// GetExt get the file ext
func GetExt(fileName string) string {
	return path.Ext(fileName)
}

// CheckNotExist check if the file exists
func CheckNotExist(src string) bool {
	_, err := os.Stat(src)

	return os.IsNotExist(err)
}

// CheckPermission check if the file has permission
func CheckPermission(src string) bool {
	_, err := os.Stat(src)

	return os.IsPermission(err)
}

// IsNotExistMkDir create a directory if it does not exist
func IsNotExistMkDir(src string) error {
	if notExist := CheckNotExist(src); notExist == true {
		if err := MkDir(src); err != nil {
			return err
		}
	}

	return nil
}

// MkDir create a directory
func MkDir(src string) error {
	err := os.MkdirAll(src, os.ModePerm)
	if err != nil {
		return err
	}

	return nil
}

// Open a file according to a specific mode
func Open(name string, flag int, perm os.FileMode) (*os.File, error) {
	f, err := os.OpenFile(name, flag, perm)
	if err != nil {
		return nil, err
	}

	return f, nil
}

// MustOpen maximize trying to open the file
func MustOpen(fileName, filePath string) (*os.File, error) {
	dir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("os.Getwd err: %v", err)
	}

	src := dir + "/" + filePath
	perm := CheckPermission(src)
	if perm == true {
		return nil, fmt.Errorf("file.CheckPermission Permission denied src: %s", src)
	}

	err = IsNotExistMkDir(src)
	if err != nil {
		return nil, fmt.Errorf("file.IsNotExistMkDir src: %s, err: %v", src, err)
	}

	f, err := Open(src+fileName, os.O_APPEND|os.O_CREATE|os.O_RDWR, 0644)
	if err != nil {
		return nil, fmt.Errorf("Fail to OpenFile :%v", err)
	}

	return f, nil
}

func Write(filename, content string) (err error) {
	err = os.WriteFile(filename, []byte(content), 0644)
	return
}

func Read(filename string) (string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

func AbsPath(path string) string {
	absPath, err := filepath.Abs(path)
	if err != nil {
		log.Warn("cat't convert to abs path, return: ", path)
		return path
	}
	return absPath
}

func CreateTempFile(content string) (string, error) {
	f := uuid.NewV4().String() + "-" + time.Now().Format("0102150405")
	tmpDir := filepath.Join(config.GetConfig().App.RuntimeDir, "tmp")
	_ = IsNotExistMkDir(tmpDir)
	fp := AbsPath(filepath.Join(tmpDir, f))
	if err := Write(fp, content); err != nil {
		return "", err
	}
	return fp, nil
}

func CreateTempDir() (string, error) {
	tmpDir := filepath.Join(config.GetConfig().App.RuntimeDir, "tmp", uuid.NewV4().String()+"-"+time.Now().Format("0102150405"))
	err := IsNotExistMkDir(tmpDir)
	return tmpDir, err
}
