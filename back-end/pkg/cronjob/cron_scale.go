package cronjob

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/service/cron_scale_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/scale_log_service"
	log "github.com/sirupsen/logrus"
	"time"
)

type cronScaleFlag struct {
	ScaleUpFlag map[string]bool
}

func (t *cronScaleFlag) key(sca models.CronScale) string {
	return fmt.Sprintf("cronScaleFlag@@%s@@%s@@%s", sca.Cluster, sca.Namespace, sca.App)
}

func (t *cronScaleFlag) setScaleUp(sca models.CronScale, flag bool) {
	t.ScaleUpFlag[t.key(sca)] = flag
}
func (t *cronScaleFlag) hasScaleUp(sca models.CronScale) bool {
	return t.ScaleUpFlag[t.key(sca)]
}

var flags = cronScaleFlag{ScaleUpFlag: make(map[string]bool)}

/*
*
定时扩缩容
*/
func cronScale() {
	log.Info("start job")
	allItems, err := cron_scale_service.FindAll()
	if err != nil {
		log.Error(err)
		return
	}
	items := make([]models.CronScale, 0, len(allItems))
	for _, it := range allItems {
		if c := config.GetSetting().GetCluster(it.Cluster); c != nil && c.CronScale {
			items = append(items, it)
		}
	}

	for _, sca := range items {
		if err := cronScaleTask(sca); err != nil {
			log.Errorf("job fail, app: %s, err: %s", sca.AppFullName(), err.Error())
		}
		time.Sleep(20 * time.Millisecond)
	}
	return
}

func cronScaleTask(sca models.CronScale) error {
	dep, err := k8s_service.GetDeploymentDTO(sca.Cluster, sca.Namespace, sca.App)
	if err != nil {
		return err
	}
	pipe, err := pipeline_service.FirstInEnv(sca.Cluster, sca.Namespace, sca.App)
	if err != nil {
		return err
	}
	if sca.InTimeRange() && uint(dep.Replicas) < sca.Replicas {
		//扩容: 1. 进入到扩容时间范围， 2. [运行副本数]小于[扩容副本数]， 3. 快扩（一次性扩容到[扩容副本数]）
		if err := kubectl.Scale(sca.Cluster, sca.Namespace, sca.App, int32(sca.Replicas)); err != nil {
			return err
		}
		flags.setScaleUp(sca, true)
		log_service.CreateBySys("定时扩缩容-扩容", sca.App, fmt.Sprintf("%s 扩容到: %d", sca.AppFullName(), sca.Replicas))

		event_service.CreateBySys(event_service.BuildAppKey(sca.Cluster, sca.Namespace, sca.App),
			fmt.Sprintf("服务定时扩容，副本数：%d → %d", dep.Replicas, sca.Replicas))

		scale_log_service.CreateBySys("定时扩缩容-扩容", sca.Cluster, sca.Namespace, sca.App,
			fmt.Sprintf("%s 扩容到: %d", sca.AppFullName(), sca.Replicas),
			dep.Replicas, int32(sca.Replicas))

		//qixin := []string{
		//	fmt.Sprintf("--- 定时扩容 ---"),
		//	fmt.Sprintf("【应用】：%s", sca.App),
		//	fmt.Sprintf("【环境】：%s", sca.Namespace),
		//	fmt.Sprintf("【所在集群】：%s", sca.Cluster),
		//	fmt.Sprintf("【扩容前副本数】：%d", dep.Replicas),
		//	fmt.Sprintf("【扩容后副本数】：%d", sca.Replicas),
		//}
		//if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.GetOwnerIds(sca.App), strings.Join(qixin, "\n")); err != nil {
		//	log.Warn("定时扩容企信发送失败，" + err.Error())
		//}
	} else if !sca.InTimeRange() && flags.hasScaleUp(sca) && pipe.Replicas < uint(dep.Replicas) && pipe.Replicas != 1 {
		//缩容：1. 扩容时间范围外， 2. [发布流程副本数]小于[运行副本数] 3. 不能缩容为0 4. 慢缩（一次缩一个）
		newReplicas := dep.Replicas - 1
		if err := kubectl.Scale(sca.Cluster, sca.Namespace, sca.App, newReplicas); err != nil {
			return err
		}
		if newReplicas == 1 || uint(newReplicas) == pipe.Replicas {
			flags.setScaleUp(sca, false)
		}
		log_service.CreateBySys("定时扩缩容-缩容", sca.App, fmt.Sprintf("%s, 缩容到: %d", sca.AppFullName(), newReplicas))

		event_service.CreateBySys(event_service.BuildAppKey(sca.Cluster, sca.Namespace, sca.App),
			fmt.Sprintf("服务定时缩容，副本数：%d → %d", dep.Replicas, newReplicas))

		scale_log_service.CreateBySys("定时扩缩容-缩容", sca.Cluster, sca.Namespace, sca.App,
			fmt.Sprintf("%s, 缩容到: %d", sca.AppFullName(), newReplicas),
			dep.Replicas, newReplicas)

		//qixin := []string{
		//	fmt.Sprintf("--- 定时缩容 ---"),
		//	fmt.Sprintf("【应用】：%s", sca.App),
		//	fmt.Sprintf("【环境】：%s", sca.Namespace),
		//	fmt.Sprintf("【所在集群】：%s", sca.Cluster),
		//	fmt.Sprintf("【扩容前副本数】：%d", dep.Replicas),
		//	fmt.Sprintf("【扩容后副本数】：%d", newReplicas),
		//}
		//if err := notify_service.SendQiXinToImportantAlertSession(cmdb_service.GetOwnerIds(sca.App), strings.Join(qixin, "\n")); err != nil {
		//	log.Warn("定时缩容企信发送失败，" + err.Error())
		//}
	}
	return nil
}
