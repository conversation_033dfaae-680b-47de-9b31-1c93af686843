package dto

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

type Hpa struct {
	Cluster                        string `json:"cluster" binding:"required"`
	Namespace                      string `json:"namespace" binding:"required"`
	Name                           string `json:"name" binding:"required"`
	MaxReplicas                    int32  `json:"maxReplicas" binding:"required,gte=2"`
	MinReplicas                    int32  `json:"minReplicas"`
	TargetCPUUtilizationPercentage int32  `json:"targetCPUUtilizationPercentage" binding:"required"`
}

type CronScale struct {
	ID           uint   `json:"id"`
	App          string `json:"app" binding:"required"`
	Cluster      string `json:"cluster" binding:"required"`
	Namespace    string `json:"namespace" binding:"required"`
	StartTime    string `json:"startTime" binding:"required"`
	EndTime      string `json:"endTime" binding:"required"`
	Replicas     uint   `json:"replicas" binding:"required"`
	CurrReplicas string `json:"currReplicas"`
	PipeReplicas string `json:"pipeReplicas"`
	Author       string `json:"author"`
	CreatedTime  *Time  `json:"createdTime"`
	UpdatedTime  *Time  `json:"updatedTime"`
}

func (my *CronScale) ToModel() models.CronScale {
	return models.CronScale{
		Model: gorm.Model{
			ID: my.ID,
		},
		App:       my.App,
		Cluster:   my.Cluster,
		Namespace: my.Namespace,
		StartTime: my.StartTime,
		EndTime:   my.EndTime,
		Replicas:  my.Replicas,
		Author:    my.Author,
	}
}
func (my *CronScale) ParseByModel(entity models.CronScale) {
	my.ID = entity.ID
	my.App = entity.App
	my.Cluster = entity.Cluster
	my.Namespace = entity.Namespace
	my.StartTime = entity.StartTime
	my.EndTime = entity.EndTime
	my.Replicas = entity.Replicas
	my.Author = entity.Author
	my.CurrReplicas = "-"
	my.PipeReplicas = "-"
	my.CreatedTime = NewTimePointer(entity.CreatedAt)
	my.UpdatedTime = NewTimePointer(entity.UpdatedAt)

}

func ToCronScaleList(entities []models.CronScale) []CronScale {
	ret := make([]CronScale, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := CronScale{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}

//type AutoScale struct {
//	ID                 uint   `json:"id"`
//	App                string `json:"app" binding:"required"`
//	Cluster            string `json:"cluster" binding:"required"`
//	Namespace          string `json:"namespace" binding:"required"`
//	CpuTargetPercent   int    `json:"cpuTargetPercent" binding:"required"`
//	Replicas           uint   `json:"replicas" binding:"required"`
//	ScaleUpThreshold   int    `json:"scaleUpThreshold"`
//	ScaleDownStartTime string `json:"scaleDownStartTime"`
//	ScaleDownEndTime   string `json:"scaleDownEndTime"`
//	Author             string `json:"author"`
//	CurrReplicas       int32  `json:"currReplicas"`
//	PipeReplicas       int32  `json:"pipeReplicas"`
//	CreatedTime        *Time  `json:"createdTime"`
//	UpdatedTime        *Time  `json:"updatedTime"`
//	Analyze            string `json:"analyze"`
//}

//func (my *AutoScale) ToModel() models.AutoScale {
//	return models.AutoScale{
//		Model: gorm.Model{
//			ID: my.ID,
//		},
//		App:                my.App,
//		Cluster:            my.Cluster,
//		Namespace:          my.Namespace,
//		CpuTargetPercent:   my.CpuTargetPercent,
//		Replicas:           my.Replicas,
//		Author:             my.Author,
//		ScaleUpThreshold:   my.ScaleUpThreshold,
//		ScaleDownStartTime: my.ScaleDownStartTime,
//		ScaleDownEndTime:   my.ScaleDownEndTime,
//	}
//}
//func (my *AutoScale) ParseByModel(entity models.AutoScale) {
//	my.ID = entity.ID
//	my.App = entity.App
//	my.Cluster = entity.Cluster
//	my.Namespace = entity.Namespace
//	my.ScaleUpThreshold = entity.ScaleUpThreshold
//	my.ScaleDownStartTime = entity.ScaleDownStartTime
//	my.ScaleDownEndTime = entity.ScaleDownEndTime
//	my.Replicas = entity.Replicas
//	my.CpuTargetPercent = entity.CpuTargetPercent
//	my.Author = entity.Author
//	my.CurrReplicas = 0
//	my.PipeReplicas = 0
//	my.CreatedTime = NewTimePointer(entity.CreatedAt)
//	my.UpdatedTime = NewTimePointer(entity.UpdatedAt)
//
//}
//
//func ToAutoScaleList(entities []models.AutoScale) []AutoScale {
//	ret := make([]AutoScale, 0, len(entities))
//	if entities != nil {
//		for _, it := range entities {
//			item := AutoScale{}
//			item.ParseByModel(it)
//			ret = append(ret, item)
//		}
//	}
//	return ret
//}
