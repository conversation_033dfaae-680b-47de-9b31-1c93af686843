package dto

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

type Artifact struct {
	ID          uint                   `json:"id"`
	GitUrl      string                 `json:"gitUrl"`
	Module      string                 `json:"module"`
	Author      string                 `json:"author"`
	Remark      string                 `json:"remark"`
	CreatedTime *Time                  `json:"createdTime"`
	ExtraAttr   map[string]interface{} `json:"extraAttr"`
}

func (my *Artifact) ToModel() models.Artifact {
	return models.Artifact{
		Model: gorm.Model{
			ID: my.ID,
		},
		GitUrl: my.GitUrl,
		Module: my.Module,
		Remark: my.Remark,
	}
}
func (my *Artifact) ParseByModel(entity models.Artifact) {
	my.ID = entity.ID
	my.GitUrl = entity.GitUrl
	my.Module = entity.Module
	my.Remark = entity.Remark
	my.Author = entity.Author
	my.CreatedTime = NewTimePointer(entity.CreatedAt)
}

func ToArtifactList(entities []models.Artifact) []Artifact {
	ret := make([]Artifact, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := Artifact{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}
func ToArtifact(entity models.Artifact) Artifact {
	ret := Artifact{}
	ret.ParseByModel(entity)
	return ret
}
