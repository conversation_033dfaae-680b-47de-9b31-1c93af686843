package dto

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

type Org struct {
	ID     uint     `json:"id" form:"id"`
	Name   string   `json:"name" form:"name" binding:"required"`
	Remark string   `json:"remark" form:"remark"`
	Users  []string `json:"users" form:"users"`
}

func (my *Org) ToModel() models.Org {
	return models.Org{
		Model: gorm.Model{
			ID: my.ID,
		},
		Name:   my.Name,
		Remark: my.Remark,
		Users:  my.Users,
	}
}
func (my *Org) ParseByModel(entity models.Org) {
	my.ID = entity.ID
	my.Name = entity.Name
	my.Remark = entity.Remark
	my.Users = entity.Users
}

func ToOrgList(entities []models.Org) []Org {
	ret := make([]Org, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := Org{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}
