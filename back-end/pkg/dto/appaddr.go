package dto

import "k8s.io/apimachinery/pkg/util/intstr"

type AppAddress struct {
	Cluster    string             `json:"cluster"`
	Namespace  string             `json:"namespace"`
	App        string             `json:"app"`
	Name       string             `json:"name"`
	Protocol   string             `json:"protocol"`
	Port       int32              `json:"port"`
	TargetPort intstr.IntOrString `json:"targetPort"`
	NodePort   int32              `json:"nodePort"`
	Addresses  []string           `json:"addresses"`
	Remark     string             `json:"remark"`
}
