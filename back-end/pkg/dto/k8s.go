package dto

import (
	"fs-k8s-app-manager/models/datatype"
	"k8s.io/apimachinery/pkg/util/intstr"
	"time"
)

type DeployModule struct {
	GitUrl        string `json:"gitUrl"`
	Module        string `json:"module"`
	ContextPath   string `json:"contextPath"`
	Tag           string `json:"tag"`
	CommitID      string `json:"commitId"`
	CommitIDShort string `json:"commitIdShort"`
}

func ToDeployModules(modules datatype.DeployModules) []DeployModule {
	ret := make([]DeployModule, 0, len(modules))
	for _, module := range modules {
		commitIDShort := module.CommitID
		if len(module.CommitID) > 7 {
			commitIDShort = module.CommitID[:7]
		}
		ret = append(ret, DeployModule{
			GitUrl:        module.GitUrl,
			Module:        module.Module,
			ContextPath:   module.ContextPath,
			Tag:           module.Tag,
			CommitID:      module.CommitID,
			CommitIDShort: commitIDShort,
		})
	}
	return ret
}

type Deployment struct {
	Cluster            string         `json:"cluster"`
	Name               string         `json:"name"`
	Namespace          string         `json:"namespace"`
	Replicas           int32          `json:"replicas"`
	DeployTag          string         `json:"deployTag"`
	DeployModules      []DeployModule `json:"deployModules"`
	Container0Name     string         `json:"container0Name"`
	Container0Image    string         `json:"container0Image"` //第一个容器的镜像
	Container0ImageTag string         `json:"container0ImageTag"`
	RequestMemory      int64          `json:"requestMemory"`
	RequestCPU         int64          `json:"requestCpu"`
	LimitMemory        int64          `json:"limitMemory"`
	LimitCPU           int64          `json:"limitCpu"`
	CreateTime         Time           `json:"createTime"`
	UpdateTime         Time           `json:"updateTime"`
	Revision           string         `json:"revision"`
	Language           string         `json:"language"`
	LastModifyUser     string         `json:"lastModifyUser"`
	DeployUser         string         `json:"deployUser"`
	DeployTime         string         `json:"deployTime"`
	DeployRemark       string         `json:"deployRemark"`
}

type Node struct {
	Name                    string `json:"name"`
	Schedulable             bool   `json:"schedulable"`
	Arch                    string `json:"arch"`
	Os                      string `json:"os"`
	ContainerRuntimeVersion string `json:"containerRuntimeVersion"`
	KernelVersion           string `json:"kernelVersion"`
	OsImage                 string `json:"osImage"`
	DedicatedName           string `json:"dedicatedName"`
	CPUCapacity             int64  `json:"cpuCapacity"`    //单位：milliCore
	MemoryCapacity          int64  `json:"memoryCapacity"` //单位：MB
}

type ReplicaSet struct {
	Deployment
}

type Service struct {
	Name      string        `json:"name"`
	Namespace string        `json:"namespace"`
	Ports     []ServicePort `json:"ports"`
}

type ServicePort struct {
	Name       string             `json:"name"`
	Protocol   string             `json:"protocol"`
	Port       int32              `json:"port"`
	TargetPort intstr.IntOrString `json:"targetPort"`
	NodePort   int32              `json:"nodePort"`
}

type Pod struct {
	Name                string         `json:"name"`
	Cluster             string         `json:"cluster"`
	Namespace           string         `json:"namespace"`
	PodIP               string         `json:"podIP"`
	HostIP              string         `json:"hostIP"`
	NodeName            string         `json:"hostName"`
	LabelApp            string         `json:"labelApp"`
	CreateTime          Time           `json:"createTime"`
	StartTime           Time           `json:"startTime"`
	DeleteTime          Time           `json:"deleteTime"`
	Phase               string         `json:"phase"`
	StatusDesc          string         `json:"statusDesc"`
	StatusReason        string         `json:"statusReason"`
	Ready               bool           `json:"ready"`
	RestartCount        int32          `json:"restartCount"`
	RestartCode         int32          `json:"restartCode"`
	RestartReason       string         `json:"restartReason"`
	RestartTime         Time           `json:"restartTime"`
	DeployTag           string         `json:"deployTag"`
	DeployModules       []DeployModule `json:"deployModules"`
	InitContainersName  []string       `json:"initContainersName"`
	InitContainersImage []string       `json:"initContainersImage"`
	Container0Name      string         `json:"container0Name"`
	Container0Image     string         `json:"container0Image"`
	Container0ImageTag  string         `json:"container0ImageTag"`
	Container0Status    string         `json:"container0Status"`
	Container0Ready     bool           `json:"container0Ready"`
	RequestMemory       int64          `json:"requestMemory"`
	RequestCPU          int64          `json:"requestCpu"`
	LimitMemory         int64          `json:"limitMemory"`
	LimitCPU            int64          `json:"limitCpu"`
	Deregister          bool           `json:"deregister"`
	VersionRetain       bool           `json:"versionRetain"`

	Events []Event `json:"events"`

	CpuUsage           int64 `json:"cpuUsage"`
	MemoryUsage        int64 `json:"memoryUsage"`
	CpuUsagePercent    int   `json:"cpuUsagePercent"`
	MemoryUsagePercent int   `json:"memoryUsagePercent"`

	ResourcePool string `json:"resourcePool"`
}

func (p Pod) LastStartTime() time.Time {
	if p.RestartTime.IsZero() {
		return p.StartTime.Time
	} else {
		return p.RestartTime.Time
	}
}

type PodFile struct {
	Name         string `json:"name"`
	IsDir        bool   `json:"isDir"`
	Size         int64  `json:"size"`
	HumanizeSize string `json:"humanizeSize"`
	ModifyTime   string `json:"modifyTime"`
}

const (
	EventTypeNormal  string = "Normal"
	EventTypeWarning string = "Warning"
)

type Event struct {
	Name               string `json:"name"`
	Namespace          string `json:"namespace"`
	InvolvedObjectType string `json:"involvedObjectType"`
	InvolvedObjectName string `json:"involvedObjectName"`
	Type               string `json:"type"`
	Reason             string `json:"reason"`
	Message            string `json:"message"`
	Action             string `json:"action"`
	Count              int32  `json:"count"`
	CreateTime         Time   `json:"createTime"`
	EventTime          Time   `json:"eventTime"`
	FirstTime          Time   `json:"firstTime"`
	LastTime           Time   `json:"lastTime"`
}
