package dto

import (
	"fs-k8s-app-manager/models"
)

type Log struct {
	ID          uint   `json:"id" form:"id"`
	Author      string `json:"author"`
	Operate     string `json:"operate"`
	Target      string `json:"target"`
	Content     string `json:"content"`
	CreatedTime *Time  `json:"createdTime"`
}

func ParseLog(entity models.Log) *Log {
	dto := Log{}
	dto.ID = entity.ID
	dto.Author = entity.Author
	dto.Operate = entity.Operate
	dto.Target = entity.Target
	dto.Content = entity.Content
	dto.CreatedTime = NewTimePointer(entity.CreatedAt)
	return &dto
}

func ParseLogList(entities []models.Log) []Log {
	ret := make([]Log, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			ret = append(ret, *ParseLog(it))
		}
	}
	return ret
}
