package dto

import (
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"gorm.io/gorm"
)

type App struct {
	ID             uint                 `json:"id" form:"id"`
	Name           string               `json:"name" form:"name" binding:"required"`
	Level          string               `json:"level" form:"level"`
	Department     string               `json:"department" form:"department"`
	Orgs           []string             `json:"orgs" form:"orgs" `
	Remark         string               `json:"remark" form:"remark"`
	CreatedTime    *Time                `json:"createdTime"`
	UpdatedTime    *Time                `json:"updatedTime"`
	TimeWindow     datatype.TimePeriods `json:"timeWindow" form:"timeWindow"`
	TimeWindowDesc string               `json:"timeWindowDesc"`
	MainOwner      string               `json:"mainOwner" form:"mainOwner"`
	Owners         []string             `json:"owners" form:"owners"`
	Category       string               `json:"category" form:"category"`
}

func (my *App) ToModel() models.App {
	return models.App{
		Model: gorm.Model{
			ID: my.ID,
		},
		Name:       my.Name,
		Level:      my.Level,
		Department: my.Department,
		Orgs:       my.Orgs,
		Remark:     my.Remark,
		TimeWindow: my.TimeWindow,
		MainOwner:  my.MainOwner,
		Owners:     my.Owners,
		Category:   my.Category,
	}
}
func (my *App) ParseByModel(entity models.App) {
	my.ID = entity.ID
	my.Name = entity.Name
	my.Level = entity.Level
	my.Department = entity.Department
	my.Orgs = entity.Orgs
	my.Remark = entity.Remark
	my.CreatedTime = NewTimePointer(entity.CreatedAt)
	my.UpdatedTime = NewTimePointer(entity.UpdatedAt)
	my.TimeWindow = entity.TimeWindow
	my.TimeWindowDesc = entity.TimeWindow.Desc()
	my.MainOwner = entity.MainOwner
	my.Owners = entity.Owners
	my.Category = entity.Category
}

func ToAppList(entities []models.App) []App {
	ret := make([]App, 0, len(entities))
	for _, it := range entities {
		item := App{}
		item.ParseByModel(it)
		ret = append(ret, item)
	}
	return ret
}
func ToApp(entity models.App) App {
	ret := App{}
	ret.ParseByModel(entity)
	return ret
}
