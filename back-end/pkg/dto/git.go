package dto

import (
	"time"
)

type CreateGitTag struct {
	GitURL string `json:"gitURL" form:"gitUrl" binding:"required"`
	Branch string `json:"branch" form:"branch" binding:"required"`
	Tag    string `json:"tag" form:"tag" binding:"required"`
	Msg    string `json:"msg" form:"msg"`
}

type GitTagProperty struct {
	GitURL   string       `json:"gitUrl"`
	Branches []*GitBranch `json:"branches"`
	Tags     []*GitTag    `json:"tags"`
	Branch   string       `json:"branch"`
	NextTag  string       `json:"nextTag"`
}

type GitTag struct {
	Name            string     `json:"name"`
	Message         string     `json:"message"`
	CommitID        string     `json:"commitId"`
	CommitAuthor    string     `json:"commitAuthor"`
	CommitCreatedAt *time.Time `json:"commitCreatedAt"`
	CommitMessage   string     `json:"commitMessage"`
}

type GitBranch struct {
	Name            string     `json:"name"`
	Message         string     `json:"message"`
	CommitID        string     `json:"commitId"`
	CommitAuthor    string     `json:"commitAuthor"`
	CommitCreatedAt *time.Time `json:"commitCreatedAt"`
	CommitMessage   string     `json:"commitMessage"`
}

type GitCommit struct {
	ID        string     `json:"commitId"`
	ShortID   string     `json:"shortId"`
	Author    string     `json:"author"`
	CreatedAt *time.Time `json:"createdAt"`
	Message   string     `json:"message"`
}

type BranchDeleteGitTagAndBranch struct {
	GitURL   string   `json:"gitUrl" binding:"required"`
	Tags     []string `json:"tags" `
	Branches []string `json:"branches"`
}
