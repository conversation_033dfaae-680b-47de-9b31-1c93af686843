package dto

import (
	"fs-k8s-app-manager/models"
)

type ScaleLog struct {
	Operate     string `json:"operate"`
	App         string `json:"app"`
	Cluster     string `json:"cluster"`
	Namespace   string `json:"namespace"`
	OldReplicas int32  `json:"oldReplicas"`
	NewReplicas int32  `json:"newReplicas"`
	Author      string `json:"author"`
	Remark      string `json:"remark"`
	CreatedTime *Time  `json:"createdTime"`
}

func ParseScaleLog(entity models.ScaleLog) ScaleLog {
	dto := ScaleLog{
		Operate:     entity.Operate,
		App:         entity.App,
		Cluster:     entity.Cluster,
		Namespace:   entity.Namespace,
		OldReplicas: entity.OldReplicas,
		NewReplicas: entity.NewReplicas,
		Author:      entity.Author,
		Remark:      entity.Remark,
	}
	dto.CreatedTime = NewTimePointer(entity.CreatedAt)
	return dto
}

func ParseScaleLogList(entities []models.ScaleLog) []ScaleLog {
	ret := make([]ScaleLog, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			ret = append(ret, ParseScaleLog(it))
		}
	}
	return ret
}

type ScaleMonitorLog struct {
	App         string `json:"app"`
	Cluster     string `json:"cluster"`
	Namespace   string `json:"namespace"`
	Remark      string `json:"remark"`
	CreatedTime *Time  `json:"createdTime"`
}

func ParseScaleMonitorLog(entity models.ScaleMonitorLog) ScaleLog {
	dto := ScaleLog{
		App:       entity.App,
		Cluster:   entity.Cluster,
		Namespace: entity.Namespace,
		Remark:    entity.Remark,
	}
	dto.CreatedTime = NewTimePointer(entity.CreatedAt)
	return dto
}

func ParseScaleLogMonitorList(entities []models.ScaleMonitorLog) []ScaleLog {
	ret := make([]ScaleLog, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			ret = append(ret, ParseScaleMonitorLog(it))
		}
	}
	return ret
}
