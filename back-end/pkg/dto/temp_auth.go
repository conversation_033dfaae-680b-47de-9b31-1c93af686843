package dto

import (
	"fs-k8s-app-manager/models"
	"time"

	"gorm.io/gorm"
)

type TempAuth struct {
	ID            uint   `json:"id"`
	App           string `json:"app"`
	User          string `json:"user"`
	Operate       string `json:"operate"`
	OperateDesc   string `json:"operateDesc"`
	Approver      string `json:"approver"`
	Approved      bool   `json:"approved"`
	Date          string `json:"date"`
	Authorizer    string `json:"authorizer"`
	Remark        string `json:"remark"`
	StartTime     Time   `json:"startTime"`
	EndTime       Time   `json:"endTime"`
	TimeRangeDesc string `json:"timeRangeDesc"`
	CreatedTime   Time   `json:"createdTime"`
}

func (my *TempAuth) ToModel() models.TempAuth {
	return models.TempAuth{
		Model: gorm.Model{
			ID: my.ID,
		},
		App:        my.App,
		User:       my.User,
		Operate:    my.Operate,
		Approver:   my.Approver,
		Approved:   my.Approved,
		Authorizer: my.Authorizer,
		Remark:     my.Remark,
		StartTime:  my.StartTime.Time,
		EndTime:    my.EndTime.Time,
	}
}

func (my *TempAuth) ParseByModel(entity models.TempAuth) {
	my.ID = entity.ID
	my.App = entity.App
	my.User = entity.User
	my.Operate = entity.Operate
	my.Approver = entity.Approver
	my.Approved = entity.Approved
	my.StartTime = NewTime(entity.StartTime)
	my.EndTime = NewTime(entity.EndTime)
	my.Authorizer = entity.Authorizer
	my.Remark = entity.Remark
	my.OperateDesc = entity.OperateDesc()
	my.Date = entity.CreatedAt.Format("2006-01-02")
	my.CreatedTime = NewTime(entity.CreatedAt)
	my.TimeRangeDesc = ""
	if time.Now().Before(entity.StartTime) {
		my.TimeRangeDesc = "未到开始时间"
	} else if time.Now().After(entity.EndTime) {
		my.TimeRangeDesc = "已过期"
	}
}

func ToTempAuthList(entities []models.TempAuth) []TempAuth {
	ret := make([]TempAuth, 0, len(entities))
	for _, it := range entities {
		item := TempAuth{}
		item.ParseByModel(it)
		ret = append(ret, item)
	}
	return ret
}
