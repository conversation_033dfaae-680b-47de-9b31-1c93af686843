package dto

import (
	"fs-k8s-app-manager/models"
	"gorm.io/gorm"
)

type Reboot struct {
	ID         uint   `json:"id" form:"id"`
	Cluster    string `json:"cluster" form:"cluster" binding:"required"`
	Namespace  string `json:"namespace" form:"namespace" binding:"required"`
	App        string `json:"app" form:"app" binding:"required"`
	Author     string `json:"author" form:"author"`
	Remark     string `json:"remark" form:"remark"`
	RebootHour int    `json:"rebootHour" form:"rebootHour"`
}

func (my *Reboot) ToModel() models.Reboot {
	return models.Reboot{
		Model: gorm.Model{
			ID: my.ID,
		},
		Cluster:    my.Cluster,
		Namespace:  my.Namespace,
		App:        my.App,
		Author:     my.Author,
		Remark:     my.Remark,
		RebootHour: my.RebootHour,
	}
}
func (my *Reboot) ParseByModel(entity models.Reboot) {
	my.ID = entity.ID
	my.Cluster = entity.Cluster
	my.Namespace = entity.Namespace
	my.App = entity.App
	my.Remark = entity.Remark
	my.RebootHour = entity.RebootHour
	my.Author = entity.Author
}

func ToRebootList(entities []models.Reboot) []Reboot {
	ret := make([]Reboot, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item := Reboot{}
			item.ParseByModel(it)
			ret = append(ret, item)
		}
	}
	return ret
}
