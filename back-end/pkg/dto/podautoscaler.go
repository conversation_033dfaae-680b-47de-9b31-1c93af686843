package dto

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type PodAutoScalerDTO struct {
	Cluster                             string            `json:"cluster" yaml:"Cluster"`
	Namespace                           string            `json:"namespace" yaml:"Namespace"`
	App                                 string            `json:"app" yaml:"App"`
	Labels                              map[string]string `json:"labels" yaml:"Labels"`
	Annotations                         map[string]string `json:"annotations" yaml:"Annotations"`
	Paused                              bool              `json:"paused" yaml:"Paused"`
	MinReplicas                         int32             `json:"minReplicas" yaml:"MinReplicas"`
	MaxReplicas                         int32             `json:"maxReplicas" yaml:"MaxReplicas"`
	ScaleUpInitialDelaySeconds          int32             `json:"scaleUpInitialDelaySeconds" yaml:"ScaleUpInitialDelaySeconds"`
	ScaleUpStabilizationWindowSeconds   int32             `json:"scaleUpStabilizationWindowSeconds" yaml:"ScaleUpStabilizationWindowSeconds"`
	ScaleUpReplicaStep                  int32             `json:"scaleUpReplicaStep" yaml:"ScaleUpReplicaStep"`
	ScaleUpHourWindow                   []int32           `json:"scaleUpHourWindow" yaml:"ScaleUpHourWindow"`
	ScaleDownStabilizationWindowSeconds int32             `json:"scaleDownStabilizationWindowSeconds" yaml:"ScaleDownStabilizationWindowSeconds"`
	ScaleDownReplicaStep                int32             `json:"scaleDownReplicaStep" yaml:"ScaleDownReplicaStep"`
	ScaleDownHourWindow                 []int32           `json:"scaleDownHourWindow" yaml:"ScaleDownHourWindow"`
	TriggerCpuUtilization               string            `json:"triggerCpuUtilization" yaml:"TriggerCpuUtilization"`
}

type ScaleTrigger struct {
	Type     string            `json:"type"`
	Metadata map[string]string `json:"metadata"`
}

type ScaleUpSpec struct {
	InitialDelaySeconds        *int32  `json:"initialDelaySeconds,omitempty"`
	StabilizationWindowSeconds *int32  `json:"stabilizationWindowSeconds,omitempty"`
	HourWindow                 []int32 `json:"hourWindow,omitempty"`
	ReplicaStep                *int32  `json:"replicaStep,omitempty"`
	HourWindowDesc             string  `json:"hourWindowDesc,omitempty"`
}

type ScaleDownSpec struct {
	StabilizationWindowSeconds *int32  `json:"stabilizationWindowSeconds,omitempty"`
	HourWindow                 []int32 `json:"hourWindow,omitempty"`
	ReplicaStep                *int32  `json:"replicaStep,omitempty"`
	HourWindowDesc             string  `json:"hourWindowDesc,omitempty"`
}

type PodAutoScalerSpec struct {
	App             string         `json:"app"`
	Paused          bool           `json:"paused"`
	MinReplicaCount *int32         `json:"minReplicaCount,omitempty"`
	MaxReplicaCount *int32         `json:"maxReplicaCount,omitempty"`
	ScaleUp         *ScaleUpSpec   `json:"scaleUp,omitempty"`
	ScaleDown       *ScaleDownSpec `json:"scaleDown,omitempty"`
	Triggers        []ScaleTrigger `json:"triggers"`
}

type PodAutoScalerStatus struct {
	CurrentReplica    *int32       `json:"currentReplica,omitempty"`
	ScaleUpCount      int32        `json:"scaleUpCount,omitempty"`
	ScaleDownCount    int32        `json:"scaleDownCount,omitempty"`
	LastScaleUpTime   *metav1.Time `json:"lastScaleUpTime,omitempty"`
	LastScaleDownTime *metav1.Time `json:"lastScaleDownTime,omitempty"`
}

type PodAutoScaler struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   PodAutoScalerSpec   `json:"spec,omitempty"`
	Status PodAutoScalerStatus `json:"status,omitempty"`
}

type PodAutoScalerList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []PodAutoScaler `json:"items"`
}
