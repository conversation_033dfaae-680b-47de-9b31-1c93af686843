package dto

type Metrics struct {
	Data struct {
		Result     result `json:"result"`
		ResultType string `json:"resultType"`
	} `json:"data"`
	Status string `json:"status"`
}

type result []struct {
	Metric struct {
		Pod string `json:"pod"`
	} `json:"metric"`
	Value []interface{} `json:"value"`
}

type PodMetric struct {
	//Name   string `json:"name"`
	//CPU    string `json:"cpu"`
	AvgMemory   string `json:"avgMemory"`
	MaxMemory   string `json:"maxMemory"`
	AvgCpuUsage string `json:"avgCpuUsage"`
	MaxCpuUsage string `json:"maxCpuUsage"`
}
