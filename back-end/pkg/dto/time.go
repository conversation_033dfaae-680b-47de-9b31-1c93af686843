package dto

import (
	"encoding/json"
	"time"
)

type Time struct {
	time.Time  `protobuf:"-"`
	_formatter string
}

func (t Time) getFormatter() string {
	if t._formatter == "" {
		return "2006-01-02 15:04:05"
	} else {
		return t._formatter
	}
}

func (t Time) String() string {
	return t.Time.String()
}

func (t Time) FormatStr() string {
	return t.Time.Format(t.getFormatter())
}

//func (t Time) Format() string {
//	return t.Time.String()
//}

func NewTime(time time.Time) Time {
	return Time{Time: time}
}
func NewTimeWithFormatter(time time.Time, formatter string) Time {
	return Time{Time: time, _formatter: formatter}
}
func NewTimePointer(time time.Time) *Time {
	ret := NewTime(time)
	return &ret
}

func Date(year int, month time.Month, day, hour, min, sec, nsec int, loc *time.Location) Time {
	return NewTime(time.Date(year, month, day, hour, min, sec, nsec, loc))
}

func NowTime() Time {
	return NewTime(time.Now())
}

func (t *Time) IsZero() bool {
	if t == nil {
		return true
	}
	return t.Time.IsZero()
}

func (t *Time) Before(u *Time) bool {
	return t.Time.Before(u.Time)
}

func (t *Time) Equal(u *Time) bool {
	if t == nil && u == nil {
		return true
	}
	if t != nil && u != nil {
		return t.Time.Equal(u.Time)
	}
	return false
}

func (t *Time) UnmarshalJSON(b []byte) error {
	if len(b) == 4 && string(b) == "null" {
		t.Time = time.Time{}
		return nil
	}

	var str string
	err := json.Unmarshal(b, &str)
	if err != nil {
		return err
	}

	pt, err := time.Parse(t.getFormatter(), str)
	if err != nil {
		return err
	}

	t.Time = pt
	return nil
}

func (t Time) MarshalJSON() ([]byte, error) {
	if t.IsZero() {
		// Encode unset/nil objects as JSON's "null".
		return []byte("null"), nil
	}

	return json.Marshal(t.Format(t.getFormatter()))
}
