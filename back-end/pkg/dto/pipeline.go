package dto

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/data"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"sort"
	"strings"

	"gorm.io/gorm"
)

type PipelineExtraAttr struct {
	ClusterSummary    string   `json:"clusterSummary"`
	CloudCategory     string   `json:"cloudCategory"`
	CloudCategoryDesc string   `json:"cloudCategoryDesc"`
	ClusterLabels     []string `json:"clusterLabels"`
	DeployTag         string   `json:"deployTag"`
	RunningPodNum     int32    `json:"runningPodNum"`
	LastDeployUser    string   `json:"lastDeployUser"`
	LastDeployTime    string   `json:"lastDeployTime"`
}

type Pipeline struct {
	ID                   uint                    `json:"id" form:"id"`
	Status               string                  `json:"status"`
	Author               string                  `json:"author"`
	CreatedTime          *Time                   `json:"createdTime"`
	UpdatedTime          *Time                   `json:"updatedTime"`
	App                  string                  `json:"app" form:"app" binding:"required"`
	Cluster              string                  `json:"cluster" form:"cluster" binding:"required"`
	Namespace            string                  `json:"namespace" form:"namespace" binding:"required"`
	BaseImage            string                  `json:"baseImage" form:"baseImage" binding:"required"`
	Replicas             uint                    `json:"replicas" form:"replicas"`
	DeployStrategy       constant.DeployStrategy `json:"deployStrategy" form:"deployStrategy" binding:"required"`
	AppModules           datatype.AppModules     `json:"appModules" form:"appModules"`
	Resources            data.Resources          `json:"resources" form:"resources" binding:"required"`
	LivenessProbe        data.Probe              `json:"livenessProbe" form:"livenessProbe"`
	ReadinessProbe       data.Probe              `json:"readinessProbe" form:"readinessProbe"`
	StartupProbe         data.Probe              `json:"startupProbe" form:"startupProbe"`
	Schedule             data.Schedule           `json:"schedule" form:"schedule"`
	PVC                  data.PVC                `json:"pvc" form:"pvc"`
	JvmOpts              string                  `json:"jvmOpts" form:"jvmOpts"`
	Envs                 datatype.Envs           `json:"envs" form:"envs"`
	Ports                datatype.Ports          `json:"ports" form:"ports"`
	PartnerApps          datatype.StrList        `json:"partnerApps" form:"partnerApps"`
	ExclusiveApps        datatype.StrList        `json:"exclusiveApps" form:"exclusiveApps"`
	PreStopWebhook       string                  `json:"preStopWebhook" form:"preStopWebhook"`
	PreStopRetainSeconds uint                    `json:"preStopRetainSeconds" form:"preStopRetainSeconds"`
	Webhook              datatype.Webhook        `json:"webhook" form:"webhook"`
	Options              data.PipelineOption     `json:"options"`
	EolinkerIDs          datatype.EolinkerIDs    `json:"eolinkerIDs" form:"eolinkerIDs"`
	ExtraAttr            PipelineExtraAttr       `json:"extraAttr"`
	Remark               string                  `json:"remark"`
	ExtInitContainer     string                  `json:"extInitContainer"`
}

// SortPipelines 发布流程排序
// 排序规则：纷享云排在专属云之前
// 纷享云：环境名长度（一般辅助环境名比主环境名要长,比如：foneshare-gray > foneshare)
//func SortPipelines(pipes []Pipeline) []Pipeline {
//	//纷享云
//	saasPipes := make([]Pipeline, 0, len(pipes))
//	//专属云
//	dedicatedPipes := make([]Pipeline, 0, len(pipes))v
//	for _, pipe := range pipes {
//		if pipe.ExtraAttr.DedicatedCloud {
//			dedicatedPipes = append(dedicatedPipes, pipe)
//		} else {
//			saasPipes = append(saasPipes, pipe)
//		}
//	}
//
//	sort.Slice(pipes, func(i, j int) bool {
//		pi := pipes[i]
//		pj := pipes[j]
//		//排序规则：纷享云排在专属云之前，其他情况比较环境名长度（一般辅助环境名比主环境名要长,比如：foneshare-gray > foneshare)
//		if !pi.ExtraAttr.DedicatedCloud && pj.ExtraAttr.DedicatedCloud {
//			return true
//		} else if pi.ExtraAttr.DedicatedCloud && !pj.ExtraAttr.DedicatedCloud {
//			return false
//		} else {
//			return len(pipes[i].Namespace) <= len(pipes[j].Namespace)
//		}
//	})
//}

func (p *Pipeline) NamespaceDesc() string {
	switch p.Namespace {
	case "foneshare-yqsl":
		return "元气森林"
	case "foneshare-yinlu":
		return "银鹭"
	case "foneshare-haoliyou":
		return "好丽友"
	case "foneshare-ruijie":
		return "锐捷"
	case "foneshare-didi":
		return "滴滴"
	case "foneshare-meifu":
		return "美孚"
	case "foneshare-vip":
		return "VIP"
	case "foneshare-svip":
		return "SVIP"
	default:
		return ""
	}
}

func ConvertPipeline(dto Pipeline) (*models.Pipeline, error) {
	ret := &models.Pipeline{
		Model: gorm.Model{
			ID: dto.ID,
		},
		Status:               dto.Status,
		Author:               dto.Author,
		App:                  dto.App,
		Cluster:              dto.Cluster,
		Namespace:            dto.Namespace,
		BaseImage:            dto.BaseImage,
		Replicas:             dto.Replicas,
		DeployStrategy:       dto.DeployStrategy,
		AppModules:           dto.AppModules,
		Resources:            dto.Resources,
		LivenessProbe:        dto.LivenessProbe,
		ReadinessProbe:       dto.ReadinessProbe,
		StartupProbe:         dto.StartupProbe,
		Schedule:             dto.Schedule,
		PVC:                  dto.PVC,
		Ports:                dto.Ports,
		Envs:                 dto.Envs,
		PartnerApps:          dto.PartnerApps,
		ExclusiveApps:        dto.ExclusiveApps,
		PreStopWebhook:       dto.PreStopWebhook,
		PreStopRetainSeconds: dto.PreStopRetainSeconds,
		Webhook:              dto.Webhook,
		Options:              dto.Options,
		EolinkerIDs:          dto.EolinkerIDs,
		Remark:               dto.Remark,
		ExtInitContainer:     dto.ExtInitContainer,
	}

	ret.Envs = envsHandler(dto)
	return ret, nil
}

func envsHandler(p Pipeline) datatype.Envs {
	processName := config.GetSetting().GetProcessName(p.Namespace, p.App)
	envMap := make(map[string]datatype.Env)
	for _, it := range p.Envs {
		envMap[it.Name] = it
	}

	// 设置系统环境变量
	envMap["JAVA_OPTS"] = datatype.Env{
		Name:  "JAVA_OPTS",
		Value: p.JvmOpts,
		Type:  constant.ENV_TYPE_SYSTEM,
	}
	envMap["CATALINA_OPTS"] = datatype.Env{
		Name: "CATALINA_OPTS",
		Value: fmt.Sprintf("-Dprocess.profile=%s -Dspring.profiles.active=%s -Dprocess.name=%s -Dapp.name=%s",
			p.Namespace, p.Namespace, processName, p.App),
		Type: constant.ENV_TYPE_SYSTEM,
	}

	envMap["K8S_APP_NAME"] = datatype.Env{
		Name:  "K8S_APP_NAME",
		Value: p.App,
		Type:  constant.ENV_TYPE_SYSTEM,
	}
	envMap["K8S_PROCESS_NAME"] = datatype.Env{
		Name:  "K8S_PROCESS_NAME",
		Value: processName,
		Type:  constant.ENV_TYPE_SYSTEM,
	}

	cluster := config.GetSetting().GetCluster(p.Cluster)
	if cluster != nil && cluster.ThirdServices.OOMReportUrl != "" {
		envMap["OOM_REPORT_URL"] = datatype.Env{
			Name:  "OOM_REPORT_URL",
			Value: cluster.ThirdServices.OOMReportUrl,
			Type:  constant.ENV_TYPE_SYSTEM,
		}
	}

	ret := make([]datatype.Env, 0, len(envMap))
	for _, it := range envMap {
		ret = append(ret, it)
	}
	sort.Slice(ret, func(i, j int) bool {
		e1 := ret[i]
		e2 := ret[j]
		//排序规则：系统变量排前面
		if e1.Type == constant.ENV_TYPE_SYSTEM && e2.Type != constant.ENV_TYPE_SYSTEM {
			return true
		} else {
			return strings.Compare(e1.Name, e2.Name) < 1
		}
	})
	return ret
}

func ParsePipeline(entity models.Pipeline) (*Pipeline, error) {
	dto := Pipeline{}
	dto.Status = entity.Status
	dto.Author = entity.Author
	dto.ID = entity.ID
	dto.App = entity.App
	dto.Cluster = entity.Cluster
	dto.Namespace = entity.Namespace
	dto.BaseImage = entity.BaseImage
	dto.Replicas = entity.Replicas
	dto.DeployStrategy = entity.DeployStrategy
	dto.AppModules = entity.AppModules
	dto.Resources = entity.Resources
	dto.LivenessProbe = entity.LivenessProbe
	dto.ReadinessProbe = entity.ReadinessProbe
	dto.StartupProbe = entity.StartupProbe
	dto.Schedule = entity.Schedule
	dto.PVC = entity.PVC
	dto.Ports = entity.Ports
	dto.Envs = entity.Envs
	dto.PartnerApps = entity.PartnerApps
	dto.ExclusiveApps = entity.ExclusiveApps
	dto.PreStopWebhook = entity.PreStopWebhook
	dto.PreStopRetainSeconds = entity.PreStopRetainSeconds
	dto.Webhook = entity.Webhook
	dto.Options = entity.Options
	dto.EolinkerIDs = entity.EolinkerIDs
	dto.Remark = entity.Remark
	dto.ExtInitContainer = entity.ExtInitContainer
	dto.CreatedTime = NewTimePointer(entity.CreatedAt)
	dto.UpdatedTime = NewTimePointer(entity.UpdatedAt)

	for _, it := range dto.Envs {
		if it.Name == "JAVA_OPTS" {
			dto.JvmOpts = it.Value
			break
		}
	}
	return &dto, nil
}

func ParsePipelineList(entities []models.Pipeline) ([]Pipeline, error) {
	ret := make([]Pipeline, 0, len(entities))
	if entities != nil {
		for _, it := range entities {
			item, err := ParsePipeline(it)
			if err != nil {
				return nil, err
			}
			ret = append(ret, *item)
		}
	}
	return ret, nil
}

type clusterNamespace struct {
	SourceCluster   string `json:"sourceCluster" form:"sourceCluster" binding:"required"`
	SourceNamespace string `json:"sourceNamespace" form:"sourceNamespace" binding:"required"`
	TargetCluster   string `json:"targetCluster" form:"targetCluster" binding:"required"`
	TargetNamespace string `json:"targetNamespace" form:"targetNamespace" binding:"required"`
}

type GetClonePipelineReq struct {
	clusterNamespace
}

type ClonePipelineReq struct {
	IDs []uint `json:"ids" binding:"gt=0,required"`
	clusterNamespace
}

type GetDedicatedCloudPipelineReq struct {
	clusterNamespace
}

// 在Pipeline结构体基础上增加了目标的额外属性
type DedicatedCloudPipeline struct {
	SourcePipeline Pipeline `json:"sourcePipeline"`
	TargetPipeline struct {
		ID        uint              `json:"id"`
		ExtraAttr PipelineExtraAttr `json:"extraAttr"`
	} `json:"targetPipeline"`
}

type DedicatedCloudPublish struct {
	Pipelines []struct {
		SourceID uint `json:"sourceID"`
		TargetID uint `json:"targetID"`
		// 目标部署的tag，不为master,则查询最后一次部署的tag版本
		DeployTag string `json:"deployTag"`
	} `json:"pipelines"`
}
