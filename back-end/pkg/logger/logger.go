package logger

import (
	"fmt"
	"fs-k8s-app-manager/config"
	log "github.com/sirupsen/logrus"
	"os"
	"path"
	"path/filepath"
	"runtime"
)

func Setup() {
	fmt.Println("logger init...")
	log.SetReportCaller(true)
	log.SetFormatter(&log.TextFormatter{
		DisableColors:   true,
		TimestampFormat: "01-02 15:04:05",
		FullTimestamp:   true,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			filename := path.Base(f.File)
			return "", fmt.Sprintf("%s:%d", filename, f.Line)
		},
	})

	logDir := filepath.Join(config.GetConfig().App.RuntimeDir, "logs")
	_ = os.MkdirAll(logDir, 0666)
	fPath := filepath.Join(logDir, "app.log")

	file, err := os.OpenFile(fPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		log.SetOutput(file)
	} else {
		log.Info("Failed to log to file, using default stderr")
	}
}
