apiVersion: v1
kind: ConfigMap
metadata:
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  labels:
    app: {{DEPLOY_APP_NAME}}-redis
  name: {{DEPLOY_APP_NAME}}-redis
data:
  redis.conf: |-
    bind 0.0.0.0
    port 6379
    tcp-backlog 511
    tcp-keepalive 300
    loglevel notice
    databases 2
    rdbcompression no
    # 关闭持久化
    # save 900 1  
    # save 300 10
    # save 60 10000
    dir /data/redis
    appendonly no
    maxmemory 8589934592

---
apiVersion: v1
kind: Service
metadata:
  name: {{DEPLOY_APP_NAME}}-redis
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  labels:
    app: {{DEPLOY_APP_NAME}}-redis
spec:
  type: ClusterIP
  ports:
    - name: redis
      port: 6379
      protocol: TCP
  selector:
    app: {{DEPLOY_APP_NAME}}-redis

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{DEPLOY_APP_NAME}}-redis
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  labels:
    app: {{DEPLOY_APP_NAME}}-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{DEPLOY_APP_NAME}}-redis
  template:
    metadata:
      labels:
        app: {{DEPLOY_APP_NAME}}-redis
    spec:
      containers:
        - name: {{DEPLOY_APP_NAME}}-redis
          image: reg.foneshare.cn/docker.io/redis:5.0-alpine
          imagePullPolicy: IfNotPresent
          args: ["/etc/redis.conf"]
          ports:
            - containerPort: 6379
              name: redis
              protocol: TCP
          resources:
            limits:
              cpu: 100m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          volumeMounts:
            - name: redis-config
              mountPath: /etc/redis.conf
              subPath: redis.conf
              readOnly: true
            - name: data
              mountPath: /data/redis
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 120
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 6379
            timeoutSeconds: 5
          securityContext:
            capabilities:
              add: []
              drop:
                - ALL
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 1001
      securityContext:
        fsGroup: 100
      volumes:
        - name: redis-config
          configMap:
            name: {{DEPLOY_APP_NAME}}-redis
        - name: data
          emptyDir: {}