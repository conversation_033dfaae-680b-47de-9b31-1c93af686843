apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{DEPLOY_APP_NAME}}
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  labels:
    app: {{DEPLOY_APP_NAME}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
    fxiaoke.com/language: {{DEPLOY_APP_LANGUAGE}}
spec:
  strategy:
    rollingUpdate:
      maxSurge: 100%
    type: RollingUpdate
  replicas: {{DEPLOY_APP_REPLICAS}}
  selector:
    matchLabels:
      app: {{DEPLOY_APP_NAME}}
      version: v0
  template:
    metadata:
      labels:
        app: {{DEPLOY_APP_NAME}}
        version: v0
      annotations:
        fxiaoke.com/managed-by: gitlab-ci
        fxiaoke.com/language: {{DEPLOY_APP_LANGUAGE}}
        fxiaoke.com/commit-sha: "{{CI_COMMIT_SHORT_SHA}}"
    spec:
      tolerations:
        - effect: NoSchedule
          key: fxiaoke.com/dedicated
          operator: Equal
          value: Manage
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: fxiaoke.com/dedicated
                    operator: In
                    values:
                      - Manage
      containers:
        - name: {{DEPLOY_APP_NAME}}
          image: {{DEPLOY_APP_DOCKER_IMAGE}}
          imagePullPolicy: Always
          volumeMounts:
            - name: app-conf
              mountPath: /opt/fs-app/back-end/conf
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: {{DEPLOY_APP_REQUESTS_CPU}}
              memory: {{DEPLOY_APP_REQUESTS_MEMORY}}
            limits:
              cpu: {{DEPLOY_APP_LIMITS_CPU}}
              memory: {{DEPLOY_APP_LIMITS_MEMORY}}
          env:
            - name: TZ
              value: Asia/Shanghai
        - name: configmap-reload
          image: "{{DEPLOY_K8S_CONFIGMAP_RELOAD_IMAGE}}"
          args:
            - -volume-dir=/etc/config
            - -webhook-url=http://localhost/api/configmap/reload
            - -webhook-retries=3
          volumeMounts:
            - name: app-conf
              mountPath: /etc/config
          resources:
            limits:
              cpu: 20m
              memory: 64Mi
      volumes:
        - name: app-conf
          configMap:
            name: {{DEPLOY_APP_NAME}}-config
            items:
              - key: k8s1
                path: kubeconf/k8s1
              - key: k8s0
                path: kubeconf/k8s0
              - key: k8s2
                path: kubeconf/k8s2
              - key: ucd-k8s1
                path: kubeconf/ucd-k8s1
              - key: ucd-k8s2
                path: kubeconf/ucd-k8s2
              - key: sbt-k8s1
                path: kubeconf/sbt-k8s1
              - key: ksc-k8s1
                path: kubeconf/ksc-k8s1
              - key: hws-k8s1
                path: kubeconf/hws-k8s1
              - key: ale-k8s1
                path: kubeconf/ale-k8s1
              - key: cloudmodel-k8s1
                path: kubeconf/cloudmodel-k8s1
              - key: forceecrm-k8s1
                path: kubeconf/forceecrm-k8s1
              - key: xjgc-k8s1
                path: kubeconf/xjgc-k8s1
              - key: hisense-k8s1
                path: kubeconf/hisense-k8s1
              - key: chinatower-k8s1
                path: kubeconf/chinatower-k8s1
              - key: mengniu-k8s1
                path: kubeconf/mengniu-k8s1
              - key: hsyk-k8s1
                path: kubeconf/hsyk-k8s1
              - key: wuzizui99-k8s1
                path: kubeconf/wuzizui99-k8s1
              - key: iflytek-k8s1
                path: kubeconf/iflytek-k8s1
              - key: yangnongchem-k8s1
                path: kubeconf/yangnongchem-k8s1
              - key: hexagonmi-k8s1
                path: kubeconf/hexagonmi-k8s1
              - key: kemaicrm-k8s1
                path: kubeconf/kemaicrm-k8s1
              - key: allink8s-k8s1
                path: kubeconf/allink8s-k8s1
              - key: forsharecrm-k8s1
                path: kubeconf/forsharecrm-k8s1
              - key: teleagi-k8s1
                path: kubeconf/teleagi-k8s1
              - key: cpgc-k8s1
                path: kubeconf/cpgc-k8s1
              - key: wingd-k8s1
                path: kubeconf/wingd-k8s1
              - key: kehua-k8s1
                path: kubeconf/kehua-k8s1
              - key: jingbo-k8s1
                path: kubeconf/jingbo-k8s1
              - key: tbea-k8s1
                path: kubeconf/tbea-k8s1
              - key: config.json
                path: config.json
              - key: settings.json
                path: settings.json
              - key: oncall.json
                path: oncall.json
              - key: lb-pools.json
                path: lb-pools.json

