apiVersion: v1
kind: Service
metadata:
  labels:
    app: k8s-app-publish-jenkins
  name: k8s-app-publish-jenkins
  namespace: jenkins
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
    - name: slavelistener
      port: 50000
      protocol: TCP
  selector:
    app: k8s-app-publish-jenkins
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: k8s-app-publish-jenkins
  namespace: jenkins
spec:
  replicas: 1
  selector:
    matchLabels:
      app: k8s-app-publish-jenkins
  template:
    metadata:
      annotations:
        fxiaoke.com/managed-by: <EMAIL>
      labels:
        app: k8s-app-publish-jenkins
    spec:
      containers:
        - env:
            - name: JAVA_OPTS
              value: -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai
            - name: JENKINS_OPTS
              value: --accessLoggerClassName=winstone.accesslog.SimpleAccessLogger --simpleAccessLogger.format=combined
                --simpleAccessLogger.file=/var/jenkins_home/logs/access_log
            - name: ADMIN_PASSWORD
              value: ""
            - name: ADMIN_USER
              value: ""
          image: reg.firstshare.cn/docker.io/jenkins/jenkins:2.489-jdk17
          imagePullPolicy: IfNotPresent
          name: k8s-app-publish-jenkins
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
            - containerPort: 50000
              name: slavelistener
              protocol: TCP
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - mountPath: /var/jenkins_home
              name: jenkins-home
      restartPolicy: Always
      securityContext:
        runAsUser: 0
      volumes:
        - name: jenkins-home
          persistentVolumeClaim:
            claimName: k8s-app-publish-jenkins
