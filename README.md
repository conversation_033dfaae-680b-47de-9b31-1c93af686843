fs-k8s-app-manager 
===
k8s应用管理系统 

### 构建
使用jenkins来构建源码  

#####  jenkins配置
- 安装插件：Least Load （※很重要※ jenkins默认的调度策略会让任务的分配不均衡，使用 least load 方式来均衡分配任务到节点上）
- 安装插件：Pipeline Utility Steps 
- 安装插件：Jacoco、Docker Pipeline、Blue Ocean、Rebuild
- 匿名用户和登录用户授权对job资源可读权限
- 请将 Jenkins 系统的静默期设置为 0。原因是当发布系统执行 Jenkins 任务时，如果存在静默期，任务会被放到队列中等待执行。此时发布系统通过 gojenkins 执行任务，如果检测到任务处于排队状态，则不会执行该任务，从而导致发布系统执行 Jenkins job 失败。


##### 一些问题处理
- could not lock config file .git/config: File exists 
```
hudson.plugins.git.GitException: Command "git config remote.origin.url http://git.firstshare.cn/devops/fs-jenkins-pipeline.git" returned status code 255:
stdout: 
stderr: error: could not lock config file .git/config: File exists
```
问题描述：这类错误，主要是master 在拉取Jenkinsfile文件时出现了git文件锁定问题  
解决方案：删除掉master下的缓存文件，比如：rm -rf /var/jenkins_home/caches/*


- Node版本大于16时，运行出现如下错误。
```console
⠹  Building for production...Error: error:0308010C:digital envelope routines::unsupported
    at new Hash (node:internal/crypto/hash:69:19)
    at Object.createHash (node:crypto:133:10)

```
问题描述：原因是node升级后要求的SSL版本较高。
解决方案：先 export NODE_OPTIONS=--openssl-legacy-provider，再执行npm run xxx。


##### 参考资料
- https://www.jianshu.com/p/000c2331b891
- http://www.gaowei.vip/info-9NJSP5.html

### 注意事项
- 只有发布系统为https访问，且嵌入到iframe的jenkins地址也是https的情况下。iframe里嵌入的jenkins pipeline页面才会自动刷新进度


