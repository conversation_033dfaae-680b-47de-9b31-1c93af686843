#说明： 这原本是推荐的方式，提交代码后自动对前端和后台代码进行构建编译。但由于GWF（Fuck Your)的问题，国内网络环境基本无法编译前端代码，所以这种方式先不适用

FROM reg.firstshare.cn/docker.io/node:12 AS front-end
#先install, 充分利用缓存，节省构建时间

COPY front-end/package.json /opt/fs-app/front-end/package.json
WORKDIR /opt/fs-app/front-end
# Fuck the GWF
RUN npm install --registry=https://registry.npm.taobao.org
COPY front-end /opt/fs-app/front-end
RUN  npm run build:prod

# final build
FROM reg.firstshare.cn/docker.io/golang:1.14-buster
MAINTAINER <EMAIL>
COPY --from=reg.firstshare.cn/docker.io/bitnami/kubectl:1.12 /opt/bitnami/kubectl/bin/kubectl /bin/kubectl
RUN chmod +x /bin/kubectl

RUN echo "\
deb http://mirrors.aliyun.com/debian/ buster main non-free contrib \n\
deb-src http://mirrors.aliyun.com/debian/ buster main non-free contrib \n\
deb http://mirrors.aliyun.com/debian-security buster/updates main \n\
deb-src http://mirrors.aliyun.com/debian-security buster/updates main \n\
deb http://mirrors.aliyun.com/debian/ buster-updates main non-free contrib \n\
deb-src http://mirrors.aliyun.com/debian/ buster-updates main non-free contrib \n\
deb http://mirrors.aliyun.com/debian/ buster-backports main non-free contrib \n\
deb-src http://mirrors.aliyun.com/debian/ buster-backports main non-free contrib \n\
" > /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y zip unzip cron vim curl less\
    && apt-get clean

ENV GOPROXY=https://goproxy.cn,direct

COPY back-end/go.mod /opt/fs-app/back-end/go.mod
RUN cd /opt/fs-app/back-end/ && go mod download

COPY back-end /opt/fs-app/back-end
COPY --from=front-end /opt/fs-app/back-end/templates /opt/fs-app/back-end/templates

WORKDIR /opt/fs-app/back-end
RUN go build -v -o ${GOPATH}/bin/fs-k8s-app-manager

EXPOSE 80
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]
