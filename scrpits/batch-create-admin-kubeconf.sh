#!/bin/bash

clusters=(
  k8s1
  k8s0
  k8s2
  ucd-k8s2
  ucd-k8s1
  sbt-k8s1
  ksc-k8s1
  hws-k8s1
  ale-k8s1
  forceecrm-k8s1
  xjgc-k8s1
  hisense-k8s1
  cloudmodel-k8s1
  mengniu-k8s1
  iflytek-k8s1
  yangnongchem-k8s1
  hexagonmi-k8s1
  wuzizui99-k8s1
  hsyk-k8s1
  kemaicrm-k8s1
  chinatower-k8s1
  allink8s-k8s1
  teleagi-k8s1
  forsharecrm-k8s1
  cpgc-k8s1
  wingd-k8s1
  kehua-k8s1
)

mkdir -p /home/<USER>/.kube-admin
for clu in "${clusters[@]}"; do
    echo ""
    echo "=== $clu ==="
    clusterName=$clu
    clusterServer=$(grep 'server:' /home/<USER>/.kube/$clu | awk '{print $2}')
    namespace=kube-system
    serviceAccount=cluster-admin
    secretName=cluster-admin
    insecureSkipTlsVerify=false

    adminUser=$(kubectl --kubeconfig=/home/<USER>/.kube/$clu -n kube-system get secret  | grep admin-user | head -n 1 | awk '{print $1}')
    if [ -n "$adminUser" ]; then
        secretName=${adminUser}
    fi
    echo "Secret Name: $secretName"


    ca=$(kubectl --kubeconfig=/home/<USER>/.kube/$clu --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}')
    token=$(kubectl --kubeconfig=/home/<USER>/.kube/$clu --namespace $namespace get secret/$secretName -o jsonpath='{.data.token}' | base64 --decode)

    certItem="certificate-authority-data: ${ca}"
    if [ "${insecureSkipTlsVerify}" = 'true' ]; then
        certItem="insecure-skip-tls-verify: true"
    fi


    echo "
apiVersion: v1
kind: Config
clusters:
  - name: ${clusterName}
    cluster:
      ${certItem}
      server: ${clusterServer}
contexts:
  - name: ${serviceAccount}@${clusterName}
    context:
      cluster: ${clusterName}
      namespace: default
      user: ${serviceAccount}@${clusterName}@user
users:
  - name: ${serviceAccount}@${clusterName}@user
    user:
      token: ${token}
current-context: ${serviceAccount}@${clusterName}
" > /home/<USER>/.kube-admin/$clu

done
