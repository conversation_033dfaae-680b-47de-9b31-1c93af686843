专属云harbor镜像预热脚本
===

1. 从发布系统拿去到最新的镜像预热json文件，放入到 fs-image-preheat-job.json 文件中。
2. 执行 python3 build-job.py 脚本，生成预热任务部署脚本。

### 一些常用命令

- 查看镜像预热job
```
bash batch-kubectl.sh 'kubectl get job -n kube-public | grep harbor-preheat-job'
```

- 创建镜像预热job
```
bash batch-kubectl.sh 'kubectl create -f 20240914'
```

- 删除镜像预热job
```
bash batch-kubectl.sh 'kubectl delete -f 20240914'
```

- 删除镜像预热job2
```
bash batch-kubectl.sh 'kubectl get job -n kube-public  -o wide | grep harbor-preheat-job  | awk '{print $1}' | xargs -I {} kubectl delete job -n kube-public {}'
```

- 查看镜像预热pod
```
bash batch-kubectl.sh 'kubectl get pod -n kube-public | grep harbor-preheat-job'
```

- 删除kube-public命名空间所有event
```
bash batch-kubectl.sh 'kubectl delete event --all -n kube-public'
```

- 查看init状态的pod
```
bash batch-kubectl.sh 'kubectl get pod --all-namespaces  -o wide | grep -v Running | grep Init'
```

- 查看pending状态的pod
```
bash batch-kubectl.sh 'kubectl get pod --all-namespaces  -o wide | grep Pending'
```

- 查看非Running状态的pod
```
bash batch-kubectl.sh 'kubectl get pod --all-namespaces  -o wide --no-headers | grep -v Running | grep -v Completed | grep -v Evicted | grep -v ContainerStatusUnknown | grep -v Error'
```
