#!/bin/bash

clusters=(
  k8s2
  ucd-k8s2
  ucd-k8s1
  sbt-k8s1
  ksc-k8s1
  hws-k8s1
  ale-k8s1
  xjgc-k8s1
  hisense-k8s1
  cloudmodel-k8s1
  mengniu-k8s1
  iflytek-k8s1
  yangnongchem-k8s1
  hexagonmi-k8s1
  wuzizui99-k8s1
  hsyk-k8s1
  kemaicrm-k8s1
  chinatower-k8s1
  allink8s-k8s1
  teleagi-k8s1
  forsharecrm-k8s1
  cpgc-k8s1
  wingd-k8s1
  kehua-k8s1
  jingbo-k8s1
)

if [ "$#" -ne 1 ]; then
  echo 'usage: batch-kubectl.sh "<command string>"'
  echo 'example: batch-kubectl.sh "kubectl get pod -n kube-public | grep harbor-preheat-job"'
  exit 1
fi

cmd_str="$1"
echo "command: $1"

#for clu in "${clusters[@]}"; do
for i in "${!clusters[@]}"; do
    clu="${clusters[$i]}"
    index=$((i + 1))
    echo ""
    echo "===($index): $clu ==="
    # shellcheck disable=SC2001
    cmd=$(echo "$cmd_str" | sed "s#kubectl #kubectl --kubeconfig=/home/<USER>/.kube/$clu #g")
    eval $cmd
done