#!/bin/bash

# 严格参数检查
if [ $# -ne 2 ]; then
  echo "错误：必须提供两个参数！" >&2
  echo "使用方法: $0 <起始值> <结束值>" >&2
  echo "示例: $0 1 20" >&2
  exit 1
fi

start=$1
end=$2

# 参数校验函数
validate_args() {
  # 检查参数是否是正整数
  if ! [[ "$start" =~ ^[0-9]+$ ]]; then
    echo "错误：起始值 '$start' 不是有效的正整数！" >&2
    return 1
  fi
  if ! [[ "$end" =~ ^[0-9]+$ ]]; then
    echo "错误：结束值 '$end' 不是有效的正整数！" >&2
    return 1
  fi

  # 检查 end 是否大于 start
  if [ "$end" -le "$start" ]; then
    echo "错误：结束值 ($end) 必须大于起始值 ($start)！" >&2
    return 1
  fi

  return 0
}

# 执行参数校验
if ! validate_args; then
  exit 1
fi

# 执行循环
echo "正在执行从 $start 到 $end 的循环..."
for ((i = start; i <= end; i++)); do
  echo "Number: $i"
  echo "$i" >>test.bin
  docker build --no-cache -t "reg.foneshare.cn/test/wuzh-test:${i}" -f Dockerfile-for-test .
  docker push "reg.foneshare.cn/test/wuzh-test:${i}"
done

echo "操作完成！"
