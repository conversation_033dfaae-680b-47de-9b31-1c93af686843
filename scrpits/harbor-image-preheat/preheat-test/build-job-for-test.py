import json
from datetime import datetime
from pathlib import Path

start = 1
end = 20
all_images = []
for it in range(start, end + 1):
    all_images.append("apdb-harbor.foneshare.cn:30040/foneshare-proxy/test/wuzh-test:{}".format(it))

images = []
for it in all_images:
    images.append(it)


def split_array(array, group_size):
    return [array[i:i + group_size] for i in range(0, len(array), group_size)]


# 分组
grouped_arrays = split_array(images, int(len(images) / 6) + 1)

output_dir = datetime.now().strftime('%Y%m%d')
Path(output_dir).mkdir(parents=True, exist_ok=True)

# 打印结果
for i, group in enumerate(grouped_arrays):
    item = json.load(open("harbor-preheat-job-template.json"))
    job_name = "harbor-preheat-job-{}".format(i)
    item["metadata"]["name"] = job_name
    init_containers = []
    for ig_idx, ig in enumerate(group):
        init_containers.append({
            "name": "image-index-init-{}-{}".format(i, ig_idx),
            "image": ig,
            "imagePullPolicy": "Always",
            "command": ["echo", "success"]
        })
    item["spec"]["template"]["spec"]["initContainers"] = init_containers

    with open(output_dir + "/" + job_name + ".json", 'w', encoding='utf-8') as f:
        f.write(json.dumps(item, indent=2))
