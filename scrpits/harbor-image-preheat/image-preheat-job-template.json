{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "<must-customized>", "namespace": "image-preheat", "labels": {"app": "fs-image-preheat-job"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "fs-image-preheat-job"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["fs-image-preheat-job"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [], "containers": [{"name": "fs-image-preheat-job", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}