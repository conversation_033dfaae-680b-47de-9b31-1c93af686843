#!/bin/bash

clusters=(
  k8s0
  k8s1
  k8s2
  ucd-k8s2
  ucd-k8s1
  sbt-k8s1
  ksc-k8s1
  hws-k8s1
  ale-k8s1
  xjgc-k8s1
  hisense-k8s1
  cloudmodel-k8s1
  mengniu-k8s1
  iflytek-k8s1
  yangnongchem-k8s1
  hexagonmi-k8s1
  wuzizui99-k8s1
  hsyk-k8s1
  kemaicrm-k8s1
  chinatower-k8s1
  teleagi-k8s1
  forsharecrm-k8s1
  allink8s-k8s1
  cpgc-k8s1
  wingd-k8s1
)

for clu in "${clusters[@]}"; do
    echo ""
    echo "=== $clu ==="
    kubectl --kubeconfig=/home/<USER>/.kube/$clu get deployments --all-namespaces -o jsonpath='{range .items[?(@.spec.replicas>0)]}{"\n"}{.metadata.namespace}{"\t"}{.metadata.name}{"\t"}{.status.conditions[0].lastUpdateTime}{"\t"}{range .spec.template.spec.containers[?(@.env)]}{range .env[?(@.name=="FS_WAR_SERVER_HOST")]}{"FOUND"}{end}{end}{end}' | grep "FOUND" | awk -v cluster="${clu}" '{print cluster"/"$1"/"$2"/"$3}'
done